{"name": "hero-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-input/mask": "^2.0.4", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "axios-hooks": "^5.1.1", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "chart.js": "^4.5.0", "classnames": "^2.5.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.1", "history": "^5.3.0", "html-to-react": "^1.7.0", "js-file-download": "^0.4.12", "jszip": "^3.10.1", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "photoswipe": "^5.4.4", "prettier": "^3.5.3", "qrcode.react": "^4.2.0", "quill": "^2.0.3", "react": "^19.1.0", "react-accessible-treeview": "^2.11.1", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-csv": "^2.2.2", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.58.0", "react-icons": "^5.5.0", "react-query": "^3.39.3", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "sass": "^1.89.2", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.3.0"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}