import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения данных конкретного тренера
 * @param {string} publicId - Идентификатор тренера
 * @returns {Promise} Promise с данными тренера
 */
const getCoach = (publicId) => {
  return axios.get(`${APIRoute.API_MEMBER}/${publicId}`)
}

/**
 * Хук для получения данных конкретного тренера
 * @param {string} publicId - Идентификатор тренера
 * @returns {Object} Объект с данными useQuery
 */
export const useGetCoach = (publicId) => {
  return useQuery({
    queryKey: ['coach', publicId],
    queryFn: () => getCoach(publicId),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data,
    enabled: !!publicId,
  })
}
