import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения списка тренеров
 * @returns {Promise} Promise с данными тренеров
 */
const getCoaches = () => {
  return axios.get(APIRoute.GET_ALL_COACHES)
}

/**
 * Хук для получения списка тренеров
 * @returns {Object} Объект с данными useQuery
 */
export const useGetCoaches = () => {
  return useQuery({
    queryKey: ['coaches'],
    queryFn: getCoaches,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data.values || [],
  })
}
