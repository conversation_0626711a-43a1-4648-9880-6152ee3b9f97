import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для удаления тренера
 * @param {string} publicId - Идентификатор тренера
 * @returns {Promise} Promise с результатом запроса
 */
const deleteCoach = (publicId) => {
  return axios.delete(`${APIRoute.API_MEMBER}/${publicId}`)
}

/**
 * Хук для удаления тренера
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useDeleteCoach = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: deleteCoach,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['coaches'])
        openToast.success({ message: 'Тренер успешно удален!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при удалении тренера' })
    },
  })
}
