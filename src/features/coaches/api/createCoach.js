import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для создания тренера
 * @param {Object} data - Данные тренера
 * @returns {Promise} Promise с данными созданного тренера
 */
const createCoach = (data) => {
  return axios.post(APIRoute.API_MEMBER, {
    ...data,
    kind: 'coaches',
  })
}

/**
 * Хук для создания тренера
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useCreateCoach = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: createCoach,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['coaches'])
        openToast.success({ message: 'Тренер успешно добавлен!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при добавлении тренера' })
    },
  })
}
