import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для обновления тренера
 * @param {Object} data - Данные тренера с public_id
 * @returns {Promise} Promise с обновленными данными тренера
 */
const updateCoach = ({ public_id, ...data }) => {
  return axios.put(`${APIRoute.API_MEMBER}/${public_id}`, data)
}

/**
 * Хук для обновления тренера
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useUpdateCoach = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: updateCoach,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['coaches'])
        queryClient.invalidateQueries(['coach', response.data.public_id])
        openToast.success({ message: 'Тренер успешно обновлен!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при обновлении тренера' })
    },
  })
}
