import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 1

const getUserHistory = (publicId) => {
  return axios.get(`${APIRoute.USER_AUTH_HISTORY}/${publicId}`)
}

export const useGetUserHistory = (publicId) => {
  return useQuery({
    enabled: publicId?.length > 0,
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['userHistory', publicId],
    queryFn: () => getUserHistory(publicId),
  })
}
