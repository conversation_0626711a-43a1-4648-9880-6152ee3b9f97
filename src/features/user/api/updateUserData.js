import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import storage from '@/utils/storage'

const updateUserData = (data) => {
  return axios.put(APIRoute.REGISTER, data)
}

export const useUpdateUserData = ({ config } = {}, onClose) => {
  const user = storage.getUserObj()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (onClose) {
          onClose()
        }

        storage.setUserObj({ ...user, ...JSON.parse(data?.config?.data) })
      }
    },
    ...config,
    mutationFn: updateUserData,
  })
}
