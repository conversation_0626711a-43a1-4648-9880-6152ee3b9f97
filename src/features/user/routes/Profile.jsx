import { useCallback, useEffect, useState } from 'react'
import { Accordion, Col, Row, Tab, Tabs } from 'react-bootstrap'
import { useParams } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import Layout from '@/components/Layout/Layout'
import Loader from '@/components/Loader/Loader'

import { Order, UpdateOrdersTicketsContextProvider } from '@/features/orders'
import { useGetUserOrders } from '@/features/orders/api/getUserOrders'
import { OrderTicketsProfile, Soldgoods } from '@/features/tickets'
import { useGetUserTickets } from '@/features/tickets/api/getUserTickets'
import { useGetUser } from '@/features/user/api/getUser'
import { useGetUserHistory } from '@/features/user/api/getUserHistory'
import { ProfileStatistics } from '@/features/user/components/ProfileStatistics/ProfileStatistics'
import { UserClubs } from '@/features/user/components/UserClubs'
import { UserHistory } from '@/features/user/components/UserHistory/UserHistory'
import { UserInfo } from '@/features/user/components/UserInfo/UserInfo'
import { UserNotFound } from '@/features/user/components/UserNotFound'
import { queryClient } from '@/lib/react-query'
import storage from '@/utils/storage'

export const Profile = () => {
  const { id } = useParams()

  const userStorage = storage.getUserObj()
  const userId = id ?? userStorage.public_id
  const [tickets, setTickets] = useState([])
  const [products, setProducts] = useState([])
  const [insurances, setInsurances] = useState([])
  const [activeTab, setActiveTab] = useState('orders')
  const [ordersRender, setOrdersRender] = useState([])
  const [ticketsTransfered, setTicketsTransfered] = useState([])
  const [isDirtyTab, setIsDirtyTab] = useState({
    history: false,
  })

  const getUserQuery = useGetUser(userId)
  const user = getUserQuery?.data?.data
  const getUserOrdersQuery = useGetUserOrders(userId, Boolean(user))
  const getUserTicketsQuery = useGetUserTickets(userId, Boolean(user))
  const getUserHistory = useGetUserHistory(isDirtyTab.history && userId)
  const orders = getUserOrdersQuery?.data?.data?.values
  const ticketsData = getUserTicketsQuery?.data?.data?.values
  const userHistory = queryClient.getQueryData(['userHistory', userId])?.data?.values

  const isUserNotFound = getUserQuery?.data?.response?.status === 400

  const updateRoles = useCallback(() => {
    if (
      userStorage?.public_id === user?.public_id &&
      userStorage?.role &&
      user?.role &&
      userStorage.role.toString() !== user.role.toString()
    ) {
      const newUserLocal = { ...userStorage }

      newUserLocal.role = user.role

      storage.setUserObj(newUserLocal)
      window.location.reload()
    }
  }, [userStorage, user])

  useEffect(() => {
    updateRoles()
  }, [user, updateRoles])

  useEffect(() => {
    if (ticketsData?.length > 0) {
      const mainTickets = []
      const transferedTickets = []
      const products = []
      const insurances = []

      ticketsData.forEach((item) => {
        if (item?.type === 'ticket' && !item?.transfered) {
          mainTickets.push(item)
        } else if (item?.type === 'ticket' && item?.transfered) {
          transferedTickets.push(item)
        } else if (item?.type === 'product') {
          products.push(item)
        } else if (item?.type === 'insurance') {
          insurances.push(item)
        }
      })

      setTickets([...mainTickets])
      setTicketsTransfered([...transferedTickets])
      setProducts([...products])
      setInsurances([...insurances])
    }
  }, [ticketsData])

  const handleUpdateTickets = () => {
    getUserTicketsQuery.refetch()
  }

  const handleSelectTab = (key) => {
    setActiveTab(key)

    if (key === 'history' && !isDirtyTab[key]) {
      setIsDirtyTab((prev) => ({ ...prev, history: true }))
    }
  }

  const getOrders = () => {
    getUserOrdersQuery.refetch()
  }

  const handleUpdateUser = () => {
    getUserQuery.refetch()
  }

  if (isUserNotFound) {
    return (
      <Layout>
        <UserNotFound />
      </Layout>
    )
  }

  return (
    <Layout>
      <UserInfo user={user} isLoading={getUserQuery.isLoading} onUpdate={handleUpdateUser} />

      <ProfileStatistics
        orders={orders?.length}
        tickets={tickets?.length + ticketsTransfered?.length}
        products={products?.length}
        insurances={insurances?.length}
        clubs={user?.club?.length}
      />

      <Tabs className="mb-3" defaultActiveKey="orders" onSelect={handleSelectTab}>
        <Tab title="Заказы" eventKey="orders">
          <Loader isLoading={getUserOrdersQuery.isLoading} text="Загрузка заказов">
            {orders?.length > 0 ? (
              <>
                <Accordion className="mb-3" defaultActiveKey={`${ordersRender[0]?.public_id}`}>
                  {ordersRender.map((order) => (
                    <Accordion.Item eventKey={`${order.public_id}`} key={order.public_id}>
                      <Accordion.Header>
                        {order?.amount ? order.amount.toLocaleString() : 0} &#8381;, {order.email}
                      </Accordion.Header>
                      <Accordion.Body className="pt-3">
                        <Order order={order} onUpdateOrders={getOrders} />

                        <OrderTicketsProfile tickets={order?.items?.tickets} />
                      </Accordion.Body>
                    </Accordion.Item>
                  ))}
                </Accordion>

                <AdvancedPagination values={orders} setValues={setOrdersRender} />
              </>
            ) : (
              <p>Нет заказов</p>
            )}
          </Loader>
        </Tab>

        <Tab title="Билеты" eventKey="tickets">
          {ticketsData?.length > 0 ? (
            <>
              <Row className="mb-4">
                <Col>
                  <UpdateOrdersTicketsContextProvider value={{ handleUpdateTickets: handleUpdateTickets }}>
                    <Soldgoods soldgoods={tickets} variant="full" />
                  </UpdateOrdersTicketsContextProvider>
                </Col>
              </Row>

              <Row className="mb-4">
                <Col>
                  <h5 className="mb-2">Полученные билеты</h5>
                  <UpdateOrdersTicketsContextProvider value={{ handleUpdateTickets: handleUpdateTickets }}>
                    <Soldgoods soldgoods={ticketsTransfered} variant="full" />
                  </UpdateOrdersTicketsContextProvider>
                </Col>
              </Row>
            </>
          ) : (
            <p>Нет билетов</p>
          )}
        </Tab>

        <Tab title="Товары" eventKey="products">
          {products?.length > 0 ? (
            <UpdateOrdersTicketsContextProvider value={{ handleUpdateTickets: handleUpdateTickets }}>
              <Soldgoods soldgoods={products} variant="full" />
            </UpdateOrdersTicketsContextProvider>
          ) : (
            <p>Нет товаров</p>
          )}
        </Tab>

        <Tab title="Страховки" eventKey="insurances">
          {insurances?.length > 0 ? (
            <UpdateOrdersTicketsContextProvider value={{ handleUpdateTickets: handleUpdateTickets }}>
              <Soldgoods soldgoods={insurances} variant="full" />
            </UpdateOrdersTicketsContextProvider>
          ) : (
            <p>Нет страховок</p>
          )}
        </Tab>

        <Tab title="Клубы" eventKey="clubs">
          {user?.club?.length > 0 ? activeTab === 'clubs' && <UserClubs clubs={user?.club} /> : <p>Нет клубов</p>}
        </Tab>
        <Tab title="История активности" eventKey="history">
          <Loader isLoading={getUserHistory.isLoading}>
            {userHistory?.length > 0 ? <UserHistory history={userHistory} /> : <p>Нет истории активности</p>}
          </Loader>
        </Tab>
      </Tabs>
    </Layout>
  )
}
