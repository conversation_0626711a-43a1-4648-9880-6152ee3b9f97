import { useEffect, useState } from 'react'
import { Card } from 'react-bootstrap'

import IconDesktop from '@/assets/img/icons/icon-desktop-mac.svg?react'
import IconPhone from '@/assets/img/icons/icon-phone.svg?react'
import IconSignIn from '@/assets/img/icons/icon-sign-in.svg?react'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'

import { unixToMoment } from '@/utils/date'

import styles from './UserHistory.module.scss'

const returnDeviceIcon = (os) => {
  switch (os) {
    case 'Android':
      return <IconPhone className={styles.iconPhone} />
    case 'iOS':
      return <IconPhone className={styles.iconPhone} />
    case 'Windows':
      return <IconDesktop className={styles.iconDesktop} />
    case 'Mac OS X':
      return <IconDesktop className={styles.iconDesktop} />
    case 'Linux':
      return <IconDesktop className={styles.iconDesktop} />
    case 'Ubuntu':
      return <IconDesktop className={styles.iconDesktop} />
    default:
      return <IconSignIn className={styles.iconDesktop} />
  }
}

export const UserHistory = ({ history }) => {
  const [valuesRender, setValuesRender] = useState([])
  const [reverseHistory, setReverseHistory] = useState([])

  useEffect(() => {
    if (history?.length > 0) {
      const copyHistory = [...history]
      setReverseHistory(copyHistory.reverse())
    }
  }, [history])

  if (!history || history?.length === 0) return null

  return (
    <>
      <Card className={`${styles.card} mb-3`}>
        <Card.Body>
          {valuesRender?.map((item, index) => (
            <div className={styles.item} key={index}>
              <div className={styles.iconWrap}>{returnDeviceIcon(item.os)}</div>
              <div>
                <p className={styles.os}>{item.os}</p>
                <p className={styles.browser}>
                  Браузер {item.browser} · {item.ip}
                </p>
                <p className={styles.country}>
                  {item.city}, {item.country} · {unixToMoment(item.temp).format('D MMMM YYYY в HH:mm')}
                </p>
              </div>
            </div>
          ))}
        </Card.Body>
      </Card>

      <AdvancedPagination values={reverseHistory} setValues={setValuesRender} />
    </>
  )
}
