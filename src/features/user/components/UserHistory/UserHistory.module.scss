@import '../../../../assets/styles/variables';

.card {
  margin: 0 auto;
  max-width: $grid-breakpoint-sm;
}

.item {
  margin-bottom: 8px;
  padding: 4px 0;

  display: grid;
  grid-template-columns: 50px 1fr;
}

.os {
  margin-bottom: 0;
}

.browser {
  margin-bottom: 0;

  font-size: 15px;
  color: #646d8f;
}

.country {
  margin-bottom: 0;

  font-size: 14px;
  color: rgba(100, 109, 143, 0.7);
}

.iconWrap {
  display: grid;
  place-items: center;
}

.iconPhone {
  width: 29px;
  height: 28px;
}

.iconDesktop {
  width: 28px;
  height: 28px;
}

@media (min-width: $grid-breakpoint-lg) {
  .item {
    border-radius: 4px;

    transition: background-color 0.25s;

    &:hover {
      background-color: rgba(100, 109, 143, 0.05);
    }
  }
}
