import { Card } from 'react-bootstrap'

import { getRightNames } from '@/utils/common'

import styles from './ProfileStatistics.module.scss'

export const ProfileStatistics = ({ orders, tickets, products, insurances, clubs }) => {
  return (
    <Card className="mb-4">
      <Card.Body className={styles.cardBody}>
        <p className={styles.item}>
          <span className={styles.itemValue}>{orders ?? 0}</span>
          <span className={styles.itemLabel}>{getRightNames(orders ?? 0, 'Заказ', 'Заказа', 'Заказов')}</span>
        </p>
        <p className={styles.item}>
          <span className={styles.itemValue}>{tickets}</span>
          <span className={styles.itemLabel}>{getRightNames(tickets, 'Билет', 'Билета', 'Билетов')}</span>
        </p>
        <p className={styles.item}>
          <span className={styles.itemValue}>{products}</span>
          <span className={styles.itemLabel}>{getRightNames(products, 'Товар', 'Товара', 'Товаров')}</span>
        </p>
        <p className={styles.item}>
          <span className={styles.itemValue}>{insurances}</span>
          <span className={styles.itemLabel}>{getRightNames(insurances, 'Страховка', 'Страховки', 'Страховок')}</span>
        </p>
        <p className={styles.item}>
          <span className={styles.itemValue}>{clubs ?? 0}</span>
          <span className={styles.itemLabel}>{getRightNames(clubs ?? 0, 'Клуб', 'Клуба', 'Клубов')}</span>
        </p>
      </Card.Body>
    </Card>
  )
}
