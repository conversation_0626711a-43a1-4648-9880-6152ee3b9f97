@import '../../../../assets/styles/variables';

.cardBody {
  position: relative;
  padding: 24px;

  display: grid;
  justify-content: space-around;
  grid-template-columns: auto auto;
  row-gap: 24px;

  &::before {
    position: absolute;
    top: 24px;
    left: 50%;
    bottom: 24px;

    display: block;
    width: 1px;

    background-color: var(--bs-border-color);

    content: '';
  }
}

.item {
  margin: 0;

  display: grid;
  text-align: center;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.itemValue {
  font-size: 20px;
  line-height: 1;
  color: var(--bs-body-color);
  font-weight: 600;
  margin-bottom: 4px;
}

.itemLabel {
  font-size: 14px;
  line-height: 1;
  color: var(--bs-secondary-color);
  font-weight: 500;
}

@media (min-width: $grid-breakpoint-md) {
  .cardBody {
    grid-template-columns: repeat(5, 1fr);

    &::before {
      display: none;
    }
  }

  .item {
    width: 100%;
    justify-content: center;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:not(:last-child) {
      border-right: 1px solid var(--bs-border-color);
    }

    &:hover {
      background-color: var(--bs-secondary-bg);
      transform: translateY(-2px);
    }
  }

  .itemValue {
    font-size: 24px;
  }

  .itemLabel {
    font-size: 13px;
  }
}
