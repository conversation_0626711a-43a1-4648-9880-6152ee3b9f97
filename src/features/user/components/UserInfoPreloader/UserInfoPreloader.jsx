import { Card, Spinner } from 'react-bootstrap'

import styles from './UserInfoPreloader.module.scss'

export const UserInfoPreloader = () => {
  return (
    <>
      <Card className={`${styles.main} mb-3`}>
        <Spinner className={styles.spinner} animation="grow" />
      </Card>

      <div className={`${styles.extraWrap} mb-3`}>
        <Card className={`${styles.extra}`}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>

        <Card className={`${styles.socials}`}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </div>

      <Card className={`${styles.roles} mb-3`}>
        <Spinner className={styles.spinner} animation="grow" />
      </Card>
    </>
  )
}
