@import '../../../../assets/styles/variables';

.extraWrap {
  display: grid;
  gap: 16px;
}

.main,
.extra,
.roles,
.socials {
  display: grid;
  place-items: center;
}

.main {
  min-height: 307px;
}

.extra {
  min-height: 162px;
}

.socials {
  min-height: 86px;
}

.roles {
  min-height: 251px;
}

.spinner {
  background-color: rgba(100, 109, 143, 0.5);
}

@media (min-width: $grid-breakpoint-md) {
  .extraWrap {
    grid-template-columns: 1fr 200px;
  }

  .main {
    min-height: 244px;
  }

  .extra {
    min-height: 126px;
  }

  .socials {
    min-height: 126px;
  }

  .roles {
    min-height: 133px;
  }
}

@media (min-width: $grid-breakpoint-lg) {
  .extraWrap {
    grid-template-columns: 1fr 1fr;
  }

  .main {
    min-height: 186px;
  }

  .extra {
    min-height: 104px;
  }

  .socials {
    min-height: 104px;
  }

  .roles {
    min-height: 90px;
  }
}
