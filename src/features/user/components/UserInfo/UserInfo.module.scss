@import '../../../../assets/styles/mixins';

.nameWrap {
  display: grid;
  align-items: center;
  justify-content: start;
  grid-template-columns: auto auto;
  gap: 4px;
}

.name {
  margin-bottom: 0;

  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  color: var(--bs-body-color);
}

.secondName {
  display: inline-flex;
  gap: 4px;
  align-items: center;
}

.nameEmpty {
  font-style: italic;
  text-transform: none;
  color: var(--bs-secondary-color);
}

.nameIcon {
  transform: translateY(7px);

  width: 16px;
  height: 16px;
  color: var(--bs-warning);
  transition: color 0.2s ease;
}

.noSecondName {
  transform: translateY(3px);
}

.buttonEdit {
  padding: 0;

  font-size: 12px;
  text-decoration: underline;
  color: var(--bs-secondary-color);
  transition: color 0.2s ease;

  background: none;
  border: none;

  &:hover {
    color: var(--bs-primary);
  }

  &:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
  }
}

.info {
  gap: 16px;
  display: grid;
  align-content: start;
  grid-template-columns: 1fr 1fr;
}

.email {
  margin-bottom: 8px;
  padding-left: 26px;
  color: var(--bs-body-color);
  position: relative;

  .bi {
    position: absolute;
    left: 0;
    top: 2px;
    color: var(--bs-primary);
  }
}

.phone {
  padding-left: 26px;
  color: var(--bs-body-color);
  position: relative;

  .bi {
    position: absolute;
    left: 0;
    top: 2px;
    color: var(--bs-success);
  }
}

.bottom {
  display: grid;
  grid-template-columns: auto auto;
  justify-content: space-between;

  font-size: 12px;
  color: var(--bs-secondary-color);
}

.lastVisit {
  margin: 0;

  display: grid;
}

.historyBtn {
  @include smallBtn;
  color: var(--bs-secondary-color);
  transition: color 0.2s ease;

  &:hover:not(:disabled),
  &:active:not(:disabled) {
    color: var(--bs-primary);
  }

  &:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
  }
}

.extraWrap {
  display: grid;
  gap: 16px;
}

.cardTitle {
  font-size: 18px;
  color: var(--bs-body-color);
  font-weight: 600;
}

.extraCardBody {
  gap: 16px;
  display: grid;
  align-items: start;
  grid-template-columns: repeat(auto-fit, minmax(146px, 1fr));
}

.socialCardBody {
  display: grid;
  grid-template-columns: auto auto;
  gap: 16px;
}

.socialItem {
  gap: 6px;
  display: grid;
  align-items: center;
  grid-template-columns: auto 1fr;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--bs-secondary-bg);
  }

  svg {
    width: 24px;
    height: 24px;
    color: var(--bs-body-color);
  }
}

.socialText {
  margin: 0;

  font-size: 14px;
  color: var(--bs-secondary-color);
}

.connectedWrap {
  margin: 0;

  display: grid;
}

.connectedLabel {
  font-size: 11px;
  color: var(--bs-success);
  font-weight: 600;
  text-transform: uppercase;
}

.connectedValue {
  font-size: 10px;
  color: var(--bs-secondary-color);
  font-family: monospace;
}

@media (min-width: $grid-breakpoint-md) {
  .name {
    font-size: 24px;
  }

  .nameIcon {
    transform: translateY(11px);

    width: 20px;
    height: 20px;
  }

  .noSecondName {
    transform: translateY(4px);
  }

  .infoWrap {
    display: grid;
    grid-template-columns: 1fr auto;
  }

  .info {
    column-gap: 48px;
    justify-content: start;
    grid-template-columns: auto auto;
  }

  .lastVisit {
    gap: 4px;
    align-items: end;
    grid-template-columns: auto auto;
  }

  .extraWrap {
    grid-template-columns: 1fr 200px;
  }

  .extraCardBody {
    justify-content: space-between;
    grid-template-columns: repeat(3, auto);
  }

  .socialCardBody {
    gap: 16px;
    grid-template-columns: auto;
  }
}

@media (min-width: $grid-breakpoint-lg) {
  .info {
    column-gap: 24px;
    grid-template-columns: auto auto auto auto;
  }

  .buttonEdit {
    @include smallBtn;
    color: var(--bs-secondary-color);
    transition: color 0.2s ease;

    &:hover:not(:disabled),
    &:active:not(:disabled) {
      color: var(--bs-primary);
    }

    &:focus {
      outline: 2px solid var(--bs-primary);
      outline-offset: 2px;
    }
  }

  .extraWrap {
    grid-template-columns: 1fr 1fr;
  }

  .socialCardBody {
    gap: 16px;
    grid-template-columns: 180px 180px;
  }
}
