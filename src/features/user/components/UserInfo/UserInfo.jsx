import { useState } from 'react'
import { Card } from 'react-bootstrap'

import GoogleIcon from '@/assets/img/icons/icon-google.svg?react'
import SettingsIcon from '@/assets/img/icons/icon-settings2.svg?react'
import VkIcon from '@/assets/img/icons/icon-vk.svg?react'

import { CheckPermission } from '@/components/CheckPermission/CheckPermission'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import PagePreloader from '@/components/PagePreloader/PagePreloader'

import { accessConfig } from '@/accessConfig'
import { Role } from '@/const'
import { useTheme } from '@/contexts/ThemeContext'
import { useGetUserHistory } from '@/features/user/api/getUserHistory'
import { ProfileCompletion } from '@/features/user/components/ProfileCompletion/ProfileCompletion'
import { UserInfoPreloader } from '@/features/user/components/UserInfoPreloader/UserInfoPreloader'
import { UserRoles } from '@/features/user/components/UserRoles/UserRoles'
import { queryClient } from '@/lib/react-query'
import { EditUserModal } from '@/pages/UsersScreen/components/EditUserModal/EditUserModal'
import { formatPhoneNumber, getRightNames, getSex } from '@/utils/common'
import { unixToMoment } from '@/utils/date'

import styles from './UserInfo.module.scss'

const returnAge = (date) => {
  const age = date ? unixToMoment().diff(date, 'years') : null
  return age ? `${age} ${getRightNames(age, 'год', 'года', 'лет')}` : null
}

export const UserInfo = ({ user, isLoading, onUpdate }) => {
  const [isVisibleHistory, setIsVisibleHistory] = useState(false)
  const [isOpenEditModal, setIsOpenEditModal] = useState(false)
  const { effectiveTheme, themes } = useTheme()

  const getUserHistory = useGetUserHistory(isVisibleHistory && user?.public_id)
  const userHistory = queryClient.getQueryData(['userHistory', user?.public_id])?.data?.values

  const returnHistoryDate = (history) => {
    if (history?.length === 0) {
      return <span>нет истории</span>
    } else if (history?.length > 0) {
      return <span>{unixToMoment(userHistory[userHistory.length - 1]?.temp).format('D MMMM YYYY, HH:mm')}</span>
    }

    return (
      <button
        className={styles.historyBtn}
        onClick={() => setIsVisibleHistory(true)}
        disabled={getUserHistory.isLoading}
      >
        показать
      </button>
    )
  }

  const handleCloseEditModal = () => {
    setIsOpenEditModal(false)
  }

  return (
    <PagePreloader isLoading={isLoading} preloader={<UserInfoPreloader />}>
      <Card className="mb-3">
        <Card.Body>
          <div className={`${styles.nameWrap}`}>
            <p className={`${styles.name}`}>
              {user?.last_name || user?.name || user?.second_name ? (
                <span>
                  {user?.last_name} {user?.name}{' '}
                  <span className={styles.secondName}>
                    {user?.second_name}{' '}
                    {[Role.ADMIN, Role.SUPERADMIN].some((el) => user?.role.includes(el)) && (
                      <SettingsIcon
                        className={`${styles.nameIcon} ${!user?.second_name ? styles.noSecondName : ''}`}
                        title="Администратор"
                      />
                    )}
                  </span>
                </span>
              ) : (
                <span className={styles.nameEmpty}>ФИО не заполнены</span>
              )}
            </p>
          </div>

          <div className="mb-4">
            {onUpdate && (
              <CheckPermission allowedRoles={accessConfig.profile.editButton}>
                <button className={styles.buttonEdit} onClick={() => setIsOpenEditModal(true)}>
                  редактировать
                </button>
              </CheckPermission>
            )}
          </div>

          <div className={styles.infoWrap}>
            <div className={`${styles.info} mb-3`}>
              <ItemLabelValue
                value={user?.birth_date && unixToMoment(user.birth_date).format('D MMMM YYYY')}
                label="дата рождения"
              />
              <ItemLabelValue value={returnAge(user?.birth_date)} label="возраст" />
              <ItemLabelValue value={user?.gender ? getSex(user?.gender) : '-'} label="пол" />
            </div>

            <div>
              <p className={styles.email}>
                <i className="bi bi-envelope-fill me-2" />
                {user?.email}
              </p>
              <p className={styles.phone}>
                <i className="bi bi-phone-fill me-2" />
                {formatPhoneNumber(user?.phone)}
              </p>
            </div>
          </div>

          <div className={styles.bottom}>
            <p className={styles.lastVisit}>
              <span>Последний вход: </span>
              {returnHistoryDate(userHistory)}
            </p>

            <ProfileCompletion user={user} />
          </div>
        </Card.Body>
      </Card>

      <div className={`${styles.extraWrap} mb-3`}>
        <Card>
          <Card.Header className="pb-0">
            <Card.Title className={styles.cardTitle}>Экстренный контакт</Card.Title>
          </Card.Header>
          <Card.Body className={styles.extraCardBody}>
            <ItemLabelValue value={user?.extra_contact} label="имя и фамилия" />

            <ItemLabelValue value={formatPhoneNumber(user?.contact_phone)} label="телефон" />

            <ItemLabelValue value={user?.type_relate} label="отношения" />
          </Card.Body>
        </Card>

        <Card>
          <Card.Header className="pb-0">
            <Card.Title className={styles.cardTitle}>Соцсети</Card.Title>
          </Card.Header>
          <Card.Body className={styles.socialCardBody}>
            <div className={styles.socialItem}>
              <VkIcon
                style={{
                  filter: effectiveTheme === themes.LIGHT ? 'invert(0)' : 'invert(1)',
                }}
              />
              {user?.vk_id ? (
                <p className={styles.connectedWrap}>
                  <span className={styles.connectedLabel}>подключено</span>
                  <span className={styles.connectedValue}>{user.vk_id}</span>
                </p>
              ) : (
                <p className={styles.socialText}>не подключено</p>
              )}
            </div>

            <div className={styles.socialItem}>
              <GoogleIcon
                style={{
                  filter: effectiveTheme === themes.LIGHT ? 'invert(0)' : 'invert(1)',
                }}
              />
              {user?.google_id ? (
                <p className={styles.connectedWrap}>
                  <span className={styles.connectedLabel}>подключено</span>
                  <span className={styles.connectedValue}>{user.google_id}</span>
                </p>
              ) : (
                <p className={styles.socialText}>не подключено</p>
              )}
            </div>
          </Card.Body>
        </Card>
      </div>

      <UserRoles user={user} />

      <EditUserModal
        selectedUser={user}
        isShowModal={isOpenEditModal}
        onCloseModal={handleCloseEditModal}
        onUpdate={onUpdate}
      />
    </PagePreloader>
  )
}
