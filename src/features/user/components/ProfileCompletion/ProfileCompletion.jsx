import classNames from 'classnames/bind'
import { useEffect, useState } from 'react'

import styles from './ProfileCompletion.module.scss'

const cx = classNames.bind(styles)

export const ProfileCompletion = ({ user }) => {
  const [progress, setProgress] = useState(-1)

  useEffect(() => {
    let count = 0

    if (user) {
      if (user?.name) count += 10
      if (user?.last_name) count += 10
      if (user?.second_name) count += 10
      if (user?.birth_date) count += 10
      if (user?.gender) count += 10
      if (user?.phone) count += 10
      if (user?.email) count += 10
      if (user?.extra_contact) count += 10
      if (user?.contact_phone) count += 10
      if (user?.type_relate) count += 10

      setProgress(count)
    }
  }, [user])

  const progressBarClassName = cx({
    progressBar: true,
    progressBarDanger: progress <= 50,
    progressBarWarning: progress > 50 && progress <= 70,
    progressBarSuccess: progress > 70,
  })

  if (!user || progress < 0 || progress === 100) return null

  return (
    <div>
      Профиль заполнен на
      <div className={styles.scaleWrap}>
        <div className={styles.scale}>
          <div className={progressBarClassName} style={{ width: `${progress}%` }} />
        </div>
        <span className={styles.progressValue}>{progress}%</span>
      </div>
    </div>
  )
}
