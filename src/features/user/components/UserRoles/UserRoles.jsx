import React from 'react'
import { Card } from 'react-bootstrap'

import { getRoleName } from '@/utils/common'

import styles from './UserRoles.module.scss'

export const UserRoles = ({ user }) => {
  return (
    <Card className="mb-4">
      <Card.Header className="pb-0">
        <Card.Title className={styles.cardTitle}>Роли</Card.Title>
      </Card.Header>
      <Card.Body>
        <div className={styles.roles}>
          {user?.role?.map((role) => (
            <span className={styles.role} key={role}>
              {getRoleName(role)}{' '}
            </span>
          ))}
        </div>
      </Card.Body>
    </Card>
  )
}
