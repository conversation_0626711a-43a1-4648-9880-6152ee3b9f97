import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createProcessing = (data) => {
  return axios.post(APIRoute.CUD_PROCESSING, data)
}

export const useCreateProcessing = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['processingList'])
        openToast.success({ message: 'Объект процессинга создан!' })
      }
    },
    mutationFn: createProcessing,
  })
}
