import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteProcessing = (publicId) => {
  return axios.delete(`${APIRoute.CUD_PROCESSING}/${publicId}`)
}

export const useDeleteProcessing = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['processingList'])
        openToast.success({ message: 'Объект процессинга удалён!' })
      }
    },
    mutationFn: deleteProcessing,
  })
}
