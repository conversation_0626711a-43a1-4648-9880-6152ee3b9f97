import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateProcessing = ({ publicId, data }) => {
  return axios.put(`${APIRoute.CUD_PROCESSING}/${publicId}`, data)
}

export const useUpdateProcessing = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['processingList'])
        openToast.success({ message: 'Объект процессинга обновлён!' })
      }
    },
    mutationFn: updateProcessing,
  })
}
