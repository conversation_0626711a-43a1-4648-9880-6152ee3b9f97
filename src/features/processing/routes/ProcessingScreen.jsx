import { useState } from 'react'
import { Button } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { useDeleteProcessing } from '@/features/processing/api/deleteProcessing'
import { useGetProcessingList } from '@/features/processing/api/getProcessingList'
import { ProcessingModalForm } from '@/features/processing/components/ProcessingModalForm'
import { processingTableData } from '@/features/processing/processingScreenData'

export const ProcessingScreen = () => {
  const [isShowModalForm, setIsShowModalForm] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)

  const deleteProcessingMutation = useDeleteProcessing()
  const getProcessingListQuery = useGetProcessingList()
  const processingList = getProcessingListQuery?.data?.data?.values

  const handleClickRow = (item) => {
    setSelectedItem(item)
    setIsShowModalForm(true)
  }

  const handleConfirmDelete = (item) => {
    setSelectedItem(item)
    setConfirmDeleteModal(true)
  }

  const handleCloseConfirmDelete = () => {
    setSelectedItem(null)
    setConfirmDeleteModal(false)
  }

  const handleDeleteItem = () => {
    deleteProcessingMutation.mutate(selectedItem.public_id, {
      onSuccess: () => {
        handleCloseConfirmDelete()
      },
    })
  }

  const handleCloseModalForm = () => {
    setIsShowModalForm(false)
    setSelectedItem(null)
  }

  const returnActions = (item) => {
    return (
      <td className="d-flex justify-content-end" onClick={(evt) => evt.stopPropagation()}>
        <Button onClick={() => handleConfirmDelete(item)} variant="outline-danger" size="sm">
          <i className="bi bi-trash" />
        </Button>
      </td>
    )
  }

  return (
    <Layout title="Процессинг" onClickAddButton={() => setIsShowModalForm(true)}>
      <TableTemplate
        values={processingList}
        data={processingTableData}
        actionRow={handleClickRow}
        actions={returnActions}
      />

      <ProcessingModalForm isShow={isShowModalForm} onClose={handleCloseModalForm} selectedItem={selectedItem} />

      <ConfirmDeleteModal
        isShow={confirmDeleteModal}
        onClose={handleCloseConfirmDelete}
        onDeleteItem={handleDeleteItem}
      />
    </Layout>
  )
}
