import React from 'react'
import { Col, Container, Row, Spinner } from 'react-bootstrap'
import { useParams } from 'react-router-dom'

import { UpdateOrdersTicketsContextProvider } from '@/features/orders'
import { useGetTicket } from '@/features/tickets/api/getTicket'
import { InsuranceInOrder } from '@/features/tickets/components/InsuranceInOrder'
import { ProductInOrder } from '@/features/tickets/components/ProductInOrder'
import { Ticket } from '@/features/tickets/components/Ticket'

const returnTypeName = (type) => {
  if (type === 'ticket') {
    return 'билете'
  } else if (type === 'product') {
    return 'товаре'
  } else if (type === 'license') {
    return 'лицензии'
  } else if (type === 'insurance') {
    return 'страховке'
  } else {
    return 'заказе'
  }
}

export const SoldgoodPage = () => {
  const { id } = useParams()
  const { data, isLoading, error, refetch } = useGetTicket(id)

  const handleUpdateTickets = () => {
    refetch()
  }

  if (isLoading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '80vh' }}>
        <Spinner animation="border" />
      </Container>
    )
  }

  if (error) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '80vh' }}>
        <p>Произошла ошибка при загрузке билета: {error.message}</p>
      </Container>
    )
  }

  if (!data?.data) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '80vh' }}>
        <p>Билет не найден</p>
      </Container>
    )
  }

  return (
    <Container className="my-4">
      <h1 className="fs-3 mb-4">Информация о {returnTypeName(data.data.type)}</h1>
      <Row>
        <Col>
          <UpdateOrdersTicketsContextProvider value={{ handleUpdateTickets }}>
            {data.data.type === 'ticket' ? (
              <Ticket ticket={data.data} variant="full" isExpandedByDefault={true} />
            ) : data.data.type === 'product' ? (
              <ProductInOrder product={data.data} variant="full" />
            ) : data.data.type === 'insurance' ? (
              <InsuranceInOrder insurance={data.data} variant="full" />
            ) : null}
          </UpdateOrdersTicketsContextProvider>
        </Col>
      </Row>
    </Container>
  )
}

export default SoldgoodPage
