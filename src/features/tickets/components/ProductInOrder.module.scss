@import '../../../assets/styles/mixins';

.product {
  margin-bottom: 6px;
  position: relative;
  padding: 16px;

  background-color: var(--bs-card-bg);
  border-radius: 4px;
  box-shadow:
    0 1px 0 0 var(--bs-border-color),
    0 0 0 1px var(--bs-border-color-translucent);
}

.priceInfoWrap {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 16px;
}

.productInfoWrap {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, auto));
  gap: 20px;
  width: 100%;
  padding: 0 8px;

  & > * {
    min-width: 0; // Предотвращает переполнение текста
    word-break: break-word; // Перенос длинных строк
  }
}

.link {
  color: var(--bs-link-color);
  text-decoration: underline;

  &:hover {
    color: var(--bs-link-hover-color);
  }
}

.btnWrap {
  position: absolute;
  top: 16px;
  right: 16px;

  display: grid;
  grid-template-columns: auto;
  align-items: stretch;
  gap: 6px;
}

.btn {
  background: transparent;
  border: none;
  border-radius: 4px;
}

.dropDownDisable {
  opacity: 0.3;
  pointer-events: none;
}

.dropDownToggle {
  display: flex;
  justify-content: center;
  cursor: pointer;
}

.iconMore {
  & path {
    fill: var(--bs-secondary-color);
  }
}

.danger,
.danger:hover {
  color: var(--bs-danger);
}

// Медиа запросы для адаптивности
@media (max-width: 992px) {
  .productInfoWrap {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 16px;
    padding: 0 4px;
  }
}

@media (max-width: 768px) {
  .priceInfoWrap {
    grid-template-columns: 1fr;
    gap: 16px;
    text-align: left;
  }

  .productInfoWrap {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0;
    margin-top: 12px;
  }

  .btnWrap {
    position: static;
    grid-column: 1;
    justify-self: end;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .productInfoWrap {
    gap: 12px;
  }

  .priceInfoWrap {
    gap: 12px;
  }
}
