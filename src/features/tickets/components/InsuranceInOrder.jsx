import classNames from 'classnames/bind'
import { useContext, useState } from 'react'
import { ButtonGroup, Dropdown } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import IconMoreVertical from '@/assets/img/icons/icon-more-vertical.svg?react'

import { CostStatus } from '@/components/CostStatus/CostStatus'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { accessConfig } from '@/accessConfig'
import { AppRoute } from '@/const'
import { UpdateOrdersTicketsContext } from '@/features/orders'
import { useToast } from '@/hooks/useToast'
import { isAccessPermissions } from '@/utils/common'
import storage from '@/utils/storage'

import { ConfirmRefundTicketModal } from './ConfirmRefundTicketModal'
import styles from './ProductInOrder.module.scss'
import { useCloseOrderFiscal } from '../api/closeOrderFiscal'
import { useDeleteTicket } from '../api/deleteTicket'
import { useForcedRefundSoldgood } from '../api/forcedRefundSoldgood'
import { useRefundTicket } from '../api/refundTicket'

const cx = classNames.bind(styles)

export const InsuranceInOrder = ({ insurance, variant }) => {
  const [confirmRefundModal, setConfirmRefundModal] = useState(false)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const openToast = useToast()

  const user = storage.getUserObj()
  const hasInsuranceAccess = isAccessPermissions(accessConfig.insurances.route, user.role)

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const refundTicketMutation = useRefundTicket(setConfirmRefundModal, handleUpdate)
  const forcedRefundSoldgoodMutation = useForcedRefundSoldgood(setConfirmRefundModal, handleUpdate)
  const deleteTicketMutation = useDeleteTicket(setConfirmDeleteModal, handleUpdate)
  const closeOrderFiscalMutation = useCloseOrderFiscal()

  const insuranceClassName = cx({
    product: true,
    productFull: variant === 'full',
  })

  const dropDownClassName = cx({
    dropDown: true,
    dropDownDisable: insurance?.status === 'deleted',
  })

  const handleConfirmRefund = () => {
    setConfirmRefundModal(true)
  }

  const handleRefund = () => {
    const data = {
      soldgood: {
        public_id: insurance.public_id,
      },
    }

    refundTicketMutation.mutate(data)
  }

  const handleForcedRefund = () => {
    const data = {
      soldgood: {
        public_id: insurance.public_id,
      },
    }

    forcedRefundSoldgoodMutation.mutate(data)
  }

  const handleConfirmDelete = () => {
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = () => {
    deleteTicketMutation.mutate(insurance.public_id)
  }

  const copyInsuranceLink = () => {
    const url = `${window.location.origin}/manager/soldgood/${insurance.public_id}`
    navigator.clipboard.writeText(url)
    openToast.success({ message: 'Ссылка скопирована в буфер обмена' })
  }

  const handleCloseFiscal = () => {
    const data = {
      format_public_id: insurance.info.format_public_id,
      order_public_id: insurance.order_public_id,
    }

    closeOrderFiscalMutation.mutate(data, {
      onSuccess: () => {
        handleUpdate()
      },
    })
  }

  if (!insurance) return null

  return (
    <>
      <section className={insuranceClassName}>
        <div className={styles.priceInfoWrap}>
          <CostStatus cost={insurance.info.cost} status={insurance.status} />

          <div className={styles.productInfoWrap}>
            <ItemLabelValue
              value={
                insurance.info.number ? (
                  hasInsuranceAccess ? (
                    <Link className={styles.link} to={`${AppRoute.INSURANCES}?number=${insurance.info.number}`}>
                      {insurance.info.number}
                    </Link>
                  ) : (
                    insurance.info.number
                  )
                ) : (
                  '-'
                )
              }
              label="номер"
            />
            <ItemLabelValue value={insurance.info.insurance.public_id} label="страховка" />
          </div>
        </div>

        <div>
          <div className={styles.btnWrap} onClick={(e) => e.stopPropagation()}>
            <Dropdown className={dropDownClassName} drop="start">
              <Dropdown.Toggle as={ButtonGroup} bsPrefix={styles.dropDownToggle} variant="success" id="dropdown-basic">
                <IconMoreVertical className={styles.iconMore} />
              </Dropdown.Toggle>

              <Dropdown.Menu>
                <Dropdown.Item as="button" onClick={copyInsuranceLink}>
                  Ссылка
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={handleCloseFiscal} disabled={insurance?.receipt_closed}>
                  Закрывающий чек
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item
                  onClick={handleConfirmRefund}
                  className={styles.danger}
                  disabled={
                    insurance.status === 'deleted' ||
                    insurance.status === 'canceled' ||
                    insurance.status === 'partially_canceled'
                  }
                  as="button"
                >
                  Возврат
                </Dropdown.Item>
                <Dropdown.Item
                  onClick={handleConfirmDelete}
                  className={styles.danger}
                  disabled={insurance.status === 'deleted'}
                  as="button"
                >
                  Удалить
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </div>
      </section>

      <ConfirmRefundTicketModal
        isShow={confirmRefundModal}
        onClose={setConfirmRefundModal}
        onRefund={handleRefund}
        onForcedRefund={handleForcedRefund}
      />
      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
