@import '../../../assets/styles/mixins';

.ticket {
  margin-bottom: 6px;
  position: relative;
  padding: 16px;

  background-color: var(--bs-card-bg);
  border-radius: 4px;
  box-shadow:
    0 1px 0 0 var(--bs-border-color),
    0 0 0 1px var(--bs-border-color-translucent);
}

.priceNumberWrap {
  position: relative;
  padding-bottom: 16px;

  display: grid;
  grid-template-columns: 1fr auto 32px;
  align-items: center;
  gap: 16px;

  &::after {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);

    display: block;
    width: 80%;
    height: 1px;

    background-color: var(--bs-border-color-translucent);

    content: '';
  }
}

.itemNumber {
  margin-top: 16px;

  display: grid;
}

.dateDesktop {
  display: none;
}

.userInfo {
  position: relative;
  padding-top: 16px;
  padding-bottom: 20px;

  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(159px, 1fr));
  row-gap: 16px;

  &::after {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);

    display: block;
    width: 80%;
    height: 1px;

    background-color: var(--bs-border-color-translucent);

    content: '';
  }
}

.nameUnited {
  display: grid;
  grid-template-columns: auto auto auto;
  justify-content: start;
  grid-column: 1 / -1;
  gap: 8px;
}

.emailWrap {
  display: grid;
  gap: 8px;
}

.otherInfo {
  padding-top: 16px;
  padding-bottom: 16px;

  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(159px, 1fr));
  row-gap: 16px;
}

.text {
  margin-bottom: 12px;
}

.itemFormat,
.itemComment {
  margin-bottom: 0;

  display: grid;
  grid-column: 1 / -1;
}

.date {
  margin-bottom: 0;

  font-size: 11px;
  color: var(--bs-secondary-color);
  text-align: right;
}

.link {
  color: var(--bs-link-color);
  text-decoration: underline;

  &:hover {
    color: var(--bs-link-hover-color);
  }
}

.userLink {
  color: var(--bs-success);
}

.btnWrap {
  position: absolute;
  top: 16px;
  right: 16px;

  display: grid;
  grid-template-columns: auto;
  align-items: stretch;
  gap: 6px;
}

.btn {
  background: transparent;
  border: none;
  border-radius: 4px;
}

.itemBtn {
  @include smallBtn;
}

.toggle {
  transform: rotate(180deg);
}

.dropDownDisable {
  opacity: 0.3;

  pointer-events: none;
}

.dropDownToggle {
  display: flex;
  justify-content: center;
  cursor: pointer;
}

.iconMore {
  & path {
    fill: var(--bs-secondary-color);
  }
}

.iconToggle {
  & path {
    fill: var(--bs-secondary-color);
  }
}

.danger,
.danger:hover {
  color: var(--bs-danger);
}

// isCollapsed ticket start
.ticketCollapsed {
  .userInfo,
  .otherInfo,
  .date {
    display: none;
  }

  .priceNumberWrap {
    padding-bottom: 0;
    align-content: center;

    &::after {
      display: none;
    }
  }

  .itemNumber {
    margin-top: 0;
    margin-bottom: 0;
  }
}

.itemCollapsed,
.nameUnitedCollapsed {
  display: none;
}

.toggleCollapsed {
  transform: rotate(0);
}
// isCollapsed ticket end

// Таблица передачи билета
.transferTableWrap {
  margin: 0 auto;

  grid-column: 1 / -1;
  width: 100%;
  max-width: 768px;

  overflow: auto;
}
.transferTable {
  width: 100%;
  min-width: 480px;

  // Адаптация к темам
  th {
    background-color: var(--bs-secondary-bg);
    color: var(--bs-body-color);
    border-color: var(--bs-border-color);
  }

  td {
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    border-color: var(--bs-border-color);
  }

  // Стили для зебры
  tbody tr:nth-child(even) {
    background-color: var(--bs-secondary-bg-subtle);
  }
}

@media (min-width: $grid-breakpoint-md) {
  .ticket {
    display: grid;
    grid-template-columns: auto 200px 1fr 24px;
    gap: 16px;

    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .ticketFull {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .priceNumberWrap {
    padding-right: 18px;
    padding-bottom: 0;

    display: flex;
    flex-direction: column;
    gap: 0;

    &::after {
      left: auto;
      right: 0;
      top: 50%;
      bottom: auto;
      transform: translateY(-50%);

      width: 1px;
      height: 70%;
    }
  }

  .itemNumber {
    margin-bottom: auto;
  }

  .dateDesktop {
    display: block;

    text-align: center;
  }

  .userInfo {
    padding: 0;

    &::after {
      left: auto;
      right: 0;
      top: 50%;
      bottom: auto;
      transform: translateY(-50%);

      width: 1px;
      height: 70%;
    }
  }

  .nameUnited {
    grid-template-columns: auto auto;
  }

  .otherInfo {
    padding: 0;
    padding-left: 16px;
  }

  .dateMobile {
    display: none;
  }

  // isCollapsed ticket start
  .ticketCollapsed {
    display: block;

    .priceNumberWrap {
      padding-right: 32px;
      display: grid;
      grid-template-columns: 170px 100px 1fr;
      align-content: center;
      align-items: center;
    }

    .nameUnitedCollapsed {
      display: grid;
      grid-template-columns: auto auto auto;
      grid-column: auto;
    }

    .itemNumber {
      margin-bottom: 0;
    }
  }

  .btn {
    &:hover,
    &:active {
      background-color: var(--bs-secondary-bg);
    }
  }
  // isCollapsed ticket end
}

@media (min-width: $grid-breakpoint-lg) {
  .ticket {
    display: grid;
    grid-template-columns: auto 320px 1fr 24px;
  }

  .userInfo {
    align-content: start;
  }

  .nameUnited {
    grid-template-columns: auto auto auto;
  }
}

@media (min-width: $grid-breakpoint-xxl) {
  // isCollapsed ticket start
  .ticketCollapsed {
    display: block;

    .priceNumberWrap {
      grid-template-columns: 170px 100px 0.7fr 0.4fr 1fr;
    }

    .itemCollapsed {
      margin-bottom: 0;

      display: grid;
      grid-column: auto;
    }
  }
  // isCollapsed ticket end
}
