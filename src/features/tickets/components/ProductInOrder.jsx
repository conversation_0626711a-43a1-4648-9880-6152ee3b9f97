import classNames from 'classnames/bind'
import { useContext, useState } from 'react'
import { ButtonGroup, Dropdown } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import IconMoreVertical from '@/assets/img/icons/icon-more-vertical.svg?react'

import { CostStatus } from '@/components/CostStatus/CostStatus'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { accessConfig } from '@/accessConfig'
import { AppRoute } from '@/const'
import { UpdateOrdersTicketsContext } from '@/features/orders'
import { useToast } from '@/hooks/useToast'
import { isAccessPermissions } from '@/utils/common'
import storage from '@/utils/storage'

import { ConfirmRefundTicketModal } from './ConfirmRefundTicketModal'
import styles from './ProductInOrder.module.scss'
import { useCloseOrderFiscal } from '../api/closeOrderFiscal'
import { useDeleteTicket } from '../api/deleteTicket'
import { useForcedRefundSoldgood } from '../api/forcedRefundSoldgood'
import { useRefundTicket } from '../api/refundTicket'

const cx = classNames.bind(styles)

export const ProductInOrder = ({ product, variant }) => {
  const [confirmRefundModal, setConfirmRefundModal] = useState(false)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const openToast = useToast()

  const user = storage.getUserObj()
  const hasShopAccess = isAccessPermissions(accessConfig.shopForm.route, user.role)

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const refundTicketMutation = useRefundTicket(setConfirmRefundModal, handleUpdate)
  const forcedRefundSoldgoodMutation = useForcedRefundSoldgood(setConfirmRefundModal, handleUpdate)
  const deleteTicketMutation = useDeleteTicket(setConfirmDeleteModal, handleUpdate)
  const closeOrderFiscalMutation = useCloseOrderFiscal()

  const productClassName = cx({
    product: true,
    productFull: variant === 'full',
  })

  const dropDownClassName = cx({
    dropDown: true,
    dropDownDisable: product?.status === 'deleted',
  })

  const handleConfirmRefund = () => {
    setConfirmRefundModal(true)
  }

  const handleRefund = () => {
    const data = {
      soldgood: {
        public_id: product.public_id,
      },
    }

    refundTicketMutation.mutate(data)
  }

  const handleForcedRefund = () => {
    const data = {
      soldgood: {
        public_id: product.public_id,
      },
    }

    forcedRefundSoldgoodMutation.mutate(data)
  }

  const handleConfirmDelete = () => {
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = () => {
    deleteTicketMutation.mutate(product.public_id)
  }

  const copyProductLink = () => {
    const url = `${window.location.origin}/manager/soldgood/${product.public_id}`
    navigator.clipboard.writeText(url)
    openToast.success({ message: 'Ссылка скопирована в буфер обмена' })
  }

  const handleCloseFiscal = () => {
    const data = {
      format_public_id: product.info.format_public_id,
      order_public_id: product.order_public_id,
    }

    closeOrderFiscalMutation.mutate(data, {
      onSuccess: () => {
        handleUpdate()
      },
    })
  }

  if (!product) return null

  return (
    <>
      <section className={productClassName}>
        <div className={styles.priceInfoWrap}>
          <CostStatus cost={product.info.cost} status={product.status} />

          <div className={styles.productInfoWrap}>
            <ItemLabelValue
              value={
                hasShopAccess ? (
                  <Link className={styles.link} to={`${AppRoute.SHOP_FORM}/${product.info.product_public_id}`}>
                    {product.info.product_public_id}
                  </Link>
                ) : (
                  product.info.product_public_id
                )
              }
              label="товар"
            />
            <ItemLabelValue
              value={
                product.info.proportion?.public_id ? (
                  hasShopAccess ? (
                    <Link
                      className={styles.link}
                      to={`${AppRoute.SHOP_FORM}/${product.info.product_public_id}?proportion=${product.info.proportion?.public_id}`}
                    >
                      {product.info.proportion?.public_id}
                    </Link>
                  ) : (
                    product.info.proportion?.public_id
                  )
                ) : (
                  '-'
                )
              }
              label="размер"
            />
          </div>
        </div>

        <div>
          <div className={styles.btnWrap} onClick={(e) => e.stopPropagation()}>
            <Dropdown className={dropDownClassName} drop="start">
              <Dropdown.Toggle as={ButtonGroup} bsPrefix={styles.dropDownToggle} variant="success" id="dropdown-basic">
                <IconMoreVertical className={styles.iconMore} />
              </Dropdown.Toggle>

              <Dropdown.Menu>
                <Dropdown.Item as="button" onClick={copyProductLink}>
                  Ссылка
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={handleCloseFiscal} disabled={product?.receipt_closed}>
                  Закрывающий чек
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item
                  onClick={handleConfirmRefund}
                  className={styles.danger}
                  disabled={
                    product.status === 'deleted' ||
                    product.status === 'canceled' ||
                    product.status === 'partially_canceled'
                  }
                  as="button"
                >
                  Возврат
                </Dropdown.Item>
                <Dropdown.Item
                  onClick={handleConfirmDelete}
                  className={styles.danger}
                  disabled={product.status === 'deleted'}
                  as="button"
                >
                  Удалить
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </div>
      </section>

      <ConfirmRefundTicketModal
        isShow={confirmRefundModal}
        onClose={setConfirmRefundModal}
        onRefund={handleRefund}
        onForcedRefund={handleForcedRefund}
      />
      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
