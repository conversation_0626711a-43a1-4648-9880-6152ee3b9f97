import React from 'react'
import { Col, Row, Table } from 'react-bootstrap'

import styles from './OrderTicketsProfile.module.scss'

export const OrderTicketsProfile = ({ tickets }) => {
  if (!tickets || tickets?.length === 0) return null

  return (
    <Row className="mb-4">
      <Col>
        <h6 className="mb-2">Билеты</h6>
        <div className={styles.tableWrap}>
          <Table className={`${styles.table} p-3`}>
            <thead>
              <tr>
                <th>#</th>
                <th>Id города</th>
                <th>Страховка</th>
                <th>Промокод</th>
                <th>Количество</th>
                <th>Public id события</th>
                <th>Public id формата</th>
              </tr>
            </thead>
            <tbody>
              {tickets.map((item, index) => (
                <tr key={item.event_public_id + index}>
                  <td>{index + 1}</td>
                  <td>{item.city_id}</td>
                  <td>{item.insurance ? 'Есть' : 'Нет'}</td>
                  <td>{item.promocode}</td>
                  <td>{item.ticket_count}</td>
                  <td>{item.event_public_id}</td>
                  <td>{item.format_public_id}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        </div>
      </Col>
    </Row>
  )
}
