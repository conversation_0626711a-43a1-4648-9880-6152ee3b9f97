import { Button, Modal } from 'react-bootstrap'

export const ConfirmRefundTicketModal = ({ isShow, onClose, onRefund, onForcedRefund }) => {
  return (
    <Modal show={isShow} onHide={onClose} centered size="sm">
      <Modal.Header closeButton>
        <Modal.Title>Возврат билета</Modal.Title>
      </Modal.Header>
      <Modal.Body className="py-4">
        <h6 className="text-center">Выберите действие:</h6>
      </Modal.Body>
      <Modal.Footer className="d-flex flex-column w-100">
        <Button onClick={onRefund} variant="danger" className="w-100 mb-2">
          Возврат средств
        </Button>
        <Button onClick={onForcedRefund} variant="primary" className="w-100 mb-4">
          Смена статуса
        </Button>
        <Button onClick={() => onClose(false)} variant="secondary" className="w-100">
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
