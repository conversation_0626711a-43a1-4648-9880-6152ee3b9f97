import { useContext } from 'react'
import { Button, FloatingLabel, Form, FormControl, Modal } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { UpdateOrdersTicketsContext } from '@/features/orders'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'

import { useChangeUserTicket } from '../api/changeUserTicket'

const ChangeUserTicketForm = ({ selectedItem, onClose }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const changeUserTicketMutation = useChangeUserTicket({}, onClose, handleUpdate)

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)

    filteredData.ticket = {
      public_id: selectedItem.public_id,
    }

    if (Object.keys(filteredData).length > 0) {
      changeUserTicketMutation.mutate({ ...filteredData })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Form.Group>
        <FloatingLabel controlId={'userPublicIdInput'} label="Идентификатор пользователя">
          <FormControl
            {...register('user.public_id', {
              required: true,
              setValueAs: (v) => checkSetValue(v, undefined, 'text'),
            })}
            type="text"
            isInvalid={errors?.user?.public_id}
            placeholder="Идентификатор пользователя"
          />
        </FloatingLabel>
      </Form.Group>
    </Form>
  )
}

export const ChangeUserTicketModal = ({ isShow, onClose, selectedItem }) => {
  return (
    <Modal show={isShow} onHide={() => onClose(false)}>
      <Modal.Header closeButton />
      <Modal.Body>
        <ChangeUserTicketForm selectedItem={selectedItem} onClose={onClose} />
      </Modal.Body>
      <Modal.Footer>
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
        <Button onClick={() => onClose(false)} variant="outline-secondary">
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
