import { useContext } from 'react'
import { Button, FloatingLabel, Form, FormControl, Modal } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { UpdateOrdersTicketsContext } from '@/features/orders'
import { checkSetValue } from '@/utils/forms'

import { useChangeTeamTicket } from '../api/changeTeamTicket'
import { useDeleteTeamTicket } from '../api/deleteTeamTicket'

const ChangeTeamTicketForm = ({ selectedItem, teams, onClose }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      new_team: {
        public_id: selectedItem?.info?.team?.public_id,
      },
    },
  })

  const { handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)

  const changeCorpTeamMutation = useChangeTeamTicket({}, onClose, handleUpdateTickets)
  const deleteTeamTicketMutation = useDeleteTeamTicket({}, onClose, handleUpdateTickets)

  const onSubmit = (data) => {
    const body = { ...data }

    body.ticket = {
      public_id: selectedItem.public_id,
    }

    if (body.new_team.public_id === 'remove') {
      deleteTeamTicketMutation.mutate(selectedItem.public_id)
    } else {
      changeCorpTeamMutation.mutate(body)
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Form.Group>
        <FloatingLabel controlId="teamLabel" label="Корп команда">
          <FormControl
            as="select"
            {...register('new_team.public_id', {
              required: true,
              setValueAs: (v) => checkSetValue(v, selectedItem?.info?.team?.public_id, 'text'),
            })}
            type="text"
            isInvalid={errors?.new_team?.public_id}
            placeholder="Корп команда"
          >
            {selectedItem?.info?.team ? (
              <option value="remove">Удалить команду</option>
            ) : (
              <option value="">Выберите один из вариантов</option>
            )}

            {teams?.map((team) => (
              <option value={team.public_id} key={team.public_id}>
                {team.name}
              </option>
            ))}
          </FormControl>
        </FloatingLabel>
      </Form.Group>
    </Form>
  )
}

export const ChangeTeamTicketModal = ({ selectedItem, teams, isShow, onClose }) => {
  return (
    <Modal show={isShow} onHide={() => onClose(false)}>
      <Modal.Header closeButton />
      <Modal.Body>
        <ChangeTeamTicketForm selectedItem={selectedItem} teams={teams} onClose={onClose} />
      </Modal.Body>
      <Modal.Footer>
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
        <Button onClick={() => onClose(false)} variant="outline-secondary">
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
