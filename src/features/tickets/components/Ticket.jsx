import classNames from 'classnames/bind'
import { useContext, useState } from 'react'
import { ButtonGroup, Dropdown, Table } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import IconMoreVertical from '@/assets/img/icons/icon-more-vertical.svg?react'
import IconMore from '@/assets/img/icons/icon-more2.svg?react'

import { CopyToClipboard } from '@/components/CopyToClipboard/CopyToClipboard'
import { CostStatus } from '@/components/CostStatus/CostStatus'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { UpdateOrdersTicketsContext } from '@/features/orders'
import { useGetTicketTeam } from '@/features/tickets/api/getTicketTeam'
import { useToast } from '@/hooks/useToast'
import { formatPhoneNumber, getKindName, getSex } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import storage from '@/utils/storage'

import { ChangeFormatTicketModal } from './ChangeFormatTicketModal'
import { ChangeNumberTicketModal } from './ChangeNumberTicketModal'
import { ChangeTeamTicketModal } from './ChangeTeamTicketModal'
import { ChangeUserTicketModal } from './ChangeUserTicketModal'
import { ConfirmRefundTicketModal } from './ConfirmRefundTicketModal'
import { EditUserDataModal } from './EditUserDataModal'
import styles from './Ticket.module.scss'
import { useCloseOrderFiscal } from '../api/closeOrderFiscal'
import { useDeleteTicket } from '../api/deleteTicket'
import { useForcedRefundSoldgood } from '../api/forcedRefundSoldgood'
import { useRefundTicket } from '../api/refundTicket'
const cx = classNames.bind(styles)

export const Ticket = ({ ticket, teams, variant, orderUserId, isExpandedByDefault = false }) => {
  const userStorage = storage.getUserObj()
  const userId = orderUserId ?? userStorage.public_id
  const [isShowTeam, setIsShowTeam] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(!isExpandedByDefault)
  const [isEditUserData, setIsEditUserData] = useState(false)
  const [isChangeUserTicket, setIsChangeUserTicket] = useState(false)
  const [confirmRefundModal, setConfirmRefundModal] = useState(false)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const [isChangeTeamTicket, setIsChangeTeamTicket] = useState(false)
  const [isChangeFormatTicket, setIsChangeFormatTicket] = useState(false)
  const [isChangeNumberTicket, setIsChangeNumberTicket] = useState(false)
  const openToast = useToast()

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const getTicketTeam = useGetTicketTeam(isShowTeam && ticket.info.team.public_id)
  const refundTicketMutation = useRefundTicket(setConfirmRefundModal, handleUpdate)
  const forcedRefundSoldgoodMutation = useForcedRefundSoldgood(setConfirmRefundModal, handleUpdate)
  const deleteTicketMutation = useDeleteTicket(setConfirmDeleteModal, handleUpdate)
  const closeOrderFiscalMutation = useCloseOrderFiscal()
  const ticketTeamNumber = getTicketTeam?.data?.data?.number

  const ticketClassName = cx({
    ticket: true,
    ticketCollapsed: isCollapsed,
    ticketFull: variant === 'full',
  })

  const toggleClassName = cx({
    btn: true,
    toggle: true,
    toggleCollapsed: isCollapsed,
  })

  const dropDownClassName = cx({
    dropDown: true,
    dropDownDisable: ticket?.status === 'deleted',
  })

  const handleConfirmRefund = () => {
    setConfirmRefundModal(true)
  }

  const handleRefund = () => {
    const data = {
      soldgood: {
        public_id: ticket.public_id,
      },
    }

    refundTicketMutation.mutate(data)
  }

  const handleForcedRefund = () => {
    const data = {
      soldgood: {
        public_id: ticket.public_id,
      },
    }

    forcedRefundSoldgoodMutation.mutate(data)
  }

  const handleConfirmDelete = () => {
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = () => {
    deleteTicketMutation.mutate(ticket.public_id)
  }

  const handleClickTeam = () => {
    setIsShowTeam(true)
  }

  const copyTicketLink = () => {
    const url = `${window.location.origin}/manager/soldgood/${ticket.public_id}`
    navigator.clipboard.writeText(url)
    openToast.success({ message: 'Ссылка скопирована в буфер обмена' })
  }

  const returnTransferStatus = (item) => {
    const { transfer_to, transfer_confirm } = item

    const statusTypes = {
      true: 'Принял',
      false: 'Отказался',
      transfer: 'Передал',
    }

    if (transfer_to) return statusTypes.transfer
    if (typeof transfer_confirm === 'boolean') {
      return statusTypes[transfer_confirm]
    }

    return ''
  }

  const handleCloseFiscal = () => {
    const data = {
      format_public_id: ticket.info.format_public_id,
      order_public_id: ticket.order_public_id,
    }

    closeOrderFiscalMutation.mutate(data, {
      onSuccess: () => {
        handleUpdate()
      },
    })
  }

  if (!ticket) return null

  return (
    <>
      <section className={ticketClassName}>
        <div className={styles.priceNumberWrap}>
          <CostStatus cost={ticket.info.cost} status={ticket.status} isCollapsed={isCollapsed} />
          <ItemLabelValue className={styles.itemNumber} value={ticket.info.number} label="номер" />
          <div className={`${styles.nameUnited} ${styles.nameUnitedCollapsed}`}>
            <ItemLabelValue
              value={ticket.info.last_name ? <CopyToClipboard>{ticket.info.last_name}</CopyToClipboard> : '-'}
              label="фамилия"
            />
            <ItemLabelValue
              value={ticket.info.first_name ? <CopyToClipboard>{ticket.info.first_name}</CopyToClipboard> : '-'}
              label="имя"
            />
            <ItemLabelValue
              value={ticket.info.second_name ? <CopyToClipboard>{ticket.info.second_name}</CopyToClipboard> : '-'}
              label="отчество"
            />
          </div>
          <ItemLabelValue
            className={styles.itemCollapsed}
            value={
              <Link className={styles.link} to={`/events/${ticket.info.event_public_id}`}>
                {ticket.info.event_public_id}
              </Link>
            }
            label="мероприятие"
          />
          <ItemLabelValue
            className={`${styles.itemFormat} ${styles.itemCollapsed}`}
            value={
              <Link className={styles.link} to={`/city-format/${ticket.info.format_public_id}`}>
                {ticket.info.format_public_id}
              </Link>
            }
            label="формат"
          />
          <p className={`${styles.date} ${styles.dateDesktop}`}>
            {unixToMoment(ticket.created).format('D MMMM YYYY, HH:mm')}
          </p>
        </div>

        <div className={styles.userInfo}>
          <div className={styles.nameUnited}>
            <ItemLabelValue
              value={ticket.info.last_name ? <CopyToClipboard>{ticket.info.last_name}</CopyToClipboard> : '-'}
              label="фамилия"
            />
            <ItemLabelValue
              value={ticket.info.first_name ? <CopyToClipboard>{ticket.info.first_name}</CopyToClipboard> : '-'}
              label="имя"
            />
            <ItemLabelValue
              value={ticket.info.second_name ? <CopyToClipboard>{ticket.info.second_name}</CopyToClipboard> : '-'}
              label="отчество"
            />
          </div>
          <ItemLabelValue
            value={<CopyToClipboard>{formatPhoneNumber(ticket.info.phone)}</CopyToClipboard>}
            label="телефон"
          />
          <ItemLabelValue
            value={
              ticket.info.birth_date && (
                <CopyToClipboard>{unixToMoment(ticket.info.birth_date).format('D MMMM YYYY')}</CopyToClipboard>
              )
            }
            label="дата рождения"
          />
          <ItemLabelValue value={getSex(ticket.info.gender)} label="пол" />
          <ItemLabelValue value={ticket.info.item_size} label="футболка" />

          <div className={styles.emailWrap}>
            <ItemLabelValue
              value={
                <Link
                  className={`${styles.link} ${userId === ticket.user_public_id ? styles.userLink : ''}`}
                  to={`/user/${ticket.user_public_id}`}
                >
                  {ticket.info.email}
                </Link>
              }
              label="почта"
            />
            <ItemLabelValue
              value={
                <Link
                  className={`${styles.link} ${userId === ticket.user_public_id ? styles.userLink : ''}`}
                  to={`/user/${ticket.user_public_id}`}
                >
                  {ticket.user_public_id}
                </Link>
              }
              label="пользователь"
            />
          </div>
        </div>

        <div className={styles.otherInfo}>
          <ItemLabelValue
            value={
              ticket.info.promocode ? (
                <Link className={styles.link} to={`/promocode/${ticket.info.promocode}`}>
                  {ticket.info.promocode}
                </Link>
              ) : (
                '-'
              )
            }
            label="промокод"
          />
          <ItemLabelValue
            value={
              <Link className={styles.link} to={`/orders/${ticket.order_public_id}`}>
                {ticket.order_public_id}
              </Link>
            }
            label="заказ"
          />
          <ItemLabelValue
            value={
              <Link className={styles.link} to={`/events/${ticket.info.event_public_id}`}>
                {ticket.info.event_public_id}
              </Link>
            }
            label="мероприятие"
          />
          <ItemLabelValue value={ticket.info.city_id} label="город" />
          {ticket?.info?.team?.public_id && (
            <ItemLabelValue
              value={
                ticketTeamNumber ?? (
                  <button
                    className={styles.itemBtn}
                    onClick={handleClickTeam}
                    disabled={getTicketTeam.isLoading}
                    type="button"
                  >
                    показать
                  </button>
                )
              }
              label="команда"
            />
          )}
          <ItemLabelValue value={getKindName(ticket.info.kind)} label="Тип" />
          <ItemLabelValue value={ticket?.receipt_closed ? 'Да' : 'Нет'} label="Чек напечатан" />
          <p className={styles.text}>{ticket.info.offline_registered ? 'Пришёл' : 'Не пришёл'}</p>
          <ItemLabelValue
            className={styles.itemFormat}
            value={
              <Link className={styles.link} to={`/city-format/${ticket.info.format_public_id}`}>
                {ticket.info.format_public_id}
              </Link>
            }
            label="формат"
          />
          <ItemLabelValue className={styles.itemComment} value={ticket.info.comment} label="комментарий" />
        </div>

        <p className={`${styles.date} ${styles.dateMobile}`}>
          {unixToMoment(ticket.created).format('D MMMM YYYY, HH:mm')}
        </p>

        <div>
          <div className={styles.btnWrap} onClick={(e) => e.stopPropagation()}>
            <Dropdown className={dropDownClassName} drop="start">
              <Dropdown.Toggle as={ButtonGroup} bsPrefix={styles.dropDownToggle} variant="success" id="dropdown-basic">
                <IconMoreVertical className={styles.iconMore} />
              </Dropdown.Toggle>

              <Dropdown.Menu>
                <Dropdown.Item as="button" onClick={() => setIsEditUserData(true)}>
                  Редактировать
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={() => setIsChangeNumberTicket(true)}>
                  Изменить номер
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={() => setIsChangeFormatTicket(true)}>
                  Изменить формат
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={() => setIsChangeTeamTicket(true)}>
                  Изменить команду
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={() => setIsChangeUserTicket(true)}>
                  Сменить пользователя
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item as="button" onClick={copyTicketLink}>
                  Ссылка
                </Dropdown.Item>
                <Dropdown.Item as="button" onClick={handleCloseFiscal} disabled={ticket?.receipt_closed}>
                  Закрывающий чек
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item
                  onClick={handleConfirmRefund}
                  className={styles.danger}
                  disabled={
                    ticket.status === 'deleted' ||
                    ticket.status === 'canceled' ||
                    ticket.status === 'partially_canceled'
                  }
                  as="button"
                >
                  Возврат
                </Dropdown.Item>
                <Dropdown.Item
                  onClick={handleConfirmDelete}
                  className={styles.danger}
                  disabled={ticket.status === 'deleted'}
                  as="button"
                >
                  Удалить
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>

            <button className={toggleClassName} onClick={() => setIsCollapsed(!isCollapsed)}>
              <IconMore className={styles.iconToggle} />
            </button>
          </div>
        </div>

        {ticket?.transfer_history && !isCollapsed && (
          <div className={`${styles.transferTableWrap} mt-4`}>
            <p className="mb-2">История передачи билета</p>
            <Table className={styles.transferTable}>
              <thead>
                <tr>
                  <th>Дата</th>
                  <th>Отправитель</th>
                  <th>Получатель</th>
                  <th>Статус</th>
                </tr>
              </thead>
              <tbody>
                {ticket.transfer_history.map((item) => (
                  <tr key={item.date}>
                    <td>{unixToMoment(item.date).format('D.M.YYYY, HH:mm')}</td>
                    <td>
                      <Link
                        className={`${styles.link} ${userId === item.user_public_id ? styles.userLink : ''}`}
                        to={`/user/${item.user_public_id}`}
                      >
                        {item.user_public_id}
                      </Link>
                    </td>
                    <td>
                      <Link
                        className={`${styles.link} ${userId === item.transfer_to ? styles.userLink : ''}`}
                        to={`/user/${item.transfer_to}`}
                      >
                        {item.transfer_to}
                      </Link>
                    </td>
                    <td>{returnTransferStatus(item)}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        )}
      </section>

      <EditUserDataModal selectedItem={ticket} isShow={isEditUserData} onClose={setIsEditUserData} />
      <ChangeUserTicketModal selectedItem={ticket} isShow={isChangeUserTicket} onClose={setIsChangeUserTicket} />
      <ChangeNumberTicketModal selectedItem={ticket} isShow={isChangeNumberTicket} onClose={setIsChangeNumberTicket} />
      <ChangeFormatTicketModal selectedItem={ticket} isShow={isChangeFormatTicket} onClose={setIsChangeFormatTicket} />
      <ChangeTeamTicketModal
        teams={teams}
        selectedItem={ticket}
        isShow={isChangeTeamTicket}
        onClose={setIsChangeTeamTicket}
      />
      <ConfirmRefundTicketModal
        isShow={confirmRefundModal}
        onClose={setConfirmRefundModal}
        onRefund={handleRefund}
        onForcedRefund={handleForcedRefund}
      />
      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
