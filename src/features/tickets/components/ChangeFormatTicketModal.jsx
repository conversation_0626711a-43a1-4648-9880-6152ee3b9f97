import { useContext } from 'react'
import { Button, FloatingLabel, Form, FormControl, Modal } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useGetEventFormats } from '@/features/events/api/getEventFormats'
import { UpdateOrdersTicketsContext } from '@/features/orders'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'

import { useChangeFormatTicket } from '../api/changeFormatTicket'

const ChangeFormatTicketForm = ({ selectedItem, onClose }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const changeFormatTicketMutation = useChangeFormatTicket({}, onClose, handleUpdate)
  const getEventFormatsQuery = useGetEventFormats({}, selectedItem.info.event_city.public_id)

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)

    filteredData.ticket = {
      public_id: selectedItem.public_id,
    }

    if (Object.keys(filteredData).length > 0) {
      changeFormatTicketMutation.mutate({ ...filteredData })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Form.Group>
        <FloatingLabel controlId="formatLabel" label="Формат">
          <FormControl
            as="select"
            {...register('format.public_id', {
              required: true,
              setValueAs: (v) => checkSetValue(v, selectedItem?.info?.gender, 'text'),
            })}
            type="text"
            isInvalid={errors?.format?.public_id}
            placeholder="Формат"
          >
            <option value="">Выберите один из вариантов</option>
            {getEventFormatsQuery?.data?.data?.values.map((format) => (
              <option
                value={format.public_id}
                key={format.public_id}
                disabled={selectedItem.info.format_public_id === format.public_id}
              >
                {format.title}
              </option>
            ))}
          </FormControl>
        </FloatingLabel>
      </Form.Group>
    </Form>
  )
}

export const ChangeFormatTicketModal = ({ isShow, onClose, selectedItem }) => {
  return (
    <Modal show={isShow} onHide={() => onClose(false)}>
      <Modal.Header closeButton />
      <Modal.Body>
        <ChangeFormatTicketForm selectedItem={selectedItem} onClose={onClose} />
      </Modal.Body>
      <Modal.Footer>
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
        <Button onClick={() => onClose(false)} variant="outline-secondary">
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
