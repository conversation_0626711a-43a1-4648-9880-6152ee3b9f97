import classNames from 'classnames/bind'
import { useEffect, useState } from 'react'
import { Button, ButtonGroup } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'

import { InsuranceInOrder } from './InsuranceInOrder'
import { ProductInOrder } from './ProductInOrder'
import styles from './Soldgoods.module.scss'
import { Ticket } from './Ticket'

const cx = classNames.bind(styles)

const buttons = [
  {
    type: 'tickets',
    label: 'Билеты',
  },
  {
    type: 'products',
    label: 'Товары',
  },
  {
    type: 'licenses',
    label: 'Лицензии',
  },
  {
    type: 'insurances',
    label: 'Страховки',
  },
  {
    type: 'others',
    label: 'Остальное',
  },
]

export const Soldgoods = ({ soldgoods, teams, variant, orderUserId }) => {
  const [valuesRender, setValuesRender] = useState([])
  const [activeTab, setActiveTab] = useState('tickets')
  const [filteredSoldgoods, setFilteredSoldgoods] = useState({
    tickets: [],
    products: [],
    licenses: [],
    insurances: [],
    others: [],
  })

  useEffect(() => {
    if (soldgoods?.length > 0) {
      const newTickets = {
        tickets: [],
        products: [],
        licenses: [],
        insurances: [],
        others: [],
      }

      soldgoods.forEach((item) => {
        if (item.type === 'ticket') {
          newTickets.tickets.push(item)
        } else if (item.type === 'product') {
          newTickets.products.push(item)
        } else if (item.type === 'license') {
          newTickets.licenses.push(item)
        } else if (item.type === 'insurance') {
          newTickets.insurances.push(item)
        } else {
          newTickets.others.push(item)
        }
      })

      if (newTickets.tickets.length > 0) {
        setActiveTab('tickets')
      } else if (newTickets.products.length > 0) {
        setActiveTab('products')
      } else if (newTickets.licenses.length > 0) {
        setActiveTab('licenses')
      } else if (newTickets.insurances.length > 0) {
        setActiveTab('insurances')
      } else if (newTickets.others.length > 0) {
        setActiveTab('others')
      }

      setFilteredSoldgoods(newTickets)
    }
  }, [soldgoods])

  const listClassName = cx({
    list: true,
    listFull: variant === 'full',
  })

  if (!soldgoods || soldgoods?.length === 0) return null

  return (
    <>
      <ButtonGroup className="mx-3 mb-3">
        {buttons.map(
          (item) =>
            filteredSoldgoods[item.type]?.length > 0 && (
              <Button
                variant={item.type === activeTab ? 'primary' : 'light'}
                onClick={() => setActiveTab(item.type)}
                type="button"
                size="sm"
                key={item.type}
              >
                {item.label} {filteredSoldgoods[item.type].length}
              </Button>
            )
        )}
      </ButtonGroup>

      <div className={listClassName}>
        <div className="mb-3">
          {valuesRender?.map((ticket) => {
            if (ticket.type === 'product') {
              return <ProductInOrder product={ticket} key={ticket.public_id} variant="full" />
            } else if (ticket.type === 'insurance') {
              return <InsuranceInOrder insurance={ticket} key={ticket.public_id} variant="full" />
            } else {
              return (
                <Ticket ticket={ticket} key={ticket.public_id} teams={teams} variant="full" orderUserId={orderUserId} />
              )
            }
          })}
        </div>
      </div>

      <AdvancedPagination values={filteredSoldgoods[activeTab]} setValues={setValuesRender} />
    </>
  )
}
