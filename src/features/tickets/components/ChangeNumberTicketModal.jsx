import { useContext } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Modal, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { UpdateOrdersTicketsContext } from '@/features/orders'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'

import { useChangeNumberTicket } from '../api/changeNumberTicket'
import { useGenerateNumberTicket } from '../api/generateNumberTicket'

const ChangeNumberTicketForm = ({ selectedItem }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const changeNumberTicketMutation = useChangeNumberTicket({}, handleUpdate)
  const generateNumberTicketMutation = useGenerateNumberTicket({}, handleUpdate)

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)

    filteredData.ticket = {
      public_id: selectedItem.public_id,
    }

    if (Object.keys(filteredData).length > 0) {
      changeNumberTicketMutation.mutate({ ...filteredData })
    }
  }

  const handleClickGenerateNumber = () => {
    const data = {
      ticket: {
        public_id: selectedItem.public_id,
      },
    }

    generateNumberTicketMutation.mutate(data)
  }

  return (
    <>
      <Form className="mb-4" onSubmit={handleSubmit(onSubmit)}>
        <Row>
          <Col xs={12} sm={true}>
            <Form.Group className="mb-2">
              <FloatingLabel controlId={'customNumberInput'} label="Произвольный номер">
                <FormControl
                  {...register('number', {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, undefined, 'number'),
                  })}
                  type="number"
                  isInvalid={errors?.user?.public_id}
                  placeholder="Произвольный номер"
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
          <Col sm="auto">
            <Button type="submit" disabled={changeNumberTicketMutation.isLoading}>
              Присвоить
            </Button>
          </Col>
        </Row>
      </Form>

      <Button onClick={handleClickGenerateNumber} disabled={generateNumberTicketMutation.isLoading}>
        Сгенерировать новый номер билета
      </Button>
    </>
  )
}

export const ChangeNumberTicketModal = ({ isShow, onClose, selectedItem }) => {
  return (
    <Modal show={isShow} onHide={() => onClose(false)}>
      <Modal.Header closeButton />
      <Modal.Body>
        <ChangeNumberTicketForm selectedItem={selectedItem} />
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={() => onClose(false)} variant="secondary">
          Закрыть
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
