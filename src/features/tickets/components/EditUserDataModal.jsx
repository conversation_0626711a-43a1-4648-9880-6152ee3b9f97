import { nanoid } from '@reduxjs/toolkit'
import { useContext } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Modal, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useGetEventFormat } from '@/features/events/api/getEventFormat'
import { UpdateOrdersTicketsContext } from '@/features/orders'
import { useGetShopShirts } from '@/features/shop/api/getShopShirts'
import { useGetFormatTeams } from '@/features/tickets/api/getFormatTeams'
import { useGetInsideTeamNumbers } from '@/features/tickets/api/getInsideTeamNumbers'
import { removeEmptyString } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

import { useEditTicketUserData } from '../api/editTicketUserData'

const fields = [
  {
    id: nanoid(),
    name: 'first_name',
    label: 'Имя',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'last_name',
    label: 'Фамилия',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'second_name',
    label: 'Отчество',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'phone',
    label: 'Телефон (формат: 79998887766)',
    type: 'tel',
    pattern: /[7][0-9]{10}$/,
    required: false,
  },
  {
    id: nanoid(),
    name: 'birth_date',
    label: 'Дата рождения',
    type: 'date',
    required: false,
  },
  {
    id: nanoid(),
    name: 'comment',
    label: 'Комментарий',
    type: 'text',
    required: false,
  },
]

const EditUserDataForm = ({ selectedItem, onClose }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      phone: selectedItem?.info?.phone,
      gender: selectedItem?.info?.gender,
      comment: selectedItem?.info?.comment,
      company: selectedItem?.info?.company,
      position: selectedItem?.info?.position,
      item_size: selectedItem?.info?.item_size,
      last_name: selectedItem?.info?.last_name,
      cluster: {
        name: selectedItem?.info?.cluster?.name,
      },
      first_name: selectedItem?.info?.first_name,
      second_name: selectedItem?.info?.second_name,
      inside_number: selectedItem?.info?.inside_number,
      team_public_id: selectedItem?.info?.team_public_id,
      birth_date: unixToMoment(selectedItem?.info?.birth_date).format('YYYY-MM-DD'),
    },
  })

  const { handleUpdateOrders, handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)
  const handleUpdate = handleUpdateTickets ?? handleUpdateOrders

  const editTicketMutation = useEditTicketUserData({}, onClose, handleUpdate)
  const getShopShirts = useGetShopShirts(selectedItem.info.event_city.public_id)
  const getEventFormat = useGetEventFormat(selectedItem.info.format_public_id)
  const getInsideTeamNumbers = useGetInsideTeamNumbers(selectedItem.info.team_public_id)

  const eventFormat = getEventFormat?.data?.data?.[0]

  const shopShirts = getShopShirts?.data?.data?.values
  const clusters = eventFormat?.clusters
  const additionalFields = eventFormat?.additional_fields

  const getFormatTeams = useGetFormatTeams(eventFormat?.team || false, selectedItem.info.format_public_id)

  const teams = getFormatTeams?.data?.data?.values
  const insideTeamNumbers = getInsideTeamNumbers?.data?.data?.free_numbers

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)
    const filteredDataSecond = removeEmptyString(filteredData)

    filteredDataSecond.public_id = selectedItem.public_id

    if (Object.keys(filteredDataSecond).length > 0) {
      editTicketMutation.mutate({ ...filteredDataSecond })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Row className="g-3 mb-3">
        {fields.map((field) => (
          <Col md={12} key={field.id}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                <FormControl
                  {...register(field.name, {
                    required: field.required,
                    pattern: field.pattern,
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.[field.name], field.type),
                  })}
                  type={field.type}
                  isInvalid={errors[field.name]}
                  placeholder={field.label}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}

        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="genderLabel" label="Пол">
              <FormControl
                as="select"
                {...register('gender', {
                  setValueAs: (v) => checkSetValue(v, selectedItem?.info?.gender, 'text'),
                })}
                type="text"
                placeholder="Пол"
              >
                <option value="">Выберите один из вариантов</option>
                <option value="male">Мужской</option>
                <option value="female">Женский</option>
              </FormControl>
            </FloatingLabel>
          </Form.Group>
        </Col>

        {additionalFields?.map((field) => (
          <Col md={12} key={field.name}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                <FormControl
                  {...register(field.name, {
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.[field.name], 'text'),
                  })}
                  type="text"
                  isInvalid={errors[field.name]}
                  placeholder={field.label}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}

        {clusters?.length > 0 && (
          <Col md={12}>
            <Form.Group>
              <FloatingLabel controlId="clusterLabel" label="Кластер">
                <FormControl
                  as="select"
                  {...register('cluster.name', {
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.cluster, 'text'),
                  })}
                  type="text"
                  placeholder="Кластер"
                >
                  <option value="">Выберите один из вариантов</option>
                  {clusters?.map((cluster) => (
                    <option key={cluster.value} value={cluster.value}>
                      {cluster.label}
                    </option>
                  ))}
                </FormControl>
              </FloatingLabel>
            </Form.Group>
          </Col>
        )}

        {eventFormat?.team && !eventFormat?.team_all && teams?.length > 0 && (
          <Col md={12}>
            <Form.Group>
              <FloatingLabel controlId="teamPublicLabel" label="Команда">
                <FormControl
                  as="select"
                  {...register('team_public_id', {
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.team_public_id, 'text'),
                  })}
                  type="text"
                  placeholder="Команда"
                >
                  <option value="">Выберите один из вариантов</option>
                  {teams?.map((team) => (
                    <option
                      key={team.public_id}
                      value={team.public_id}
                      disabled={selectedItem?.info?.team_public_id === team.public_id}
                    >
                      {team.number} (свободно {team.tickets_left} мест из {team.event_format.max_count})
                    </option>
                  ))}
                </FormControl>
              </FloatingLabel>
            </Form.Group>
          </Col>
        )}

        {eventFormat?.team && (
          <Col md={12}>
            <Form.Group>
              <FloatingLabel controlId="insideNumberLabel" label="Место внутри команды">
                <FormControl
                  as="select"
                  {...register('inside_number', {
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.inside_number, 'number'),
                  })}
                  type="number"
                  placeholder="Место внутри команды"
                >
                  {!selectedItem?.info?.inside_number && <option value="">Выберите один из вариантов</option>}

                  {selectedItem?.info?.inside_number && (
                    <option value={selectedItem?.info?.inside_number} disabled>
                      {selectedItem?.info?.inside_number}
                    </option>
                  )}

                  {insideTeamNumbers?.map((number) => (
                    <option key={number} value={number}>
                      {number}
                    </option>
                  ))}
                </FormControl>
              </FloatingLabel>
            </Form.Group>
          </Col>
        )}

        {shopShirts?.length > 0 && (
          <Col md={12}>
            <Form.Group>
              <FloatingLabel controlId="itemSizeLabel" label="Размер футболки">
                <FormControl
                  as="select"
                  {...register('item_size', {
                    setValueAs: (v) => checkSetValue(v, selectedItem?.info?.item_size, 'text'),
                  })}
                  type="text"
                  placeholder="Размер футболки"
                >
                  <option value="">Выберите один из вариантов</option>
                  {shopShirts?.map((shirt) => (
                    <option key={shirt.size} value={shirt.size} disabled={shirt.count < 1}>
                      {shirt.size}
                    </option>
                  ))}
                </FormControl>
              </FloatingLabel>
            </Form.Group>
          </Col>
        )}
      </Row>
    </Form>
  )
}

export const EditUserDataModal = ({ isShow, onClose, selectedItem }) => {
  return (
    <Modal show={isShow} onHide={() => onClose(false)}>
      <Modal.Header closeButton>
        <Modal.Title>{selectedItem?.user_public_id}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <EditUserDataForm selectedItem={selectedItem} onClose={onClose} />
      </Modal.Body>
      <Modal.Footer>
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
        <Button onClick={() => onClose(false)} variant="outline-secondary">
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
