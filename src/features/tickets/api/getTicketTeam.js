import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getTicketTeam = (teamPublicId) => {
  return axios.get(`${APIRoute.CORP_TEAM}/${teamPublicId}`)
}

export const useGetTicketTeam = (publicId) => {
  return useQuery({
    enabled: publicId?.length > 0,
    queryKey: ['ticketTeam', publicId],
    queryFn: () => getTicketTeam(publicId),
  })
}
