import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const deleteTeamTicket = (ticketPublicId) => {
  return axios.delete(`${APIRoute.REMOVE_TEAM_TICKET}${ticketPublicId}`)
}

export const useDeleteTeamTicket = ({ config } = {}, onClose, onUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        onUpdate && onUpdate()
        onClose(false)
        openToast.success({ message: 'Команда успешно изменена!' })
      }
    },
    ...config,
    mutationFn: deleteTeamTicket,
  })
}
