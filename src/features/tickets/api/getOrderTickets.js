import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getOrderTickets = (orderPublicId) => {
  return orderPublicId ? axios.get(`${APIRoute.TICKETS}/${orderPublicId}`) : undefined
}

export const useGetOrderTickets = (orderPublicId) => {
  return useQuery({
    enabled: orderPublicId?.length > 0,
    queryKey: ['orderTickets', orderPublicId],
    queryFn: () => getOrderTickets(orderPublicId),
  })
}
