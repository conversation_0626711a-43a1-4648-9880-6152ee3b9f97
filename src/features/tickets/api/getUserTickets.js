import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getUserTickets = (publicId) => {
  return axios.get(`${APIRoute.USER_SOLDGOODS}/${publicId}`)
}

export const useGetUserTickets = (publicId, enabled = true) => {
  return useQuery({
    enabled: Boolean(publicId) && enabled,
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['userTickets', publicId],
    queryFn: () => getUserTickets(publicId),
  })
}
