import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const generateNumberTicket = (data) => {
  return axios.post(APIRoute.GENERATE_NEW_TICKET_NUMBER, data)
}

export const useGenerateNumberTicket = ({ config } = {}, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        openToast.success({ message: `Новый номер билета ${data.data.number}!`, duration: 6000 })
      }
    },
    ...config,
    mutationFn: generateNumberTicket,
  })
}
