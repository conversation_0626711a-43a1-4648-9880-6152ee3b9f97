import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const changeTeamTicket = (data) => {
  return axios.put(APIRoute.EDIT_TEAM_TICKET, data)
}

export const useChangeTeamTicket = ({ config } = {}, onClose, onUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        onUpdate && onUpdate()
        onClose(false)
        openToast.success({ message: 'Команда успешно изменена!' })
      }
    },
    ...config,
    mutationFn: changeTeamTicket,
  })
}
