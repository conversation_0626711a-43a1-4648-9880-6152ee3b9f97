import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const forcedRefundSoldgood = (data) => {
  return axios.post(APIRoute.SOLDGOOD_FORCED_REFUND, data)
}

export const useForcedRefundSoldgood = (onClose, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        onClose(false)
        openToast.success({ message: 'Статус изменён успешно!' })
      }
    },
    mutationFn: forcedRefundSoldgood,
  })
}
