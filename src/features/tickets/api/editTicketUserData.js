import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const editTicketUserData = (data) => {
  return axios.put(APIRoute.EDIT_TICKET_DATA, data)
}

export const useEditTicketUserData = ({ config } = {}, onClose, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        onClose()
        openToast.success({ message: 'Билет успешно отредактирован!' })
      }
    },
    ...config,
    mutationFn: editTicketUserData,
  })
}
