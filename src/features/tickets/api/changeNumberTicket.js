import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const changeNumberTicket = (data) => {
  return axios.post(APIRoute.CHANGE_CUSTOM_TICKET_NUMBER, data)
}

export const useChangeNumberTicket = ({ config } = {}, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        openToast.success({ message: 'Номер успешно изменён!' })
      }
    },
    ...config,
    mutationFn: changeNumberTicket,
  })
}
