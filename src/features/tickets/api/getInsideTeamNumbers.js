import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getInsideTeamNumbers = (teamPublicId) => {
  return axios.get(`${APIRoute.INSIDE_TEAM_NUMBERS}/${teamPublicId}`)
}

export const useGetInsideTeamNumbers = (teamPublicId) => {
  return useQuery({
    enabled: teamPublicId?.length > 0,
    queryKey: ['insideTeamNumbers', teamPublicId],
    queryFn: () => getInsideTeamNumbers(teamPublicId),
  })
}
