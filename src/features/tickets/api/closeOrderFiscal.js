import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const closeOrderFiscal = (data) => {
  return axios.post(APIRoute.CLOSE_ORDER_FISCAL, data)
}

export const useCloseOrderFiscal = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: closeOrderFiscal,
    onSuccess: (data) => {
      if (data?.status === 200) {
        openToast.success({ message: 'Закрывающий чек успешно отправлен!' })
      }
    },
  })
}
