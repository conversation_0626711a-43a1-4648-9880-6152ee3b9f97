import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const changeUserTicket = (data) => {
  return axios.post(APIRoute.CHANGE_USER_TICKET, data)
}

export const useChangeUserTicket = ({ config } = {}, onClose, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        onClose()
        openToast.success({ message: 'Билет успешно перенесён!' })
      }
    },
    ...config,
    mutationFn: changeUserTicket,
  })
}
