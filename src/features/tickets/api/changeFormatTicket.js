import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const changeFormatTicket = (data) => {
  return axios.post(APIRoute.CHANGE_TICKET_FORMAT, data)
}

export const useChangeFormatTicket = ({ config } = {}, onClose, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        onClose()
        openToast.success({ message: 'Формат успешно изменён!' })
      }
    },
    ...config,
    mutationFn: changeFormatTicket,
  })
}
