import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getTicket = (publicId) => {
  return axios.get(`${APIRoute.GET_VOLUNTEER_SOLDGOOD}/${publicId}`)
}

export const useGetTicket = (publicId) => {
  return useQuery({
    enabled: Bo<PERSON><PERSON>(publicId),
    queryKey: ['ticket', publicId],
    queryFn: () => getTicket(publicId),
  })
}
