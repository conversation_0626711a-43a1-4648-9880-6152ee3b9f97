import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getFormatTeams = (formatPublicId) => {
  return axios.get(`${APIRoute.FORMAT_TEAMS}/${formatPublicId}`)
}

export const useGetFormatTeams = (isTeam, formatPublicId) => {
  return useQuery({
    enabled: isTeam && formatPublicId?.length > 0,
    queryKey: ['teams', formatPublicId],
    queryFn: () => getFormatTeams(formatPublicId),
  })
}
