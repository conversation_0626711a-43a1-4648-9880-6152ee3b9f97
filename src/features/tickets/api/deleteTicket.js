import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const deleteTicket = (publicId) => {
  return axios.delete(`${APIRoute.DELETE_TICKET}/${publicId}`)
}

export const useDeleteTicket = (onClose, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        onClose(false)
        openToast.success({ message: 'Билет успешно удалён!' })
      }
    },
    mutationFn: deleteTicket,
  })
}
