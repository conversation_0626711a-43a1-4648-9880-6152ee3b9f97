import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const createPoll = (data) => {
  return axios.post(APIRoute.ACTIONS_POLL, data)
}

export const useCreatePoll = (onClose) => {
  const openToast = useToast()

  return useMutation({
    mutationFn: createPoll,
    onSuccess: (response) => {
      if (response?.status === 200) {
        queryClient.invalidateQueries(['polls'])
        onClose?.()
        openToast.success({ message: 'Голосование создано!' })
      }
    },
  })
}
