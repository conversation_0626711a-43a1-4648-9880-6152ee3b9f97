import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

export const POLLS_QUERY_KEY = 'polls'

const getPolls = () => {
  return axios.get(APIRoute.POLLS)
}

export const useGetPolls = () => {
  const MINUTES = 5
  return useQuery({
    queryKey: [POLLS_QUERY_KEY],
    queryFn: getPolls,
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    refetchOnWindowFocus: false,
    select: (response) => response?.data || [],
  })
}
