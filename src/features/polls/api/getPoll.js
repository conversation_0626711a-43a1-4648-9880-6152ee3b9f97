import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const getPoll = (publicId) => {
  return axios.get(`${APIRoute.POLL}/${publicId}`)
}

export const useGetPoll = (publicId) => {
  const MINUTES = 5
  return useQuery({
    enabled: <PERSON><PERSON><PERSON>(publicId),
    queryKey: ['poll', publicId],
    queryFn: () => getPoll(publicId),
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    refetchOnWindowFocus: false,
  })
}
