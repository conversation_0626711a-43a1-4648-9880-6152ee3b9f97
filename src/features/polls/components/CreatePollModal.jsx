import { But<PERSON>, Col, Form, Modal, Row } from 'react-bootstrap'
import { useFieldArray, useForm } from 'react-hook-form'

import { useCreatePoll } from '@/features/polls/api/createPoll'

import { MediaUpload } from './MediaUpload'

export const CreatePollModal = ({ isShow, onClose }) => {
  const createPollMutation = useCreatePoll(() => {
    reset()
    onClose?.()
  })

  const {
    control,
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { isValid, errors },
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      question: '',
      expires_at: '',
      public_results: true,
      options: [
        { text: '', media: '' },
        { text: '', media: '' },
      ],
    },
  })

  const { fields, append, remove } = useFieldArray({ control, name: 'options' })
  const watchedOptions = watch('options')

  const onSubmit = (data) => {
    const payload = {
      public_results: !!data.public_results,
      expires_at: new Date(data.expires_at).toISOString(),
      question: data.question.trim(),
      options: (data.options || [])
        .map((o) => {
          const option = { text: (o?.text || '').trim() }
          // Добавляем media только если оно не пустое
          if (o?.media && o.media.trim() !== '') {
            option.media = o.media
          }
          return option
        })
        .filter((o) => o.text.length > 0),
    }

    // Минимум 2 ответа
    if (payload.options.length < 2) return

    createPollMutation.mutate(payload)
  }

  const handleHide = () => {
    reset()
    onClose?.()
  }

  return (
    <Modal show={isShow} onHide={handleHide} centered>
      <Form onSubmit={handleSubmit(onSubmit)} id="create-poll-form">
        <Modal.Header closeButton>
          <Modal.Title>Новое голосование</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row className="mb-4">
            <Col>
              <Form.Label>Вопрос</Form.Label>
              <Form.Control
                type="text"
                placeholder="Введите вопрос"
                isInvalid={!!errors.question}
                {...register('question', { required: true })}
                required
              />
            </Col>
          </Row>

          <Row className="mb-4">
            <Col>
              <p>Варианты ответа</p>
              {fields.map((field, idx) => (
                <Row key={field.id} className="g-2 mb-4 align-items-end">
                  <Col xs="12">
                    <MediaUpload
                      media={watchedOptions?.[idx]?.media || ''}
                      onMediaChange={(media) => {
                        setValue(`options.${idx}.media`, media)
                      }}
                      onMediaRemove={() => {
                        setValue(`options.${idx}.media`, '')
                      }}
                    />
                  </Col>
                  <Col>
                    <Form.Control
                      type="text"
                      placeholder={`Ответ ${idx + 1}`}
                      isInvalid={!!errors?.options?.[idx]?.text}
                      {...register(`options.${idx}.text`, { required: true })}
                      required
                    />
                  </Col>
                  <Col xs="auto">
                    <Button
                      variant="outline-danger"
                      onClick={() => remove(idx)}
                      disabled={fields.length <= 2}
                      title="Удалить ответ"
                      type="button"
                    >
                      <i className="bi bi-trash" />
                    </Button>
                  </Col>
                </Row>
              ))}

              <Button
                variant="outline-primary"
                onClick={() => append({ text: '', media: '' })}
                className="mt-2"
                type="button"
                size="sm"
              >
                <i className="bi bi-plus-circle me-2" />
                Добавить ответ
              </Button>
            </Col>
          </Row>

          <Row className="mb-4">
            <Col>
              <Form.Label>Дата окончания</Form.Label>
              <Form.Control
                type="datetime-local"
                isInvalid={!!errors.expires_at}
                {...register('expires_at', { required: true })}
                required
              />
            </Col>
          </Row>

          <Row className="mb-4">
            <Col className="mt-3 mt-md-0">
              <Form.Check type="switch" id="public-results-switch" label="Публичный" {...register('public_results')} />
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleHide} type="button">
            Отмена
          </Button>
          <Button type="submit" variant="success" disabled={!isValid || createPollMutation.isLoading}>
            Создать
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  )
}
