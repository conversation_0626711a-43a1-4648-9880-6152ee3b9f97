import { useRef, useState } from 'react'
import { But<PERSON>, Figure } from 'react-bootstrap'

import { MAX_IMG_SIZE } from '@/const'
import { useToast } from '@/hooks/useToast'
import { convertBase64 } from '@/utils/common'
import { getImageSrc } from '@/utils/images'

const ACCEPTED_FORMATS = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml']

export const MediaUpload = ({ media, onMediaChange, onMediaRemove, disabled = false }) => {
  const fileInputRef = useRef(null)
  const [isHovered, setIsHovered] = useState(false)
  const openToast = useToast()

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    if (!file) return

    // Проверяем формат файла
    if (!ACCEPTED_FORMATS.includes(file.type)) {
      openToast.error({
        message: 'Поддерживаются только форматы: JPG, JPEG, PNG, WEBP, SVG',
        duration: 6000,
      })
      evt.target.value = ''
      return
    }

    const fileSizeInB = file.size
    const base64 = await convertBase64(file)

    if (base64 === '') {
      onMediaRemove?.()
    } else if (fileSizeInB <= MAX_IMG_SIZE) {
      onMediaChange?.(base64)
    } else {
      openToast.error({
        message: `Файл слишком большой: ${file.name}. Максимальный размер: 5MB`,
        duration: 6000,
      })
      evt.target.value = ''
    }
  }

  const handleDeleteMedia = () => {
    onMediaRemove?.()
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleUploadClick = () => {
    if (!disabled) {
      fileInputRef.current?.click()
    }
  }

  return (
    <div className="d-flex align-items-center">
      <input
        ref={fileInputRef}
        type="file"
        accept=".jpg,.jpeg,.png,.webp,.svg"
        onChange={handleFileRead}
        className="d-none"
        disabled={disabled}
      />

      {media ? (
        <div className="position-relative">
          <Figure
            className="d-block mb-0 position-relative border rounded overflow-hidden"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <Figure.Image
              src={getImageSrc(media)}
              alt="Медиа превью"
              style={{ width: '160px', height: '160px', objectFit: 'cover' }}
              className="d-block rounded mb-0"
            />
            {isHovered && !disabled && (
              <div
                className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center rounded"
                style={{ backgroundColor: 'rgba(0, 0, 0, 0.6)' }}
              >
                <Button variant="danger" size="sm" onClick={handleDeleteMedia}>
                  <i className="bi bi-trash" />
                </Button>
              </div>
            )}
          </Figure>
        </div>
      ) : (
        <Button
          variant="outline-secondary"
          onClick={handleUploadClick}
          disabled={disabled}
          style={{ borderStyle: 'dashed', width: '160px', height: '160px' }}
          onMouseEnter={(e) => (e.target.style.borderStyle = 'solid')}
          onMouseLeave={(e) => (e.target.style.borderStyle = 'dashed')}
        >
          <i className="bi bi-image" />
        </Button>
      )}
    </div>
  )
}
