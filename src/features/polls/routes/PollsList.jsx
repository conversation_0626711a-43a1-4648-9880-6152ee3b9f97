import { useState } from 'react'
import { Container, Spinner } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { useGetPolls } from '@/features/polls/api/getPolls'
import { CreatePollModal } from '@/features/polls/components/CreatePollModal'
import { pollsTableData } from '@/features/polls/pollsTableData'

export const PollsList = () => {
  const { data: polls, isLoading, error } = useGetPolls()
  const [isShowCreateModal, setIsShowCreateModal] = useState(false)

  return (
    <Layout title="Голосования" onClickAddButton={() => setIsShowCreateModal(true)}>
      {isLoading ? (
        <Container className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <Spinner animation="border" />
        </Container>
      ) : error ? (
        <Container className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
          <p>Не удалось загрузить список голосований</p>
        </Container>
      ) : (
        <TableTemplate data={pollsTableData} values={polls} />
      )}

      <CreatePollModal isShow={isShowCreateModal} onClose={() => setIsShowCreateModal(false)} />
    </Layout>
  )
}
