import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const getHonestCodes = (proportionPublicId) => {
  return axios.get(`${APIRoute.SHOP_PROPORTION_HONEST_CODES}/${proportionPublicId}/honest_codes`)
}

export const useGetHonestCodes = (proportionPublicId) => {
  return useQuery({
    queryKey: ['honest_codes', proportionPublicId],
    queryFn: () => getHonestCodes(proportionPublicId),
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    enabled: !!proportionPublicId,
  })
}
