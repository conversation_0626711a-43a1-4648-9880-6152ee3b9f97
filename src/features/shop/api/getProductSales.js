import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getProductSales = (productId) => {
  return axios.get(`${APIRoute.SHOP_STATISTICS}/${productId}`)
}

export const useGetProductSales = (productId, enabled = true) => {
  return useQuery({
    enabled: enabled && !!productId,
    queryKey: ['productSales', productId],
    queryFn: () => getProductSales(productId),
  })
}
