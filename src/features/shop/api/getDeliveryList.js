import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const DELIVERY_QUERY_KEY = 'deliveries'

const getDeliveryList = () => {
  return axios.get(APIRoute.GET_DELIVERY_LIST)
}

export const useDeliveryList = () => {
  return useQuery({
    queryKey: [DELIVERY_QUERY_KEY],
    queryFn: getDeliveryList,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}
