import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteHonestCode = (code) => {
  // Кодирование URI компонента, чтобы избежать проблем со специальными символами
  const encodedCode = encodeURIComponent(code)
  return axios.delete(`${APIRoute.SHOP_HONEST_CODE}/${encodedCode}`)
}

export const useDeleteHonestCode = (proportionPublicId) => {
  const openToast = useToast()

  return useMutation({
    mutationFn: deleteHonestCode,
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['honest_codes', proportionPublicId])
        openToast.success({ message: 'Код удален' })
      }
    },
  })
}
