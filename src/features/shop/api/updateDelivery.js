import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

import { DELIVERY_QUERY_KEY } from './getDeliveryList'

const updateDelivery = ({ public_id, ...data }) => {
  return axios.put(`${APIRoute.GET_DELIVERY_LIST}/${public_id}`, data)
}

export const useUpdateDelivery = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries([DELIVERY_QUERY_KEY])
        openToast.success({ message: 'Доставка обновлена!' })
      }
    },
    mutationFn: updateDelivery,
  })
}
