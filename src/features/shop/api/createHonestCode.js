import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'
import { getRightNames } from '@/utils/common'

export const createHonestCodes = (data) => {
  return axios.post(`${APIRoute.SHOP_HONEST_CODE}`, {
    codes: data.codes,
    proportion_public_id: data.proportion_public_id,
  })
}

export const useCreateHonestCode = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: createHonestCodes,
    onSuccess: (data, variables) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['honest_codes', variables.proportion_public_id])
        const count = variables.codes.length

        openToast.success({
          message: count === 1 ? 'Код добавлен' : `Добавлено ${count} ${getRightNames(count, 'код', 'кода', 'кодов')}`,
        })
      }
    },
  })
}
