import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getShopShirts = (eventsCityPublicId) => {
  return axios.get(`${APIRoute.SHOP_SHIRTS}/${eventsCityPublicId}`)
}

export const useGetShopShirts = (eventsCityPublicId) => {
  return useQuery({
    enabled: eventsCityPublicId?.length > 0,
    queryKey: ['shopShirts', eventsCityPublicId],
    queryFn: () => getShopShirts(eventsCityPublicId),
  })
}
