import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

import { DELIVERY_QUERY_KEY } from './getDeliveryList'

const createDelivery = (data) => {
  return axios.post(APIRoute.GET_DELIVERY_LIST, data)
}

export const useCreateDelivery = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries([DELIVERY_QUERY_KEY])
        openToast.success({ message: 'Доставка добавлена!' })
      }
    },
    mutationFn: createDelivery,
  })
}
