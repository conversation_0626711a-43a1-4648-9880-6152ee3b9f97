import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для создания участника
 * @param {Object} data - Данные участника
 * @returns {Promise} Promise с данными созданного участника
 */
const createAthlete = (data) => {
  return axios.post(APIRoute.API_MEMBER, {
    ...data,
    kind: 'athletes',
  })
}

/**
 * Хук для создания участника
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useCreateAthlete = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: createAthlete,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['athletes'])
        openToast.success({ message: 'Участник успешно добавлен!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при добавлении участника' })
    },
  })
}
