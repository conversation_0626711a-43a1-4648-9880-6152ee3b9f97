import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для обновления участника
 * @param {Object} data - Данные участника с public_id
 * @returns {Promise} Promise с обновленными данными участника
 */
const updateAthlete = ({ public_id, ...data }) => {
  return axios.put(`${APIRoute.API_MEMBER}/${public_id}`, data)
}

/**
 * Хук для обновления участника
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useUpdateAthlete = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: updateAthlete,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['athletes'])
        openToast.success({ message: 'Участник успешно обновлен!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при обновлении участника' })
    },
  })
}
