import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для удаления участника
 * @param {string} publicId - Идентификатор участника
 * @returns {Promise} Promise с результатом запроса
 */
const deleteAthlete = (publicId) => {
  return axios.delete(`${APIRoute.API_MEMBER}/${publicId}`)
}

/**
 * Хук для удаления участника
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useDeleteAthlete = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: deleteAthlete,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['athletes'])
        openToast.success({ message: 'Участник успешно удален!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при удалении участника' })
    },
  })
}
