import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения списка участников
 * @returns {Promise} Promise с данными участников
 */
const getAthletes = () => {
  return axios.get(APIRoute.ATHLETES_GET_ALL)
}

/**
 * Хук для получения списка участников
 * @returns {Object} Объект с данными useQuery
 */
export const useGetAthletes = () => {
  return useQuery({
    queryKey: ['athletes'],
    queryFn: getAthletes,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data.values || [],
  })
}
