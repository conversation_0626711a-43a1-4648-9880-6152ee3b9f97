import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения данных конкретной сборной
 * @param {string} publicId - Идентификатор сборной
 * @returns {Promise} Promise с данными сборной
 */
const getGroup = (publicId) => {
  return axios.get(`${APIRoute.GROUP_MEMBERS}/${publicId}`)
}

/**
 * Хук для получения данных конкретной сборной
 * @param {string} publicId - Идентификатор сборной
 * @returns {Object} Объект с данными useQuery
 */
export const useGetGroup = (publicId) => {
  return useQuery({
    queryKey: ['group', publicId],
    queryFn: () => getGroup(publicId),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data.values,
    enabled: !!publicId,
  })
}
