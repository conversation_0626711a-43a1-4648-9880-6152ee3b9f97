import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для удаления сборной
 * @param {string} publicId - Идентификатор сборной
 * @returns {Promise} Promise с результатом запроса
 */
const deleteGroup = (publicId) => {
  return axios.delete(`${APIRoute.GROUP_MEMBERS}/${publicId}`)
}

/**
 * Хук для удаления сборной
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useDeleteGroup = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: deleteGroup,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['groups'])
        openToast.success({ message: 'Сборная успешно удалена!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при удалении сборной' })
    },
  })
}
