import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для обновления сборной
 * @param {Object} data - Данные сборной с public_id
 * @returns {Promise} Promise с обновленными данными сборной
 */
const updateGroup = ({ public_id, ...data }) => {
  return axios.put(`${APIRoute.GROUP_MEMBERS}/${public_id}`, data)
}

/**
 * Хук для обновления сборной
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useUpdateGroup = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: updateGroup,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['groups'])
        queryClient.invalidateQueries(['group', response.data.public_id])
        openToast.success({ message: 'Сборная успешно обновлена!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при обновлении сборной' })
    },
  })
}
