import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения списка сборных
 * @returns {Promise} Promise с данными сборных
 */
const getGroups = () => {
  return axios.get(APIRoute.GROUP_MEMBERS)
}

/**
 * Хук для получения списка сборных
 * @returns {Object} Объект с данными useQuery
 */
export const useGetGroups = () => {
  return useQuery({
    queryKey: ['groups'],
    queryFn: getGroups,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data.values || [],
  })
}
