import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * Функция для создания сборной
 * @param {Object} data - Данные сборной
 * @returns {Promise} Promise с данными созданной сборной
 */
const createGroup = ({ name }) => {
  return axios.post(APIRoute.GROUP_MEMBERS, { name })
}

/**
 * Хук для создания сборной
 * @returns {Object} Объект с функцией мутации и статусом
 */
export const useCreateGroup = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: createGroup,
    onSuccess: (response) => {
      if (response.status === 200) {
        queryClient.invalidateQueries(['groups'])
        openToast.success({ message: 'Сборная успешно создана!' })
      }
    },
    onError: (error) => {
      console.error(error)
      openToast.error({ message: 'Ошибка при создании сборной' })
    },
  })
}
