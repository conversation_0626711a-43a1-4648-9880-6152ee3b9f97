import { Parser } from 'html-to-react'
import { useEffect, useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Modal, Row } from 'react-bootstrap'
import { useForm, Controller } from 'react-hook-form'

import ImageField from '@/components/Forms/ImageField/ImageField'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import QuillEditor from '@/components/QuillEditor/QuillEditor'

import { useCreateManagement } from '@/features/management/api/createManagement'
import { useDeleteManagement } from '@/features/management/api/deleteManagement'
import { useEditManagement } from '@/features/management/api/editManagement'
import { removeEmptyString } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

import styles from './ManagementFormModal.module.scss'

const FormModal = ({ defaultFormData, isEdit, priorityNumber, onClose }) => {
  const {
    register,
    setValue,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      fio: defaultFormData?.fio,
      title: defaultFormData?.title,
      position: defaultFormData?.position,
      birth_date: unixToMoment(defaultFormData?.birth_date).format('YYYY-MM-DD'),
      education: defaultFormData?.education,
      priority_number: defaultFormData?.priority_number ?? priorityNumber,
      public: defaultFormData?.public ?? true,
      description: defaultFormData?.description,
    },
  })

  const createManagementMutation = useCreateManagement(onClose)
  const editManagementMutation = useEditManagement(onClose)

  const submitForm = (data) => {
    const filteredData = removeEmptyString(data)

    if (Object.keys(defaultFormData)?.length > 0) {
      editManagementMutation.mutate({ id: defaultFormData.public_id, data: filteredData })
    } else {
      createManagementMutation.mutate(filteredData)
    }
  }

  return (
    <Form onSubmit={handleSubmit(submitForm)} id="form">
      <ImageField
        fieldName="picture"
        isClickRow={!isEdit}
        imagePath={defaultFormData?.picture}
        isEdit={isEdit}
        setValue={setValue}
      />

      <Form.Group className="mb-3">
        <FloatingLabel controlId="positionLabel" label="Должность">
          <FormControl
            {...register('position', {
              required: Object.keys(defaultFormData)?.length === 0,
              setValueAs: (v) => checkSetValue(v, defaultFormData?.position, 'text'),
            })}
            type="text"
            isInvalid={errors?.position}
            placeholder="Должность"
            disabled={!isEdit}
          />
        </FloatingLabel>
      </Form.Group>

      <Form.Group className="mb-3">
        <FloatingLabel controlId="fioLabel" label="ФИО">
          <FormControl
            {...register('fio', {
              required: Object.keys(defaultFormData)?.length === 0,
              setValueAs: (v) => checkSetValue(v, defaultFormData?.fio, 'text'),
            })}
            type="text"
            isInvalid={errors?.fio}
            placeholder="ФИО"
            disabled={!isEdit}
          />
        </FloatingLabel>
      </Form.Group>

      <Form.Group className="mb-3">
        <FloatingLabel controlId="titleLabel" label="Заголовок">
          <FormControl
            {...register('title', {
              setValueAs: (v) => checkSetValue(v, defaultFormData?.title, 'text'),
            })}
            type="text"
            isInvalid={errors?.title}
            placeholder="Заголовок"
            disabled={!isEdit}
          />
        </FloatingLabel>
      </Form.Group>

      <Form.Group className="mb-3">
        <FloatingLabel controlId="birthDateLabel" label="Дата рождения">
          <FormControl
            {...register('birth_date', {
              setValueAs: (v) => checkSetValue(v, defaultFormData?.birth_date, 'date'),
            })}
            type="date"
            isInvalid={errors?.birth_date}
            placeholder="Дата рождения"
            disabled={!isEdit}
          />
        </FloatingLabel>
      </Form.Group>

      <Form.Group className="mb-3">
        <FloatingLabel controlId="educationLabel" label="Образование">
          <FormControl
            {...register('education', {
              setValueAs: (v) => checkSetValue(v, defaultFormData?.education, 'text'),
            })}
            type="text"
            isInvalid={errors?.education}
            placeholder="Образование"
            disabled={!isEdit}
          />
        </FloatingLabel>
      </Form.Group>

      <Row>
        <Col>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="priorityNumberLabel" label="Приоритет">
              <FormControl
                {...register('priority_number', {
                  required: Object.keys(defaultFormData)?.length === 0,
                  setValueAs: (v) => checkSetValue(v, defaultFormData?.priority_number, 'number'),
                })}
                type="number"
                isInvalid={errors?.priority_number}
                placeholder="Приоритет"
                disabled={!isEdit}
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={3}>
          <Form.Group className="mb-3" controlId="publicForm">
            <Form.Check
              type="checkbox"
              id={'public'}
              label={'Показывать'}
              {...register('public', {
                setValueAs: (v) => checkSetValue(v, defaultFormData?.public, 'checkbox'),
              })}
              disabled={!isEdit}
            />
          </Form.Group>
        </Col>
      </Row>

      {isEdit ? (
        <Form.Group className={styles.editorWrapper}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <QuillEditor
                {...field}
                placeholder="Профессиональная деятельность"
                value={field.value || ''}
                onChange={(value) => field.onChange(value)}
              />
            )}
          />
        </Form.Group>
      ) : (
        <Row className={styles.descWrapper}>
          <Col xs={12}>
            <span className={styles.descLabel}>Профессиональная деятельность</span>
          </Col>
          <Col>{Parser().parse(defaultFormData.description)}</Col>
        </Row>
      )}
    </Form>
  )
}

export const ManagementFormModal = ({ isShow, defaultFormData, priorityNumber, onClose }) => {
  const [isEdit, setIsEdit] = useState(false)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)

  useEffect(() => {
    if (Object.keys(defaultFormData).length === 0) {
      setIsEdit(true)
    } else {
      setIsEdit(false)
    }
  }, [isShow, defaultFormData])

  const handleClose = () => {
    if (confirmDeleteModal) {
      setConfirmDeleteModal(false)
    }
    onClose()
  }

  const deleteManagement = useDeleteManagement(handleClose)

  const handleEdit = (evt) => {
    evt.preventDefault()
    setIsEdit(true)
  }

  const handleConfirmDelete = () => {
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = (evt) => {
    evt.preventDefault()
    deleteManagement.mutate(defaultFormData.public_id)
  }

  if (!isShow) return null

  return (
    <>
      <Modal show={isShow} onHide={handleClose} size="lg">
        <Modal.Header closeButton />

        <Modal.Body>
          <FormModal
            defaultFormData={defaultFormData}
            isEdit={isEdit}
            onClose={handleClose}
            priorityNumber={priorityNumber}
          />
        </Modal.Body>

        <Modal.Footer>
          <Button onClick={onClose} variant="secondary">
            Закрыть
          </Button>
          {isEdit ? (
            <Button type="submit" form="form">
              Сохранить
            </Button>
          ) : (
            <>
              <Button type="button" onClick={handleEdit}>
                Изменить
              </Button>
              <Button variant="danger" type="button" onClick={handleConfirmDelete}>
                Удалить
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>

      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
