import { useEffect, useState } from 'react'

import Layout from '@/components/Layout/Layout'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { useGetManagements } from '@/features/management/api/getManagements'
import { ManagementFormModal } from '@/features/management/components/ManagementFormModal/ManagementFormModal'
import { managementData } from '@/features/management/managementData'

export const Management = () => {
  const [selectedItem, setSelectedItem] = useState({})
  const [isShowFromModal, setIsShowFromModal] = useState(false)
  const [priorityNumber, setPriorityNumber] = useState(1)

  const managementsQuery = useGetManagements()
  const managements = managementsQuery?.data?.data?.values

  useEffect(() => {
    if (managements?.length > 0) {
      const lastItem = managements[managements.length - 1]

      setPriorityNumber(lastItem.priority_number + 1)
    }
  }, [managements])

  const handleCloseModal = () => {
    setIsShowFromModal(false)
    setSelectedItem({})
  }

  const handleClickRow = (data) => {
    setSelectedItem(data)
    setIsShowFromModal(true)
  }

  return (
    <Layout title="Менеджмент" onClickAddButton={() => setIsShowFromModal(true)}>
      <TableTemplate data={managementData} values={managements} actionRow={handleClickRow} />

      <ManagementFormModal
        isShow={isShowFromModal}
        defaultFormData={selectedItem}
        priorityNumber={priorityNumber}
        onClose={handleCloseModal}
      />
    </Layout>
  )
}
