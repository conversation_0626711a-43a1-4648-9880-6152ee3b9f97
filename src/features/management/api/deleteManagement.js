import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteManagement = (id) => {
  return axios.delete(`${APIRoute.CRUD_ADMIN_MANAGEMENT}/${id}`)
}

export const useDeleteManagement = (onClose) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('managements')

        onClose(false)
        openToast.success({ message: 'Данные успешно удалены!' })
      }
    },
    mutationFn: deleteManagement,
  })
}
