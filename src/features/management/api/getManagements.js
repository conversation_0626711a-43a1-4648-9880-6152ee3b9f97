import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getManagements = () => {
  return axios.get(APIRoute.GET_MANAGEMENTS)
}

export const useGetManagements = () => {
  return useQuery({
    // enabled: publicId?.length > 0,
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['managements'],
    queryFn: () => getManagements(),
  })
}
