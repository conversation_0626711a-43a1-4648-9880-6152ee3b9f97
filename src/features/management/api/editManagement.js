import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const editManagement = ({ id, data }) => {
  return axios.put(`${APIRoute.CRUD_ADMIN_MANAGEMENT}/${id}`, data)
}

export const useEditManagement = (onClose) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('managements')
        onClose(false)
        openToast.success({ message: 'Данные о руководителе обновлены!' })
      }
    },
    mutationFn: editManagement,
  })
}
