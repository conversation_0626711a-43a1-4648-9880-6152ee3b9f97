import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const createManagement = (data) => {
  return axios.post(APIRoute.CRUD_ADMIN_MANAGEMENT, data)
}

export const useCreateManagement = (onClose) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('managements')
        onClose(false)
        openToast.success({ message: 'Данные о руководителе добавлены!' })
      }
    },
    mutationFn: createManagement,
  })
}
