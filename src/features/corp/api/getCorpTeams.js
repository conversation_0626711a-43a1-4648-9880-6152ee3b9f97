import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const getCorpTeams = (companyPublicId) => {
  return axios.get(`${APIRoute.GET_CORP_TEAMS}/${companyPublicId}`)
}

export const useGetCorpTeams = (companyPublicId) => {
  return useQuery({
    queryKey: ['corpTeams', companyPublicId],
    queryFn: () => getCorpTeams(companyPublicId),
    enabled: !!companyPublicId,
    staleTime: 15 * 60 * 1000, // 15 минут
    cacheTime: 15 * 60 * 1000, // 15 минут
  })
}
