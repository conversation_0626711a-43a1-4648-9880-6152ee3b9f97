import { APIRoute } from '@/const'
import { useOptimisticMutation } from '@/helpers/optimisticMutation'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteCorpTeam = (publicId) => {
  return axios.delete(APIRoute.CORP_TEAM, { data: { public_id: publicId } })
}

export const useDeleteCorpTeam = (companyPublicId) => {
  return useOptimisticMutation({
    queryKey: ['corpTeams', companyPublicId],
    mutationFn: deleteCorpTeam,
    successMessage: 'Команда успешно удалена!',
    errorMessage: 'Ошибка при удалении команды',
    onSuccess: (response, publicId) => {
      const previousTeams = queryClient.getQueryData(['corpTeams', companyPublicId])

      if (previousTeams && response?.status === 200) {
        const newData = {
          ...previousTeams,
          data: {
            ...previousTeams.data,
            values: previousTeams.data.values.filter((team) => team.public_id !== publicId),
          },
        }

        queryClient.setQueryData(['corpTeams', companyPublicId], newData)
      }
    },
  })
}
