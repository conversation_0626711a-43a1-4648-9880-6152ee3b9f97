import { APIRoute } from '@/const'
import { useOptimisticMutation } from '@/helpers/optimisticMutation'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const updateCorpTeam = ({ teamId, data }) => axios.put(`${APIRoute.CORP_TEAM}/${teamId}`, data)

export const useUpdateCorpTeam = (companyPublicId) =>
  useOptimisticMutation({
    queryKey: ['corpTeams', companyPublicId],
    mutationFn: updateCorpTeam,
    successMessage: 'Команда успешно обновлена!',
    errorMessage: 'Ошибка при обновлении команды',
    onSuccess: (response) => {
      const currentData = queryClient.getQueryData(['corpTeams', companyPublicId])

      if (currentData?.data?.values) {
        queryClient.setQueryData(['corpTeams', companyPublicId], {
          ...currentData,
          data: {
            ...currentData.data,
            values: currentData.data.values.map((team) =>
              team.public_id === response.data.public_id ? response.data : team
            ),
          },
        })
      }
    },
  })
