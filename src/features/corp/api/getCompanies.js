import { useMutation, useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const getCompanies = (data = {}) => {
  return axios.post(APIRoute.GET_COMPANIES, data)
}

export const useGetCompanies = (data = {}) => {
  return useQuery({
    queryKey: ['companies', data],
    queryFn: () => getCompanies(data),
    staleTime: 15 * 60 * 1000, // 15 минут
    cacheTime: 15 * 60 * 1000, // 15 минут
  })
}

export const useGetCompaniesMutation = () => {
  return useMutation({
    mutationFn: getCompanies,
    onSuccess: (data) => {
      queryClient.setQueryData(['companies', {}], data)
    },
  })
}
