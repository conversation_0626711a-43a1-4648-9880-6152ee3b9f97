import { APIRoute } from '@/const'
import { useOptimisticMutation } from '@/helpers/optimisticMutation'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const createCorpTeam = (data) => axios.post(APIRoute.CORP_TEAM, data)

export const useCreateCorpTeam = (companyPublicId) =>
  useOptimisticMutation({
    queryKey: ['corpTeams', companyPublicId],
    mutationFn: createCorpTeam,
    successMessage: 'Команда успешно создана!',
    errorMessage: 'Ошибка при создании команды',
    onSuccess: (response) => {
      const currentData = queryClient.getQueryData(['corpTeams', companyPublicId])

      if (currentData?.data?.values) {
        queryClient.setQueryData(['corpTeams', companyPublicId], {
          ...currentData,
          data: {
            ...currentData.data,
            values: [response.data, ...currentData.data.values],
          },
        })
      }
    },
  })
