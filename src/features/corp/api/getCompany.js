import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 15

const getCompany = (publicId) => {
  return axios.get(`${APIRoute.GET_COMPANY}/${publicId}`)
}

export const useGetCompany = (publicId) => {
  return useQuery({
    enabled: <PERSON><PERSON><PERSON>(publicId),
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['company', publicId],
    queryFn: () => getCompany(publicId),
  })
}
