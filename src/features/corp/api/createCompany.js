import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createCompany = (data) => {
  return axios.post(APIRoute.CUD_COMPANY, data)
}

export const useCreateCompany = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries(['companies', {}])

      const previousCompanies = queryClient.getQueryData(['companies', {}])

      return { previousCompanies }
    },
    onSuccess: (response) => {
      if (response?.status === 200) {
        const currentData = queryClient.getQueryData(['companies', {}])

        if (currentData) {
          const newData = {
            ...currentData,
            data: {
              ...currentData.data,
              values: [response.data, ...(currentData.data.values || [])],
            },
          }

          queryClient.setQueryData(['companies', {}], newData)
        } else {
          queryClient.invalidateQueries(['companies', {}])
        }

        openToast.success({ message: 'Компания успешно создана!' })
      }
    },
    onError: (context) => {
      if (context?.previousCompanies) {
        queryClient.setQueryData(['companies', {}], context.previousCompanies)
      }
      openToast.error({ message: 'Ошибка при создании компании' })
    },
    mutationFn: createCompany,
  })
}
