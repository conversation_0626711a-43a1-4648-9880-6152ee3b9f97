import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteCompany = (companyId) => {
  return axios.delete(`${APIRoute.CUD_COMPANY}/${companyId}`)
}

export const useDeleteCompany = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async (companyId) => {
      await queryClient.cancelQueries(['companies', {}])

      const previousCompanies = queryClient.getQueryData(['companies', {}])

      if (previousCompanies) {
        const newData = {
          ...previousCompanies,
          data: {
            ...previousCompanies.data,
            values: previousCompanies.data.values.filter((company) => company.public_id !== companyId),
          },
        }

        queryClient.setQueryData(['companies', {}], newData)
      }

      return { previousCompanies }
    },
    onSuccess: (response) => {
      if (response?.status === 200) {
        openToast.success({ message: 'Компания успешно удалена!' })
      }
    },
    onError: (context) => {
      if (context?.previousCompanies) {
        queryClient.setQueryData(['companies', {}], context.previousCompanies)
      }
      openToast.error({ message: 'Ошибка при удалении компании' })
    },
    mutationFn: deleteCompany,
  })
}
