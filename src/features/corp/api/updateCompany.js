import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateCompany = ({ companyId, data }) => {
  return axios.put(`${APIRoute.CUD_COMPANY}/${companyId}`, data)
}

export const useUpdateCompany = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async (updateData) => {
      await queryClient.cancelQueries(['companies', {}])
      await queryClient.cancelQueries(['company', updateData.companyId])

      const previousCompanies = queryClient.getQueryData(['companies', {}])
      const previousCompany = queryClient.getQueryData(['company', updateData.companyId])

      return { previousCompanies, previousCompany }
    },
    onSuccess: (response, updateData) => {
      if (response?.status === 200) {
        const currentData = queryClient.getQueryData(['companies', {}])
        if (currentData) {
          const updatedCompany = response.data
          const newData = {
            ...currentData,
            data: {
              ...currentData.data,
              values: currentData.data.values.map((company) =>
                company.public_id === updatedCompany.public_id ? updatedCompany : company
              ),
            },
          }

          queryClient.setQueryData(['companies', {}], newData)
        } else {
          queryClient.invalidateQueries(['companies', {}])
        }

        queryClient.setQueryData(['company', updateData.companyId], {
          status: 200,
          data: response.data,
        })

        openToast.success({ message: 'Компания успешно обновлена!' })
      }
    },
    onError: (updateData, context) => {
      if (context?.previousCompanies) {
        queryClient.setQueryData(['companies', {}], context.previousCompanies)
      }
      if (context?.previousCompany) {
        queryClient.setQueryData(['company', updateData.companyId], context.previousCompany)
      }
      openToast.error({ message: 'Ошибка при обновлении компании' })
    },
    mutationFn: updateCompany,
  })
}
