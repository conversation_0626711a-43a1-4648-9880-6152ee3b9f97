import { useState, useCallback } from 'react'

import { DEFAULT_FAQ_ITEM } from '../components/FaqFormModal/constants'

export const useFaqForm = (setValue, watchedValues, openNewSection, openNewItem) => {
  const [showCityModal, setShowCityModal] = useState(false)
  const [currentItemIndex, setCurrentItemIndex] = useState(null)
  const [currentSectionIndex, setCurrentSectionIndex] = useState(null)

  const handleAddSection = useCallback(
    (sectionsLength) => {
      const newSectionIndex = sectionsLength
      const newSection = {
        id: newSectionIndex,
        sectionName: '',
        items: [DEFAULT_FAQ_ITEM],
      }

      setValue('values', [...watchedValues, newSection])
      openNewSection(newSectionIndex)
    },
    [setValue, watchedValues, openNewSection]
  )

  const handleAddItem = useCallback(
    (sectionIndex) => {
      const currentSection = watchedValues[sectionIndex]
      const newItemIndex = currentSection.items.length
      const updatedItems = [...currentSection.items, DEFAULT_FAQ_ITEM]

      setValue(`values.${sectionIndex}.items`, updatedItems)
      openNewItem(sectionIndex, newItemIndex)
    },
    [setValue, watchedValues, openNewItem]
  )

  const handleRemoveItem = useCallback(
    (sectionIndex, itemIndex) => {
      const currentSection = watchedValues[sectionIndex]
      const updatedItems = currentSection.items.filter((_, index) => index !== itemIndex)
      setValue(`values.${sectionIndex}.items`, updatedItems)
    },
    [setValue, watchedValues]
  )

  const handleOpenCityModal = useCallback((sectionIndex, itemIndex) => {
    setCurrentSectionIndex(sectionIndex)
    setCurrentItemIndex(itemIndex)
    setShowCityModal(true)
  }, [])

  const handleCitySelection = useCallback(
    (selectedCityIds) => {
      if (currentSectionIndex !== null && currentItemIndex !== null) {
        setValue(`values.${currentSectionIndex}.items.${currentItemIndex}.cities`, selectedCityIds)
      }
    },
    [setValue, currentSectionIndex, currentItemIndex]
  )

  const handleCloseCityModal = useCallback(() => {
    setShowCityModal(false)
  }, [])

  return {
    showCityModal,
    currentItemIndex,
    currentSectionIndex,
    handleAddSection,
    handleAddItem,
    handleRemoveItem,
    handleOpenCityModal,
    handleCitySelection,
    handleCloseCityModal,
  }
}
