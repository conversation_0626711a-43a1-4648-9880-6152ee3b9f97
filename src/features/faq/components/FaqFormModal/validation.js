export const validateAnchor = (value) => {
  if (!value) return true // Якорь не обязателен
  const latinRegex = /^[a-zA-Z0-9_-]*$/
  return latinRegex.test(value) || 'Якорь может содержать только латинские буквы, цифры, дефисы и подчеркивания'
}

export const validateQuillContent = (value) => {
  // Проверяем пустое значение
  if (!value || value === '') {
    return 'Ответ обязателен'
  }

  // Проверяем стандартные пустые значения Quill
  if (value === '<p><br></p>' || value === '<p></p>') {
    return 'Ответ обязателен'
  }

  // Создаем временный элемент для парсинга HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = value

  // Получаем текстовое содержимое без HTML тегов
  const textContent = tempDiv.textContent || tempDiv.innerText || ''

  // Проверяем, есть ли реальное содержимое
  if (textContent.trim().length === 0) {
    return 'Ответ обязателен'
  }

  return true
}
