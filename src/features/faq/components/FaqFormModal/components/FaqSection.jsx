import {
  <PERSON>,
  Col,
  Floating<PERSON><PERSON><PERSON>,
  FormControl,
  Form,
  Button,
  Accordion,
  Card,
  Badge,
  ButtonGroup,
} from 'react-bootstrap'

import FaqItem from './FaqItem'

const FaqSection = ({
  section,
  sectionIndex,
  sectionsLength,
  watchedValues,
  register,
  control,
  errors,
  trigger,
  activeItemKeys,
  onItemSelect,
  onMoveUp,
  onMoveDown,
  onRemove,
  onAddItem,
  onRemoveItem,
  onMoveItemUp,
  onMoveItemDown,
  onOpenCityModal,
  getCityNames,
}) => {
  const sectionName = watchedValues[sectionIndex]?.sectionName || `Категория ${sectionIndex + 1}`

  return (
    <Accordion.Item key={section.id} eventKey={sectionIndex.toString()} className="mb-3 border shadow-sm">
      <Accordion.Header>
        <div className="d-flex justify-content-between align-items-center w-100 me-3">
          <div className="d-flex align-items-center gap-2">
            <span className="fw-bold text-primary">{sectionName}</span>
            <Badge bg="info" pill>
              {watchedValues[sectionIndex]?.items?.length || 0}
            </Badge>
          </div>
          <ButtonGroup size="sm">
            {sectionIndex > 0 && (
              <Button
                as="span"
                variant="outline-secondary"
                className="border-0"
                onClick={(e) => {
                  e.stopPropagation()
                  onMoveUp(sectionIndex)
                }}
                title="Переместить вверх"
                role="button"
                tabIndex={0}
              >
                <i className="bi bi-arrow-up" />
              </Button>
            )}
            {sectionIndex < sectionsLength - 1 && (
              <Button
                as="span"
                variant="outline-secondary"
                className="border-0"
                onClick={(e) => {
                  e.stopPropagation()
                  onMoveDown(sectionIndex, sectionsLength)
                }}
                title="Переместить вниз"
                role="button"
                tabIndex={0}
              >
                <i className="bi bi-arrow-down" />
              </Button>
            )}
            {sectionsLength > 1 && (
              <Button
                as="span"
                variant="outline-danger"
                className="border-0"
                onClick={(e) => {
                  e.stopPropagation()
                  onRemove(sectionIndex)
                }}
                title="Удалить категорию"
                role="button"
                tabIndex={0}
              >
                <i className="bi bi-trash" />
              </Button>
            )}
          </ButtonGroup>
        </div>
      </Accordion.Header>
      <Accordion.Body className="bg-body-tertiary">
        <Card className="mb-3 border">
          <Card.Body>
            <Row className="g-2">
              <Col>
                <FloatingLabel label="Название категории">
                  <FormControl
                    {...register(`values.${sectionIndex}.sectionName`, {
                      required: 'Название категории обязательно',
                    })}
                    type="text"
                    placeholder="Название категории"
                    isInvalid={errors.values?.[sectionIndex]?.sectionName}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.values?.[sectionIndex]?.sectionName?.message}
                  </Form.Control.Feedback>
                </FloatingLabel>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        <Card className="mb-4 border">
          <Card.Header className="d-flex justify-content-between align-items-center">
            <span className="fw-semibold">Вопросы и ответы</span>
            <div className="d-flex align-items-center gap-2">
              <Badge bg="secondary">{watchedValues[sectionIndex]?.items?.length || 0}</Badge>
              <Button variant="outline-success" size="sm" type="button" onClick={() => onAddItem(sectionIndex)}>
                + Добавить вопрос
              </Button>
            </div>
          </Card.Header>
          <Card.Body>
            <Accordion
              activeKey={activeItemKeys[sectionIndex] || ''}
              onSelect={(key) => onItemSelect(sectionIndex, key)}
            >
              {watchedValues[sectionIndex]?.items?.map((item, itemIndex) => {
                const questionText = item.question || `Вопрос ${itemIndex + 1}`

                return (
                  <Accordion.Item key={itemIndex} eventKey={itemIndex.toString()}>
                    <Accordion.Header>
                      <div className="d-flex align-items-center w-100 me-3">
                        <span className="fw-semibold">{questionText}</span>
                        <ButtonGroup size="sm" className="ms-auto">
                          {itemIndex > 0 && (
                            <Button
                              as="span"
                              variant="outline-secondary"
                              className="border-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                onMoveItemUp(sectionIndex, itemIndex)
                              }}
                              title="Переместить вверх"
                              role="button"
                              tabIndex={0}
                            >
                              <i className="bi bi-arrow-up" />
                            </Button>
                          )}
                          {itemIndex < watchedValues[sectionIndex].items.length - 1 && (
                            <Button
                              as="span"
                              variant="outline-secondary"
                              className="border-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                onMoveItemDown(sectionIndex, itemIndex)
                              }}
                              title="Переместить вниз"
                              role="button"
                              tabIndex={0}
                            >
                              <i className="bi bi-arrow-down" />
                            </Button>
                          )}
                          {watchedValues[sectionIndex].items.length > 1 && (
                            <Button
                              as="span"
                              variant="outline-danger"
                              className="border-0"
                              onClick={(e) => {
                                e.stopPropagation()
                                onRemoveItem(sectionIndex, itemIndex)
                              }}
                              title="Удалить вопрос"
                              role="button"
                              tabIndex={0}
                            >
                              <i className="bi bi-trash" />
                            </Button>
                          )}
                        </ButtonGroup>
                      </div>
                    </Accordion.Header>
                    <Accordion.Body>
                      <FaqItem
                        sectionIndex={sectionIndex}
                        itemIndex={itemIndex}
                        item={item}
                        register={register}
                        control={control}
                        errors={errors}
                        trigger={trigger}
                        onOpenCityModal={onOpenCityModal}
                        getCityNames={getCityNames}
                      />
                    </Accordion.Body>
                  </Accordion.Item>
                )
              })}
            </Accordion>
          </Card.Body>
        </Card>
      </Accordion.Body>
    </Accordion.Item>
  )
}

export default FaqSection
