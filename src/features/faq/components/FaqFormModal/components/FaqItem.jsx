import { Row, Col, FloatingLabel, FormControl, Form, Button, Card, Alert } from 'react-bootstrap'
import { Controller } from 'react-hook-form'

import QuillEditor from '@/components/QuillEditor/QuillEditor'

import { validateAnchor, validateQuillContent } from '../validation'

const FaqItem = ({
  sectionIndex,
  itemIndex,
  item,
  register,
  control,
  errors,
  trigger,
  onOpenCityModal,
  getCityNames,
}) => {
  return (
    <div>
      <Card className="mb-3 border">
        <Card.Header className="d-flex align-items-center">
          <span className="text-muted">Основное</span>
        </Card.Header>
        <Card.Body>
          <Row className="g-3">
            <Col md={8}>
              <FloatingLabel label="Вопрос">
                <FormControl
                  {...register(`values.${sectionIndex}.items.${itemIndex}.question`, {
                    required: 'Вопрос обязателен',
                  })}
                  type="text"
                  placeholder="Вопрос"
                  isInvalid={errors.values?.[sectionIndex]?.items?.[itemIndex]?.question}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.values?.[sectionIndex]?.items?.[itemIndex]?.question?.message}
                </Form.Control.Feedback>
              </FloatingLabel>
            </Col>

            <Col md={4}>
              <FloatingLabel label="Якорь (необязательно)">
                <FormControl
                  {...register(`values.${sectionIndex}.items.${itemIndex}.anchor`, {
                    validate: validateAnchor,
                  })}
                  type="text"
                  placeholder="Якорь"
                  isInvalid={errors.values?.[sectionIndex]?.items?.[itemIndex]?.anchor}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.values?.[sectionIndex]?.items?.[itemIndex]?.anchor?.message}
                </Form.Control.Feedback>
              </FloatingLabel>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      <Card className="mb-3 border">
        <Card.Header>
          <span className="text-muted">Ответ</span>
        </Card.Header>
        <Card.Body>
          <div className="mb-3">
            <div
              className={`quill-wrapper ${
                errors.values?.[sectionIndex]?.items?.[itemIndex]?.answer ? 'is-invalid' : ''
              }`}
            >
              <Controller
                name={`values.${sectionIndex}.items.${itemIndex}.answer`}
                control={control}
                rules={{
                  validate: validateQuillContent,
                  required: false,
                }}
                render={({ field }) => (
                  <QuillEditor
                    value={field.value || ''}
                    onChange={(value) => {
                      field.onChange(value)
                      trigger(`values.${sectionIndex}.items.${itemIndex}.answer`)
                    }}
                    placeholder="Введите ответ на вопрос..."
                  />
                )}
              />
            </div>
            {errors.values?.[sectionIndex]?.items?.[itemIndex]?.answer && (
              <div className="invalid-feedback d-block">
                {errors.values?.[sectionIndex]?.items?.[itemIndex]?.answer?.message}
              </div>
            )}
          </div>
        </Card.Body>
      </Card>

      <Card className="border">
        <Card.Header>
          <span className="text-muted">Города</span>
        </Card.Header>
        <Card.Body>
          <div className="d-flex flex-column gap-2">
            <Button
              variant="outline-primary"
              size="sm"
              type="button"
              onClick={() => onOpenCityModal(sectionIndex, itemIndex)}
            >
              Выбрать города ({item.cities?.length || 0})
            </Button>
            {item.cities?.length > 0 && (
              <Alert variant="info" className="py-2 mb-0">
                <small>
                  {getCityNames(item.cities).map((cityName, index) => (
                    <span key={index}>
                      {cityName}
                      {index < item.cities.length - 1 ? ', ' : ''}
                    </span>
                  ))}
                </small>
              </Alert>
            )}
          </div>
        </Card.Body>
      </Card>
    </div>
  )
}

export default FaqItem
