import { Badge } from 'react-bootstrap'

import { useTheme } from '@/contexts/ThemeContext'

const FaqStats = ({ stats }) => {
  const { effectiveTheme } = useTheme()

  return (
    <div className={`card border mb-4 shadow-sm ${effectiveTheme === 'dark' ? 'bg-dark text-light' : 'bg-white'}`}>
      <div className="card-body py-3">
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center gap-3">
            <div className="d-flex gap-3">
              <div className="d-flex align-items-center gap-1">
                <span className={`small ${effectiveTheme === 'dark' ? 'text-light' : 'text-muted'}`}>Категории:</span>
                <Badge
                  bg={effectiveTheme === 'dark' ? 'light' : 'primary'}
                  text={effectiveTheme === 'dark' ? 'dark' : 'white'}
                >
                  {stats.categoriesCount}
                </Badge>
              </div>
              <div className="d-flex align-items-center gap-1">
                <span className={`small ${effectiveTheme === 'dark' ? 'text-light' : 'text-muted'}`}>Вопросы:</span>
                <Badge
                  bg={effectiveTheme === 'dark' ? 'light' : 'success'}
                  text={effectiveTheme === 'dark' ? 'dark' : 'white'}
                >
                  {stats.questionsCount}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FaqStats
