import { useCallback } from 'react'

export const useArrayManipulation = (setValue, watchedValues) => {
  const moveSectionUp = useCallback(
    (sectionIndex) => {
      if (sectionIndex > 0) {
        const newValues = [...watchedValues]
        const temp = newValues[sectionIndex]
        newValues[sectionIndex] = newValues[sectionIndex - 1]
        newValues[sectionIndex - 1] = temp
        setValue('values', newValues)
      }
    },
    [setValue, watchedValues]
  )

  const moveSectionDown = useCallback(
    (sectionIndex, sectionsLength) => {
      if (sectionIndex < sectionsLength - 1) {
        const newValues = [...watchedValues]
        const temp = newValues[sectionIndex]
        newValues[sectionIndex] = newValues[sectionIndex + 1]
        newValues[sectionIndex + 1] = temp
        setValue('values', newValues)
      }
    },
    [setValue, watchedValues]
  )

  const moveItemUp = useCallback(
    (sectionIndex, itemIndex) => {
      if (itemIndex > 0) {
        const currentSection = watchedValues[sectionIndex]
        const newItems = [...currentSection.items]
        const temp = newItems[itemIndex]
        newItems[itemIndex] = newItems[itemIndex - 1]
        newItems[itemIndex - 1] = temp
        setValue(`values.${sectionIndex}.items`, newItems)
      }
    },
    [setValue, watchedValues]
  )

  const moveItemDown = useCallback(
    (sectionIndex, itemIndex) => {
      const currentSection = watchedValues[sectionIndex]
      if (itemIndex < currentSection.items.length - 1) {
        const newItems = [...currentSection.items]
        const temp = newItems[itemIndex]
        newItems[itemIndex] = newItems[itemIndex + 1]
        newItems[itemIndex + 1] = temp
        setValue(`values.${sectionIndex}.items`, newItems)
      }
    },
    [setValue, watchedValues]
  )

  return {
    moveSectionUp,
    moveSectionDown,
    moveItemUp,
    moveItemDown,
  }
}
