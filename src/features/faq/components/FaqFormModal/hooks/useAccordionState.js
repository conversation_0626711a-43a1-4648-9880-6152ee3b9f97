import { useState, useCallback } from 'react'

export const useAccordionState = () => {
  const [activeSectionKey, setActiveSectionKey] = useState('0')
  const [activeItemKeys, setActiveItemKeys] = useState({})

  const handleSectionSelect = useCallback((key) => {
    setActiveSectionKey(key)
  }, [])

  const handleItemSelect = useCallback((sectionIndex, key) => {
    setActiveItemKeys((prev) => ({ ...prev, [sectionIndex]: key }))
  }, [])

  const openNewSection = useCallback((sectionIndex) => {
    setActiveSectionKey(sectionIndex.toString())
    setActiveItemKeys((prev) => ({ ...prev, [sectionIndex]: '0' }))
  }, [])

  const openNewItem = useCallback((sectionIndex, itemIndex) => {
    setActiveItemKeys((prev) => ({ ...prev, [sectionIndex]: itemIndex.toString() }))
  }, [])

  return {
    activeSectionKey,
    activeItemKeys,
    handleSectionSelect,
    handleItemSelect,
    openNewSection,
    openNewItem,
  }
}
