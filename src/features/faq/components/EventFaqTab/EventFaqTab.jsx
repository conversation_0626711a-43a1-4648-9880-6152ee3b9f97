import { useState } from 'react'
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Card<PERSON><PERSON>le, <PERSON>ner, Alert } from 'react-bootstrap'

import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { useGetEventCities } from '@/features/events/api/getEventCities'
import { useDeleteFaq } from '@/features/faq/api/deleteFaq'
import { useGetFaq } from '@/features/faq/api/getFaq'
import { FaqList } from '@/features/faq/components/FaqList/FaqList'

import FaqFormModal from '../FaqFormModal/FaqFormModal'

export const EventFaqTab = ({ eventPublicId }) => {
  const [showFaqModal, setShowFaqModal] = useState(false)
  const [editFaqData, setEditFaqData] = useState(null)
  const [isEditMode, setIsEditMode] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [faqToDelete, setFaqToDelete] = useState(null)

  const eventCitiesQuery = useGetEventCities(eventPublicId)
  const eventCities = eventCitiesQuery?.data?.data?.values || []

  const faqQuery = useGetFaq(eventPublicId)
  const faqData = faqQuery?.data?.data?.values || []

  const deleteFaqMutation = useDeleteFaq()

  const handleOpenFaqModal = () => {
    setShowFaqModal(true)
  }

  const handleCloseFaqModal = () => {
    setShowFaqModal(false)
    setEditFaqData(null)
    setIsEditMode(false)
  }

  const handleEditFaq = (faqData) => {
    setEditFaqData(faqData)
    setIsEditMode(true)
    setShowFaqModal(true)
  }

  const handleDeleteFaq = (faqData) => {
    setFaqToDelete(faqData)
    setShowDeleteModal(true)
  }

  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false)
    setFaqToDelete(null)
  }

  const handleConfirmDelete = () => {
    if (faqToDelete) {
      deleteFaqMutation.mutate(faqToDelete.public_id, {
        onSuccess: () => {
          handleCloseDeleteModal()
        },
      })
    }
  }

  return (
    <>
      <Card className="mb-3">
        <CardHeader>
          <CardTitle>Частые вопросы</CardTitle>
        </CardHeader>
        <CardBody>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <p className="mb-0">Управление часто задаваемыми вопросами для события</p>
              <small className="text-muted">Создавайте и редактируйте ЧаВо для различных городов события</small>
            </div>
            <Button variant="success" onClick={handleOpenFaqModal} disabled={!eventPublicId}>
              Создать
            </Button>
          </div>
        </CardBody>
      </Card>

      {faqQuery.isLoading && (
        <div className="text-center py-4">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Загрузка...</span>
          </Spinner>
        </div>
      )}

      {faqQuery.isError && (
        <Alert variant="danger">
          Ошибка при загрузке ЧаВо: {faqQuery.error?.response?.data?.message || 'Неизвестная ошибка'}
        </Alert>
      )}

      {!faqQuery.isLoading && !faqQuery.isError && (
        <FaqList
          faqData={faqData}
          eventPublicId={eventPublicId}
          onEditFaq={handleEditFaq}
          onDeleteFaq={handleDeleteFaq}
        />
      )}

      <FaqFormModal
        show={showFaqModal}
        onHide={handleCloseFaqModal}
        eventPublicId={eventPublicId}
        eventCities={eventCities}
        editData={editFaqData}
        isEditMode={isEditMode}
      />

      <ConfirmDeleteModal
        isShow={showDeleteModal}
        onClose={handleCloseDeleteModal}
        onDeleteItem={handleConfirmDelete}
      />
    </>
  )
}
