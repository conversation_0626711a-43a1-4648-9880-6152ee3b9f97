import React from 'react'
import { Card, CardBody, CardHeader, CardTitle, Accordion, Badge, Alert, Button } from 'react-bootstrap'

import { useGetEventCities } from '@/features/events/api/getEventCities'
import { unixToMoment } from '@/utils/date'

export const FaqList = ({ faqData, eventPublicId, onEditFaq, onDeleteFaq }) => {
  const eventCitiesQuery = useGetEventCities(eventPublicId)
  const eventCities = eventCitiesQuery?.data?.data?.values || []

  const getCityNames = (cityIds) => {
    if (!cityIds || cityIds.length === 0) return ['Все города']
    return cityIds.map((id) => {
      const city = eventCities.find((c) => c.public_id === id)
      if (city) {
        return `${city.city.name_ru} — ${unixToMoment(city.start_time).tz(city.timezone).format('DD.MM.YYYY')}`
      }
      return `ID: ${id}`
    })
  }

  if (!faqData || faqData.length === 0) {
    return (
      <Alert variant="info" className="text-center">
        <h6>ЧаВо не найдены</h6>
        <p className="mb-0">Создайте первый ЧаВо для этого события</p>
      </Alert>
    )
  }

  return (
    <div>
      {faqData.map((faq, faqIndex) => (
        <Card key={faq.public_id} className="mb-4">
          <CardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <CardTitle className="mb-0">
                ЧаВо #{faqIndex + 1}
                <Badge bg="secondary" className="ms-2">
                  {new Date(faq.created).toLocaleDateString('ru-RU')}
                </Badge>
              </CardTitle>
              <div className="d-flex gap-2">
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => onEditFaq(faq)}
                  className="d-flex align-items-center border-0"
                  title="Редактировать FAQ"
                >
                  <i className="bi bi-pencil" />
                </Button>
                <Button
                  variant="outline-danger"
                  size="sm"
                  onClick={() => onDeleteFaq(faq)}
                  className="d-flex align-items-center border-0"
                  title="Удалить FAQ"
                >
                  <i className="bi bi-trash" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            {faq.values?.map((section, sectionIndex) => (
              <Card key={section.id || sectionIndex} className="mb-3">
                <CardHeader>
                  <h6 className="mb-0">{section.sectionName}</h6>
                </CardHeader>
                <CardBody>
                  <Accordion>
                    {section.items?.map((item, itemIndex) => (
                      <Accordion.Item key={itemIndex} eventKey={`${faqIndex}-${sectionIndex}-${itemIndex}`}>
                        <Accordion.Header>
                          <div className="w-100">
                            <div className="fw-bold">{item.question}</div>
                            {item.anchor && <small className="text-muted">Якорь: {item.anchor}</small>}
                          </div>
                        </Accordion.Header>
                        <Accordion.Body>
                          <div className="mb-3">
                            <strong>Ответ:</strong>
                            <div className="mt-2" dangerouslySetInnerHTML={{ __html: item.answer || '' }} />
                          </div>
                          <div>
                            <strong>Города:</strong>
                            <div className="mt-1">
                              {getCityNames(item.cities).map((cityName, index) => (
                                <React.Fragment key={index}>
                                  <Badge bg="secondary">{cityName}</Badge>
                                  {index < (item.cities?.length || 0) - 1 ? ' ' : ''}
                                </React.Fragment>
                              ))}
                            </div>
                          </div>
                        </Accordion.Body>
                      </Accordion.Item>
                    ))}
                  </Accordion>
                </CardBody>
              </Card>
            ))}
          </CardBody>
        </Card>
      ))}
    </div>
  )
}
