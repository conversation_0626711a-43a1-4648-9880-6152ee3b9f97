import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateFaq = ({ publicId, data }) => {
  return axios.put(`${APIRoute.FAQ_ADMIN}/${publicId}`, data)
}

export const useUpdateFaq = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('updateFaq')
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['faq'])
      openToast.success({ message: 'ЧаВо успешно обновлен!' })
    },
    mutationFn: updateFaq,
  })
}
