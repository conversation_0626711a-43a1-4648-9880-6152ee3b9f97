import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const getFaqDetail = (publicId) => {
  return axios.get(`${APIRoute.GET_FAQ_DETAIL}/${publicId}`)
}

export const useGetFaqDetail = (publicId, options = {}) => {
  return useQuery({
    queryKey: ['faqDetail', publicId],
    queryFn: () => getFaqDetail(publicId),
    enabled: !!publicId,
    ...options,
  })
}
