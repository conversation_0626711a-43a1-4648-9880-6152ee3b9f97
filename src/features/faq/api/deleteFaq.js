import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteFaq = (publicId) => {
  return axios.delete(`${APIRoute.FAQ_ADMIN}/${publicId}`)
}

export const useDeleteFaq = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('deleteFaq')
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['faq'])
      openToast.success({ message: 'ЧаВо успешно удален!' })
    },
    mutationFn: deleteFaq,
  })
}
