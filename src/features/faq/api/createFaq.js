import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createFaq = (data) => {
  return axios.post(APIRoute.FAQ_ADMIN, data)
}

export const useCreateFaq = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('createFaq')
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['faq'])
      openToast.success({ message: 'ЧаВо успешно создан!' })
    },
    mutationFn: createFaq,
  })
}
