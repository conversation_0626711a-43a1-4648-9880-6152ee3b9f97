import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getClubs = ({ queryKey }) => {
  const [, { skip, limit }] = queryKey
  return axios.get(`${APIRoute.GET_CLUBS}/${skip}/${limit}`)
}

export const useGetClubs = ({ skip = 0, limit = 20 } = {}) => {
  return useQuery({
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['clubs', { skip, limit }],
    queryFn: getClubs,
  })
}
