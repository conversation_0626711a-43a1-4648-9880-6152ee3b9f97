import React from 'react'
import { Form, Spinner } from 'react-bootstrap'

import { useGetObrnadzorRegions } from '../api/getObrnadzorRegions'

export const RegionSelector = ({ selectedRegion, onRegionChange, disabled = false }) => {
  const regionsQuery = useGetObrnadzorRegions()

  const regions = regionsQuery?.data?.data?.region_names || []
  const isLoading = regionsQuery.isLoading

  return (
    <Form.Group className="mb-3">
      <Form.Label>Выберите регион</Form.Label>
      <Form.Select
        value={selectedRegion}
        onChange={(e) => onRegionChange(e.target.value)}
        disabled={disabled || isLoading}
      >
        <option value="">{isLoading ? 'Загрузка регионов...' : 'Выберите регион'}</option>
        {regions.map((region) => (
          <option key={region} value={region}>
            {region}
          </option>
        ))}
      </Form.Select>
      {regionsQuery.isError && <Form.Text className="text-danger">Ошибка загрузки регионов</Form.Text>}
    </Form.Group>
  )
}
