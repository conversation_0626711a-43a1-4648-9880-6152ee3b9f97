import React, { useState, useEffect } from 'react'
import { FormControl, InputGroup, Spinner } from 'react-bootstrap'

import { useSearchObrnadzorUniversities } from '../api/searchObrnadzorUniversities'

export const GlobalUniversitySearch = ({ onSearchResults, onSearchChange }) => {
  const [searchText, setSearchText] = useState('')
  const [debouncedSearchText, setDebouncedSearchText] = useState('')

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText)
    }, 500)

    return () => clearTimeout(timer)
  }, [searchText])

  const searchQuery = useSearchObrnadzorUniversities(debouncedSearchText)

  // Handle search results
  useEffect(() => {
    if (searchQuery.data?.data?.values) {
      onSearchResults(searchQuery.data.data.values)
    } else if (debouncedSearchText === '') {
      onSearchResults([])
    }
  }, [searchQuery.data, debouncedSearchText, onSearchResults])

  // Notify parent about search state changes
  useEffect(() => {
    onSearchChange({
      isSearching: !!debouncedSearchText,
      isLoading: searchQuery.isLoading,
      hasError: searchQuery.isError,
      searchText: debouncedSearchText,
    })
  }, [debouncedSearchText, searchQuery.isLoading, searchQuery.isError, onSearchChange])

  const handleSearchChange = (e) => {
    setSearchText(e.target.value)
  }

  return (
    <InputGroup className="mb-3">
      <InputGroup.Text>
        <i className="bi bi-search" />
      </InputGroup.Text>
      <FormControl
        placeholder="Поиск учебных заведений по всем регионам"
        value={searchText}
        onChange={handleSearchChange}
      />
      {searchQuery.isLoading && (
        <InputGroup.Text>
          <Spinner animation="border" size="sm" />
        </InputGroup.Text>
      )}
    </InputGroup>
  )
}
