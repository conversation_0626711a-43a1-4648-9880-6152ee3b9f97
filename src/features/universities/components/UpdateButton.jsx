import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap'

const BUTTON_BLOCK_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
const LAST_UPDATE_KEY = 'universities_last_update'

export const UpdateButton = ({ onUpdate, isLoading, disabled = false }) => {
  const [lastUpdateTime, setLastUpdateTime] = useState(null)
  const [timeRemaining, setTimeRemaining] = useState(0)

  // Check if button should be blocked
  const isButtonBlocked = lastUpdateTime && Date.now() - lastUpdateTime < BUTTON_BLOCK_DURATION

  // Load last update time from localStorage on component mount
  useEffect(() => {
    const savedTime = localStorage.getItem(LAST_UPDATE_KEY)
    if (savedTime) {
      const time = parseInt(savedTime, 10)
      setLastUpdateTime(time)
    }
  }, [])

  // Update countdown timer
  useEffect(() => {
    if (!isButtonBlocked) {
      setTimeRemaining(0)
      return
    }

    const updateTimer = () => {
      const remaining = BUTTON_BLOCK_DURATION - (Date.now() - lastUpdateTime)
      if (remaining <= 0) {
        setTimeRemaining(0)
        setLastUpdateTime(null)
        localStorage.removeItem(LAST_UPDATE_KEY)
      } else {
        setTimeRemaining(remaining)
      }
    }

    updateTimer()
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [lastUpdateTime, isButtonBlocked])

  const handleUpdateData = () => {
    if (isButtonBlocked || disabled) return

    const currentTime = Date.now()
    setLastUpdateTime(currentTime)
    localStorage.setItem(LAST_UPDATE_KEY, currentTime.toString())
    onUpdate()
  }

  const formatTimeRemaining = (milliseconds) => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60))
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000)

    if (hours > 0) {
      return `${hours}ч ${minutes}м ${seconds}с`
    } else if (minutes > 0) {
      return `${minutes}м ${seconds}с`
    } else {
      return `${seconds}с`
    }
  }

  const isDisabled = isLoading || isButtonBlocked || disabled

  return (
    <div className="d-flex flex-column">
      <Button
        variant="primary"
        onClick={handleUpdateData}
        disabled={isDisabled}
        title={isButtonBlocked ? `Кнопка заблокирована. Осталось: ${formatTimeRemaining(timeRemaining)}` : ''}
        style={{ minWidth: '120px' }}
      >
        {isLoading && <Spinner animation="border" size="sm" className="me-2" />}
        Обновить
      </Button>
      {isButtonBlocked && (
        <div
          className="text-muted mt-1"
          style={{
            fontSize: '0.75rem',
            minHeight: '18px',
            textAlign: 'center',
          }}
        >
          {formatTimeRemaining(timeRemaining)}
        </div>
      )}
    </div>
  )
}
