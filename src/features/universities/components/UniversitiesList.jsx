import React from 'react'
import { Badge, Card, Col, Row, Spinner } from 'react-bootstrap'

export const UniversitiesList = ({ universities, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="text-center py-4">
        <Spinner animation="border" />
        <div className="mt-2">Загрузка учебных заведений...</div>
      </div>
    )
  }

  if (error) {
    return <div className="text-center py-4 text-danger">Ошибка загрузки учебных заведений</div>
  }

  if (!universities || universities.length === 0) {
    return <div className="text-center py-4 text-muted">Учебные заведения не найдены</div>
  }

  return (
    <Row>
      {universities.map((university) => (
        <Col key={university.public_id} md={6} lg={4} className="mb-3">
          <Card className="h-100">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start mb-2">
                <Badge bg={university.status ? 'success' : 'secondary'} className="mb-2">
                  {university.status ? 'Активно' : 'Неактивно'}
                </Badge>
              </div>

              <Card.Title className="h6 mb-2">{university.name}</Card.Title>

              <Card.Text className="small text-muted mb-2">{university.full_name}</Card.Text>

              <div className="small text-muted">
                <div>Регион: {university.region_name}</div>
                <div>ID региона: {university.region_id}</div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      ))}
    </Row>
  )
}
