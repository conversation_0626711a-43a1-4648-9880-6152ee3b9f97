import React, { useState, useCallback } from 'react'
import { Col, Container, Row, Alert } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import Layout from '@/components/Layout/Layout'
import PageSearch from '@/components/PageSearch/PageSearch'

import { useGetObrnadzorStatus } from '../api/getObrnadzorStatus'
import { useGetObrnadzorUniversities } from '../api/getObrnadzorUniversities'
import { useUpdateObrnadzorData } from '../api/updateObrnadzorData'
import { GlobalUniversitySearch } from '../components/GlobalUniversitySearch'
import { RegionSelector } from '../components/RegionSelector'
import { UniversitiesList } from '../components/UniversitiesList'
import { UpdateButton } from '../components/UpdateButton'

export const Universities = () => {
  const [selectedRegion, setSelectedRegion] = useState('')
  const [filteredUniversities, setFilteredUniversities] = useState([])
  const [paginatedUniversities, setPaginatedUniversities] = useState([])

  // Global search state
  const [globalSearchResults, setGlobalSearchResults] = useState([])
  const [globalSearchState, setGlobalSearchState] = useState({
    isSearching: false,
    isLoading: false,
    hasError: false,
    searchText: '',
  })
  const [globalPaginatedResults, setGlobalPaginatedResults] = useState([])

  const statusQuery = useGetObrnadzorStatus()
  const updateMutation = useUpdateObrnadzorData()
  const universitiesQuery = useGetObrnadzorUniversities(selectedRegion)

  const status = statusQuery?.data?.data
  const universities = universitiesQuery?.data?.data?.values || []
  const isLoading = statusQuery.isLoading || updateMutation.isLoading

  const handleUpdateData = () => {
    updateMutation.mutate()
  }

  const handleRegionChange = (region) => {
    setSelectedRegion(region)
    setFilteredUniversities([])
    setPaginatedUniversities([])
  }

  const handleGlobalSearchResults = useCallback((results) => {
    setGlobalSearchResults(results)
    setGlobalPaginatedResults([])
  }, [])

  const handleGlobalSearchChange = useCallback((searchState) => {
    setGlobalSearchState(searchState)
    if (!searchState.isSearching) {
      setGlobalSearchResults([])
      setGlobalPaginatedResults([])
    }
  }, [])

  const formatDate = (dateString) => {
    if (!dateString) return 'Не определено'

    try {
      const date = new Date(dateString)
      return date.toLocaleString('ru-RU', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    } catch {
      return 'Неверный формат даты'
    }
  }

  const isGlobalSearchActive = globalSearchState.isSearching
  const showRegionalSearch = selectedRegion && !isGlobalSearchActive
  const showGlobalResults = isGlobalSearchActive && globalSearchResults.length > 0

  return (
    <Layout title="Универы">
      <Container fluid>
        <div className="d-flex align-items-start gap-3 mt-4 mb-4">
          <UpdateButton onUpdate={handleUpdateData} isLoading={isLoading} />

          <div className="d-flex flex-column align-items-start">
            {statusQuery.isLoading ? (
              <span className="text-muted">Загрузка...</span>
            ) : statusQuery.isError ? (
              <span className="text-danger">Ошибка загрузки данных</span>
            ) : (
              <>
                <span className="text-muted">{formatDate(status?.start_date)}</span>
                <span className="text-muted">
                  {status?.count !== undefined ? status.count.toLocaleString('ru-RU') : '0'} объектов
                </span>
              </>
            )}
          </div>
        </div>

        {/* Global Search */}
        <Row className="mb-4">
          <Col>
            <GlobalUniversitySearch
              onSearchResults={handleGlobalSearchResults}
              onSearchChange={handleGlobalSearchChange}
            />
            {globalSearchState.hasError && (
              <Alert variant="danger" className="mt-2">
                Ошибка при поиске учебных заведений
              </Alert>
            )}
          </Col>
        </Row>

        {/* Regional Search Section */}
        {!isGlobalSearchActive && (
          <Row>
            <Col md={4} lg={3}>
              <RegionSelector
                selectedRegion={selectedRegion}
                onRegionChange={handleRegionChange}
                disabled={isLoading}
              />
            </Col>
          </Row>
        )}

        {/* Global Search Results */}
        {showGlobalResults && (
          <>
            <UniversitiesList
              universities={globalPaginatedResults}
              isLoading={globalSearchState.isLoading}
              error={globalSearchState.hasError}
            />

            {globalSearchResults.length > 0 && (
              <AdvancedPagination values={globalSearchResults} setValues={setGlobalPaginatedResults} limitValues={12} />
            )}
          </>
        )}

        {/* Regional Search Results */}
        {showRegionalSearch && (
          <>
            {universities.length > 0 && (
              <Row className="mb-3">
                <Col>
                  <PageSearch
                    values={universities}
                    setValues={setFilteredUniversities}
                    placeholder="Поиск по учебным заведениям региона"
                  />
                </Col>
              </Row>
            )}

            <UniversitiesList
              universities={paginatedUniversities}
              isLoading={universitiesQuery.isLoading}
              error={universitiesQuery.isError}
            />

            {filteredUniversities.length > 0 && (
              <AdvancedPagination values={filteredUniversities} setValues={setPaginatedUniversities} limitValues={12} />
            )}
          </>
        )}

        {/* Empty States */}
        {isGlobalSearchActive &&
          globalSearchState.searchText &&
          !globalSearchState.isLoading &&
          globalSearchResults.length === 0 && (
            <div className="text-center py-4 text-muted">
              По запросу &ldquo;{globalSearchState.searchText}&rdquo; ничего не найдено
            </div>
          )}

        {!isGlobalSearchActive && !selectedRegion && (
          <div className="text-center py-4 text-muted">Выберите регион или воспользуйтесь глобальным поиском</div>
        )}
      </Container>
    </Layout>
  )
}
