import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 1

const getObrnadzorStatus = () => {
  return axios.get(APIRoute.OBRNADZOR_STATUS)
}

export const useGetObrnadzorStatus = () => {
  return useQuery({
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['obrnadzor-status'],
    queryFn: () => getObrnadzorStatus(),
  })
}
