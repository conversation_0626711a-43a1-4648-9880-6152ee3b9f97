import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const updateObrnadzorData = () => {
  return axios.put(APIRoute.OBRNADZOR_UPDATE_DATA)
}

export const useUpdateObrnadzorData = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('obrnadzor-status')
        openToast.success({ message: 'Данные обновлены успешно!' })
      }
    },
    onError: (error) => {
      openToast.error({
        message: error?.response?.data?.message || 'Ошибка при обновлении данных',
      })
    },
    mutationFn: updateObrnadzorData,
  })
}
