import { useQuery } from 'react-query'

import { MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const searchObrnadzorUniversities = (searchText) => {
  return axios.get(`/api/obrnadzor/search/${encodeURIComponent(searchText)}`)
}

export const useSearchObrnadzorUniversities = (searchText, options = {}) => {
  return useQuery({
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['obrnadzor-search', searchText],
    queryFn: () => searchObrnadzorUniversities(searchText),
    enabled: !!searchText && searchText.length >= 2,
    ...options,
  })
}
