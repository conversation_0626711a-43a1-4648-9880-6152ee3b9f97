import { useQuery } from 'react-query'

import { MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getObrnadzorUniversities = (regionName) => {
  return axios.get(`/api/obrnadzor/region/${encodeURIComponent(regionName)}`)
}

export const useGetObrnadzorUniversities = (regionName) => {
  return useQuery({
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['obrnadzor-universities', regionName],
    queryFn: () => getObrnadzorUniversities(regionName),
    enabled: !!regionName,
  })
}
