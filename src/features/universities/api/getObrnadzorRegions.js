import { useQuery } from 'react-query'

import { MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getObrnadzorRegions = () => {
  return axios.get('/api/obrnadzor/regions')
}

export const useGetObrnadzorRegions = () => {
  return useQuery({
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['obrnadzor-regions'],
    queryFn: () => getObrnadzorRegions(),
  })
}
