import { Card, Col, Row } from 'react-bootstrap'

import { useGetOlympicFeedback } from '@/features/olympicReserve/api/getOlympicFeedback'
import { unixToMoment } from '@/utils/date'

export const OlympicFeedbackTab = () => {
  const getOlympicFeedbackQuery = useGetOlympicFeedback()
  const feedbackValues = getOlympicFeedbackQuery?.data?.data?.values

  return (
    <Row className="g-3">
      {feedbackValues?.map((item, index) => (
        <Col md={12} key={index}>
          <Card>
            <Card.Header>
              {unixToMoment(item.created_date).format('DD.MM.YYYY, hh:mm')} — {item.email}
            </Card.Header>
            <Card.Body>
              <h5>{item.name}</h5>
              <p>{item.text}</p>
            </Card.Body>
          </Card>
        </Col>
      ))}
    </Row>
  )
}
