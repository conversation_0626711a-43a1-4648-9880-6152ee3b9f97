import { Card, Col, ListGroup, Row } from 'react-bootstrap'

import { useGetOlympicApplications } from '@/features/olympicReserve/api/getOlympicApplications'
import { formatPhoneNumber } from '@/utils/common'
import { unixToMoment } from '@/utils/date'

const listLabels = [
  { label: 'ФИО (Рус.)', path: 'name' },
  { label: 'ФИО (Англ.)', path: 'name_en' },
  { label: 'День рождения', path: 'b_date', param: 'date' },
  { label: 'Город проживания', path: 'city_name' },
  { label: 'Телефон', path: 'phone', param: 'phone' },
  { label: 'E-mail', path: 'email' },
  { label: 'Квалификация (разряд, звание)', path: 'qualification' },
  { label: 'Лучший результат (личный рекорд)', path: 'best_result' },
  { label: 'Первый вид спорта', path: 'first_sport' },
  { label: 'Спортивный стаж', path: 'experience' },
  { label: 'ФИО тренера', path: 'trainer_name' },
  { label: 'Телефон, E-mail тренера', path: 'trainer_contact' },
  { label: 'Рост в см', path: 'height' },
  { label: 'Вес в кг', path: 'weight' },
  { label: 'Срок действия паспорта', path: 'passport_valid', param: 'date' },
  {
    label: 'Ссылка на видео о прохождении дистанции',
    path: 'video',
    param: 'link',
    url: '',
  },
  {
    label: 'Протокол результатов',
    path: 'filename',
    param: 'link',
    url: 'https://rustriathlon.ru/olympicdata/',
  },
]

const switchListItem = (itemLabel, itemData) => {
  switch (itemLabel.param) {
    case 'link':
      return (
        <b>
          <a href={itemLabel.url + itemData[itemLabel.path]} target="_blank" rel="noopener noreferrer">
            {itemData[itemLabel.path]}
          </a>
        </b>
      )
    case 'date':
      return <b>{unixToMoment(itemData[itemLabel.path]).format('DD.MM.YYYY')}</b>
    case 'phone':
      return <b>{formatPhoneNumber(itemData[itemLabel.path])}</b>
    default:
      return <b>{itemData[itemLabel.path]}</b>
  }
}

export const OlympicApplicationsTab = () => {
  const getOlympicApplicationsQuery = useGetOlympicApplications()
  const applicationsValues = getOlympicApplicationsQuery?.data?.data?.values

  return (
    <Row className="g-3">
      {applicationsValues?.map((item, index) => (
        <Col md={12} key={index}>
          <Card className="mb-4" style={{ minWidth: '450px' }}>
            <Card.Header>
              {item.created_date && `${unixToMoment(item.created_date).format('DD.MM.YYYY')}: `}
              {item.name ? item.name : 'Имя не указано'}
            </Card.Header>
            <Card.Body>
              <ListGroup>
                <ListGroup.Item>
                  {listLabels.map((el) => (
                    <Row className="mb-2" key={el.label}>
                      <Col xs="5">{el.label}:</Col>
                      <Col xs="7">{switchListItem(el, item)}</Col>
                    </Row>
                  ))}
                </ListGroup.Item>
              </ListGroup>
            </Card.Body>
          </Card>
        </Col>
      ))}
    </Row>
  )
}
