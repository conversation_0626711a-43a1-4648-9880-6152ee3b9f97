import { Tab, Tabs } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'

import { OlympicApplicationsTab } from '@/features/olympicReserve/components/OlympicApplicationsTab'
import { OlympicFeedbackTab } from '@/features/olympicReserve/components/OlympicFeedbackTab'

const TabKey = {
  FEEDBACK: 'feedback',
  APPLICATION: 'application',
}

export const OlympicReserve = () => {
  return (
    <Layout title="Олимпийский резерв">
      <Tabs className="mb-4" defaultActiveKey={TabKey.FEEDBACK}>
        <Tab eventKey={TabKey.FEEDBACK} title="Обратная связь">
          <OlympicFeedbackTab />
        </Tab>

        <Tab eventKey={TabKey.APPLICATION} title="Заявки">
          <OlympicApplicationsTab />
        </Tab>
      </Tabs>
    </Layout>
  )
}
