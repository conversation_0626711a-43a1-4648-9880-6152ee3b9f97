import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 60

const getRegionCities = (iso) => {
  return axios.get(`${APIRoute.GET_REGION_CITIES}/${iso}`)
}

export const useGetRegionCities = (iso) => {
  return useQuery({
    enabled: Boolean(iso),
    queryKey: ['regionCities', iso],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: () => getRegionCities(iso),
  })
}
