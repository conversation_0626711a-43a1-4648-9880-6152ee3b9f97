import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 60

const getCountryList = () => {
  return axios.get(APIRoute.COUNTRY_LIST)
}

export const useGetCountryList = () => {
  return useQuery({
    queryKey: ['countryList'],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: getCountryList,
  })
}
