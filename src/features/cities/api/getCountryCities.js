import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 60

const getCountryCities = (iso) => {
  return axios.get(`${APIRoute.GET_COUNTRY_CITIES}/${iso}`)
}

export const useGetCountryCities = (iso) => {
  return useQuery({
    queryKey: ['countryCities', iso],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: () => getCountryCities(iso),
  })
}
