import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 60

const getCountryRegions = (iso) => {
  return axios.get(`${APIRoute.GET_COUNTRY_REGIONS}/${iso}`)
}

export const useGetCountryRegions = (iso) => {
  return useQuery({
    queryKey: ['countryRegions', iso],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: () => getCountryRegions(iso),
  })
}
