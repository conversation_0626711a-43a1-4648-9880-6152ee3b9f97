import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const updateUserRole = ({ userId, data }) => {
  return axios.put(`${APIRoute.SUPERADMIN_ROLE}/${userId}`, data)
}

export const useUpdateUserRole = () => {
  const queryClient = useQueryClient()
  const openToast = useToast()

  return useMutation({
    mutationFn: updateUserRole,
    onSuccess: () => {
      queryClient.invalidateQueries(['usersWithRoles'])
      openToast.success({ message: 'Роль пользователя успешно изменена' })
    },
  })
}
