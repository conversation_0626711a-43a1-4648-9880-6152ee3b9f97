import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const USER_DATE_STATISTICS_QUERY_KEY = 'userDateStatistics'

const getUserDateStatistics = (data) => {
  return axios.post(APIRoute.USER_DATE_STATISTICS, data)
}

export const useGetUserDateStatistics = () => {
  return useMutation({
    mutationKey: USER_DATE_STATISTICS_QUERY_KEY,
    mutationFn: getUserDateStatistics,
  })
}
