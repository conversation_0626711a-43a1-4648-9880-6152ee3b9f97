import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const syncOfflineEvents = () => {
  return axios.post(APIRoute.SYNC_OFFLINE_EVENTS)
}

export const useSyncOfflineEvents = (onSetCount) => {
  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200 && data?.data?.message) {
        onSetCount(0, 'events')
      }
    },
    mutationFn: syncOfflineEvents,
  })
}
