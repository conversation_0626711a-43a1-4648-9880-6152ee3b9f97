import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const syncOfflineUsers = (data) => {
  return axios.put(APIRoute.SYNC_OFFLINE_USERS, data)
}

export const useSyncOfflineUsers = (onSetCount) => {
  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        onSetCount(data.data.count, 'users')
      }
    },
    mutationFn: syncOfflineUsers,
  })
}
