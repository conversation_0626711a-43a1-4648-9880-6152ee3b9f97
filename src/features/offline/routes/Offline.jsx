import useAxios from 'axios-hooks'
import moment from 'moment'
import { useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, FormSelect, Modal, Row, Spinner } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import Layout from '@/components/Layout/Layout'
import SuccessModal from '@/components/Modal/SuccessModal/SuccessModal'

import { APIRoute } from '@/const'
import { useSyncOfflineEvents } from '@/features/offline/api/syncOfflineEvents'
import { useSyncOfflineUsers } from '@/features/offline/api/syncOfflineUsers'
import { getFormatTime } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

export const Offline = () => {
  const [countString, setCountString] = useState('')
  const [isShowSuccess, setIsShowSuccess] = useState(false)
  const [isShowSuccessTickets, setIsShowSuccessTickets] = useState(false)
  const [isShowAuthModal, setIsShowAuthModal] = useState(false)
  const [eventId, setEventId] = useState('')
  const [eventCityId, setEventCityId] = useState('')
  const [variantSendRequest, setVariantSendRequest] = useState('')
  const [isShowPassword, setIsShowPassword] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    getValues,
  } = useForm()

  const [{ data: ticketsTeams, loading: ticketsTeamsLoading }, getTicketsTeams] = useAxios(
    {
      url: APIRoute.ADMIN_RASPBERRY,
      method: 'PUT',
    },
    { manual: true }
  )

  const [{ data: eventTypeList }, apiEventTypeList] = useAxios(
    {
      url: APIRoute.EVENT_TYPE_LIST,
      method: 'GET',
    },
    { manual: false }
  )

  const [{ data: eventCityFiltered }, apiEventCityFiltered] = useAxios(
    {
      url: APIRoute.EVENT_CITY_FILTERED,
      method: 'POST',
    },
    { manual: true }
  )

  const handleShowSuccessModal = (count, type) => {
    if (type === 'users') {
      setCountString(
        <>
          Синхронизировано <b>{count}</b> пользователей
        </>
      )
    } else {
      setCountString(<>Список событий обновлён</>)
      apiEventTypeList()
    }

    setIsShowSuccess(true)
  }

  const syncOfflineUsersMutation = useSyncOfflineUsers(handleShowSuccessModal)
  const syncOfflineEventsMutation = useSyncOfflineEvents(handleShowSuccessModal)

  const handleCloseSuccessModal = () => {
    setCountString('')
    setIsShowSuccess(false)
  }

  const handleSync = () => {
    if (!isValid) {
      setIsShowAuthModal(true)
      setVariantSendRequest('syncOfflineUsers')
    } else {
      const data = getValues()
      syncOfflineUsersMutation.mutate(data)
      setVariantSendRequest('')
    }
  }

  const handleUpdateEvents = () => {
    syncOfflineEventsMutation.mutate()
  }

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = eventTypeList?.values.find((event) => event.public_id === eventId)

    if (event) {
      const body = {
        city: {
          id: 0,
        },
        date_from: moment().subtract(3, 'days'),
        date_to: moment().add(1, 'years'),
        event_type: {
          public_id: event.public_id,
        },
      }

      setEventId(event.public_id)
      setEventCityId('')

      apiEventCityFiltered({ data: body })
    }
  }

  const handleChangeCity = (evt) => {
    const cityPublicId = evt.target.value

    setEventCityId(cityPublicId)
  }

  const handleGetTicketsTeams = () => {
    if (!isValid) {
      setIsShowAuthModal(true)
      setVariantSendRequest('getTicketsTeams')
    } else {
      const data = getValues()

      getTicketsTeams({ url: `${APIRoute.ADMIN_RASPBERRY}/${eventCityId}`, data: data }).then((r) => {
        if (r?.status === 200) {
          setIsShowSuccessTickets(true)
        }
      })

      setVariantSendRequest('')
    }
  }

  const handleSubmitAuthForm = () => {
    setIsShowAuthModal(false)

    if (variantSendRequest === 'getTicketsTeams') {
      handleGetTicketsTeams()
    } else if (variantSendRequest === 'syncOfflineUsers') {
      handleSync()
    }
  }

  if (window.location.hostname === 'heroleague.ru') return null

  return (
    <Layout title="Оффлайн">
      <Row className="mb-5">
        <Col sm={12} lg={{ span: 4, offset: 4 }}>
          {/*<Row>*/}
          {/*  <Col className="mb-3" md={7}>*/}
          {/*    Синхронизация пользователей:*/}
          {/*  </Col>*/}
          {/*  <Col>*/}
          {/*    <Button onClick={handleSync} disabled={syncOfflineUsersMutation.isLoading} style={{minWidth: '147px'}} size="sm">*/}
          {/*      {syncOfflineUsersMutation.isLoading ? (*/}
          {/*        <Spinner animation="grow" size="sm" />*/}
          {/*      ):(*/}
          {/*        'Синхронизировать'*/}
          {/*      )}*/}
          {/*    </Button>*/}
          {/*  </Col>*/}
          {/*</Row>*/}
          <Row>
            <Col md={7}>Обновление списка событий:</Col>
            <Col>
              <Button
                onClick={handleUpdateEvents}
                disabled={syncOfflineEventsMutation.isLoading}
                style={{ minWidth: '147px' }}
                size="sm"
              >
                {syncOfflineEventsMutation.isLoading ? <Spinner animation="grow" size="sm" /> : 'Обновить'}
              </Button>
            </Col>
          </Row>
        </Col>
      </Row>

      <Row>
        <Col className="mb-3" sm={12} lg={{ span: 4, offset: 4 }}>
          <FloatingLabel controlId="eventLabelTicket" label="Событие">
            <FormSelect onChange={handleChangeEvent} name="event_type" aria-label="Событие">
              <option value="">выберите один из вариантов</option>
              {eventTypeList?.values?.map((event) => (
                <option value={event.public_id} key={event.public_id}>
                  {event.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col className="mb-3" sm={12} lg={{ span: 4, offset: 4 }}>
          <FloatingLabel controlId="cityLabelTicket" label="Город">
            <FormSelect onChange={handleChangeCity} name="event_city" aria-label="Город">
              <option value="">выберите один из вариантов</option>
              {eventCityFiltered?.values?.map((city) => (
                <option value={city.public_id} key={city.public_id}>
                  {city.address} — {getFormatTime(city.start_time, city.timezone)}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col sm={12} lg={{ span: 4, offset: 4 }}>
          <Button
            onClick={handleGetTicketsTeams}
            style={{ width: '100%', minWidth: '230px' }}
            disabled={eventId === '' || eventCityId === '' || ticketsTeamsLoading}
          >
            {ticketsTeamsLoading ? <Spinner animation="grow" size="sm" /> : 'Получить участников'}
          </Button>
        </Col>
      </Row>

      <SuccessModal show={isShowSuccess} handleCloseModal={handleCloseSuccessModal}>
        <center>{countString}</center>
      </SuccessModal>

      <SuccessModal show={isShowSuccessTickets} handleCloseModal={() => setIsShowSuccessTickets(false)}>
        <center>
          Получено: Билетов <b>{ticketsTeams?.tickets}</b>, Команд <b>{ticketsTeams?.teams}</b>
        </center>
      </SuccessModal>

      <Modal show={isShowAuthModal} onHide={() => setIsShowAuthModal(false)}>
        <Modal.Header closeButton />
        <Modal.Body>
          <Form onSubmit={handleSubmit(handleSubmitAuthForm)} id="authForm">
            <Row>
              <Col>
                <Form.Group>
                  <FloatingLabel controlId="emailLabel" label="Почта">
                    <FormControl
                      {...register('email', {
                        setValueAs: (v) => checkSetValue(v, {}, 'email'),
                        required: true,
                      })}
                      type="email"
                      isInvalid={errors?.email}
                      placeholder="Почта"
                      autoComplete="email"
                    />
                  </FloatingLabel>
                </Form.Group>
              </Col>

              <Col>
                <Form.Group>
                  <FloatingLabel controlId="passwordLabel" label="Пароль">
                    <FormControl
                      {...register('password', {
                        setValueAs: (v) => checkSetValue(v, {}, 'text'),
                        required: true,
                      })}
                      type={isShowPassword ? 'text' : 'password'}
                      isInvalid={errors?.password}
                      placeholder="Пароль"
                      autoComplete="current-password"
                    />
                  </FloatingLabel>
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col />
              <Col md="auto">
                <Button onClick={() => setIsShowPassword((prev) => !prev)} variant="link">
                  {isShowPassword ? 'Скрыть пароль' : 'Показать пароль'}
                </Button>
              </Col>
            </Row>
          </Form>
        </Modal.Body>

        <Modal.Footer>
          <Button type="submit" variant="primary" form="authForm">
            Ок
          </Button>
        </Modal.Footer>
      </Modal>
    </Layout>
  )
}
