import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createSportType = (data) => {
  return axios.post(APIRoute.SPORT_TYPE, data)
}

export const useCreateSportType = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('createSportType')
    },
    onSuccess: (data) => {
      if (data?.status === 200) {
        // Получаем текущие данные из кэша
        const currentData = queryClient.getQueryData('sportTypeList')

        if (currentData && data.data) {
          // Добавляем новый объект в список
          const newValues = [...(currentData.data?.values || []), data.data]

          // Обновляем кэш
          queryClient.setQueryData('sportTypeList', {
            ...currentData,
            data: {
              ...currentData.data,
              values: newValues,
            },
          })
        }

        openToast.success({ message: 'Вид спорта успешно добавлен!' })
      }
    },
    mutationFn: createSportType,
  })
}
