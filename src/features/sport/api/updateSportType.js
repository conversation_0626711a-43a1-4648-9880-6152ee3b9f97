import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateSportType = ({ id, data }) => {
  return axios.put(`${APIRoute.SPORT_TYPE}/${id}`, data)
}

export const useUpdateSportType = () => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('updateSportType')
    },
    onSuccess: (data) => {
      if (data?.status === 200) {
        // Получаем текущие данные из кэша
        const currentData = queryClient.getQueryData('sportTypeList')

        if (currentData && data.data) {
          // Обновляем объект в списке
          const updatedValues = (currentData.data?.values || []).map((item) =>
            item.public_id === data.data.public_id ? data.data : item
          )

          // Обновляем кэш
          queryClient.setQueryData('sportTypeList', {
            ...currentData,
            data: {
              ...currentData.data,
              values: updatedValues,
            },
          })
        }

        openToast.success({ message: 'Вид спорта успешно обновлен!' })
      }
    },
    mutationFn: updateSportType,
  })
}
