import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteSportType = (id) => {
  return axios.delete(`${APIRoute.SPORT_TYPE}/${id}`)
}

export const useDeleteSportType = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data, variables) => {
      if (data?.status === 200) {
        // Получаем текущие данные из кэша
        const currentData = queryClient.getQueryData('sportTypeList')

        if (currentData) {
          // Удаляем объект из списка
          const filteredValues = (currentData.data?.values || []).filter((item) => item.public_id !== variables)

          // Обновляем кэш
          queryClient.setQueryData('sportTypeList', {
            ...currentData,
            data: {
              ...currentData.data,
              values: filteredValues,
            },
          })
        }

        openToast.success({ message: 'Вид спорта успешно удалён!' })
      }
    },
    mutationFn: deleteSportType,
  })
}
