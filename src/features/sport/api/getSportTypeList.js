import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 30

const getSportTypeList = () => {
  return axios.get(APIRoute.SPORT_TYPE_LIST)
}

export const useGetSportTypeList = () => {
  return useQuery({
    queryKey: ['sportTypeList'],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: getSportTypeList,
  })
}
