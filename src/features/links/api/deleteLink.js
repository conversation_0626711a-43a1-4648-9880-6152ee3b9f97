import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteLink = (linkId) => {
  return axios.delete(`${APIRoute.CREATE_LINK}/${linkId}`)
}

export const useDeleteLink = ({ config } = {}) => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('links')
    },
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('links')

        openToast.success({ message: 'Ссылка была удалена' })
      }
    },
    ...config,
    mutationFn: deleteLink,
  })
}
