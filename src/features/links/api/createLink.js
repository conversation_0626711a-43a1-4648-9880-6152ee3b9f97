import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createLink = (data) => {
  return axios.post(APIRoute.CREATE_LINK, data)
}

export const useCreateLink = ({ config } = {}) => {
  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('createLink')
    },
    onSuccess: () => {
      queryClient.invalidateQueries('links')
    },
    ...config,
    mutationFn: createLink,
  })
}
