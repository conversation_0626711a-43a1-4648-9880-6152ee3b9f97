import { useState } from 'react'
import { Card } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'

import { LinkCard } from '@/features/links/components/LinkCard'

export const LinksCard = ({ links, baseUrl }) => {
  const [valuesRender, setValuesRender] = useState(links)

  return (
    <>
      <Card className="mb-3">
        <Card.Body className="py-2 px-3 py-lg-3 px-lg-4">
          {valuesRender?.length > 0 ? (
            valuesRender?.map((item) => <LinkCard link={item} baseUrl={baseUrl} key={item.public_id} />)
          ) : (
            <center>Ссылок нет</center>
          )}
        </Card.Body>
      </Card>

      <AdvancedPagination values={links} setValues={setValuesRender} />
    </>
  )
}
