import React from 'react'
import { Form } from 'react-bootstrap'
import { useDispatch, useSelector } from 'react-redux'

import { addStatusHrcl, getIsHrcl } from '@/store/slices/links'

export const HrclSwitcher = ({ id = '' }) => {
  const isHrcl = useSelector(getIsHrcl)

  const dispatch = useDispatch()

  const handleSwitchHrcl = (evt) => {
    dispatch(addStatusHrcl(evt.target.checked))
  }

  if (
    window.origin !== 'https://herodev.ru' &&
    window.origin !== 'https://heroleague.ru' &&
    window.origin !== 'http://localhost:3001'
  )
    return null

  return (
    <Form.Check type="switch" id={`hrcl-switch-${id}`} label="hrcl.ru" checked={isHrcl} onChange={handleSwitchHrcl} />
  )
}
