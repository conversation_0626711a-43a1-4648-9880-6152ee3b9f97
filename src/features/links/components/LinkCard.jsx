import { useState } from 'react'
import { <PERSON><PERSON>, Modal, OverlayTrigger } from 'react-bootstrap'
import { useSelector } from 'react-redux'
import { Link } from 'react-router-dom'

import IconQRCode from '@/assets/img/icons/icon-qr-code.svg?react'

import { CopyToClipboard } from '@/components/CopyToClipboard/CopyToClipboard'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { AppRoute } from '@/const'
import { useDeleteLink } from '@/features/links/api/deleteLink'
import { QRCodeLink } from '@/features/links/components/QRCodeLink'
import { getIsHrcl } from '@/store/slices/links'
import { unixToMoment } from '@/utils/date'
import { renderBtnTooltip } from '@/utils/tooltips.jsx'

import styles from './LinkCard.module.scss'

export const LinkCard = ({ link, baseUrl }) => {
  const [isOpenQRCodeModal, setIsOpenQRCodeModal] = useState(false)
  const [confirmModal, setConfirmModal] = useState(false)

  const isHrcl = useSelector(getIsHrcl)

  const deleteLinkMutation = useDeleteLink()

  const idSvgModal = 'qrcodeLinkSvgModal'
  const idCanvasModal = 'qrcodeLinkCanvasModal'
  const fullLink = isHrcl ? `https://hrcl.ru/${link.public_id}` : `${baseUrl}${link.public_id}`

  const handleConfirmDelete = () => {
    setConfirmModal(true)
  }

  const handleDeleteItem = () => {
    deleteLinkMutation.mutate(link.public_id)
    deleteLinkMutation.isSuccess && setConfirmModal(false)
  }

  if (!link) return null

  return (
    <>
      <div className={styles.linkCard}>
        <div className={styles.qrcodeWrapper} onClick={() => setIsOpenQRCodeModal(true)}>
          <IconQRCode className={styles.qrcodeIcon} />
          {/*<QRCodeLink baseUrl={baseUrl} link={link} size={70} />*/}
        </div>

        <div>
          <CopyToClipboard>{fullLink}</CopyToClipboard>
          <p className="text-muted">{link.url}</p>
          <p className={`${styles.date} text-muted mb-0`}>{unixToMoment(link.created).format('DD MMMM YYYY, HH:mm')}</p>
          {/*<p className={styles.description}>Описание ссылки</p>*/}
        </div>

        <div className={styles.historyCountWrapper}>
          <span className={styles.historyCount}>{link.history_count}</span>
        </div>

        <div className={styles.userWrap}>
          <p className={styles.publicId}>
            <Link to={`${AppRoute.USER_PROFILE}/${link.user_public_id}`}>{link.user_public_id}</Link>
          </p>
          <p className={`${styles.authorLabel} text-muted`}>Пользователь</p>
        </div>

        <div className={styles.buttonWrapper}>
          <OverlayTrigger
            placement="left"
            delay={{ show: 150, hide: 200 }}
            overlay={(evt) => renderBtnTooltip(evt, 'удалить')}
          >
            <Button onClick={handleConfirmDelete} variant="outline-danger" size="sm">
              <i className="bi bi-trash" />
              <span className="visually-hidden">Удалить ссылку</span>
            </Button>
          </OverlayTrigger>
        </div>
      </div>

      <Modal show={isOpenQRCodeModal} onHide={setIsOpenQRCodeModal}>
        <Modal.Header closeButton />
        <Modal.Body>
          <QRCodeLink
            linkId={link.public_id}
            fullLink={`${baseUrl}${link.public_id}`}
            idSvg={idSvgModal}
            idCanvas={idCanvasModal}
            downloadButtons
          />
        </Modal.Body>
      </Modal>

      <ConfirmDeleteModal isShow={confirmModal} onClose={setConfirmModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
