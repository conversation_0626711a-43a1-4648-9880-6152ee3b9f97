import { useState } from 'react'
import { <PERSON><PERSON>, Card, Col, Floating<PERSON>abel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useCreateLink } from '@/features/links/api/createLink'
import { QRCodeLink } from '@/features/links/components/QRCodeLink'
import styles from '@/pages/ClubsScreen/components/CompetitionsForm/CompetitionsForm.module.scss'
import { checkSetValue } from '@/utils/forms'

export const CreateLink = () => {
  const createLinkMutation = useCreateLink()
  const [customLinkQRC, setCustomLinkQRC] = useState('')
  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm()

  const onSubmit = (data) => {
    createLinkMutation.mutate({ ...data })
    setCustomLinkQRC('')
  }

  const handleClickGenerateQRC = () => {
    setCustomLinkQRC(getValues('url'))
  }

  return (
    <>
      <Form onSubmit={handleSubmit(onSubmit)} className="mb-4">
        <Row>
          <Col xs={8} sm={9} md={{ offset: 3, span: 5 }}>
            <Form.Group>
              <FloatingLabel controlId={'titleInput'} label="Введите ссылку, которую нужно сократить">
                <FormControl
                  {...register('url', {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, {}, 'text'),
                  })}
                  type="text"
                  isInvalid={errors?.url}
                  placeholder="Введите ссылку, которую нужно сократить"
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
          <Col className="d-grid" xs={4} sm={3} md={1}>
            <Button type="submit">Сократить</Button>
          </Col>
        </Row>
        <Row>
          <Col xs={8} sm={9} md={{ offset: 3, span: 5 }}>
            <Button className={styles.generateQRC} onClick={handleClickGenerateQRC} variant="link">
              Получить QR-код
            </Button>
          </Col>
        </Row>
      </Form>

      {createLinkMutation.isSuccess && customLinkQRC.length === 0 && (
        <Row>
          <Col md={{ offset: 3, span: 6 }}>
            <Card className="mb-4 p-3">
              <Row>
                <Col>
                  <QRCodeLink
                    fullLink={createLinkMutation.data?.data?.link}
                    linkId={createLinkMutation.data?.data?.public_id}
                    size={200}
                    downloadButtons
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {customLinkQRC.length > 0 && (
        <Row>
          <Col md={{ offset: 3, span: 6 }}>
            <Card className="mb-4 p-3">
              <Row>
                <Col>
                  <QRCodeLink fullLink={customLinkQRC} size={200} downloadButtons isToggleUrl={false} />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}
    </>
  )
}
