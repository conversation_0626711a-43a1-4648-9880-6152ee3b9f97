import { QRCodeCanvas, QRCodeSVG } from 'qrcode.react'
import { Button, Col, Row } from 'react-bootstrap'
import { useSelector } from 'react-redux'

import { CopyToClipboard } from '@/components/CopyToClipboard/CopyToClipboard'

import { useTheme } from '@/contexts/ThemeContext'
import { HrclSwitcher } from '@/features/links/components/HrclSwitcher'
import { getIsHrcl } from '@/store/slices/links'

export const QRCodeLink = ({
  linkId,
  fullLink,
  size = 200,
  idSvg = 'qrcodeLinkSvg',
  idCanvas = 'qrcodeLinkCanvas',
  downloadButtons = false,
  isToggleUrl = true,
}) => {
  const isHrcl = useSelector(getIsHrcl)
  const { effectiveTheme, themes } = useTheme()

  const link = isHrcl ? `https://hrcl.ru/${linkId}` : fullLink

  // Адаптивные цвета для QR-кода в зависимости от темы
  const qrColors = {
    bgColor: effectiveTheme === themes.DARK ? '#ffffff' : '#ffffff', // Фон всегда белый для лучшей читаемости
    fgColor: effectiveTheme === themes.DARK ? '#000000' : '#000000', // Код всегда чёрный для лучшей читаемости
  }

  function downloadStringAsFile(data, filename) {
    const a = document.createElement('a')
    a.download = filename
    a.href = data
    a.click()
  }

  const handleDownloadQRCodePng = () => {
    const node = document.querySelector(`#${idCanvas}`)
    if (node == null) {
      return
    }

    const dataURI = node.toDataURL('image/png')

    downloadStringAsFile(dataURI, 'qrcode-canvas.png')
  }

  const handleDownloadQRCodeSvg = () => {
    const node = document.querySelector(`#${idSvg}`)

    if (node == null) {
      return
    }

    const serializer = new XMLSerializer()
    const fileURI =
      'data:image/svg+xml;charset=utf-8,' +
      encodeURIComponent('<?xml version="1.0" standalone="no"?>' + serializer.serializeToString(node))

    downloadStringAsFile(fileURI, 'qrcode-svg.svg')
  }

  return (
    <>
      <Row>
        <Col className="d-grid justify-content-center">
          <div
            style={{
              backgroundColor: qrColors.bgColor,
              padding: '8px',
              borderRadius: '8px',
              display: 'inline-block',
            }}
          >
            <QRCodeSVG
              value={link}
              size={size}
              bgColor={qrColors.bgColor}
              fgColor={qrColors.fgColor}
              level={'L'}
              includeMargin={false}
              id={idSvg}
            />
          </div>
        </Col>
        {downloadButtons && (
          <>
            {isToggleUrl && (
              <Col className="mt-3 d-grid justify-content-center" xs={12}>
                <HrclSwitcher id={linkId} />
              </Col>
            )}
            <Col className="mt-3" xs={12}>
              <center>
                <CopyToClipboard>{link}</CopyToClipboard>
              </center>
            </Col>
            <Col className="mt-5" md={12}>
              <Row className="justify-content-center">
                <Col xs="auto">
                  <Button onClick={handleDownloadQRCodePng}>
                    <i className="bi bi-download me-1" /> PNG
                  </Button>
                </Col>
                <Col xs="auto">
                  <Button onClick={handleDownloadQRCodeSvg}>
                    <i className="bi bi-download me-1" /> SVG
                  </Button>
                </Col>
              </Row>
            </Col>
          </>
        )}
      </Row>
      <QRCodeCanvas
        value={link}
        size={size}
        bgColor={qrColors.bgColor}
        fgColor={qrColors.fgColor}
        level={'L'}
        includeMargin={false}
        id={idCanvas}
        className="visually-hidden"
      />
    </>
  )
}
