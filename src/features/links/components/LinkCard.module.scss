@import '../../../assets/styles/variables';

.linkCard {
  position: relative;
  padding: 0.75rem 0;

  display: grid;
  grid-template-columns: 30px 1fr 50px;
  gap: 12px;

  &:not(:last-child) {
    border-bottom: 1px solid var(--bs-border-color);
  }
}

.qrcodeIcon {
  width: 30px;
  height: 30px;

  & path {
    fill: var(--bs-body-color);
  }
}

.qrcodeWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.linkOriginal {
  margin-bottom: 6px;

  max-width: 250px;

  font-size: 14px;
  color: var(--bs-secondary-color);
}

.description {
  margin-bottom: 0;

  font-size: 14px;
  color: var(--bs-secondary-color);
}

.userWrap {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 6px;
  grid-column: 2 / -1;
}

.publicId {
  margin-bottom: 0;

  order: 1;
}

.authorLabel {
  font-size: 14px;
  color: var(--bs-secondary-color);
}

.date {
  font-size: 12px;
  color: var(--bs-secondary-color);
}

.historyCountWrapper {
  display: grid;
  justify-content: center;
  align-items: start;
}

.historyCount {
  position: relative;
  color: var(--bs-body-color);

  &::before {
    position: absolute;
    top: 50%;
    right: 130%;
    transform: translateY(-50%);

    display: block;
    width: 16px;
    height: 16px;

    background-image: url('../../../assets/img/icons/icon-stats.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;

    content: '';
  }

  :global([data-bs-theme='dark']) & {
    &::before {
      filter: invert(1);
    }
  }
}

.buttonWrapper {
  position: absolute;
  bottom: 12px;
  right: 0;
}

@media (min-width: $grid-breakpoint-sm) {
  .linkCard {
    grid-template-columns: 30px 1fr 1fr;
  }
}

@media (min-width: $grid-breakpoint-md) {
  .linkCard {
    grid-template-columns: 50px 1fr 1fr 200px;
  }

  .qrcodeIcon {
    width: 50px;
    height: 50px;
  }

  .userWrap {
    display: block;
    grid-column: auto;
  }

  .buttonWrapper {
    top: 12px;
  }
}

@media (min-width: $grid-breakpoint-lg) {
  .qrcodeWrapper {
    cursor: pointer;

    transition: transform 0.25s;

    &:hover {
      transform: scale(1.1);
    }
  }

  .buttonWrapper {
    opacity: 0;

    transition: opacity 0.25s;
  }

  .linkCard:hover .buttonWrapper {
    opacity: 1;
  }
}
