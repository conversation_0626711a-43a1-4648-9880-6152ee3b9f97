import { useEffect, useState } from 'react'
import { Col, Row, Tab, Tabs } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'
import PageSearch from '@/components/PageSearch/PageSearch'

import { useLinks } from '@/features/links/api/getLinks'
import { CreateLink } from '@/features/links/components/CreateLink'
import { HrclSwitcher } from '@/features/links/components/HrclSwitcher'
import { LinksCard } from '@/features/links/components/LinksCard'
import storage from '@/utils/storage'

export const Links = () => {
  const [filteredLinks, setFilteredLinks] = useState([])
  const [filteredUserLinks, setFilteredUserLinks] = useState([])
  const [userLinks, setUserLinks] = useState([])
  const user = storage.getUserObj()

  const linksQuery = useLinks()

  useEffect(() => {
    if (linksQuery.data?.data?.values?.length > 0) {
      setUserLinks(linksQuery.data.data.values.filter((item) => item.user_public_id === user.public_id))
    }
  }, [linksQuery.data?.data?.values, user.public_id])

  useEffect(() => {
    if (linksQuery.data?.data?.values) {
      setFilteredLinks(linksQuery.data.data.values)
    }
  }, [linksQuery.data?.data?.values])

  useEffect(() => {
    if (userLinks) {
      setFilteredUserLinks(userLinks)
    }
  }, [userLinks])

  return (
    <Layout title="Ссылки">
      <CreateLink />

      <Tabs defaultActiveKey="all" className="mb-3">
        <Tab eventKey="all" title="Все">
          <Row className="mb-3">
            <Col>
              <PageSearch values={linksQuery.data?.data?.values} setValues={setFilteredLinks} />
            </Col>
          </Row>

          <Row className="mb-3">
            <Col>
              <HrclSwitcher />
            </Col>
          </Row>

          <LinksCard links={filteredLinks} baseUrl={linksQuery.data?.data?.base_url} />
        </Tab>

        <Tab eventKey="userLinks" title="Мои">
          <Row className="mb-3">
            <Col>
              <PageSearch values={userLinks} setValues={setFilteredUserLinks} />
            </Col>
          </Row>

          <LinksCard links={filteredUserLinks} baseUrl={linksQuery.data?.data?.base_url} />
        </Tab>
      </Tabs>
    </Layout>
  )
}
