.inputWrap {
  max-width: 650px;
}

.wrapItem {
  display: flex;
  gap: 10px;
}

.input {
  border-radius: 4px;
  background: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  color: var(--bs-body-color);
  font-size: 16px;
  line-height: 14px;
  margin: 0;
  outline: unset;
  padding: 20px 20px;
  width: 100%;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

.input:hover {
  border-color: var(--bs-border-color-translucent);
}

.input:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.button {
  background-color: var(--bs-secondary-bg);
  box-shadow: none;
  color: var(--bs-secondary-color);
  cursor: default;
  border-radius: 4px;
  outline: unset;
  border: 1px solid var(--bs-border-color);
  padding-left: 20px;
  padding-right: 20px;
  transition: opacity 0.15s ease-in-out;
}

.button:hover {
  opacity: 0.5;
  pointer-events: none;
}

.disable {
  background-color: var(--bs-gray-200);
  opacity: 0.6;
}

.linksWrapper {
  margin-top: 20px;
  background-color: var(--bs-card-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
  padding: 20px;
  animation: show-card 0.3s ease-in forwards;
  transition: 0.2s;
  box-shadow: var(--bs-box-shadow-sm);
}

@keyframes show-card {
  from {
    margin-left: 100%;
    width: 300%;
  }

  to {
    margin-left: 0%;
    width: 100%;
  }
}

.link {
  text-decoration: none;
  color: var(--bs-link-color);
}

.link:hover {
  color: var(--bs-link-hover-color);
  text-decoration: underline;
}

.icon {
  color: var(--bs-body-color);
}

.copyButton {
  margin-top: 20px;
  background-color: transparent;
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
  padding: 10px;
  color: var(--bs-body-color);
  transition:
    border-color 0.15s ease-in-out,
    color 0.15s ease-in-out;
}

.copyButton:hover {
  border: 1px solid var(--bs-primary);
  color: var(--bs-primary);
}
