import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateSectionsList = (requestData) => {
  return axios.put(APIRoute.GET_SECTIONS_LIST, requestData)
}

export const useUpdateSectionsList = () => {
  return useMutation({
    mutationFn: updateSectionsList,
    onSuccess: () => {
      queryClient.invalidateQueries(['sectionLists'])
    },
  })
}
