import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateMyDocument = (requestData) => {
  return axios.put(APIRoute.ACTIONS_FILE, requestData)
}

export const useUpdateMyDocument = () => {
  return useMutation({
    mutationFn: updateMyDocument,
    onSuccess: () => {
      queryClient.invalidateQueries(['userDocuments'])
    },
  })
}
