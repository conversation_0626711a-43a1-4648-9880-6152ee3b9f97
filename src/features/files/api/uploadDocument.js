import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const uploadDocument = (data) => {
  return axios.post(APIRoute.FILES_DOCUMENT, data)
}

export const useUploadDocument = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries('siteDocuments')
      openToast.success({ message: 'Документ обновлён' })
    },
    mutationFn: uploadDocument,
  })
}
