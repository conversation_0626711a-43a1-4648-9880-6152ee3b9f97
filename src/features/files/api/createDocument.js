import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createDocument = (data) => {
  return axios.post(APIRoute.ACTIONS_FILE, data)
}

export const useCreateDocument = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: createDocument,
    onSuccess: () => {
      queryClient.invalidateQueries(['userDocuments'])
      openToast.success({ message: 'Файл добавлен' })
    },
  })
}
