import { useMutation, useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const getUserDocuments = () => {
  return axios.get(APIRoute.FILES_USER)
}

export const useGetUserDocuments = (enabled = true) => {
  return useQuery({
    enabled,
    queryKey: ['userDocuments'],
    queryFn: getUserDocuments,
    staleTime: 15 * 60 * 1000, // 15 минут
    cacheTime: 15 * 60 * 1000, // 15 минут
  })
}

export const useGetUserDocumentsMutation = () => {
  return useMutation({
    mutationFn: getUserDocuments,
    onSuccess: (data) => {
      queryClient.setQueryData(['userDocuments'], data)
    },
  })
}
