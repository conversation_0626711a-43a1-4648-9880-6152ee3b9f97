import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteSectionsList = (public_id) => {
  return axios.delete(`${APIRoute.GET_SECTIONS_LIST}/${public_id}`)
}

export const useDeleteSectionsList = () => {
  return useMutation({
    mutationFn: deleteSectionsList,
    onSuccess: () => {
      queryClient.invalidateQueries(['sectionLists'])
    },
  })
}
