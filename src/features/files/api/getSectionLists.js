import { useMutation, useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const getSectionLists = () => {
  return axios.get(APIRoute.GET_SECTIONS_LIST)
}

export const useGetSectionLists = () => {
  return useQuery({
    queryKey: ['sectionLists'],
    queryFn: getSectionLists,
    staleTime: 15 * 60 * 1000, // 15 минут
    cacheTime: 15 * 60 * 1000, // 15 минут
  })
}

export const useGetSectionListsMutation = () => {
  return useMutation({
    mutationFn: getSectionLists,
    onSuccess: (data) => {
      queryClient.setQueryData(['sectionLists'], data)
    },
  })
}
