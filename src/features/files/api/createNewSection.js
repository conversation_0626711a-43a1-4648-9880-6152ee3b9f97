import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createNewSection = (requestData) => {
  return axios.post(APIRoute.GET_SECTIONS_LIST, requestData)
}

export const useCreateNewSection = () => {
  return useMutation({
    mutationFn: createNewSection,
    onSuccess: () => {
      queryClient.invalidateQueries(['sectionLists'])
    },
  })
}
