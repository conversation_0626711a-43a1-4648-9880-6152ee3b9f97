import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getAllDocuments = (skip, limit) => {
  return axios.get(`${APIRoute.FILES}/${skip}/${limit}`)
}

export const useGetAllDocuments = (skip, limit, isEnabled = true) => {
  return useQuery({
    queryKey: ['allDocuments', skip, limit],
    queryFn: () => getAllDocuments(skip, limit),
    enabled: isEnabled,
  })
}
