import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const deleteDocument = (public_id) => {
  return axios.delete(`${APIRoute.ACTIONS_FILE}/${public_id}`)
}

export const useDeleteDocument = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: deleteDocument,
    onSuccess: () => {
      queryClient.invalidateQueries(['userDocuments'])
      openToast.success({ message: 'Документ успешно удален' })
    },
  })
}
