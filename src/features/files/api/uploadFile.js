import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

/**
 * POST /api/files/upload
 * body: { file: base64string }
 * returns: { path, filename, extension }
 */
export const uploadFile = (data) => {
  return axios.post(APIRoute.FILES_UPLOAD, data)
}
export const useUploadFile = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: uploadFile,
    onError: (error) => {
      const message = error?.response?.data?.message || 'Ошибка загрузки файла'
      openToast.error({ message })
    },
  })
}
