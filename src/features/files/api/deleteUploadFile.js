import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

// DELETE /api/files/upload
// body: { file: string } - путь к файлу, который нужно удалить
export const deleteUploadFile = ({ file }) => {
  // axios позволяет передать тело в DELETE через опцию { data }
  return axios.delete(APIRoute.FILES_UPLOAD, { data: { file } })
}

export const useDeleteUploadFile = () => {
  const openToast = useToast()
  return useMutation({
    mutationFn: deleteUploadFile,
    onError: (error) => {
      const message = error?.response?.data?.message || 'Ошибка удаления файла'
      openToast.error({ message })
    },
  })
}
