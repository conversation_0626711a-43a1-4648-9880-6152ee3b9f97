import { useEffect, useMemo } from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Spinner, Ta<PERSON>, Tab } from 'react-bootstrap'
import { Link, useParams, useNavigate, useLocation } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { AppRoute } from '@/const'
import { useGetEventCity } from '@/features/events/api/getEventCity'
import { useGetEventCityFormats } from '@/features/events/api/getEventCityFormats'
import { CityFormatCard } from '@/features/events/components/CityFormatCard/CityFormatCard'
import { NumbersManagement } from '@/features/events/components/NumbersManagement/NumbersManagement'
import { unixToMoment } from '@/utils/date'

export const CityFormatsPage = () => {
  const { eventPublicId, cityPublicId } = useParams()
  const navigate = useNavigate()
  const location = useLocation()

  // Получение активного таба из URL или установка значения по умолчанию
  const searchParams = useMemo(() => new URLSearchParams(location.search), [location.search])
  const activeTab = searchParams.get('tab') || 'formats'

  const getEventCityQuery = useGetEventCity(cityPublicId)
  const getEventCityFormatsQuery = useGetEventCityFormats(cityPublicId)
  const eventCity = getEventCityQuery?.data?.data
  const formats = getEventCityFormatsQuery?.data?.data
  const formatsCount = formats?.length || 0

  // Обновление URL при изменении таба
  const handleTabChange = (tab) => {
    const newParams = new URLSearchParams(location.search)
    newParams.set('tab', tab)
    navigate(`${location.pathname}?${newParams.toString()}`)
  }

  // Синхронизация URL и активного таба при первой загрузке страницы
  useEffect(() => {
    if (!searchParams.has('tab')) {
      const newParams = new URLSearchParams(location.search)
      newParams.set('tab', 'formats')
      navigate(`${location.pathname}?${newParams.toString()}`, { replace: true })
    }
  }, [location.pathname, location.search, navigate, searchParams])

  return (
    <Layout>
      <Row className="mb-3">
        {eventPublicId && (
          <Col xs="auto">
            <Button as={Link} to={`${AppRoute.EVENTS}/${eventPublicId}`} variant="outline-secondary">
              <i className="bi bi-arrow-left me-2" />
              Вернуться к событию
            </Button>
          </Col>
        )}
      </Row>
      <Row className="mb-4 align-items-center">
        <Col>
          <h3 className="mb-0">
            {eventCity?.address}{' '}
            <span className="fw-normal fs-4">
              ({unixToMoment(eventCity?.start_time).format('DD.MM.YYYY: HH:mm')}), {eventCity?.event_public_id}
            </span>
          </h3>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onSelect={handleTabChange} className="mb-4">
        <Tab eventKey="formats" title={`Форматы (${formatsCount})`}>
          {formatsCount > 0 ? (
            formats.map((item) => (
              <div className="mb-4" key={item.public_id}>
                <CityFormatCard item={item} />
              </div>
            ))
          ) : getEventCityFormatsQuery.isLoading ? (
            <div className="text-center py-5 text-muted">
              <Spinner animation="border" variant="primary" />
            </div>
          ) : (
            <div className="text-center py-5 text-muted">
              <i className="bi bi-calendar-x fs-1 d-block mb-3" />
              <h5>Форматы не найдены</h5>
              <p>Для данного города не создано ни одного формата</p>
            </div>
          )}
        </Tab>
        <Tab eventKey="numbers" title="Управление номерами">
          <NumbersManagement cityPublicId={cityPublicId} />
        </Tab>
      </Tabs>
    </Layout>
  )
}
