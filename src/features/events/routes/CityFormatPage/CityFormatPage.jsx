import _ from 'lodash'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Card } from 'react-bootstrap'
import { useParams, <PERSON> } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { AppRoute } from '@/const'
import { useGetEventFormat } from '@/features/events/api/getEventFormat'
import { CityFormatCard } from '@/features/events/components/CityFormatCard/CityFormatCard'
import { unixToMoment } from '@/utils/date'

export const CityFormatPage = () => {
  const { publicId } = useParams()

  const formatQuery = useGetEventFormat(publicId)
  const formats = formatQuery.data?.data

  const getFormattedDate = (date) => {
    return unixToMoment(date).format('DD.MM.YYYY')
  }

  return (
    <Layout>
      {!_.isEmpty(formats) ? (
        <>
          <Row className="mb-4">
            <Col>
              {formats[0].event && (
                <div>
                  <Link to={`${AppRoute.EVENTS}/${formats[0].event.public_id}`} className="link-dark">
                    <h3 className="mb-0 fw-bold">{formats[0].event.title}</h3>
                  </Link>
                </div>
              )}
              {formats[0].city && formats[0].event_city && (
                <div className="mt-2">
                  <Link
                    to={`/city-formats/${formats[0].event.public_id}/${formats[0].event_city.public_id}`}
                    className="link-dark"
                  >
                    <h5 className="mb-0 fw-normal text-muted">
                      {formats[0].city.name_ru}, {getFormattedDate(formats[0].start_time)}
                    </h5>
                  </Link>
                </div>
              )}
            </Col>
          </Row>

          {formats.map((item) => (
            <CityFormatCard item={item} key={item.public_id} />
          ))}
        </>
      ) : formatQuery.isLoading ? (
        <div className="text-center py-5 text-muted">
          <Spinner animation="border" variant="primary" />
        </div>
      ) : (
        <div className="text-center py-5 text-muted">
          <i className="bi bi-calendar-x fs-1 d-block mb-3"></i>
          <h5>Формат не найден</h5>
          <p>Для данного города с таким ID нет формата</p>
        </div>
      )}
    </Layout>
  )
}
