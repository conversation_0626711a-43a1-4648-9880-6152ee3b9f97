import { Button, Col, Form, FormControl, Row, ButtonGroup } from 'react-bootstrap'
import { useFieldArray, Controller } from 'react-hook-form'

export const AgesField = ({ control, register, errors, defaultValues }) => {
  const {
    fields: agesValues,
    append: appendAge,
    remove: removeAge,
  } = useFieldArray({
    control,
    name: 'ages.values',
  })

  const handleAppendAge = () => {
    appendAge({ min: '', max: '' })
  }

  return (
    <>
      <h5 className={agesValues.length > 0 ? 'mb-0' : 'mb-4'}>
        Возрастные категории{' '}
        <Button onClick={handleAppendAge} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>

      {agesValues.length > 0 && (
        <>
          <p className="mb-1">Метод расчета возраста</p>
          <Row className="mb-4">
            <Col md="auto">
              <Controller
                control={control}
                name="ages.method"
                defaultValue={defaultValues?.method}
                render={({ field: { onChange, value } }) => (
                  <ButtonGroup>
                    <Button
                      onClick={() => onChange('day')}
                      variant={value === 'day' ? 'primary' : 'light'}
                      type="button"
                      size="sm"
                    >
                      День старта
                    </Button>
                    <Button
                      onClick={() => onChange('year')}
                      variant={value === 'year' ? 'primary' : 'light'}
                      type="button"
                      size="sm"
                    >
                      Конец года
                    </Button>
                  </ButtonGroup>
                )}
              />
            </Col>
          </Row>
        </>
      )}

      <Row className="g-4">
        {agesValues.map((item, index) => (
          <Col md="2" key={item.id}>
            <Row className="g-1 align-items-center">
              <Col>
                <Form.Group>
                  <FormControl
                    className="text-center"
                    {...register(`ages.values.${index}.min`, { valueAsNumber: true, required: true })}
                    type="number"
                    min="0"
                    isInvalid={errors?.ages?.values?.[index]?.min}
                    placeholder="Мин."
                  />
                </Form.Group>
              </Col>
              <Col md="auto">-</Col>
              <Col>
                <Form.Group>
                  <FormControl
                    className="text-center"
                    {...register(`ages.values.${index}.max`, { valueAsNumber: true, required: true })}
                    type="number"
                    min="0"
                    isInvalid={errors?.ages?.values?.[index]?.max}
                    placeholder="Макс."
                  />
                </Form.Group>
              </Col>
              <Col md="auto">
                <Button onClick={() => removeAge(index)} variant="outline-danger" type="button" size="sm">
                  <i className="bi bi-trash3-fill" />
                </Button>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>
    </>
  )
}
