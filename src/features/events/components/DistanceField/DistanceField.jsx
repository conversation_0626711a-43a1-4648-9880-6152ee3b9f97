import React from 'react'
import { Button, Col, Form, FormControl, Row } from 'react-bootstrap'
import { useFieldArray } from 'react-hook-form'

import { useGetSportTypeList } from '@/features/sport/api/getSportTypeList'

export const DistanceField = ({ control, register, errors }) => {
  const { data: sportTypesResponse } = useGetSportTypeList()
  const sportTypes = sportTypesResponse?.data?.values || []

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'distances',
  })

  const handleAppendDistanceField = () => {
    append({
      sport: {
        public_id: '',
      },
      distance: '',
    })
  }

  return (
    <div className="mb-3">
      <div className="d-flex align-items-center mb-2">
        <h5 className="mb-0">Дистанции</h5>
        <Button variant="link" size="sm" onClick={handleAppendDistanceField}>
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </div>

      {fields?.map((field, index) => (
        <Row key={field.id} className="mb-2 align-items-end">
          <Col xs={5}>
            <Form.Group>
              <Form.Label>Вид спорта</Form.Label>
              <Form.Select
                {...register(`distances.${index}.sport.public_id`, {
                  required: true,
                })}
                isInvalid={!!errors?.distances?.[index]?.sport?.public_id}
              >
                <option value="">Выберите вид спорта</option>
                {sportTypes.map((sport) => (
                  <option key={sport.public_id} value={sport.public_id}>
                    {sport.name}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col xs={5}>
            <Form.Group>
              <Form.Label>Дистанция (м)</Form.Label>
              <FormControl
                {...register(`distances.${index}.distance`, {
                  setValueAs: (v) => (v ? parseInt(v, 10) : ''),
                  required: true,
                })}
                type="number"
                placeholder="Дистанция в метрах"
                isInvalid={!!errors?.distances?.[index]?.distance}
              />
            </Form.Group>
          </Col>
          <Col xs={2} className="d-flex align-items-end">
            <Button variant="outline-danger" size="sm" onClick={() => remove(index)} className="mb-1">
              <i className="bi bi-trash3-fill" />
            </Button>
          </Col>
        </Row>
      ))}
    </div>
  )
}
