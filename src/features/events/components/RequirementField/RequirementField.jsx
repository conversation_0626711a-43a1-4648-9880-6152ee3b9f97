import { Button, Col, Form, FormControl, Row } from 'react-bootstrap'
import { useFieldArray } from 'react-hook-form'

export const RequirementField = ({ control, register, errors }) => {
  const {
    fields: requirementFields,
    append: appendRequirement,
    remove: removeRequirement,
  } = useFieldArray({
    control,
    name: 'requirements',
  })

  const handleAppendRequirement = () => {
    appendRequirement('')
  }

  return (
    <>
      <h5>
        Требования{' '}
        <Button onClick={handleAppendRequirement} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      <Row className="g-3">
        {requirementFields.map((item, index) => (
          <Col md="4" key={item.id}>
            <Row className="g-1 align-items-center">
              <Col>
                <Form.Group>
                  <FormControl
                    {...register(`requirements.${index}`)}
                    type="text"
                    isInvalid={errors?.requirements?.[index]}
                    placeholder=""
                  />
                </Form.Group>
              </Col>

              <Col md="auto">
                <Button onClick={() => removeRequirement(index)} variant="outline-danger" type="button" size="sm">
                  <i className="bi bi-trash3-fill" />
                </Button>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>
    </>
  )
}
