import { useState } from 'react'
import { Button, Modal, Table } from 'react-bootstrap'

import { useToast } from '@/hooks/useToast'

import { useCompleteTeams } from '../../api/completeTeams'

export function CompleteTeamsButton({ formatPublicId }) {
  const [showModal, setShowModal] = useState(false)
  const [teamsData, setTeamsData] = useState([])

  const openToast = useToast()

  const { mutate: completeTeams, isLoading } = useCompleteTeams({
    onSuccess: (data) => {
      setTeamsData(data.values || [])
      setShowModal(true)
      openToast.success({ message: 'Команды успешно обновлены' })
    },
    onError: () => {
      openToast.error({ message: 'Ошибка при обновлении команд' })
    },
  })

  const handleCompleteTeams = () => {
    if (formatPublicId) {
      completeTeams(formatPublicId)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ru-RU')
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  return (
    <>
      <Button
        variant="info"
        type="button"
        size="sm"
        onClick={handleCompleteTeams}
        disabled={isLoading || !formatPublicId}
      >
        {isLoading ? 'Обновляем...' : 'Обновление команд'}
      </Button>

      <Modal show={showModal} onHide={handleCloseModal} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>Результаты обновления команд</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {teamsData.length > 0 ? (
            <Table striped bordered hover responsive>
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Название</th>
                  <th>Номер</th>
                  <th>Компания</th>
                  <th>Дата создания</th>
                </tr>
              </thead>
              <tbody>
                {teamsData.map((team) => (
                  <tr key={team.public_id}>
                    <td>{team.public_id}</td>
                    <td>{team.name}</td>
                    <td>{team.number}</td>
                    <td>{team.company?.public_id || '-'}</td>
                    <td>{formatDate(team.created_date)}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <p>Нет данных для отображения</p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleCloseModal}>Закрыть</Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}
