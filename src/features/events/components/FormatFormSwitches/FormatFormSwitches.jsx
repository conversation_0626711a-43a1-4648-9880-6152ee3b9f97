import { Col, <PERSON><PERSON>he<PERSON>, Row } from 'react-bootstrap'
import { Controller } from 'react-hook-form'

export const FormatFormSwitches = ({ register, city, defaultFormData, watch, setValue, control }) => {
  const preregistrationValue = watch('preregistration')

  const handlePreregistrationChange = (onChange, value) => {
    onChange(value)
    // Если предрегистрация выключается, отключаем и выбор клуба
    if (!value) {
      setValue('clubs', false)
    }
  }

  return (
    <div className="mb-3">
      <Row className="g-3 mb-3">
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('enabled')} type="switch" id="enabledSwitch" label="Активный" />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('hidden')} type="switch" id="hiddenSwitch" label="Скрытый" />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('registration')} type="switch" id="registrationSwitch" label="Регистрация" />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('_showRegions')} type="switch" id="showRegionsSwitch" label="Отображать регионы" />
        </Col>
      </Row>

      <Row className="g-3 mb-3">
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck
            {...register('shirt_unavailable')}
            type="switch"
            id="shirtUnavailableSwitch"
            label="Футболки недоступны"
          />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('license_file')} type="switch" id="licenseFileSwitch" label="Документ лицензии" />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck
            {...register('qr')}
            type="switch"
            id="qrFormatSwitch"
            label="QR-код"
            disabled={!(city.qr || defaultFormData?.qr)}
          />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('obrnadzor')} type="switch" id="obrnadzorFormatSwitch" label="Выбор универов" />
        </Col>
      </Row>

      {/* Группа связанных настроек */}
      <Row className="g-3 mb-3">
        <Col xs={12}>
          <div className="border border-primary rounded-3 p-3 bg-primary bg-opacity-10">
            <div className="d-flex align-items-center mb-3">
              <span className="badge bg-primary small">связанные параметры</span>
            </div>

            <Row className="g-3">
              <Col xs={12} sm={6}>
                <div className="d-flex align-items-center">
                  <span className="badge bg-success me-2">1</span>
                  <Controller
                    control={control}
                    name="preregistration"
                    render={({ field: { onChange, value, ref } }) => (
                      <FormCheck
                        type="switch"
                        id="preregistrationSwitch"
                        label="Предрегистрация"
                        checked={value}
                        onChange={(e) => handlePreregistrationChange(onChange, e.target.checked)}
                        ref={ref}
                      />
                    )}
                  />
                </div>
                <small className="text-muted ms-4">Основная настройка</small>
              </Col>

              <Col xs={12} sm={6}>
                <div className="d-flex align-items-center">
                  <span className="badge bg-secondary me-2">2</span>
                  <Controller
                    control={control}
                    name="clubs"
                    render={({ field: { onChange, value, ref } }) => (
                      <FormCheck
                        type="switch"
                        id="clubsSwitch"
                        label="Отображать выбор клуба"
                        checked={value}
                        onChange={(e) => onChange(e.target.checked)}
                        disabled={!preregistrationValue}
                        ref={ref}
                      />
                    )}
                  />
                </div>
                <small className={`ms-4 ${preregistrationValue ? 'text-muted' : 'text-warning'}`}>
                  {preregistrationValue ? '✓ Доступно при предрегистрации' : '⚠️ Требует включения предрегистрации'}
                </small>
              </Col>
            </Row>
          </div>
        </Col>
      </Row>
    </div>
  )
}
