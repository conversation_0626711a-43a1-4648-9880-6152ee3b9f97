import { Button, Col, Form, FormControl, Row } from 'react-bootstrap'
import { useFieldArray } from 'react-hook-form'

export const RegistrationScheduleField = ({ control, register, errors }) => {
  const {
    fields: registrationScheduleFields,
    append: appendRegistrationSchedule,
    remove: removeRegistrationSchedule,
  } = useFieldArray({
    control,
    name: 'info.registration_schedule',
  })

  const handleAppendRegistrationSchedule = () => {
    appendRegistrationSchedule('')
  }

  return (
    <div>
      <h5>
        Расписание регистрации{' '}
        <Button onClick={handleAppendRegistrationSchedule} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      <Row className="g-3">
        {registrationScheduleFields.map((item, index) => (
          <Col md="4" key={item.id}>
            <Row className="g-1 align-items-center">
              <Col>
                <Form.Group>
                  <FormControl
                    {...register(`info.registration_schedule.${index}`)}
                    type="text"
                    isInvalid={errors?.info?.registration_schedule?.[index]}
                    placeholder=""
                  />
                </Form.Group>
              </Col>

              <Col md="auto">
                <Button
                  onClick={() => removeRegistrationSchedule(index)}
                  variant="outline-danger"
                  type="button"
                  size="sm"
                >
                  <i className="bi bi-trash3-fill" />
                </Button>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>
    </div>
  )
}
