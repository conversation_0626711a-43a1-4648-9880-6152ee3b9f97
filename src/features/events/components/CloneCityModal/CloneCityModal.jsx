import moment from 'moment-timezone'
import { useState, useRef } from 'react'
import { Modal, Form, Button, FloatingLabel, FormControl } from 'react-bootstrap'
import { useForm, Controller } from 'react-hook-form'
import Select from 'react-select'

import { useGetCountryRegions } from '@/features/cities/api/getCountryRegions'
import { useGetRegionCities } from '@/features/cities/api/getRegionCities'
import { useCloneEventCity } from '@/features/events/api/cloneEventCity'
import { eventCitySelectStyles } from '@/features/events/components/EventCityForms/eventCitySelect'
import { generateRegionOptions, getOptionsSelect, setValueAsText } from '@/utils/common'

const CloneCityForm = ({ eventPublicId, cityPublicId, onSuccess, onHide }) => {
  const [region, setRegion] = useState(null)
  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm()
  const getCountryRegionsQuery = useGetCountryRegions('RU')
  const getRegionCitiesQuery = useGetRegionCities(region)
  const cloneEventCityMutate = useCloneEventCity(eventPublicId, onHide, onSuccess)
  const [currentTimezone, setCurrentTimezone] = useState(moment.tz.guess())
  const startTimeRef = useRef(null)

  const countryRegions = getCountryRegionsQuery?.data?.data?.values
  const regionCities = getRegionCitiesQuery?.data?.data?.values

  const handleChangeRegion = (option) => {
    setRegion(option.value)
    setCurrentTimezone(option.timezone)

    if (startTimeRef.current.value !== '') {
      setValue('start_time', moment.tz(startTimeRef.current.value, option?.timezone).utc().format())
    }
  }

  const handleChangeCity = (option) => {
    setValue('city.id', option.value)
  }

  const handleChangeDate = (evt) => {
    const name = evt.target.name
    const value = evt.target.value
    const formatValue = moment.tz(value, currentTimezone).utc().format()

    setValue(name, formatValue)
  }

  const onSubmit = (data) => {
    const newData = {
      ...data,
      event_city: {
        public_id: cityPublicId,
      },
    }

    cloneEventCityMutate.mutate({
      id: cityPublicId,
      data: newData,
    })
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <Form.Group className="mb-3">
        <FloatingLabel controlId="startTimeLabel" label="Старт *">
          <FormControl
            onChange={handleChangeDate}
            type="datetime-local"
            name="start_time"
            isInvalid={errors?.start_time}
            ref={startTimeRef}
            required
            placeholder=""
          />
        </FloatingLabel>
      </Form.Group>

      <Form.Group className="mb-3">
        {countryRegions?.length > 0 && (
          <Select
            onChange={handleChangeRegion}
            styles={eventCitySelectStyles}
            options={generateRegionOptions(countryRegions)}
            placeholder="Регион"
            required
          />
        )}
      </Form.Group>

      {regionCities?.length > 0 ? (
        <Controller
          name="city.id"
          control={control}
          render={() => (
            <Select
              className="mb-3"
              onChange={handleChangeCity}
              styles={eventCitySelectStyles}
              options={getOptionsSelect(regionCities, 'id', 'name_ru')}
              placeholder="Город"
              required
            />
          )}
        />
      ) : (
        <Select className="mb-3" styles={eventCitySelectStyles} placeholder="Город" isDisabled={true} />
      )}

      <Form.Group className="mb-3">
        <FloatingLabel controlId="addressLabel" label="Адрес">
          <FormControl
            {...register('address', {
              setValueAs: (v) => setValueAsText(v, {}),
            })}
            type="text"
            isInvalid={errors?.address}
            placeholder=""
            required
          />
        </FloatingLabel>
      </Form.Group>

      <Button variant="primary" type="submit">
        Сохранить
      </Button>
    </Form>
  )
}

export const CloneCityModal = ({ show, onHide, eventPublicId, cityPublicId, onSuccess }) => {
  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Копировать город</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <CloneCityForm
          eventPublicId={eventPublicId}
          cityPublicId={cityPublicId}
          onSuccess={onSuccess}
          onHide={onHide}
        />
      </Modal.Body>
    </Modal>
  )
}
