.formatItem {
  padding: 6px 10px;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  border: 1px solid var(--bs-border-color);
  transition: background-color 0.1s ease;
  user-select: none;
}

.formatItemSelected {
  background-color: var(--bs-secondary-bg);
}

.ticketNumber {
  padding: 2px 6px;
  border-radius: 4px;
  margin: 0 4px 4px 0;
  display: inline-block;
}

.ticketNumberRegular {
  background-color: var(--bs-secondary-bg);
}

.ticketNumberDuplicate {
  background-color: var(--bs-danger-bg-subtle);
  color: var(--bs-danger-text-emphasis);
}

.ticketNumberMismatch {
  background-color: var(--bs-warning-bg-subtle);
  color: var(--bs-warning-text-emphasis);
}

.legendItem {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
}

.legendDuplicate {
  background-color: var(--bs-danger-bg-subtle);
}

.legendMismatch {
  background-color: var(--bs-warning-bg-subtle);
}

.resultsWrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.resultsHeader {
  flex-shrink: 0;
}

.resultsContent {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 5px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--bs-secondary-bg);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--bs-border-color);
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--bs-secondary-color);
  }
}

.ticketWrapper {
  position: relative;
  min-height: 200px;
}

.ticketLoader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--bs-body-bg-rgb), 0.7);
  z-index: 5;
  border-radius: 8px;
}
