import { useState, useEffect, useMemo, useRef, useCallback } from 'react'
import {
  Form,
  Button,
  Card,
  Row,
  Col,
  FormControl,
  InputGroup,
  Badge,
  ListGroup,
  Spinner,
  Alert,
  ButtonGroup,
} from 'react-bootstrap'

import { useFindDuplicateNumbers } from '@/features/events/api/findDuplicateNumbers'
import { useFindMismatchNumbers } from '@/features/events/api/findMismatchNumbers'
import { useGetEventCityFormats } from '@/features/events/api/getEventCityFormats'
import { UpdateOrdersTicketsContext } from '@/features/orders'
import { useGetTicket } from '@/features/tickets/api/getTicket'
import { Ticket } from '@/features/tickets/components/Ticket'
import { getRightNames } from '@/utils/common'

import styles from './NumbersManagement.module.scss'

/**
 * Компонент управления номерами билетов для форматов мероприятия.
 *
 * Необходим централизованный интерфейс для поиска дубликатов номеров и номеров вне диапазона.
 * Позволяет администраторам быстро находить проблемные билеты и исправлять их.
 * Использует drag-and-select для множественного выбора форматов и комбинирует результаты
 * из разных типов проверок при фильтре "Все".
 *
 * @param {string} cityPublicId - Идентификатор города
 */
export const NumbersManagement = ({ cityPublicId }) => {
  const [selectedFormats, setSelectedFormats] = useState([])
  const [searchValue, setSearchValue] = useState('')
  const [activeFilter, setActiveFilter] = useState('all')
  const [selectedTicket, setSelectedTicket] = useState(null)
  const [results, setResults] = useState([])
  const [duplicatesResults, setDuplicatesResults] = useState([])
  const [mismatchResults, setMismatchResults] = useState([])
  const [ticket, setTicket] = useState(null) // нужно для отображения пока не загрузится новый билет
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  const [isMouseDown, setIsMouseDown] = useState(false)
  const [lastSelectedFormat, setLastSelectedFormat] = useState(null)
  const formatsContainerRef = useRef(null)

  const getEventCityFormatsQuery = useGetEventCityFormats(cityPublicId)
  const formats = getEventCityFormatsQuery?.data?.data || []

  const findDuplicatesNumbersMutation = useFindDuplicateNumbers()
  const findMismatchNumbersMutation = useFindMismatchNumbers()
  const getTicketQuery = useGetTicket(selectedTicket?.public_id)

  /**
   * Объединяет результаты поиска дубликатов и номеров вне диапазона.
   *
   * Требуется для отображения всех проблемных номеров в режиме "Все".
   * Создает новый массив с уникальными билетами, сохраняя информацию о типе проблемы.
   *
   * @returns {Array} Массив объединенных результатов с уникальными билетами
   */
  const mergeResultsForAllFilter = useCallback(() => {
    // Создается карта форматов для удобства объединения данных
    const mergedFormatsMap = new Map()

    // Сначала добавляются все форматы из запроса дубликатов
    duplicatesResults.forEach((result) => {
      mergedFormatsMap.set(result.format_public_id, {
        format_public_id: result.format_public_id,
        tickets: [...result.tickets],
      })
    })

    // Затем происходит добавление или объединение форматов из запроса вне диапазона
    mismatchResults.forEach((result) => {
      if (mergedFormatsMap.has(result.format_public_id)) {
        // Формат уже присутствует в карте, выполняется объединение билетов
        const existingTickets = mergedFormatsMap.get(result.format_public_id).tickets
        const ticketMap = new Map() // Используется Map для устранения дубликатов по public_id

        // Добавление существующих билетов
        existingTickets.forEach((ticket) => {
          ticketMap.set(ticket.public_id, ticket)
        })

        // Добавление новых билетов
        result.tickets.forEach((ticket) => {
          ticketMap.set(ticket.public_id, ticket)
        })

        // Обновление билетов в формате
        mergedFormatsMap.get(result.format_public_id).tickets = Array.from(ticketMap.values())
      } else {
        // Добавление нового формата
        mergedFormatsMap.set(result.format_public_id, {
          format_public_id: result.format_public_id,
          tickets: [...result.tickets],
        })
      }
    })

    return Array.from(mergedFormatsMap.values())
  }, [duplicatesResults, mismatchResults])

  useEffect(() => {
    // Обновление объединенных результатов при изменении любого из исходных массивов
    const mergedResults = mergeResultsForAllFilter()

    // Если текущий фильтр "Все", обновляется отображаемые результаты
    if (activeFilter === 'all') {
      setResults(mergedResults)
    }
  }, [duplicatesResults, mismatchResults, activeFilter, mergeResultsForAllFilter])

  // Обработка загрузки билета
  useEffect(() => {
    if (getTicketQuery.data && selectedTicket) {
      setTicket(getTicketQuery.data.data)
    }
  }, [getTicketQuery.data, selectedTicket])

  // Добавление обработчиков событий для мыши
  useEffect(() => {
    const handleMouseUp = () => {
      setIsMouseDown(false)
    }

    // Добавление слушателя на весь документ для отслеживания отпускания кнопки мыши
    // даже при выходе курсора за пределы списка
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [])

  /**
   * Обработчик начала выделения форматов мышью.
   *
   * Реализует функционал множественного выбора форматов перетаскиванием.
   * Инициирует процесс выделения и предотвращает выделение текста.
   * Сохраняет состояние последнего выбранного формата для определения режима выделения.
   *
   * @param {Event} e - Событие мыши
   * @param {string} formatId - Идентификатор формата
   */
  const handleMouseDown = (e, formatId) => {
    e.preventDefault()
    setIsMouseDown(true)
    setLastSelectedFormat(formatId)
    handleToggleFormat(formatId)
  }

  /**
   * Обработчик наведения мыши на формат при выделении.
   *
   * Обеспечивает множественное выделение форматов при перетаскивании.
   * Обновляет список выбранных форматов при движении мыши.
   * Учитывает начальное состояние первого выбранного формата.
   *
   * @param {string} formatId - Идентификатор формата
   */
  const handleMouseEnter = (formatId) => {
    // Если кнопка мыши зажата и происходит перемещение по элементам, выполняется их выбор
    if (isMouseDown) {
      // При наличии последнего выбранного формата в списке выбранных происходит добавление нового
      // в противном случае - удаление. Это позволяет как выбирать, так и снимать выбор перетаскиванием
      const wasSelected = selectedFormats.includes(lastSelectedFormat)
      const isCurrentSelected = selectedFormats.includes(formatId)

      if (wasSelected && !isCurrentSelected) {
        setSelectedFormats((prev) => [...prev, formatId])
      } else if (!wasSelected && isCurrentSelected) {
        setSelectedFormats((prev) => prev.filter((id) => id !== formatId))
      }

      setLastSelectedFormat(formatId)
    }
  }

  const handleToggleFormat = (formatId) => {
    if (selectedFormats.includes(formatId)) {
      setSelectedFormats(selectedFormats.filter((id) => id !== formatId))
    } else {
      setSelectedFormats([...selectedFormats, formatId])
    }
  }

  const handleSelectAllFormats = () => {
    if (selectedFormats.length === formats.length) {
      setSelectedFormats([])
    } else {
      setSelectedFormats(formats.map((format) => format.public_id))
    }
  }

  /**
   * Обработчик поиска дубликатов номеров.
   *
   * Запускает проверку на наличие одинаковых номеров в выбранных форматах.
   * Обновляет состояние результатов и активирует панель фильтров.
   * При успешном запросе автоматически переключает фильтр на "duplicates".
   */
  const handleFindDuplicates = () => {
    if (selectedFormats.length === 0) return

    findDuplicatesNumbersMutation.mutate(
      { event_format_ids: selectedFormats },
      {
        onSuccess: (data) => {
          const values = data?.data?.values || []
          setDuplicatesResults(values)
          setActiveFilter('duplicates')
          setResults(values)
          setShowFilterPanel(true)
        },
      }
    )
  }

  /**
   * Обработчик поиска номеров вне диапазона.
   *
   * Запускает проверку номеров на соответствие допустимому диапазону.
   * Обновляет состояние результатов и активирует панель фильтров.
   * При успешном запросе автоматически переключает фильтр на "mismatch".
   */
  const handleFindMismatch = () => {
    if (selectedFormats.length === 0) return

    findMismatchNumbersMutation.mutate(
      { event_format_ids: selectedFormats },
      {
        onSuccess: (data) => {
          const values = data?.data?.values || []
          setMismatchResults(values)
          setActiveFilter('mismatch')
          setResults(values)
          setShowFilterPanel(true)
        },
      }
    )
  }

  /**
   * Обработчик изменения фильтра результатов.
   *
   * Управляет отображением результатов по типу проблемы.
   * Обновляет отображаемые данные и активный фильтр.
   * В режиме "all" объединяет результаты обоих типов проверок.
   *
   * @param {string} filter - Идентификатор фильтра
   */
  const handleSetFilter = (filter) => {
    setActiveFilter(filter)
    if (filter === 'all') {
      const mergedResults = mergeResultsForAllFilter()
      setResults(mergedResults)
    } else if (filter === 'duplicates') {
      setResults(duplicatesResults)
    } else if (filter === 'mismatch') {
      setResults(mismatchResults)
    }
  }

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket)
  }

  const closeTicket = () => {
    setSelectedTicket(null)
    setTicket(null)
  }

  const handleUpdateTickets = () => {
    // Обновление результатов после изменения билета
    if (activeFilter === 'duplicates') {
      handleFindDuplicates()
    } else if (activeFilter === 'mismatch') {
      handleFindMismatch()
    } else {
      // Для режима 'all' требуется обновление обоих типов результатов
      if (duplicatesResults.length > 0) handleFindDuplicates()
      if (mismatchResults.length > 0) handleFindMismatch()
    }
    setSelectedTicket(null)
    setTicket(null)
  }

  const getFormatTitle = (formatId) => {
    const format = formats.find((format) => format.public_id === formatId)
    return format ? format.title : formatId
  }

  /**
   * Проверяет, является ли билет дубликатом.
   *
   * Необходимо определять наличие дублирующихся номеров.
   * Влияет на стилизацию номера и фильтрацию результатов.
   * Проверяет наличие билета в массиве дубликатов по public_id.
   *
   * @param {string} ticketPublicId - Публичный идентификатор билета
   * @returns {boolean} Признак дубликата
   */
  const isTicketDuplicate = (ticketPublicId) => {
    return duplicatesResults.some((result) => result.tickets.some((t) => t.public_id === ticketPublicId))
  }

  /**
   * Проверяет, находится ли номер билета вне допустимого диапазона.
   *
   * Требуется выявлять билеты с некорректными номерами.
   * Влияет на стилизацию номера и фильтрацию результатов.
   * Проверяет наличие билета в массиве несоответствий по public_id.
   *
   * @param {string} ticketPublicId - Публичный идентификатор билета
   * @returns {boolean} Признак несоответствия диапазону
   */
  const isTicketMismatch = (ticketPublicId) => {
    return mismatchResults.some((result) => result.tickets.some((t) => t.public_id === ticketPublicId))
  }

  /**
   * Определяет стиль отображения номера билета.
   *
   * Визуально выделяет проблемные билеты разными цветами.
   * Применяет соответствующие стили в зависимости от типа проблемы.
   * Для билетов с обоими типами проблем приоритет отдается стилю дубликата.
   *
   * @param {string} ticketPublicId - Публичный идентификатор билета
   * @returns {string} CSS класс для стилизации номера
   */
  const getTicketNumberClass = (ticketPublicId) => {
    if (isTicketDuplicate(ticketPublicId)) {
      return `${styles.ticketNumber} ${styles.ticketNumberDuplicate}`
    } else if (isTicketMismatch(ticketPublicId)) {
      return `${styles.ticketNumber} ${styles.ticketNumberMismatch}`
    }
    return `${styles.ticketNumber} ${styles.ticketNumberRegular}`
  }

  /**
   * Фильтрует результаты поиска по введенному номеру.
   *
   * Обеспечивает возможность поиска конкретных номеров среди найденных проблемных билетов.
   * Отображает только форматы, содержащие искомые номера.
   * Сохраняет структуру форматов, удаляя только те, где нет совпадений.
   */
  const filteredResults = useMemo(() => {
    if (!searchValue) return results

    return results
      .map((result) => {
        // Фильтрация номеров внутри формата
        const filteredTickets = result.tickets.filter((ticket) =>
          String(ticket.number).toLowerCase().includes(searchValue.toLowerCase())
        )

        // Если есть подходящие номера, возвращает формат с отфильтрованными номерами
        if (filteredTickets.length > 0) {
          return {
            ...result,
            tickets: filteredTickets,
          }
        }

        // Если нет подходящих номеров, возвращает null
        return null
      })
      .filter(Boolean) // Убирает пустые форматы
  }, [results, searchValue])

  const getNumbersCountText = (count) => {
    return `${count} ${getRightNames(count, 'номер', 'номера', 'номеров')}`
  }

  const isLoading =
    findDuplicatesNumbersMutation.isLoading ||
    findMismatchNumbersMutation.isLoading ||
    getEventCityFormatsQuery.isLoading

  return (
    <Row>
      <Col md={4}>
        <Card className="mb-4">
          <Card.Body>
            <Card.Title>Выберите форматы для проверки</Card.Title>
            <p className="text-muted small mb-2">
              Выбирайте форматы по одному или зажмите левую кнопку мыши и ведите по списку для выбора нескольких
              форматов
            </p>
            <div className="mb-3">
              <Button variant="outline-secondary" size="sm" onClick={handleSelectAllFormats} className="mb-3">
                {selectedFormats.length === formats.length ? 'Снять выбор со всех' : 'Выбрать все форматы'}
              </Button>
              <div className="d-flex flex-column" ref={formatsContainerRef}>
                {formats.map((format) => (
                  <div
                    key={format.public_id}
                    className={`${styles.formatItem} ${
                      selectedFormats.includes(format.public_id) ? styles.formatItemSelected : ''
                    }`}
                    onMouseDown={(e) => handleMouseDown(e, format.public_id)}
                    onMouseEnter={() => handleMouseEnter(format.public_id)}
                  >
                    <Form.Check
                      inline
                      type="checkbox"
                      id={`format-${format.public_id}`}
                      checked={selectedFormats.includes(format.public_id)}
                      onChange={() => handleToggleFormat(format.public_id)}
                      onClick={(e) => e.stopPropagation()}
                      label={format.title}
                    />
                  </div>
                ))}
                {formats.length === 0 && !getEventCityFormatsQuery.isLoading && (
                  <p className="text-muted">Нет доступных форматов</p>
                )}
                {getEventCityFormatsQuery.isLoading && <Spinner animation="border" size="sm" />}
              </div>
            </div>
            <div className="d-grid gap-2">
              <Button
                variant="primary"
                onClick={handleFindDuplicates}
                disabled={selectedFormats.length === 0 || isLoading}
              >
                {findDuplicatesNumbersMutation.isLoading ? (
                  <Spinner animation="border" size="sm" />
                ) : (
                  <>Найти дубликаты</>
                )}
              </Button>
              <Button
                variant="primary"
                onClick={handleFindMismatch}
                disabled={selectedFormats.length === 0 || isLoading}
              >
                {findMismatchNumbersMutation.isLoading ? (
                  <Spinner animation="border" size="sm" />
                ) : (
                  <>Найти номера вне диапазона</>
                )}
              </Button>
            </div>
          </Card.Body>
        </Card>

        <Card className="mb-4">
          <Card.Body>
            <div className="d-flex flex-column gap-2 mt-2">
              <div className="d-flex align-items-center">
                <div className={`${styles.legendItem} ${styles.legendDuplicate}`}></div>
                <span>Дубликаты номеров</span>
              </div>
              <div className="d-flex align-items-center">
                <div className={`${styles.legendItem} ${styles.legendMismatch}`}></div>
                <span>Номера вне диапазона</span>
              </div>
            </div>
          </Card.Body>
        </Card>
      </Col>
      <Col md={8}>
        {(showFilterPanel || results.length > 0 || isLoading) && (
          <Card>
            <Card.Body>
              <div className={styles.resultsWrapper}>
                <div className={styles.resultsHeader}>
                  <Row className="mb-3 align-items-center">
                    <Col md={6}>
                      <InputGroup>
                        <InputGroup.Text>
                          <i className="bi bi-search" />
                        </InputGroup.Text>
                        <FormControl
                          placeholder="Поиск по номеру"
                          value={searchValue}
                          onChange={(e) => setSearchValue(e.target.value)}
                        />
                        {searchValue && (
                          <Button
                            variant="outline-secondary"
                            onClick={() => setSearchValue('')}
                            className="border-start-0"
                            style={{ borderColor: 'var(--bs-border-color)' }}
                          >
                            <i className="bi bi-x" />
                          </Button>
                        )}
                      </InputGroup>
                    </Col>
                    <Col md={6}>
                      <div className="d-flex justify-content-end">
                        <ButtonGroup>
                          <Button
                            variant={activeFilter === 'all' ? 'primary' : 'outline-primary'}
                            onClick={() => handleSetFilter('all')}
                          >
                            Все
                          </Button>
                          <Button
                            variant={activeFilter === 'duplicates' ? 'primary' : 'outline-primary'}
                            onClick={() => handleSetFilter('duplicates')}
                          >
                            Дубликаты
                          </Button>
                          <Button
                            variant={activeFilter === 'mismatch' ? 'primary' : 'outline-primary'}
                            onClick={() => handleSetFilter('mismatch')}
                          >
                            Вне диапазона
                          </Button>
                        </ButtonGroup>
                      </div>
                    </Col>
                  </Row>

                  <Card.Title className="mb-3">Результаты проверки</Card.Title>
                </div>

                <div className={styles.resultsContent}>
                  {isLoading ? (
                    <div className="text-center py-5">
                      <Spinner animation="border" />
                    </div>
                  ) : filteredResults.length > 0 ? (
                    <ListGroup>
                      {filteredResults.map((result) => (
                        <ListGroup.Item key={result.format_public_id} className="mb-3">
                          <div className="mb-3">
                            <h5>
                              <Badge bg="secondary" className="me-2">
                                {getFormatTitle(result.format_public_id)}
                              </Badge>
                              <Badge bg="info" pill>
                                {getNumbersCountText(result.tickets.length)}
                              </Badge>
                            </h5>
                          </div>
                          <div className="d-flex flex-wrap" style={{ gap: '4px' }}>
                            {result.tickets.map((ticket) => (
                              <Button
                                key={ticket.public_id}
                                variant="link"
                                className={`p-0 px-1 text-decoration-none ${getTicketNumberClass(ticket.public_id)}`}
                                onClick={() => handleTicketClick(ticket)}
                              >
                                {ticket.number}
                              </Button>
                            ))}
                          </div>
                        </ListGroup.Item>
                      ))}
                    </ListGroup>
                  ) : (
                    <Alert variant="info">
                      {searchValue ? 'Нет результатов по вашему запросу' : 'Нет результатов для отображения'}
                    </Alert>
                  )}
                </div>
              </div>
            </Card.Body>
          </Card>
        )}

        {selectedTicket && (
          <div className="mt-4">
            <Card>
              <Card.Body>
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h5 className="mb-0">Информация о билете #{selectedTicket.number}</h5>
                  <Button variant="outline-secondary" size="sm" onClick={closeTicket}>
                    <i className="bi bi-x-lg"></i>
                  </Button>
                </div>
                <div className={styles.ticketWrapper}>
                  {getTicketQuery.isLoading && (
                    <div className={styles.ticketLoader}>
                      <Spinner animation="border" />
                    </div>
                  )}

                  {ticket ? (
                    <UpdateOrdersTicketsContext.Provider value={{ handleUpdateTickets }}>
                      <Ticket ticket={ticket} isExpandedByDefault={true} />
                    </UpdateOrdersTicketsContext.Provider>
                  ) : (
                    // Заглушка для сохранения места, пока билет не загрузился первый раз
                    <div style={{ minHeight: '500px' }}></div>
                  )}
                </div>
              </Card.Body>
            </Card>
          </div>
        )}
      </Col>
    </Row>
  )
}
