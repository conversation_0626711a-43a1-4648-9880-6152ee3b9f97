/**
 * Transforms schedule data from object format (with numeric keys) to proper array format
 * This fixes the issue where React Hook Form serializes arrays as objects
 */
export const transformScheduleToArray = (scheduleData) => {
  if (!scheduleData) return scheduleData

  // If it's already an array, return as is
  if (Array.isArray(scheduleData)) {
    return scheduleData.map(transformScheduleDayToArray)
  }

  // If it's an object with numeric keys, convert to array
  if (typeof scheduleData === 'object' && scheduleData !== null) {
    const keys = Object.keys(scheduleData)
    const isObjectWithNumericKeys = keys.every((key) => !isNaN(Number(key)))

    if (isObjectWithNumericKeys) {
      // Convert object to array, maintaining order
      return keys
        .map(Number)
        .sort((a, b) => a - b)
        .map((index) => transformScheduleDayToArray(scheduleData[index]))
        .filter(Boolean) // Remove any null/undefined entries
    }
  }

  return scheduleData
}

/**
 * Transforms a single schedule day's times from object to array format
 */
const transformScheduleDayToArray = (dayData) => {
  if (!dayData || typeof dayData !== 'object') return dayData

  const transformedDay = { ...dayData }

  // Transform times if they exist and are in object format
  if (transformedDay.times) {
    if (Array.isArray(transformedDay.times)) {
      // Already an array, keep as is
      return transformedDay
    }

    if (typeof transformedDay.times === 'object' && transformedDay.times !== null) {
      const timeKeys = Object.keys(transformedDay.times)
      const isObjectWithNumericKeys = timeKeys.every((key) => !isNaN(Number(key)))

      if (isObjectWithNumericKeys) {
        // Convert times object to array
        transformedDay.times = timeKeys
          .map(Number)
          .sort((a, b) => a - b)
          .map((index) => transformedDay.times[index])
          .filter(Boolean) // Remove any null/undefined entries
      }
    }
  }

  return transformedDay
}

/**
 * Transforms form data to ensure schedule is in proper array format
 * This should be called before sending data to the backend
 */
export const transformFormDataForBackend = (formData) => {
  if (!formData || typeof formData !== 'object') return formData

  const transformedData = { ...formData }

  if (transformedData.schedule) {
    transformedData.schedule = transformScheduleToArray(transformedData.schedule)
  }

  return transformedData
}
