import { Button } from 'react-bootstrap'

import { ScheduleDay } from './components/ScheduleDay'
import { useScheduleHandlers } from './hooks/useScheduleHandlers'
import { useScheduleManager } from './hooks/useScheduleManager'

export const ScheduleField = ({ control, register, errors, setValue, currentTimezone, onScheduleChange }) => {
  // Use custom hooks for managing schedule logic
  const {
    scheduleFields,
    appendScheduleDay,
    removeScheduleDay,
    updateScheduleDay,
    formatLocalDate,
    checkScheduleChanges,
    userInputTimeRef,
  } = useScheduleManager({ control, setValue, currentTimezone, onScheduleChange })

  const { handleAddScheduleDay, handleAddTimeSlot, handleChangeDate, handleLabelChange, handleRemoveTimeSlot } =
    useScheduleHandlers({
      control,
      setValue,
      currentTimezone,
      appendScheduleDay,
      updateScheduleDay,
      checkScheduleChanges,
      userInputTimeRef,
    })

  return (
    <>
      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Расписание{' '}
        <Button onClick={handleAddScheduleDay} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>

      {scheduleFields.map((day, dayIndex) => (
        <ScheduleDay
          key={day.id}
          day={day}
          dayIndex={dayIndex}
          register={register}
          errors={errors}
          formatLocalDate={formatLocalDate}
          handleChangeDate={handleChangeDate}
          handleLabelChange={handleLabelChange}
          handleAddTimeSlot={handleAddTimeSlot}
          handleRemoveTimeSlot={handleRemoveTimeSlot}
          removeScheduleDay={removeScheduleDay}
          currentTimezone={currentTimezone}
        />
      ))}
    </>
  )
}
