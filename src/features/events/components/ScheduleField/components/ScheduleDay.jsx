import { Button, Col, Form, FormControl, Row, FloatingLabel } from 'react-bootstrap'
import { FaCalendarAlt } from 'react-icons/fa'

import { TimezoneDisplay } from '@/components/TimezoneDisplay'

import { TimeSlot } from './TimeSlot'

export const ScheduleDay = ({
  day,
  dayIndex,
  register,
  errors,
  formatLocalDate,
  handleChangeDate,
  handleLabelChange,
  handleAddTimeSlot,
  handleRemoveTimeSlot,
  removeScheduleDay,
  currentTimezone,
}) => {
  return (
    <div className="mb-4 p-3" style={{ border: '1px solid #dcdcdf', borderRadius: '8px' }}>
      {/* Отображение часового пояса и времени */}
      <TimezoneDisplay timezone={currentTimezone} className="mb-2" />

      <Row className="mb-3 align-items-center">
        <Col md={4}>
          <Form.Group>
            <FloatingLabel
              controlId={`day-${dayIndex}`}
              label={
                <>
                  <FaCalendarAlt className="me-1" /> Дата
                </>
              }
            >
              <FormControl
                type="date"
                name={`schedule.${dayIndex}.day`}
                value={formatLocalDate(day.day, 'day')}
                onChange={handleChangeDate}
                isInvalid={errors?.schedule?.[dayIndex]?.day}
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md="auto">
          <Button onClick={() => handleAddTimeSlot(dayIndex)} variant="outline-primary" type="button" size="sm">
            <i className="bi bi-clock me-1" /> Добавить время
          </Button>
        </Col>
        <Col md="auto">
          <Button onClick={() => removeScheduleDay(dayIndex)} variant="outline-danger" type="button" size="sm">
            <i className="bi bi-trash3-fill me-2" /> Удалить день
          </Button>
        </Col>
      </Row>

      {/* Validation error display */}
      {errors?.schedule?.[dayIndex]?._validateTimes && (
        <div className="text-danger mb-2">{errors.schedule[dayIndex]._validateTimes.message}</div>
      )}

      {day.times && day.times.length === 0 && (
        <div className="text-muted mb-2">Добавьте хотя бы один временной интервал для этого дня</div>
      )}

      {day.times &&
        day.times.map((timeSlot, timeIndex) => (
          <TimeSlot
            key={`time-${dayIndex}-${timeIndex}`}
            dayIndex={dayIndex}
            timeIndex={timeIndex}
            timeSlot={timeSlot}
            day={day}
            register={register}
            errors={errors}
            formatLocalDate={formatLocalDate}
            handleChangeDate={handleChangeDate}
            handleLabelChange={handleLabelChange}
            handleRemoveTimeSlot={handleRemoveTimeSlot}
          />
        ))}
    </div>
  )
}
