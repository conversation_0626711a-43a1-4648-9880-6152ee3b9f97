import { Button, Col, Form, FormControl, Row, FloatingLabel } from 'react-bootstrap'

export const TimeSlot = ({
  dayIndex,
  timeIndex,
  timeSlot,
  day,
  register,
  errors,
  formatLocalDate,
  handleChangeDate,
  handleLabelChange,
  handleRemoveTimeSlot,
}) => {
  return (
    <Row className="mb-2 mt-3">
      <Col md={3}>
        <Form.Group>
          <FloatingLabel controlId={`start-time-${dayIndex}-${timeIndex}`} label="Время начала">
            <FormControl
              type="time"
              name={`schedule.${dayIndex}.times.${timeIndex}.start_time`}
              value={formatLocalDate(timeSlot.start_time, 'time')}
              onChange={handleChangeDate}
              isInvalid={errors?.schedule?.[dayIndex]?.times?.[timeIndex]?.start_time}
              required
            />
          </FloatingLabel>
        </Form.Group>
      </Col>
      <Col md={3}>
        <Form.Group>
          <FloatingLabel controlId={`end-time-${dayIndex}-${timeIndex}`} label="Время окончания">
            <FormControl
              type="time"
              name={`schedule.${dayIndex}.times.${timeIndex}.end_time`}
              value={formatLocalDate(timeSlot.end_time, 'time')}
              onChange={handleChangeDate}
              isInvalid={errors?.schedule?.[dayIndex]?.times?.[timeIndex]?.end_time}
              required
            />
          </FloatingLabel>
        </Form.Group>
      </Col>
      <Col md={5}>
        <Form.Group>
          <FloatingLabel controlId={`label-${dayIndex}-${timeIndex}`} label="Название">
            <FormControl
              {...register(`schedule.${dayIndex}.times.${timeIndex}.label`, {
                required: 'Название обязательно',
                onChange: handleLabelChange,
              })}
              type="text"
              isInvalid={errors?.schedule?.[dayIndex]?.times?.[timeIndex]?.label}
              placeholder=""
            />
          </FloatingLabel>
        </Form.Group>
      </Col>
      <Col md={1} className="d-flex align-items-center">
        <Button
          onClick={() => handleRemoveTimeSlot(dayIndex, timeIndex)}
          variant="outline-danger"
          type="button"
          size="sm"
          disabled={day.times.length <= 1}
          title={day.times.length <= 1 ? 'Невозможно удалить единственный слот времени' : ''}
        >
          <i className="bi bi-trash3-fill" />
        </Button>
      </Col>
    </Row>
  )
}
