import moment from 'moment-timezone'
import { useCallback, useEffect, useRef } from 'react'
import { useFieldArray } from 'react-hook-form'

import { unixToMoment } from '@/utils/date'

export const useScheduleManager = ({ control, setValue, currentTimezone, onScheduleChange }) => {
  const {
    fields: scheduleFields,
    append: appendScheduleDay,
    remove: removeScheduleDay,
    update: updateScheduleDay,
  } = useFieldArray({
    control,
    name: 'schedule',
  })

  // Refs for tracking state
  const prevTimezoneRef = useRef(currentTimezone)
  const userInputTimeRef = useRef({})
  const initialScheduleRef = useRef(null)
  const initializedRef = useRef(false)

  // Format local date utility
  const formatLocalDate = useCallback(
    (isoDate, type) => {
      if (!isoDate) return ''

      try {
        let timestamp

        if (!isNaN(Number(isoDate))) {
          timestamp = unixToMoment(Number(isoDate))
        } else {
          timestamp = moment.utc(isoDate)
        }

        if (!timestamp.isValid()) {
          return ''
        }

        if (type === 'day') {
          return timestamp.tz(currentTimezone).format('YYYY-MM-DD')
        } else if (type === 'time') {
          return timestamp.tz(currentTimezone).format('HH:mm')
        }
      } catch (error) {
        console.error('Ошибка форматирования даты:', error)
        return ''
      }
    },
    [currentTimezone]
  )

  // Check for schedule changes
  const checkScheduleChanges = useCallback(() => {
    if (!initialScheduleRef.current || !initializedRef.current) return false

    const currentSchedule = JSON.stringify(control._formValues.schedule)
    const hasChanges = currentSchedule !== initialScheduleRef.current

    onScheduleChange?.(hasChanges)
    return hasChanges
  }, [control._formValues.schedule, onScheduleChange])

  // Convert Unix timestamps to ISO format
  const convertUnixTimestampsToIso = useCallback(() => {
    try {
      const currentSchedule = control._formValues.schedule

      if (!currentSchedule || !Array.isArray(currentSchedule)) {
        return
      }

      currentSchedule.forEach((day, dayIndex) => {
        if (day.day) {
          // Convert day Unix timestamp to ISO
          if (!isNaN(Number(day.day))) {
            const isoDate = unixToMoment(Number(day.day)).utc().format()
            setValue(`schedule.${dayIndex}.day`, isoDate)
            updateScheduleDay(dayIndex, {
              ...day,
              day: isoDate,
            })
          }

          // Process time slots
          if (day.times && Array.isArray(day.times)) {
            const updatedTimes = day.times.map((timeSlot, timeIndex) => {
              const updatedTimeSlot = { ...timeSlot }

              // Convert start time
              if (timeSlot.start_time && !isNaN(Number(timeSlot.start_time))) {
                const isoTime = unixToMoment(Number(timeSlot.start_time)).utc().format()
                const localTime = formatLocalDate(timeSlot.start_time, 'time')
                userInputTimeRef.current[`${dayIndex}-${timeIndex}-start_time`] = localTime

                setValue(`schedule.${dayIndex}.times.${timeIndex}.start_time`, isoTime)
                updatedTimeSlot.start_time = isoTime
              }

              // Convert end time
              if (timeSlot.end_time && !isNaN(Number(timeSlot.end_time))) {
                const isoTime = unixToMoment(Number(timeSlot.end_time)).utc().format()
                const localTime = formatLocalDate(timeSlot.end_time, 'time')
                userInputTimeRef.current[`${dayIndex}-${timeIndex}-end_time`] = localTime

                setValue(`schedule.${dayIndex}.times.${timeIndex}.end_time`, isoTime)
                updatedTimeSlot.end_time = isoTime
              }

              return updatedTimeSlot
            })

            setValue(`schedule.${dayIndex}.times`, updatedTimes)
            updateScheduleDay(dayIndex, {
              ...day,
              day: day.day,
              times: updatedTimes,
            })
          }
        }
      })
    } catch (error) {
      console.error('Ошибка при преобразовании Unix timestamp в ISO UTC:', error)
    }
  }, [control._formValues.schedule, setValue, updateScheduleDay, formatLocalDate])

  // Update times for new timezone
  const updateTimesForNewTimezone = useCallback(() => {
    try {
      const currentSchedule = control._formValues.schedule

      if (!currentSchedule || !Array.isArray(currentSchedule)) {
        return
      }

      currentSchedule.forEach((day, dayIndex) => {
        if (day.day) {
          const formattedDay = formatLocalDate(day.day, 'day')
          const newDayTimestamp = moment
            .tz(`${formattedDay} 12:00:00`, 'YYYY-MM-DD HH:mm:ss', currentTimezone)
            .utc()
            .format()

          setValue(`schedule.${dayIndex}.day`, newDayTimestamp)
          updateScheduleDay(dayIndex, {
            ...day,
            day: newDayTimestamp,
          })

          if (day.times && Array.isArray(day.times)) {
            const updatedTimes = day.times.map((timeSlot, timeIndex) => {
              const updatedTimeSlot = { ...timeSlot }

              // Update start time
              if (timeSlot.start_time) {
                const userTime =
                  userInputTimeRef.current[`${dayIndex}-${timeIndex}-start_time`] ||
                  formatLocalDate(timeSlot.start_time, 'time')

                const newTimestamp = moment
                  .tz(`${formattedDay} ${userTime}:00`, 'YYYY-MM-DD HH:mm:ss', currentTimezone)
                  .utc()
                  .format()

                setValue(`schedule.${dayIndex}.times.${timeIndex}.start_time`, newTimestamp)
                updatedTimeSlot.start_time = newTimestamp
              }

              // Update end time
              if (timeSlot.end_time) {
                const userTime =
                  userInputTimeRef.current[`${dayIndex}-${timeIndex}-end_time`] ||
                  formatLocalDate(timeSlot.end_time, 'time')

                const newTimestamp = moment
                  .tz(`${formattedDay} ${userTime}:00`, 'YYYY-MM-DD HH:mm:ss', currentTimezone)
                  .utc()
                  .format()

                setValue(`schedule.${dayIndex}.times.${timeIndex}.end_time`, newTimestamp)
                updatedTimeSlot.end_time = newTimestamp
              }

              return updatedTimeSlot
            })

            setValue(`schedule.${dayIndex}.times`, updatedTimes)
            updateScheduleDay(dayIndex, {
              ...day,
              day: newDayTimestamp,
              times: updatedTimes,
            })
          }
        }
      })

      setTimeout(() => checkScheduleChanges(), 0)
    } catch (error) {
      console.error('Ошибка при обновлении времени для нового часового пояса:', error)
    }
  }, [
    control._formValues.schedule,
    currentTimezone,
    setValue,
    updateScheduleDay,
    checkScheduleChanges,
    formatLocalDate,
  ])

  // Initialize schedule
  useEffect(() => {
    if (!initializedRef.current && scheduleFields.length > 0) {
      const hasUnixTimestamps = scheduleFields.some((day) => {
        if (day.day && !isNaN(Number(day.day))) return true
        return day.times?.some(
          (time) =>
            (time.start_time && !isNaN(Number(time.start_time))) || (time.end_time && !isNaN(Number(time.end_time)))
        )
      })

      if (hasUnixTimestamps) {
        convertUnixTimestampsToIso()
      }

      setTimeout(() => {
        initialScheduleRef.current = JSON.stringify(control._formValues.schedule)
        initializedRef.current = true
        onScheduleChange?.(false)
      }, 0)
    }
  }, [scheduleFields, convertUnixTimestampsToIso, control._formValues.schedule, onScheduleChange])

  // Track schedule changes
  useEffect(() => {
    if (initializedRef.current) {
      checkScheduleChanges()
    }
  }, [control._formValues.schedule, checkScheduleChanges])

  // Handle timezone changes
  useEffect(() => {
    if (prevTimezoneRef.current !== currentTimezone && scheduleFields.length > 0) {
      updateTimesForNewTimezone()
      prevTimezoneRef.current = currentTimezone
    }
  }, [currentTimezone, scheduleFields, updateTimesForNewTimezone])

  return {
    scheduleFields,
    appendScheduleDay,
    removeScheduleDay,
    updateScheduleDay,
    formatLocalDate,
    checkScheduleChanges,
    userInputTimeRef,
  }
}
