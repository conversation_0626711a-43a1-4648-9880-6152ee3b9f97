import moment from 'moment-timezone'
import { useCallback } from 'react'

export const useScheduleHandlers = ({
  control,
  setValue,
  currentTimezone,
  appendScheduleDay,
  updateScheduleDay,
  checkScheduleChanges,
  userInputTimeRef,
}) => {
  // Add a new schedule day
  const handleAddScheduleDay = useCallback(() => {
    try {
      let defaultDate

      if (control._formValues.start_time) {
        defaultDate = moment(control._formValues.start_time).utc().format()
      } else {
        defaultDate = moment().tz(currentTimezone).utc().format()
      }

      appendScheduleDay({
        day: defaultDate,
        times: [
          {
            start_time: '',
            end_time: '',
          },
        ],
      })

      setTimeout(() => checkScheduleChanges(), 0)
    } catch (error) {
      console.error('Ошибка при добавлении дня расписания:', error)
    }
  }, [control._formValues.start_time, currentTimezone, appendScheduleDay, checkScheduleChanges])

  // Add a new time slot to an existing day
  const handleAddTimeSlot = useCallback(
    (dayIndex) => {
      try {
        const currentDay = control._formValues.schedule[dayIndex].day

        if (!currentDay) {
          const updatedTimes = [
            ...control._formValues.schedule[dayIndex].times,
            {
              start_time: '',
              end_time: '',
            },
          ]

          setValue(`schedule.${dayIndex}.times`, updatedTimes)
          updateScheduleDay(dayIndex, {
            ...control._formValues.schedule[dayIndex],
            times: updatedTimes,
          })
        } else {
          const dayDate = moment(currentDay).format('YYYY-MM-DD')
          const startTime = moment.tz(`${dayDate} 09:00`, 'YYYY-MM-DD HH:mm', currentTimezone).utc().format()
          const endTime = moment.tz(`${dayDate} 18:00`, 'YYYY-MM-DD HH:mm', currentTimezone).utc().format()

          const updatedTimes = [
            ...control._formValues.schedule[dayIndex].times,
            {
              start_time: startTime,
              end_time: endTime,
            },
          ]

          setValue(`schedule.${dayIndex}.times`, updatedTimes)
          updateScheduleDay(dayIndex, {
            ...control._formValues.schedule[dayIndex],
            times: updatedTimes,
          })
        }

        setTimeout(() => checkScheduleChanges(), 0)
      } catch (error) {
        console.error('Ошибка при добавлении временного слота:', error)
      }
    },
    [control._formValues.schedule, setValue, updateScheduleDay, currentTimezone, checkScheduleChanges]
  )

  // Handle date/time changes
  const handleChangeDate = useCallback(
    (e) => {
      try {
        const { name, value } = e.target
        const match = name.match(/schedule\.(\d+)\.times\.(\d+)\.(start_time|end_time)/)

        if (match) {
          const [, dayIndex, timeIndex, timeType] = match

          userInputTimeRef.current[`${dayIndex}-${timeIndex}-${timeType}`] = value

          const day = control._formValues.schedule[dayIndex].day
          const formattedDay = moment(day).format('YYYY-MM-DD')
          const localMoment = moment.tz(`${formattedDay} ${value}`, 'YYYY-MM-DD HH:mm', currentTimezone)
          const isoTime = localMoment.utc().format()

          setValue(`schedule.${dayIndex}.times.${timeIndex}.${timeType}`, isoTime)

          const updatedTimes = [...control._formValues.schedule[dayIndex].times]
          updatedTimes[timeIndex] = {
            ...updatedTimes[timeIndex],
            [timeType]: isoTime,
          }

          updateScheduleDay(dayIndex, {
            ...control._formValues.schedule[dayIndex],
            times: updatedTimes,
          })
        } else if (name.match(/schedule\.(\d+)\.day/)) {
          const dayIndex = name.match(/schedule\.(\d+)\.day/)[1]
          const localMoment = moment.tz(`${value} 12:00`, 'YYYY-MM-DD HH:mm', currentTimezone)
          const isoDate = localMoment.utc().format()

          setValue(`schedule.${dayIndex}.day`, isoDate)
          updateScheduleDay(dayIndex, {
            ...control._formValues.schedule[dayIndex],
            day: isoDate,
          })
        }

        setTimeout(() => checkScheduleChanges(), 0)
      } catch (error) {
        console.error('Ошибка при изменении даты или времени:', error)
      }
    },
    [control._formValues.schedule, setValue, updateScheduleDay, currentTimezone, checkScheduleChanges, userInputTimeRef]
  )

  const handleLabelChange = useCallback(() => {
    try {
      setTimeout(() => checkScheduleChanges(), 0)
    } catch (error) {
      console.error('Ошибка при изменении названия:', error)
    }
  }, [checkScheduleChanges])

  // Remove a time slot
  const handleRemoveTimeSlot = useCallback(
    (dayIndex, timeIndex) => {
      try {
        const currentValues = control._formValues.schedule[dayIndex]
        const currentTimes = [...currentValues.times]

        if (currentTimes.length <= 1) {
          return
        }

        currentTimes.splice(timeIndex, 1)

        delete userInputTimeRef.current[`${dayIndex}-${timeIndex}-start_time`]
        delete userInputTimeRef.current[`${dayIndex}-${timeIndex}-end_time`]

        updateScheduleDay(dayIndex, {
          ...currentValues,
          times: currentTimes,
        })

        setValue(`schedule.${dayIndex}.times`, currentTimes)

        setTimeout(() => checkScheduleChanges(), 0)
      } catch (error) {
        console.error('Ошибка при удалении временного слота:', error)
      }
    },
    [control._formValues.schedule, setValue, updateScheduleDay, checkScheduleChanges, userInputTimeRef]
  )

  return {
    handleAddScheduleDay,
    handleAddTimeSlot,
    handleChangeDate,
    handleLabelChange,
    handleRemoveTimeSlot,
  }
}
