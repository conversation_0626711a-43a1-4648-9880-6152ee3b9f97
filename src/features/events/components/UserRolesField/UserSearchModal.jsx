import { useState, useEffect } from 'react'
import {
  Modal,
  Button,
  FormControl,
  InputGroup,
  Row,
  Col,
  Alert,
  Spinner,
  Dropdown,
  DropdownButton,
} from 'react-bootstrap'
import { Link } from 'react-router-dom'

import { AppRoute } from '@/const'
import { useSearchUser } from '@/features/user/api/searchUser'
import { useToast } from '@/hooks/useToast'

export const UserSearchModal = ({ show, onHide, onSelectUsers, roleLabel, excludeUserIds = [] }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [foundUser, setFoundUser] = useState(null)
  const [searchType, setSearchType] = useState('auto')

  const searchUserMutation = useSearchUser()
  const openToast = useToast()

  const searchTypeLabels = {
    auto: 'Авто',
    email: 'По email',
    phone: 'По телефону',
    public_id: 'По <PERSON>',
  }

  // Сброс состояния при открытии/закрытии модала
  useEffect(() => {
    if (show) {
      setSearchQuery('')
      setFoundUser(null)
      setSearchType('auto')
    }
  }, [show])

  // Сброс результатов поиска при очистке поля ввода
  const handleSearchInputChange = (e) => {
    const value = e.target.value
    setSearchQuery(value)
    if (value.length === 0) {
      setFoundUser(null)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      openToast.warning({ message: 'Введите запрос для поиска' })
      return
    }

    try {
      // Определение типа поиска
      let searchData = {}
      const trimmedQuery = searchQuery.trim()

      if (searchType === 'auto') {
        // Автоматическое определение типа поиска
        if (trimmedQuery.includes('@')) {
          searchData = { email: trimmedQuery.toLowerCase() }
        } else if (/^\+?\d+$/.test(trimmedQuery)) {
          searchData = { phone: trimmedQuery }
        } else {
          searchData = { public_id: trimmedQuery }
        }
      } else {
        // Использование выбранного типа поиска
        const value = searchType === 'email' ? trimmedQuery.toLowerCase() : trimmedQuery
        searchData = { [searchType]: value }
      }

      const response = await searchUserMutation.mutateAsync(searchData)

      // Обработка ответа - API всегда возвращает один пользователь
      if (response?.status === 200 && response?.data) {
        const user = response.data

        // Проверка, не назначен ли уже этот пользователь на эту роль
        if (excludeUserIds.includes(user.public_id)) {
          openToast.info({ message: 'Пользователь уже назначен на эту роль' })
          setFoundUser(null)
        } else {
          setFoundUser(user)
        }
      } else {
        openToast.info({ message: 'Пользователь не найден' })
        setFoundUser(null)
      }
    } catch (error) {
      console.error('Search error:', error)
      openToast.error({ message: 'Ошибка при поиске пользователей' })
    }
  }

  const handleConfirm = () => {
    if (foundUser) {
      onSelectUsers([foundUser])
    }
    onHide()
  }

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Выбор пользователей для роли: {roleLabel}</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        <Row className="mb-3">
          <Col>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Введите почту, телефон или ID пользователя"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="border border-secondary"
              />
              <DropdownButton
                variant="outline-secondary"
                title={searchTypeLabels[searchType]}
                id="search-type-dropdown"
              >
                {Object.entries(searchTypeLabels).map(([key, label]) => (
                  <Dropdown.Item key={key} onClick={() => setSearchType(key)} active={searchType === key}>
                    {label}
                  </Dropdown.Item>
                ))}
              </DropdownButton>
              <Button variant="outline-secondary" onClick={handleSearch} disabled={searchUserMutation.isLoading}>
                {searchUserMutation.isLoading ? <Spinner size="sm" animation="border" /> : 'Поиск'}
              </Button>
            </InputGroup>
          </Col>
        </Row>

        {foundUser && (
          <Row className="mb-3">
            <Col>
              <Alert variant="success">
                <strong>Найден пользователь:</strong>
                <div className="mt-2">
                  <div className="d-flex flex-column gap-1">
                    <div>
                      <strong>ID:</strong>{' '}
                      <Link
                        to={`${AppRoute.USER_PROFILE}/${foundUser.public_id}`}
                        target="_blank"
                        className="text-decoration-none"
                      >
                        <code className="bg-body-tertiary px-2 py-1 rounded">{foundUser.public_id}</code>
                      </Link>
                    </div>
                    <div>
                      <strong>Имя:</strong> {foundUser.name} {foundUser.last_name}
                    </div>
                    <div>
                      <strong>Email:</strong> {foundUser.email}
                    </div>
                    <div>
                      <strong>Телефон:</strong> {foundUser.phone}
                    </div>
                  </div>
                </div>
              </Alert>
            </Col>
          </Row>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Отмена
        </Button>
        <Button variant="primary" onClick={handleConfirm} disabled={!foundUser}>
          Добавить пользователя
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
