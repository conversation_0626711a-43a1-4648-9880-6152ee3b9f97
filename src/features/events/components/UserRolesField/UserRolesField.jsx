import { useState, useEffect } from 'react'
import { Button, Accordion, <PERSON>, Row, Col, Badge } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import { AppRoute } from '@/const'

import { UserSearchModal } from './UserSearchModal'

const ROLE_LABELS = {
  city_admin: 'Админ города',
  city_editor: 'Редактор города',
  viewer: 'Просмотр города',
}

export const UserRolesField = ({ register, setValue, defaultRoles = {} }) => {
  const [selectedRole, setSelectedRole] = useState('')
  const [showUserModal, setShowUserModal] = useState(false)
  const [roleUsers, setRoleUsers] = useState({
    city_admin: defaultRoles.city_admin || [],
    city_editor: defaultRoles.city_editor || [],
    viewer: defaultRoles.viewer || [],
  })

  useEffect(() => {
    register('roles.city_admin')
    register('roles.city_editor')
    register('roles.viewer')
  }, [register])

  useEffect(() => {
    setValue('roles.city_admin', roleUsers.city_admin, { shouldDirty: true })
    setValue('roles.city_editor', roleUsers.city_editor, { shouldDirty: true })
    setValue('roles.viewer', roleUsers.viewer, { shouldDirty: true })
  }, [roleUsers, setValue])

  const handleAddUsers = (users) => {
    if (selectedRole && users.length > 0) {
      const userIds = users.map((user) => user.public_id)
      setRoleUsers((prev) => ({
        ...prev,
        [selectedRole]: [...new Set([...prev[selectedRole], ...userIds])],
      }))
    }
    setShowUserModal(false)
    setSelectedRole('')
  }

  const handleRemoveUser = (role, userId) => {
    setRoleUsers((prev) => ({
      ...prev,
      [role]: prev[role].filter((id) => id !== userId),
    }))
  }

  const openUserModal = (role) => {
    setSelectedRole(role)
    setShowUserModal(true)
  }

  const RoleSection = ({ role, label, users }) => (
    <Row className="mb-3">
      <Col md={12}>
        <div className="d-flex align-items-center justify-content-between mb-2">
          <Form.Label className="mb-0 fw-bold">{label}</Form.Label>
          <Button variant="outline-primary" size="sm" onClick={() => openUserModal(role)}>
            + Добавить
          </Button>
        </div>
        <div>
          {users.length > 0 ? (
            <div className="d-flex flex-wrap gap-1">
              {users.map((userId) => (
                <Badge
                  key={userId}
                  bg="secondary"
                  className="d-flex align-items-center gap-1 text-break"
                  style={{ fontSize: '0.75rem', maxWidth: '200px' }}
                >
                  <Link
                    to={`${AppRoute.USER_PROFILE}/${userId}`}
                    target="_blank"
                    className="text-white text-decoration-none text-truncate"
                    style={{ color: 'inherit' }}
                  >
                    {userId}
                  </Link>
                  <Button
                    variant="link"
                    size="sm"
                    className="p-0 text-white"
                    onClick={() => handleRemoveUser(role, userId)}
                    style={{ fontSize: '12px', lineHeight: 1, minWidth: 'auto' }}
                    title="Удалить пользователя"
                  >
                    ×
                  </Button>
                </Badge>
              ))}
            </div>
          ) : (
            <small className="text-muted">Пользователи не назначены</small>
          )}
        </div>
      </Col>
    </Row>
  )

  return (
    <>
      <Accordion className="mb-3">
        <Accordion.Item eventKey="0">
          <Accordion.Header>Роли пользователей</Accordion.Header>
          <Accordion.Body>
            {Object.entries(ROLE_LABELS).map(([role, label]) => (
              <RoleSection key={role} role={role} label={label} users={roleUsers[role]} />
            ))}
          </Accordion.Body>
        </Accordion.Item>
      </Accordion>

      <UserSearchModal
        show={showUserModal}
        onHide={() => {
          setShowUserModal(false)
          setSelectedRole('')
        }}
        onSelectUsers={handleAddUsers}
        selectedRole={selectedRole}
        roleLabel={ROLE_LABELS[selectedRole]}
        excludeUserIds={roleUsers[selectedRole] || []}
      />
    </>
  )
}
