.cardHeader {
  background-color: var(--bs-secondary-bg);
  border-bottom: 1px solid var(--bs-border-color);
}

.innerCardHeader {
  background-color: var(--bs-secondary-bg);
  border-bottom: 1px solid var(--bs-border-color);
  display: flex;
  align-items: center;
}

.totalCount {
  margin-left: 10px;
  font-size: 0.9rem;
  color: var(--bs-secondary-color);
  background-color: var(--bs-secondary-bg);
  padding: 2px 8px;
  border-radius: 12px;
}

.infoItem {
  display: flex;
  align-items: center;

  strong {
    margin-left: 5px;
  }
}

.copyButton {
  padding: 0 4px;
  color: var(--bs-secondary-color);

  &:hover {
    color: var(--bs-body-color);
  }

  &:focus,
  &:active {
    box-shadow: none;
  }
}

.sectionTitle {
  color: var(--bs-body-color);
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background-color: var(--bs-secondary-bg);
  padding: 8px 12px;
  border-radius: 6px;

  strong {
    margin-left: 2px;
  }
}

.description {
  color: var(--bs-secondary-color);
  line-height: 1.5;
  font-size: 0.95rem;
  padding: 0 10px;
}

.infoBlocks {
  margin-bottom: 15px;
}

.notes {
  font-style: italic;
  background-color: var(--bs-warning-bg-subtle);
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid var(--bs-warning);
}

.priceList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.priceItem {
  display: flex;
  align-items: center;
  background: var(--bs-body-bg);
  padding: 6px 10px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  span {
    font-size: 0.9rem;
  }
}

.listItems {
  li {
    margin-left: 14px;
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: var(--bs-secondary-bg);
    }

    i {
      flex-shrink: 0;
    }
  }
}
