import { Button, Col, Form, FormControl, Row } from 'react-bootstrap'
import { useFieldArray } from 'react-hook-form'

export const MerchField = ({ control, register, errors }) => {
  const {
    fields: merchFields,
    append: appendMerch,
    remove: removeMerch,
  } = useFieldArray({
    control,
    name: 'merch',
  })

  const handleAppendMerch = () => {
    appendMerch('')
  }

  return (
    <>
      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Мерч{' '}
        <Button onClick={handleAppendMerch} variant="link" type="button" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>
      <Row className="g-3">
        {merchFields.map((item, index) => (
          <Col md="4" key={item.id}>
            <Row className="g-1 align-items-center">
              <Col>
                <Form.Group>
                  <FormControl
                    {...register(`merch.${index}`)}
                    type="text"
                    isInvalid={errors?.merch?.[index]}
                    placeholder=""
                  />
                </Form.Group>
              </Col>

              <Col md="auto">
                <Button onClick={() => removeMerch(index)} variant="outline-danger" type="button" size="sm">
                  <i className="bi bi-trash3-fill" />
                </Button>
              </Col>
            </Row>
          </Col>
        ))}
      </Row>
    </>
  )
}
