import { useState } from 'react'
import { Button, Col, ListGroup, ListGroupItem, Row } from 'react-bootstrap'

import IconCopy from '@/assets/img/icons/icon-copy.svg?react'

import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { useDeleteEventCityFormat } from '@/features/events/api/deleteEventCityFormat'
import { useGetEventCityFormats } from '@/features/events/api/getEventCityFormats'
import { unixToMoment } from '@/utils/date'

export const Formats = ({ eventCityPublicId, onEdit, onCopy, timezone, city }) => {
  const [selectedFormat, setSelectedFormat] = useState('')
  const [confirmDeleteFormatModal, setConfirmDeleteFormatModal] = useState(false)
  const getEventCityFormatsQuery = useGetEventCityFormats(eventCityPublicId)
  const deleteEventCityFormatMutation = useDeleteEventCityFormat(eventCityPublicId)

  const formats = getEventCityFormatsQuery?.data?.data

  const returnDate = (item) => {
    const timestamp = unixToMoment(item.start_time)

    if (timestamp.isValid()) {
      return timestamp.tz(timezone).format('DD.MM.YYYY')
    }

    return null
  }

  const handleConfirmDeleteFormat = (format) => {
    setSelectedFormat(format)
    setConfirmDeleteFormatModal(true)
  }

  const handleDeleteFormat = () => {
    deleteEventCityFormatMutation.mutate({ public_id: selectedFormat.public_id })
    setSelectedFormat({})
    setConfirmDeleteFormatModal(false)
  }

  return (
    <ListGroup>
      {formats?.map((item) => (
        <ListGroupItem key={item.public_id}>
          <Row>
            <Col>
              {item.title} — {returnDate(item)}
            </Col>
            <Col md="auto">
              <Button onClick={() => onCopy(item, city)} variant="outline-light" size="sm">
                <IconCopy />
              </Button>
            </Col>
            <Col md="auto">
              <Button onClick={() => onEdit(item, city)} variant="outline-success" size="sm">
                изменить
              </Button>
            </Col>
            <Col md="auto">
              <Button onClick={() => handleConfirmDeleteFormat(item)} variant="outline-danger" size="sm">
                удалить
              </Button>
            </Col>
          </Row>
        </ListGroupItem>
      ))}

      <ConfirmDeleteModal
        isShow={confirmDeleteFormatModal}
        onClose={setConfirmDeleteFormatModal}
        onDeleteItem={handleDeleteFormat}
      />
    </ListGroup>
  )
}
