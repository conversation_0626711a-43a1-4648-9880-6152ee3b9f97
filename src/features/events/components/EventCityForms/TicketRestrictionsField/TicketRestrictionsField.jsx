import { Col, FormChe<PERSON>, <PERSON> } from 'react-bootstrap'

export const TicketRestrictionsField = ({ register, defaultValues }) => {
  return (
    <div className="mb-4">
      <h6 className="text-muted mb-3">Ограничения билетов</h6>
      <Row className="g-3">
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck
            {...register('ticket_edit_blocked')}
            type="switch"
            id={`ticketEditBlockedSwitch-${defaultValues?.public_id}`}
            label="Запрет редактирования"
          />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck
            {...register('ticket_transfer_blocked')}
            type="switch"
            id={`ticketTransferBlockedSwitch-${defaultValues?.public_id}`}
            label="Запрет передачи"
          />
        </Col>
      </Row>
    </div>
  )
}
