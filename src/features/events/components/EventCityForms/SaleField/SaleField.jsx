import { useEffect, useRef } from 'react'
import { <PERSON><PERSON>, Col, Floating<PERSON><PERSON><PERSON>, Form, FormCheck, FormControl, Row } from 'react-bootstrap'
import { Controller } from 'react-hook-form'
import Select from 'react-select'

import { eventCitySelectStyles } from '@/features/events/components/EventCityForms/eventCitySelect'
import { unixToMoment } from '@/utils/date'

export const SaleField = ({
  control,
  register,
  errors,
  defaultValues,
  formats,
  watch,
  setValue,
  isEdit,
  handleChangeDate,
  returnDefaultDate,
  currentTimezone,
}) => {
  const watchSaleActive = watch('sale.active')
  const savedFormatsRef = useRef([])

  useEffect(() => {
    if (!watchSaleActive) {
      // Сохраняем форматы, чтобы при повторном включении восстановить выбор
      const currentFormats = watch('sale.formats') || []

      setValue('sale.count', undefined)
      setValue('sale.discount', undefined)
      setValue('sale.start_date', undefined)
      setValue('sale.end_date', undefined)
      setValue('sale.description', undefined)
      // Полностью очищаем formats, чтобы не уходил в запрос
      setValue('sale.formats', undefined)

      // Сохраняем в ref чтобы вернуть при включении
      savedFormatsRef.current = currentFormats
    } else {
      // При включении восстанавливаем значения из defaultValues или сохранённые ранее
      setValue('sale.count', defaultValues.sale?.count)
      setValue('sale.discount', defaultValues.sale?.discount)
      setValue('sale.start_date', defaultValues.sale?.start_date)
      setValue('sale.end_date', defaultValues.sale?.end_date)
      setValue('sale.description', defaultValues.sale?.description)

      const restoreFormats = savedFormatsRef.current || defaultValues.sale?.formats || []
      setValue('sale.formats', restoreFormats)
    }
  }, [
    watchSaleActive,
    setValue,
    watch,
    defaultValues.sale?.count,
    defaultValues.sale?.description,
    defaultValues.sale?.discount,
    defaultValues.sale?.end_date,
    defaultValues.sale?.formats,
    defaultValues.sale?.start_date,
  ])

  const returnDate = (item) => {
    const timestamp = unixToMoment(item.start_time)

    if (timestamp.isValid()) {
      return timestamp.tz(currentTimezone).format('DD.MM.YYYY')
    }

    return null
  }

  if (!isEdit) {
    return null
  }

  return (
    <>
      <h5>Скидки</h5>

      <Row>
        <Col md={12}>
          <FormCheck
            {...register('sale.active')}
            defaultChecked={defaultValues.sale?.active}
            type="switch"
            id={`saleActiveSwitch-${defaultValues?.public_id}`}
            label="Активировать скидку"
          />
        </Col>
      </Row>

      {watchSaleActive && (
        <>
          <Row className="mt-3">
            <Col md={3}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="saleCountLabel" label="Количество билетов">
                  <FormControl
                    {...register('sale.count')}
                    type="number"
                    isInvalid={errors?.sale?.count}
                    onWheel={(e) => e.target.blur()}
                    step="1"
                    min="1"
                    placeholder=""
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="saleDiscountLabel" label="Размер скидки (%)">
                  <FormControl
                    {...register('sale.discount')}
                    type="number"
                    isInvalid={errors?.sale?.discount}
                    onWheel={(e) => e.target.blur()}
                    step="1"
                    min="0"
                    max="100"
                    placeholder=""
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="saleStartDateLabel" label="Начало действия">
                  <FormControl
                    onChange={handleChangeDate}
                    defaultValue={returnDefaultDate('sale.start_date')}
                    type="datetime-local"
                    name="sale.start_date"
                    isInvalid={errors?.sale?.start_date}
                    placeholder=""
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
            <Col md={3}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId="saleEndDateLabel" label="Окончание действия">
                  <FormControl
                    onChange={handleChangeDate}
                    defaultValue={returnDefaultDate('sale.end_date')}
                    type="datetime-local"
                    name="sale.end_date"
                    isInvalid={errors?.sale?.end_date}
                    placeholder=""
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="saleDescriptionLabel" label="Описание">
                  <FormControl
                    {...register('sale.description')}
                    type="text"
                    isInvalid={errors?.sale?.description}
                    placeholder=""
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
          </Row>

          {formats?.length && (
            <Row className="mb-3">
              <Col md={12}>
                <Form.Group>
                  <Controller
                    name="sale.formats"
                    control={control}
                    defaultValue={defaultValues?.sale?.formats || []}
                    rules={{
                      validate: (value) => {
                        if (!value || value.length === 0) {
                          return 'Выберите хотя бы один формат'
                        }
                        return true
                      },
                    }}
                    render={({ field: { onChange, value, ...field } }) => (
                      <Select
                        {...field}
                        value={formats
                          ?.filter((format) => value?.includes(format.public_id))
                          .map((format) => ({
                            value: format.public_id,
                            label: `${format.title} - ${returnDate(format)}`,
                          }))}
                        onChange={(selected) => {
                          onChange(selected ? selected.map((item) => item.value) : [])
                        }}
                        isMulti
                        options={formats?.map((format) => ({
                          value: format.public_id,
                          label: `${format.title} - ${returnDate(format)}`,
                        }))}
                        styles={eventCitySelectStyles}
                        placeholder="Выберите форматы"
                      />
                    )}
                  />
                  {errors?.sale?.formats && (
                    <div className="invalid-feedback" style={{ display: 'block' }}>
                      {errors.sale.formats.message}
                    </div>
                  )}
                </Form.Group>
              </Col>
            </Row>
          )}
        </>
      )}

      <hr style={{ color: '#dcdcdf' }} />
    </>
  )
}
