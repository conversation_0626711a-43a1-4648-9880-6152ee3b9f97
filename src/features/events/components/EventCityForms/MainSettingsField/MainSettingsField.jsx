import { Col, <PERSON><PERSON>he<PERSON>, <PERSON> } from 'react-bootstrap'

/**
 * Компонент для отображения основных настроек события
 * @param {Object} props - Пропсы компонента
 * @param {Function} props.register - Функция регистрации полей из react-hook-form
 * @param {Object} props.defaultValues - Объект с дефолтными значениями формы
 * @param {string} props.defaultValues.public_id - Публичный ID для создания уникальных ID элементов
 */
export const MainSettingsField = ({ register, defaultValues }) => {
  return (
    <div className="mb-4">
      <h6 className="text-muted mb-3">Основные настройки</h6>
      <Row className="g-3">
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck
            {...register('public')}
            type="switch"
            id={`publicSwitch-${defaultValues?.public_id}`}
            label="Публичный"
          />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck
            {...register('corp')}
            type="switch"
            id={`corpSwitch-${defaultValues?.public_id}`}
            label="Корпоративное участие"
          />
        </Col>
        <Col xs={12} sm={6} md={4} lg={3}>
          <FormCheck {...register('qr')} type="switch" id={`qrSwitch-${defaultValues?.public_id}`} label="QR-код" />
        </Col>
      </Row>
    </div>
  )
}
