import { AccordionBody, AccordionHeader, AccordionItem, Button, Card } from 'react-bootstrap'

import { useGetEventCityFormats } from '@/features/events/api/getEventCityFormats'
import { unixToMoment } from '@/utils/date'

import { CityForm } from './CityForm'
import { Formats } from './Formats'

export const AccordionCityItem = ({
  city,
  eventPublicId,
  activeAccordionItem,
  setActiveAccordionItem,
  onAddFormat,
  onEditFormat,
  onCopyFormat,
}) => {
  const getEventCityFormatsQuery = useGetEventCityFormats(
    activeAccordionItem === city.public_id ? city.public_id : null
  )
  const formats = getEventCityFormatsQuery?.data?.data

  return (
    <AccordionItem eventKey={city.public_id}>
      <AccordionHeader>
        {city.address} — {unixToMoment(city.start_time).tz(city.timezone).format('DD.MM.YYYY')}
      </AccordionHeader>
      <AccordionBody>
        <Card className="mb-3">
          <Card.Body>
            <CityForm
              defaultFormData={city}
              eventPublicId={eventPublicId}
              setActiveAccordionItem={setActiveAccordionItem}
              formats={formats}
            />
          </Card.Body>
        </Card>

        <Card>
          <Card.Body>
            <h5>
              Форматы{' '}
              <Button onClick={() => onAddFormat(city)} variant="link" size="sm">
                добавить
              </Button>
            </h5>

            {activeAccordionItem === city.public_id && (
              <Formats
                eventCityPublicId={city.public_id}
                timezone={city.timezone}
                city={city}
                onEdit={onEditFormat}
                onCopy={onCopyFormat}
              />
            )}
          </Card.Body>
        </Card>
      </AccordionBody>
    </AccordionItem>
  )
}
