import { useRef, useState } from 'react'
import { <PERSON><PERSON>, Col, Form, Row } from 'react-bootstrap'

import { convertBase64 } from '@/utils/common'

import { ReturnLink } from '../ReturnLink'

export const FileUploadField = ({
  fieldName,
  label,
  accept = '.pdf, .docx',
  defaultValue,
  watch,
  setValue,
  defaultFormData,
}) => {
  const fileInputRef = useRef(null)
  const [isViewInput, setIsViewInput] = useState(!defaultValue)

  const handleRemoveFile = () => {
    setIsViewInput(true)
    setValue(fieldName, null)
  }

  const handleResetInput = () => {
    fileInputRef.current.value = ''
    if (defaultValue) {
      setValue(fieldName, null)
    } else {
      setValue(fieldName, undefined)
    }
  }

  const handleHideInput = () => {
    setIsViewInput(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    setValue(fieldName, undefined)
  }

  const handleChangeFile = async (evt) => {
    const file = evt.target.files[0]
    const base64 = await convertBase64(file)
    setValue(fieldName, base64, { shouldDirty: true })
  }

  return (
    <Col md={12}>
      {isViewInput && (
        <Form.Group controlId={`formFile-${fieldName}-${defaultFormData?.public_id}`} className="mb-3">
          <Row>
            <Col md="auto">
              <Form.Label>
                {label} ({accept.replace(/\./g, '').replace(/,/g, ', ')}){' '}
              </Form.Label>
            </Col>
            <Col md="auto">
              {Boolean(defaultValue) && (
                <Button onClick={handleHideInput} variant="link" size="sm">
                  отмена
                </Button>
              )}
              {Boolean(watch(fieldName)) && (
                <Button onClick={handleResetInput} variant="link" size="sm">
                  очистить
                </Button>
              )}
            </Col>
          </Row>
          <Form.Control
            onChange={handleChangeFile}
            type="file"
            accept={accept}
            size="lg"
            lang="ru"
            ref={fileInputRef}
          />
        </Form.Group>
      )}

      {!isViewInput && (
        <div className="mb-3">
          <p>
            {label} загружено:{' '}
            <Button onClick={handleRemoveFile} variant="link" size="sm">
              обновить/удалить
            </Button>
          </p>
          <ReturnLink link={defaultValue} />
        </div>
      )}
    </Col>
  )
}
