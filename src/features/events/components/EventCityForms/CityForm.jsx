import moment from 'moment-timezone'
import { useEffect, useRef, useState, useMemo, useCallback } from 'react'
import { Button, Col, Floating<PERSON>abel, Form, FormControl, Row } from 'react-bootstrap'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import Select from 'react-select'

import IconCopy from '@/assets/img/icons/icon-copy.svg?react'

import ImageField from '@/components/Forms/ImageField/ImageField'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import { TimezoneDisplay } from '@/components/TimezoneDisplay'

import { useGetBanners } from '@/features/banners/api/getBanners'
import { useGetCountryRegions } from '@/features/cities/api/getCountryRegions'
import { useGetRegionCities } from '@/features/cities/api/getRegionCities'
import { useCreateEventCity } from '@/features/events/api/createEventCity'
import { useDeleteEventCity } from '@/features/events/api/deleteEventCity'
import { useUpdateEventCity } from '@/features/events/api/updateEventCity'
import { CloneCityModal } from '@/features/events/components/CloneCityModal/CloneCityModal'
import { eventCitySelectStyles } from '@/features/events/components/EventCityForms/eventCitySelect'
import { FileUploadField } from '@/features/events/components/EventCityForms/FileUploadField/FileUploadField'
import { MainSettingsField } from '@/features/events/components/EventCityForms/MainSettingsField/MainSettingsField'
import { SaleField } from '@/features/events/components/EventCityForms/SaleField/SaleField'
import { TicketRestrictionsField } from '@/features/events/components/EventCityForms/TicketRestrictionsField/TicketRestrictionsField'
import { MerchField } from '@/features/events/components/MerchField/MerchField'
import { RegistrationScheduleField } from '@/features/events/components/RegistrationScheduleField/RegistrationScheduleField'
import { RequirementField } from '@/features/events/components/RequirementField/RequirementField'
import { ScheduleField } from '@/features/events/components/ScheduleField/ScheduleField'
import { UserRolesField } from '@/features/events/components/UserRolesField'
import { getChangedValues } from '@/helpers/getChangedValues'
import { useToast } from '@/hooks/useToast'
import { getOptionsSelect, removeEmptyString2, generateRegionOptions } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

import styles from './EventCityForms.module.scss'
import { transformFormDataForBackend } from '../ScheduleField/utils/scheduleDataTransform'

export const CityForm = ({ defaultFormData = {}, eventPublicId, onCloseModal, setActiveAccordionItem, formats }) => {
  const convertScheduleUnixToIso = useCallback((schedule) => {
    if (!schedule || !Array.isArray(schedule)) return []

    return schedule.map((day) => {
      const newDay = { ...day }

      if (day.day && !isNaN(Number(day.day))) {
        newDay.day = unixToMoment(Number(day.day)).utc().format()
      }

      if (day.times && Array.isArray(day.times)) {
        newDay.times = day.times.map((timeSlot) => {
          const newTimeSlot = { ...timeSlot }

          if (timeSlot.start_time && !isNaN(Number(timeSlot.start_time))) {
            newTimeSlot.start_time = unixToMoment(Number(timeSlot.start_time)).utc().format()
          }

          if (timeSlot.end_time && !isNaN(Number(timeSlot.end_time))) {
            newTimeSlot.end_time = unixToMoment(Number(timeSlot.end_time)).utc().format()
          }

          return newTimeSlot
        })
      }

      return newDay
    })
  }, [])

  const defaultValues = useMemo(
    () => ({
      public_id: defaultFormData?.public_id,
      address: defaultFormData?.address,
      geo: {
        lat: defaultFormData?.geo?.lat,
        lon: defaultFormData?.geo?.lon,
      },
      external_url: defaultFormData?.external_url,
      // results_link: defaultFormData?.results_link,
      public: defaultFormData?.public || '',
      ticket_edit_blocked: defaultFormData?.ticket_edit_blocked || '',
      ticket_transfer_blocked: defaultFormData?.ticket_transfer_blocked || '',
      qr: defaultFormData?.qr || '',
      corp: defaultFormData?.corp || false,
      info: {
        registration_address: defaultFormData?.info?.registration_address,
        start_address: defaultFormData?.info?.start_address,
        public_transport: defaultFormData?.info?.public_transport,
        registration_schedule: defaultFormData?.info?.registration_schedule || [],
        registration_time: defaultFormData?.info?.registration_time,
        legals: defaultFormData?.info?.legals,
        guide: defaultFormData?.info?.guide,
      },
      shirts: defaultFormData?.shirts || [],
      banners: defaultFormData?.banners || [],
      merch: defaultFormData?.merch || [],
      requirements: defaultFormData?.requirements || [],
      schedule: convertScheduleUnixToIso(defaultFormData?.schedule) || [],
      sale: {
        discount: defaultFormData?.sale?.discount,
        discount_form: 'percent',
        count: defaultFormData?.sale?.count,
        active: defaultFormData?.sale?.active,
        formats: defaultFormData?.sale?.formats,
        description: defaultFormData?.sale?.description,
        start_date: defaultFormData?.sale?.start_date,
        end_date: defaultFormData?.sale?.end_date,
      },
      registration_open: defaultFormData?.registration_open,
      registration_close: defaultFormData?.registration_close,
      start_time: defaultFormData?.start_time,
      end_time: defaultFormData?.end_time,
      roles: {
        city_admin: defaultFormData?.roles?.city_admin || [],
        city_editor: defaultFormData?.roles?.city_editor || [],
        viewer: defaultFormData?.roles?.viewer || [],
      },
    }),
    [defaultFormData, convertScheduleUnixToIso]
  )

  const {
    watch,
    reset,
    control,
    register,
    setValue,
    getValues,
    unregister,
    formState: { errors, dirtyFields },
    handleSubmit,
  } = useForm({
    defaultValues: {
      public: true,
      ticket_edit_blocked: false,
      ticket_transfer_blocked: false,
      qr: '',
      corp: false,
      sale: defaultValues.sale,
    },
  })

  const [region, setRegion] = useState(null)
  const registrationOpenRef = useRef(null)
  const registrationCloseRef = useRef(null)
  const startTimeRef = useRef(null)
  const endTimeRef = useRef(null)
  const registrationTimeRef = useRef(null)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const [datesLocal] = useState({
    registration_open: registrationOpenRef,
    registration_close: registrationCloseRef,
    start_time: startTimeRef,
    end_time: endTimeRef,
    registration_time: registrationTimeRef,
  })
  const [showCopyModal, setShowCopyModal] = useState(false)
  const [currentTimezone, setCurrentTimezone] = useState(defaultFormData?.timezone ?? moment.tz.guess())
  const [scheduleChanged, setScheduleChanged] = useState(false)

  const openToast = useToast()

  const {
    fields: shirts,
    append: appendShirt,
    remove: removeShirt,
  } = useFieldArray({
    control,
    name: 'shirts',
  })

  const {
    fields: banners,
    append: appendBanner,
    remove: removeBanner,
    swap: swapBanner,
  } = useFieldArray({
    control,
    name: 'banners',
  })

  const createEventCityMutate = useCreateEventCity(eventPublicId, onCloseModal)
  const updateEventCityMutate = useUpdateEventCity(eventPublicId)
  const deleteEventCityMutate = useDeleteEventCity(eventPublicId)
  const getCountryRegionsQuery = useGetCountryRegions('RU')
  const getBannersQuery = useGetBanners('events')
  const countryRegions = getCountryRegionsQuery?.data?.data?.values
  const bannersEvents = getBannersQuery?.data?.data?.events?.events

  const getRegionCitiesQuery = useGetRegionCities(region)

  const regionCities = getRegionCitiesQuery?.data?.data?.values

  const isEdit = Object.keys(defaultFormData).length > 0

  useEffect(() => {
    if (isEdit) {
      reset(defaultValues)
    }
  }, [defaultFormData, defaultValues, isEdit, reset])

  const handleClickSubmit = () => {
    // Берём все значения и формируем объект только изменённых полей
    const allValues = getValues()
    const changedInitial = isEdit ? getChangedValues(dirtyFields, allValues, defaultValues) : allValues

    const filteredData = removeEmptyString2(changedInitial)
    // Transform schedule data to proper array format before sending to backend
    const transformedData = transformFormDataForBackend(filteredData)

    if (isEdit) {
      if (!Object.prototype.hasOwnProperty.call(defaultFormData, 'shirts') && transformedData?.shirts?.length === 0) {
        delete transformedData.shirts
      }

      // Если расписание не изменилось, удаляем его из payload
      if (!scheduleChanged) {
        delete transformedData.schedule
      }

      if (Object.keys(transformedData)?.length > 0) {
        updateEventCityMutate.mutate({ id: defaultFormData.public_id, data: transformedData })
      } else {
        openToast.warning({ message: 'Вы не внесли никаких изменений.' })
      }
    } else {
      if (Object.prototype.hasOwnProperty.call(transformedData, 'shirts') && transformedData.shirts.length === 0) {
        delete transformedData.shirts
      }

      if (Object.prototype.hasOwnProperty.call(transformedData, 'merch') && transformedData.merch.length === 0) {
        delete transformedData.merch
      }

      transformedData.event_public_id = eventPublicId
      createEventCityMutate.mutate(transformedData)
    }
  }

  const handleChangeRegion = (option) => {
    setRegion(option.value)
    setCurrentTimezone(option.timezone)

    Object.entries(datesLocal).forEach(([key, value]) => {
      if (value.current && value.current.value !== '') {
        const newDate = unixToMoment(value.current.value).tz(option?.timezone).utc().format('YYYY-MM-DDTHH:mm:ssZ')

        const currentVal = getValues(key)
        setValue(key, newDate, { shouldDirty: currentVal !== newDate })
      }
    })
  }

  const handleChangeCity = (option) => {
    setValue('city.id', option.value)
  }

  const handleChangeDate = (evt) => {
    const name = evt.target.name
    const value = evt.target.value
    // Приводим дату к формату ISO с часовым поясом «+00:00»
    const formatValue = moment.tz(value, currentTimezone).utc().format('YYYY-MM-DDTHH:mm:ssZ')

    // Обновляем значение и помечаем его "грязным" только если оно действительно изменилось
    const currentFormValue = getValues(name)
    const isDirty = currentFormValue !== formatValue

    setValue(name, formatValue, { shouldDirty: isDirty })
  }

  const getValueByPath = (obj, path) => {
    return path.split('.').reduce((acc, key) => (acc && acc[key] ? acc[key] : undefined), obj)
  }

  const returnDefaultDate = (key) => {
    const value = getValueByPath(defaultFormData, key)

    if (value) {
      const timezone = defaultFormData?.timezone || moment.tz.guess()
      // const time = moment.utc(value).tz(timezone)
      const time = unixToMoment(value).tz(timezone)
      if (time.isValid()) {
        return time.format('YYYY-MM-DDTHH:mm:ss')
      }
    }

    return ''
  }

  const handleAddShirt = () => {
    appendShirt({
      size: '',
      count: '',
    })
  }

  const handleAddBanner = (evt) => {
    const selectedId = evt?.target?.value

    appendBanner({
      public_id: selectedId,
    })
  }

  const handleDeleteItem = () => {
    deleteEventCityMutate.mutate(defaultFormData.public_id)
  }

  const returnSelectedBannerSrc = (publicId) => {
    const findBanner = bannersEvents?.find((banner) => banner.public_id === publicId)

    return getImageSrc(findBanner?.picture_main || findBanner?.picture_small)
  }

  const handleCopyCity = () => {
    setShowCopyModal(true)
  }

  return (
    <Form onSubmit={handleSubmit(handleClickSubmit)}>
      {/* Отображение часового пояса и времени */}
      <TimezoneDisplay timezone={currentTimezone} className="mb-3" />

      <Row className="mb-3">
        {!isEdit && (
          <Col md={4}>
            {countryRegions?.length > 0 && (
              <Select
                onChange={handleChangeRegion}
                styles={eventCitySelectStyles}
                options={generateRegionOptions(countryRegions)}
                placeholder="Регион"
                isDisabled={isEdit}
                required={!isEdit}
              />
            )}
          </Col>
        )}
        <Col md={4}>
          {regionCities?.length > 0 ? (
            <Controller
              name="city.id"
              control={control}
              render={() => (
                <Select
                  {...(isEdit && {
                    defaultValue: [
                      {
                        value: defaultFormData?.city?.id,
                        label: defaultFormData?.city?.name_ru,
                      },
                    ],
                  })}
                  onChange={handleChangeCity}
                  styles={eventCitySelectStyles}
                  options={getOptionsSelect(regionCities, 'id', 'name_ru')}
                  placeholder="Город"
                  isDisabled={isEdit}
                  required={!isEdit}
                />
              )}
            />
          ) : (
            <Select
              {...(isEdit && {
                defaultValue: [
                  {
                    value: defaultFormData?.city?.id,
                    label: defaultFormData?.city?.name_ru,
                  },
                ],
              })}
              styles={eventCitySelectStyles}
              placeholder="Город"
              isDisabled={true}
            />
          )}
        </Col>
        <Col md={isEdit ? 8 : 4}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`addressLabel-${defaultFormData?.public_id}`} label="Адрес">
              <FormControl {...register('address')} type="text" isInvalid={errors?.address} placeholder="" />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <Row>
        <Col md={4}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`publicIdLabel-${defaultFormData?.public_id}`} label="Идентификатор">
              <FormControl
                {...register('public_id', {
                  required: !isEdit,
                  disabled: isEdit,
                })}
                type="text"
                isInvalid={errors?.public_id}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={4}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`externalUrlLabel-${defaultFormData?.public_id}`} label="Внешняя ссылка">
              <FormControl {...register('external_url')} type="text" isInvalid={errors?.external_url} placeholder="" />
            </FloatingLabel>
          </Form.Group>
        </Col>

        {/* <Col md={4}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="resultsLinkLabel" label="Внешние результаты">
              <FormControl
                {...register('results_link', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.results_link),
                })}
                type="text"
                isInvalid={errors?.results_link}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col> */}
      </Row>

      <Row className="mb-3">
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`registrationOpenLabel-${defaultFormData?.public_id}`} label="Начало продаж *">
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('registration_open')}
                type="datetime-local"
                name="registration_open"
                isInvalid={errors?.registration_open}
                ref={registrationOpenRef}
                placeholder=""
                required={!isEdit}
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`registrationCloseLabel-${defaultFormData?.public_id}`} label="Закрытие продаж *">
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('registration_close')}
                type="datetime-local"
                name="registration_close"
                isInvalid={errors?.registration_close}
                ref={registrationCloseRef}
                required={!isEdit}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`startTimeLabel-${defaultFormData?.public_id}`} label="Старт *">
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('start_time')}
                type="datetime-local"
                name="start_time"
                isInvalid={errors?.start_time}
                ref={startTimeRef}
                required={!isEdit}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`endTimeLabel-${defaultFormData?.public_id}`} label="Окончание *">
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('end_time')}
                type="datetime-local"
                name="end_time"
                isInvalid={errors?.end_time}
                ref={endTimeRef}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      {/* Основные настройки */}
      <MainSettingsField register={register} defaultValues={defaultValues} />

      {/* Ограничения билетов */}
      <TicketRestrictionsField register={register} defaultValues={defaultValues} />

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col md={5}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`infoStartAddressLabel-${defaultFormData?.public_id}`} label="Адрес старта">
              <FormControl
                {...register('info.start_address')}
                type="text"
                isInvalid={errors?.info?.start_address}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={5}>
          <Form.Group className="mb-3">
            <FloatingLabel
              controlId={`infoRegistrationAddressLabel-${defaultFormData?.public_id}`}
              label="Адрес регистрации"
            >
              <FormControl
                {...register('info.registration_address')}
                type="text"
                isInvalid={errors?.info?.registration_address}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={2}>
          <Form.Group className="mb-3">
            <FloatingLabel
              controlId={`infoRegistrationTimeLabel-${defaultFormData?.public_id}`}
              label="Дата регистрации *"
            >
              <FormControl
                onChange={handleChangeDate}
                defaultValue={returnDefaultDate('info.registration_time')}
                name="info.registration_time"
                type="datetime-local"
                isInvalid={errors?.info?.registration_time}
                ref={registrationTimeRef}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col className="mb-3" md={12}>
          <RegistrationScheduleField control={control} register={register} errors={errors} />
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel
              controlId={`infoPublicTransportLabel-${defaultFormData?.public_id}`}
              label="Траспорт (как добраться)"
            >
              <FormControl
                {...register('info.public_transport')}
                type="text"
                isInvalid={errors?.info?.public_transport}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <hr style={{ color: '#dcdcdf' }} />

        <FileUploadField
          fieldName="info.legals"
          label="Положение/Регламент"
          accept=".pdf, .docx"
          defaultValue={defaultFormData?.info?.legals}
          watch={watch}
          setValue={setValue}
          defaultFormData={defaultFormData}
        />

        <hr style={{ color: '#dcdcdf' }} />

        <FileUploadField
          fieldName="info.guide"
          label="Гайд"
          accept=".pdf, .docx"
          defaultValue={defaultFormData?.info?.guide}
          watch={watch}
          setValue={setValue}
          defaultFormData={defaultFormData}
        />
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`geoLatLabel-${defaultFormData?.public_id}`} label="Широта">
              <FormControl
                {...register('geo.lat', {
                  validate: (value) => {
                    if (value && (value < -90 || value > 90)) {
                      return 'Широта должна быть от -90 до 90'
                    }
                    return true
                  },
                  valueAsNumber: true,
                })}
                isInvalid={errors?.geo?.lat}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId={`geoLonLabel-${defaultFormData?.public_id}`} label="Долгота">
              <FormControl
                {...register('geo.lon', {
                  validate: (value) => {
                    if (value && (value < -180 || value > 180)) {
                      return 'Долгота должна быть от -180 до 180'
                    }
                    return true
                  },
                  valueAsNumber: true,
                })}
                isInvalid={errors?.geo?.lon}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col>
          <ImageField
            fieldName="info.map_full"
            imagePath={defaultFormData?.info?.map_full}
            isEdit={false}
            setValue={setValue}
            fieldId={defaultFormData?.public_id}
            title="Карта мероприятия"
            unregister={unregister}
          />
        </Col>
        <Col>
          <ImageField
            fieldName="info.map_preview"
            imagePath={defaultFormData?.info?.map_preview}
            isEdit={isEdit}
            setValue={setValue}
            fieldId={defaultFormData?.public_id}
            title="Превью карты"
            unregister={unregister}
          />
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <Row>
        <Col>
          <ImageField
            fieldName="poster"
            imagePath={defaultFormData?.poster}
            isEdit={isEdit}
            setValue={setValue}
            fieldId={defaultFormData?.public_id}
            accept=".jpg, .jpeg, .png, .webp, .svg"
            title="Постер мероприятия"
            unregister={unregister}
          />
        </Col>
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <h5>
        Футболки{' '}
        <Button onClick={handleAddShirt} variant="link" size="sm">
          <i className="bi bi-plus-circle me-2" />
        </Button>
      </h5>

      <Row>
        {shirts.map((item, index) => (
          <Col md={2} key={item.id}>
            <div style={{ border: '1px solid #dcdcdf', borderRadius: '4px', padding: '12px' }}>
              <Form.Group className="mb-3">
                <FloatingLabel controlId={`shirtSizeLabel-${defaultFormData?.public_id}-${index}`} label="Размер">
                  <FormControl
                    {...register(`shirts.${index}.size`)}
                    type="text"
                    isInvalid={errors?.shirts?.[index]?.size}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>
              <Form.Group className="mb-3">
                <FloatingLabel controlId={`shirtCountLabel-${defaultFormData?.public_id}-${index}`} label="Количество">
                  <FormControl
                    {...register(`shirts.${index}.count`)}
                    type="number"
                    isInvalid={errors?.shirts?.[index]?.count}
                    onWheel={(e) => e.target.blur()}
                    placeholder=""
                  />
                </FloatingLabel>
              </Form.Group>

              <Row>
                <Col />
                <Col md="auto">
                  <Button onClick={() => removeShirt(index)} variant="outline-danger" size="sm">
                    <i className="bi bi-trash3-fill" />
                  </Button>
                </Col>
              </Row>
            </div>
          </Col>
        ))}
      </Row>

      <MerchField control={control} register={register} errors={errors} />

      <hr style={{ color: '#dcdcdf' }} />

      <RequirementField control={control} register={register} errors={errors} />

      <ScheduleField
        control={control}
        register={register}
        errors={errors}
        setValue={setValue}
        currentTimezone={currentTimezone}
        onScheduleChange={setScheduleChanged}
      />

      <h5>Баннеры</h5>

      <Row>
        {banners?.map((item, index) => (
          <Col md={1} key={item.id}>
            <Row>
              <Col md={12}>
                <Button className={styles.linkDelete} onClick={() => removeBanner(index)} variant="link" size="sm">
                  удалить
                </Button>
              </Col>
              <Col className="mb-1" md={12}>
                <div className={styles.bannerWrap}>
                  <img className={styles.cardImg} src={returnSelectedBannerSrc(item.public_id)} width="60" alt="" />
                </div>
              </Col>
              <Col>
                <Row className="justify-content-between">
                  <Col md="auto">
                    <Button
                      onClick={() => swapBanner(index, index - 1)}
                      variant="light"
                      size="sm"
                      disabled={index === 0}
                    >
                      {'<'}
                    </Button>
                  </Col>
                  <Col md="auto">
                    <Button
                      onClick={() => swapBanner(index, index + 1)}
                      variant="light"
                      size="sm"
                      disabled={index === banners.length - 1}
                    >
                      {'>'}
                    </Button>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>
        ))}

        {bannersEvents
          ?.filter((el) => !banners.some((banner) => banner.public_id === el.public_id))
          .map((item) => (
            <Col md={1} key={item.public_id}>
              <Row>
                <Col md={12}>
                  <Button onClick={handleAddBanner} value={item.public_id} variant="link" size="sm">
                    выбрать
                  </Button>
                </Col>
                <Col md={12}>
                  <div className={styles.bannerWrap}>
                    <img
                      className={styles.cardImg}
                      src={getImageSrc(item.picture_main || item.picture_small)}
                      width="60"
                      alt=""
                    />
                  </div>
                </Col>
              </Row>
            </Col>
          ))}
      </Row>

      <hr style={{ color: '#dcdcdf' }} />

      <SaleField
        control={control}
        register={register}
        errors={errors}
        defaultValues={defaultValues}
        formats={formats}
        watch={watch}
        setValue={setValue}
        isEdit={isEdit}
        handleChangeDate={handleChangeDate}
        returnDefaultDate={returnDefaultDate}
        currentTimezone={currentTimezone}
      />

      <UserRolesField register={register} setValue={setValue} defaultRoles={defaultFormData?.roles} />

      <Row>
        <Col>
          {isEdit && (
            <Button onClick={handleCopyCity} variant="outline-light" size="sm">
              <IconCopy />
            </Button>
          )}
        </Col>
        <Col md="auto">
          <Button variant="success" type="submit">
            {isEdit ? 'Обновить город' : 'Добавить город'}
          </Button>
        </Col>
        {isEdit && (
          <Col md="auto">
            <Button onClick={() => setConfirmDeleteModal(true)} variant="danger">
              Удалить
            </Button>
          </Col>
        )}
      </Row>

      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />

      <CloneCityModal
        show={showCopyModal}
        onHide={() => setShowCopyModal(false)}
        eventPublicId={eventPublicId}
        cityPublicId={defaultFormData?.public_id}
        onSuccess={setActiveAccordionItem}
      />
    </Form>
  )
}
