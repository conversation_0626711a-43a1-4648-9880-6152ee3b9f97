import { useState } from 'react'
import { Accordion, Button, Col, Modal, Row } from 'react-bootstrap'

import { useGetEventCities } from '@/features/events/api/getEventCities'

import { AccordionCityItem } from './AccordionCityItem'
import { CityForm } from './CityForm'
import { FormatForm } from '../FormatForm/FormatForm'

export const EventCityForms = ({ eventPublicId }) => {
  const [isOpenCreateForm, setIsOpenCreateForm] = useState(false)
  const [isOpenFormatForm, setIsOpenFormatForm] = useState(false)
  const [selectedFormat, setSelectedFormat] = useState({})
  const [copySelectedFormat, setCopySelectedFormat] = useState({})
  const [selectedCity, setSelectedCity] = useState({})
  const [timezone, setTimezone] = useState(null)
  const [activeAccordionItem, setActiveAccordionItem] = useState('')

  const getEventCitiesQuery = useGetEventCities(eventPublicId)
  const cities = getEventCitiesQuery?.data?.data?.values

  const isEditFormat = Object.keys(selectedFormat).length > 0

  const handleAddFormat = (city) => {
    setTimezone(city.timezone)
    setSelectedCity(city)
    setIsOpenFormatForm(true)
  }

  const handleEditFormat = (format, city) => {
    setSelectedFormat(format)
    setTimezone(city.timezone)
    setSelectedCity(city)
    setIsOpenFormatForm(true)
  }

  const handleCopyFormat = (format, city) => {
    setCopySelectedFormat(format)
    setTimezone(city.timezone)
    setSelectedCity(city)
    setIsOpenFormatForm(true)
  }

  const handleCloseFormatForm = () => {
    setSelectedCity({})
    setSelectedFormat({})
    setCopySelectedFormat({})
    setTimezone(null)
    setIsOpenFormatForm(false)
  }

  return (
    <>
      <Row className="mb-5 justify-content-center">
        <Col md="auto">
          <Button onClick={() => setIsOpenCreateForm(true)} variant="success">
            Добавить город
          </Button>
        </Col>
      </Row>

      <Modal show={isOpenCreateForm} onHide={() => setIsOpenCreateForm(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>Добавить город</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <CityForm eventPublicId={eventPublicId} onCloseModal={() => setIsOpenCreateForm(false)} formats={[]} />
        </Modal.Body>
      </Modal>

      <Modal show={isOpenFormatForm} onHide={handleCloseFormatForm} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>{isEditFormat ? 'Редактирование формата' : 'Добавление формата'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <FormatForm
            defaultFormData={selectedFormat}
            copyFormData={copySelectedFormat}
            city={selectedCity}
            currentTimezone={timezone}
            onCloseModal={handleCloseFormatForm}
          />
        </Modal.Body>
      </Modal>

      <Accordion onSelect={(key) => setActiveAccordionItem(key)} activeKey={activeAccordionItem}>
        {cities?.map((city) => (
          <AccordionCityItem
            key={city.public_id}
            city={city}
            eventPublicId={eventPublicId}
            activeAccordionItem={activeAccordionItem}
            setActiveAccordionItem={setActiveAccordionItem}
            onAddFormat={handleAddFormat}
            onEditFormat={handleEditFormat}
            onCopyFormat={handleCopyFormat}
          />
        ))}
      </Accordion>
    </>
  )
}
