export const eventCitySelectStyles = {
  control: (provided, state) => ({
    ...provided,
    padding: '11px 4px',
    border: '0',
    borderRadius: '4px',
    backgroundColor: state.isDisabled ? '#e9ecef' : '#f5f7f9',
  }),

  container: (provided) => ({
    ...provided,
    border: '0',

    color: 'black',
  }),

  singleValue: (provided) => ({
    ...provided,
    color: '#160e4d',
  }),

  placeholder: (provided) => ({
    ...provided,
    color: '#160e4d',
    fontWeight: '400',
  }),

  indicatorSeparator: () => ({
    display: 'none',
  }),

  menu: (provided) => ({
    ...provided,
    padding: '0',
    borderRadius: '4px',
    overflow: 'hidden',
    zIndex: 3,
  }),
}
