import { useMutation } from 'react-query'
import { useNavigate } from 'react-router-dom'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createEvent = (data) => {
  return axios.post(APIRoute.CU_EVENT, data)
}

export const useCreateEvent = () => {
  const openToast = useToast()
  const navigate = useNavigate()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('createEvent')
    },
    onSuccess: (data) => {
      const eventPublicId = data?.data?.public_id

      if (data?.status === 200 && eventPublicId) {
        queryClient.invalidateQueries('events')
        navigate(`${AppRoute.EVENTS_EDIT}/${eventPublicId}`)
        openToast.success({ message: 'Событие добавлено!' })
      }
    },
    mutationFn: createEvent,
  })
}
