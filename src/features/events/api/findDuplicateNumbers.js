import { useMutation } from 'react-query'

import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const findDuplicateNumbers = (data) => {
  return axios.post('/api/admin/dublicate/soldgood/numbers', data)
}

export const useFindDuplicateNumbers = () => {
  const openToast = useToast()

  return useMutation({
    onError: (error) => {
      openToast.error({ message: error?.response?.data?.message || 'Ошибка при поиске дубликатов' })
    },
    mutationFn: findDuplicateNumbers,
  })
}
