import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const updateEvent = ({ id, data }) => {
  return axios.put(`${APIRoute.CU_EVENT}/${id}`, data)
}

export const useUpdateEvent = (id) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['event', id])
        openToast.success({ message: 'Событие обновлено!' })
      }
    },
    mutationFn: updateEvent,
  })
}
