import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 60

const getEventCity = (publicId) => {
  return axios.get(`${APIRoute.CUD_EVENT_CITY}/${publicId}`)
}

export const useGetEventCity = (publicId) => {
  return useQuery({
    enabled: publicId?.length > 0,
    queryKey: ['eventCity', publicId],
    queryFn: () => getEventCity(publicId),
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
  })
}
