import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteEventCity = (id) => {
  return axios.delete(`${APIRoute.CUD_EVENT_CITY}/${id}`)
}

export const useDeleteEventCity = (id) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['eventCities', id])

        openToast.success({ message: 'Город успешно удалён!' })
      }
    },
    mutationFn: deleteEventCity,
  })
}
