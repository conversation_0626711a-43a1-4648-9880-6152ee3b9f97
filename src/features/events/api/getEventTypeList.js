import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 30

const getEventTypeList = () => {
  return axios.get(APIRoute.EVENT_TYPE_LIST)
}

export const useGetEventTypeList = () => {
  return useQuery({
    queryKey: ['eventTypeList'],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: getEventTypeList,
  })
}
