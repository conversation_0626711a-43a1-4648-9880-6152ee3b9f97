import { useMutation } from 'react-query'

import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const useUpdateTicketNumbers = (onSuccess) => {
  const openToast = useToast()

  return useMutation({
    mutationFn: async (formatPublicId) => {
      const response = await axios.put(`/api/admin/ticket/numbers/event-format/${formatPublicId}`)
      return response.data
    },
    onSuccess: (data) => {
      openToast.success({ message: 'Номера билетов успешно перезаписаны' })
      if (onSuccess) {
        onSuccess(data)
      }
    },
  })
}
