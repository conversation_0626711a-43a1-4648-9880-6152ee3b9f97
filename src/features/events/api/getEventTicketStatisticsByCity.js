import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const EVENT_TICKET_STATISTICS_BY_CITY_QUERY_KEY = 'eventTicketStatisticsByCity'

const getEventTicketStatisticsByCity = (data) => {
  return axios.post(APIRoute.EVENT_TICKET_STATISTICS_CITY, data)
}

export const useGetEventTicketStatisticsByCity = () => {
  return useMutation({
    mutationKey: EVENT_TICKET_STATISTICS_BY_CITY_QUERY_KEY,
    mutationFn: getEventTicketStatisticsByCity,
  })
}
