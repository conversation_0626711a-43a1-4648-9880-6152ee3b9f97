import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const cloneEventCity = ({ data }) => {
  return axios.post(APIRoute.CLONE_EVENT_CITY, data)
}

export const useCloneEventCity = (id, onCloseModal, onSuccess) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['eventCities', id])
        onCloseModal()
        openToast.success({ message: 'Город успешно скопирован!' })
        if (onSuccess && data?.data?.public_id) {
          onSuccess(data.data.public_id)
        }
      }
    },
    mutationFn: cloneEventCity,
  })
}
