import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const EVENT_TICKET_STATISTICS_BY_FORMAT_QUERY_KEY = 'eventTicketStatisticsByFormat'

const getEventTicketStatisticsByFormat = (data) => {
  return axios.post(APIRoute.EVENT_TICKET_STATISTICS_FORMAT, data)
}

export const useGetEventTicketStatisticsByFormat = () => {
  return useMutation({
    mutationKey: EVENT_TICKET_STATISTICS_BY_FORMAT_QUERY_KEY,
    mutationFn: getEventTicketStatisticsByFormat,
  })
}
