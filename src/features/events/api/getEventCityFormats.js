import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getEventCityFormats = (publicId) => {
  return axios.get(`${APIRoute.EVENT_FORMAT_LIST}/${publicId}`)
}

export const useGetEventCityFormats = (publicId) => {
  return useQuery({
    enabled: publicId?.length > 0,
    queryKey: ['eventCityFormats', publicId],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: () => getEventCityFormats(publicId),
  })
}
