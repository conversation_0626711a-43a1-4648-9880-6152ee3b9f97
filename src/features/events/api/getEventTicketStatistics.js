import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const EVENT_TICKET_STATISTICS_QUERY_KEY = 'eventTicketStatistics'

const getEventTicketStatistics = (data) => {
  return axios.post(APIRoute.EVENT_TICKET_STATISTICS, data)
}

export const useGetEventTicketStatistics = () => {
  const openToast = useToast()

  return useMutation({
    mutationKey: EVENT_TICKET_STATISTICS_QUERY_KEY,
    mutationFn: getEventTicketStatistics,
    onError: (error) => {
      openToast.error({
        message: 'Ошибка при получении статистики билетов',
        description: error?.response?.data?.message || 'Попробуйте еще раз',
      })
    },
  })
}
