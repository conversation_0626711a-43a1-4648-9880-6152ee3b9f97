import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteEventCityFormat = (data) => {
  return axios.delete(APIRoute.DELETE_EVENT_CITY_FORMAT, { data })
}

export const useDeleteEventCityFormat = (eventCityPublicId) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['eventCityFormats', eventCityPublicId])
        openToast.success({ message: 'Формат удалён!' })
      }
    },
    mutationFn: deleteEventCityFormat,
  })
}
