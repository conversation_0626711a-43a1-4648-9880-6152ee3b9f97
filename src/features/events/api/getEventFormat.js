import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getEventFormat = (publicId) => {
  return axios.get(`${APIRoute.EVENT_FORMAT}/${publicId}`)
}

export const useGetEventFormat = (publicId) => {
  return useQuery({
    enabled: publicId?.length > 0,
    queryKey: ['eventFormat', publicId],
    queryFn: () => getEventFormat(publicId),
  })
}
