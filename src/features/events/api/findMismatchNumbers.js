import { useMutation } from 'react-query'

import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const findMismatchNumbers = (data) => {
  return axios.post('/api/admin/mismatch/soldgood/numbers', data)
}

export const useFindMismatchNumbers = () => {
  const openToast = useToast()

  return useMutation({
    onError: (error) => {
      openToast.error({ message: error?.response?.data?.message || 'Ошибка при поиске номеров вне диапазона' })
    },
    mutationFn: findMismatchNumbers,
  })
}
