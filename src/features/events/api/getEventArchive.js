import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const EVENT_ARCHIVE_QUERY_KEY = 'eventArchive'

const getEventArchive = (params) => {
  return axios.get(APIRoute.EVENT_ARCHIVE, { params })
}

export const useGetEventArchive = (params) => {
  return useQuery({
    queryKey: [EVENT_ARCHIVE_QUERY_KEY, params],
    queryFn: () => getEventArchive(params),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}
