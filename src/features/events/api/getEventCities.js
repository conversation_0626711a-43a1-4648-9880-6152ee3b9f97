import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getEventCities = (publicId) => {
  return axios.get(`${APIRoute.GET_EVENT_CITIES}/${publicId}`)
}

export const useGetEventCities = (publicId) => {
  return useQuery({
    enabled: publicId?.length > 0,
    queryKey: ['eventCities', publicId],
    queryFn: () => getEventCities(publicId),
  })
}
