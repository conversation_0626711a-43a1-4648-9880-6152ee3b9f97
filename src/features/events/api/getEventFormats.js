import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getEventFormats = (eventCityId) => {
  return axios.get(`${APIRoute.EVENT_FORMATS}/${eventCityId}?hidden=true`)
}

export const useGetEventFormats = ({ config } = {}, eventCityId) => {
  return useQuery({
    ...config,
    enabled: eventCityId?.length > 0,
    queryKey: ['eventFormats', eventCityId],
    queryFn: () => getEventFormats(eventCityId),
  })
}
