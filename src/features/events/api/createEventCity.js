import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createEventCity = (data) => {
  return axios.post(APIRoute.CUD_EVENT_CITY, data)
}

export const useCreateEventCity = (id, onCloseModal) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['eventCities', id])
        onCloseModal()
        openToast.success({ message: 'Город добавлен!' })
      }
    },
    mutationFn: createEventCity,
  })
}
