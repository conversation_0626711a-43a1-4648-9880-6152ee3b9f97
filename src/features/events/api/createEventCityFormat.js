import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const createEventCityFormat = (data) => {
  return axios.post(APIRoute.CU_EVENT_CITY_FORMAT, data)
}

export const useCreateEventCityFormat = (eventCityPublicId, onCloseModal) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['eventCityFormats', eventCityPublicId])
        onCloseModal()
        openToast.success({ message: 'Формат добавлен!' })
      }
    },
    mutationFn: createEventCityFormat,
  })
}
