import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const downloadUniqueUsersXlsx = (data) => {
  return axios.post(APIRoute.EVENT_UNIQUE_USERS_XLSX, data, { responseType: 'blob' })
}

export const useDownloadUniqueUsersXlsx = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: downloadUniqueUsersXlsx,
    onSuccess: (response) => {
      if (response?.status === 200) {
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', 'unique_users.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        openToast.success({ message: 'Файл успешно скачан' })

        // Инвалидация кэша не требуется, так как это только скачивание
      }
    },
    onError: () => {
      openToast.error({ message: 'Произошла ошибка при скачивании файла' })
    },
  })
}
