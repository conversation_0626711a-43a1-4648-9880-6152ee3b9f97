import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const completeTeams = async (formatPublicId) => {
  const { data } = await axios.post(APIRoute.COMPLETE_TEAMS, {
    format_public_id: formatPublicId,
  })
  return data
}

export const useCompleteTeams = ({ onSuccess } = {}) => {
  return useMutation({
    mutationFn: completeTeams,
    onSuccess,
  })
}
