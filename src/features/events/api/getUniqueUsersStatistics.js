import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

export const UNIQUE_USERS_STATISTICS_QUERY_KEY = 'uniqueUsersStatistics'

const getUniqueUsersStatistics = (data) => {
  return axios.post(APIRoute.EVENT_UNIQUE_USERS_STATISTICS, data)
}

export const useGetUniqueUsersStatistics = () => {
  return useMutation({
    mutationKey: UNIQUE_USERS_STATISTICS_QUERY_KEY,
    mutationFn: getUniqueUsersStatistics,
  })
}
