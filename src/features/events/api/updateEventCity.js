import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const updateEventCity = ({ id, data }) => {
  return axios.put(`${APIRoute.CUD_EVENT_CITY}/${id}`, data)
}

export const useUpdateEventCity = (id) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['eventCities', id])
        openToast.success({ message: 'Город обновлён!' })
      }
    },
    mutationFn: updateEventCity,
  })
}
