import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const downloadUniqueUsersByCitiesXlsx = (data) => {
  return axios.post(APIRoute.EVENT_CITIES_UNIQUE_USERS_XLSX, data, {
    responseType: 'blob',
  })
}

export const useDownloadUniqueUsersByCitiesXlsx = () => {
  return useMutation({
    mutationFn: downloadUniqueUsersByCitiesXlsx,
    onSuccess: (response) => {
      // Создаем ссылку для скачивания файла
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'unique_users_by_cities.xlsx')
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    },
  })
}
