import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const uploadResultFormat = (data) => {
  return axios.post(APIRoute.ADDITIONAL_RESULTS, data)
}

export const useUploadResultFormat = () => {
  const openToast = useToast()

  return useMutation({
    mutationFn: uploadResultFormat,
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['results'])
        openToast.success({ message: 'Формат успешно добавлен' })
      }
    },
    onError: (error) => {
      openToast.error({
        message: error.response?.data?.message || 'Ошибка при загрузке формата',
        duration: 6000,
      })
    },
  })
}

export default useUploadResultFormat
