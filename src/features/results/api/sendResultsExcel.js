import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const sendResultsExcel = (data) => {
  return axios.post(APIRoute.SEND_RESULTS_EXCEL, data)
}

export const useSendResultsExcel = () => {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation(sendResultsExcel, {
    onSuccess: (data) => {
      queryClient.setQueryData(['resultsFormat'], data.data)
      queryClient.invalidateQueries(['eventFormats'])
    },
    onError: (error) => {
      console.error(error)
      toast.error({
        title: 'Ошибка!',
        message: 'Не удалось отправить Excel результаты',
      })
    },
  })
}
