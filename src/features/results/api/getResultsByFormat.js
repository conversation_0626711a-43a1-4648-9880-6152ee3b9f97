import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getResultsByFormat = (formatId, skip = 0, limit = 0) => {
  return axios.get(`${APIRoute.RESULTS_FORMAT}/${formatId}/${skip}/${limit}`)
}

export const useGetResultsByFormat = (formatId, skip = 0, limit = 0) => {
  return useQuery({
    queryKey: ['resultsByFormat', formatId, skip, limit],
    queryFn: () => getResultsByFormat(formatId, skip, limit),
    enabled: !!formatId,
  })
}
