import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const editResultItem = (data) => {
  return axios.put(APIRoute.RESULT_EDIT_PUBLIC_ID, data)
}

export const useEditResultItem = (formatId) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['resultsByFormat', formatId])
        openToast.success({ message: 'Результат успешно обновлен!' })
      }
    },
    onError: (error) => {
      openToast.error({ message: 'Ошибка при обновлении результата' })
      console.error('Error editing result item:', error)
    },
    mutationFn: editResultItem,
  })
}
