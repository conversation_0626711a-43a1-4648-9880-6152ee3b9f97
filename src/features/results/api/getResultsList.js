import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getResultsList = (id) => {
  return axios.get(`${APIRoute.RESULTS_LIST}/${id}`)
}

export const useGetResultsList = (id) => {
  return useQuery({
    queryKey: ['resultsList', id],
    queryFn: () => getResultsList(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}
