import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getResultsByEventType = (eventId) => {
  return axios.get(`${APIRoute.RESULTS_LIST_EVENT_TYPE}/${eventId}`)
}

export const useGetResultsByEventType = (eventId) => {
  return useQuery({
    queryKey: ['resultsByEventType', eventId],
    queryFn: () => getResultsByEventType(eventId),
    enabled: !!eventId,
  })
}
