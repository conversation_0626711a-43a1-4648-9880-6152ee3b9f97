import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const updateResult = (data) => {
  return axios.put(APIRoute.RESULTS_EDIT, data)
}

export const useUpdateResult = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['result', data.data.public_id])
        openToast.success({ message: 'Настройки результатов обновлены!' })
      }
    },
    onError: (error) => {
      openToast.error({ message: 'Ошибка при сохранении изменений' })
      console.error('Error updating results:', error)
    },
    mutationFn: updateResult,
  })
}
