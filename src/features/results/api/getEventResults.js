import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getEventResults = (formatExternal, eventCityId) => {
  if (!formatExternal || !eventCityId) return Promise.resolve({ data: { values: [] } })
  return axios.get(`${APIRoute.RESULTS_FORMAT_EVENT}/${formatExternal}/${eventCityId}`)
}

export const useGetEventResults = (formatExternal, eventCityId) => {
  return useQuery({
    queryKey: ['eventResults', formatExternal, eventCityId],
    queryFn: () => getEventResults(formatExternal, eventCityId),
    enabled: !!formatExternal && !!eventCityId,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}
