import { useMutation, useQueryClient } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const sendResultsCsv = (data) => {
  return axios.post(APIRoute.SEND_RESULTS, data)
}

export const useSendResultsCsv = () => {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation(sendResultsCsv, {
    onSuccess: (data) => {
      queryClient.setQueryData(['resultsFormat'], data.data)
      queryClient.invalidateQueries(['eventFormats'])
    },
    onError: (error) => {
      console.error(error)
      toast.error({
        title: 'Ошибка!',
        message: 'Не удалось отправить CSV результаты',
      })
    },
  })
}
