import useAxios from 'axios-hooks'
import { useEffect, useRef, useState } from 'react'
import { Col, Dropdown, DropdownButton, FormControl, InputGroup, Row, Button } from 'react-bootstrap'
import { FaTimes } from 'react-icons/fa'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { getRightNames } from '@/utils/common'

function SearchInsurance({ setLicences, resetSearch, foundValuesLength }) {
  const [searchText, setSearchText] = useState('')
  const searchRef = useRef(null)
  const openToast = useToast()
  const [, api] = useAxios(
    {
      url: APIRoute.SOLDGOOD_SEARCH,
      method: 'POST',
    },
    { manual: true }
  )

  // Очистка поля поиска при выполнении resetSearch извне
  useEffect(() => {
    if (foundValuesLength === 0) {
      setSearchText('')
      if (searchRef.current) {
        searchRef.current.value = ''
      }
    }
  }, [foundValuesLength])

  const handleChangeSearch = (evt) => {
    setSearchText(evt.target.value)
    if (evt.target.value === '') {
      setLicences([])
    }
  }

  const clearSearch = () => {
    setSearchText('')
    if (searchRef.current) {
      searchRef.current.value = ''
    }
    setLicences([])
    if (resetSearch) {
      resetSearch()
    }
  }

  const handleClickSearch = () => {
    const data = {
      text: searchRef.current.value,
      soldgood_type: 'insurance',
    }

    api({ data: data }).then((r) => {
      if (r.status === 200 && r.data?.values?.length > 0) {
        const count = r.data.values.length
        setLicences(r.data.values)
        openToast.success({
          title: true,
          message: `Найдено ${count} ${getRightNames(count, 'страховка', 'страховки', 'страховок')}`,
          duration: 4000,
        })
      } else if (r.status === 200 && r.data?.values?.length === 0) {
        openToast.error({ message: 'Страховок не найдено' })
      }
    })
  }

  const handleClickSearchNumber = () => {
    const value = searchRef.current.value

    if (isNaN(value)) {
      openToast.error({ message: 'Номер может быть только числовой' })
    } else {
      api({ method: 'GET', url: `${APIRoute.SEARCH_INSURANCE_NUMBER}/${value}` }).then((r) => {
        if (r.status === 200 && r.data?.values?.length > 0) {
          setLicences(r.data.values)
        } else if (r.status === 200 && r.data?.values?.length === 0) {
          openToast.error({ message: 'Страховок не найдено' })
        }
      })
    }
  }

  return (
    <>
      <Row className="mb-3">
        <Col>
          <InputGroup>
            <InputGroup.Text>
              <i className="bi bi-search" />
            </InputGroup.Text>

            <FormControl
              placeholder="Поиск"
              aria-label="Поиск"
              aria-describedby="basic-addon2"
              ref={searchRef}
              onChange={handleChangeSearch}
              value={searchText}
            />

            {searchText && (
              <Button variant="outline-secondary" onClick={clearSearch}>
                <FaTimes />
              </Button>
            )}

            <DropdownButton variant="outline-secondary" title="Искать по" id="input-group-dropdown-2">
              <Dropdown.Item onClick={handleClickSearch}>тексту</Dropdown.Item>
              <Dropdown.Item onClick={handleClickSearchNumber}>номеру</Dropdown.Item>
            </DropdownButton>
          </InputGroup>
        </Col>
      </Row>
    </>
  )
}

export default SearchInsurance
