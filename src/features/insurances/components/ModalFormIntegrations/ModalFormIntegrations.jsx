import { useEffect, useState, useMemo } from 'react'
import {
  Button,
  Col,
  Floating<PERSON><PERSON><PERSON>,
  Form,
  Form<PERSON>heck,
  FormControl,
  Modal,
  ModalBody,
  ModalFooter,
  <PERSON>,
} from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { useCreateInsuranceIntegrations } from '@/features/insurances/api/createInsuranceIntegrations'
import { useDeleteIntegrationsItem } from '@/features/insurances/api/deleteIntegrationsItem'
import { useGetIntegrationsItem } from '@/features/insurances/api/getIntegrationsItem'
import { useUpdateInsuranceIntegrations } from '@/features/insurances/api/updateInsuranceIntegrations'
import { useToast } from '@/hooks/useToast'
import { removeEmptyString2, setValueAsText } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'

const FormIntegrations = ({ onHide, selectedItemId }) => {
  const createInsuranceIntegrationsMutate = useCreateInsuranceIntegrations()
  const updateInsuranceIntegrationsMutate = useUpdateInsuranceIntegrations()
  const getIntegrationsItemQuery = useGetIntegrationsItem(selectedItemId)
  const selectedItem = getIntegrationsItemQuery?.data?.data

  const defaultValues = useMemo(
    () => ({
      name: selectedItem?.name,
      description: selectedItem?.description,
      username: selectedItem?.username,
      password: selectedItem?.password,
      email: selectedItem?.email,
      phone: {
        phoneNumber: selectedItem?.phone?.phoneNumber,
      },
      client_id: selectedItem?.client_id,
      package_code: selectedItem?.package_code,
      organization: {
        shortName: selectedItem?.organization?.shortName,
        fullName: selectedItem?.organization?.fullName,
        inn: selectedItem?.organization?.inn,
        kpp: selectedItem?.organization?.kpp,
        ogrn: selectedItem?.organization?.ogrn,
      },
      address: {
        typeCode: selectedItem?.address?.typeCode,
        addressStr: selectedItem?.address?.addressStr,
      },
      token_url: selectedItem?.token_url,
      policies_url: selectedItem?.policies_url,
      installments_url: selectedItem?.installments_url,
      enabled: selectedItem?.enabled,
    }),
    [selectedItem]
  )

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      enabled: true,
    },
  })

  const isEdit = Boolean(selectedItemId)

  const openToast = useToast()

  useEffect(() => {
    if (selectedItem && Object.keys(selectedItem)?.length > 0) {
      reset(defaultValues)
    }
  }, [selectedItem, defaultValues, reset])

  const handleClickSubmit = (data) => {
    const filteredData = removeEmptyString2(data)

    if (Object.keys(filteredData).length > 0) {
      if (isEdit) {
        updateSubmit(filteredData)
      } else {
        createSubmit(filteredData)
      }
    } else {
      openToast.warning({ message: 'Вы не внесли никаких изменений.' })
    }
  }

  const createSubmit = (data) => {
    createInsuranceIntegrationsMutate.mutate(data, {
      onSuccess: (data) => {
        if (data?.status === 200) {
          onHide()
        }
      },
    })
  }

  const updateSubmit = (data) => {
    updateInsuranceIntegrationsMutate.mutate(
      { id: selectedItemId, data: data },
      {
        onSuccess: (data) => {
          if (data?.status === 200) {
            onHide()
          }
        },
      }
    )
  }

  return (
    <Form onSubmit={handleSubmit(handleClickSubmit)} id="formIntegrations">
      <Row className="mb-4">
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="nameLabel" label="Название *">
              <FormControl
                {...register('name', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.name),
                })}
                type="text"
                isInvalid={errors?.name}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="descriptionLabel" label="Описание *">
              <FormControl
                {...register('description', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.description),
                })}
                type="text"
                isInvalid={errors?.description}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="usernameLabel" label="Пользователь *">
              <FormControl
                {...register('username', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.username),
                })}
                type="text"
                isInvalid={errors?.username}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="passwordLabel" label="Пароль *">
              <FormControl
                {...register('password', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.password),
                })}
                type="text"
                isInvalid={errors?.password}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="emailLabel" label="Почта *">
              <FormControl
                {...register('email', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.email),
                })}
                type="email"
                isInvalid={errors?.email}
                required
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="phonePhoneNumberLabel" label="Телефон">
              <FormControl
                {...register('phone.phoneNumber', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.phone?.phoneNumber),
                })}
                type="number"
                isInvalid={errors?.phone?.phoneNumber}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="clientIdLabel" label="Идентификатор *">
              <FormControl
                {...register('client_id', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.client_id),
                })}
                type="text"
                isInvalid={errors?.client_id}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>

        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="packageCodeLabel" label="Пакет *">
              <FormControl
                {...register('package_code', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.package_code),
                })}
                type="text"
                isInvalid={errors?.package_code}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <h5>Компания</h5>
      <Row className="mb-4">
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="organizationShortNameLabel" label="Короткое Имя">
              <FormControl
                {...register('organization.shortName', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.organization?.shortName),
                })}
                type="text"
                isInvalid={errors?.organization?.shortName}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="organizationFullNameLabel" label="Полное Имя">
              <FormControl
                {...register('organization.fullName', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.organization?.fullName),
                })}
                type="text"
                isInvalid={errors?.organization?.fullName}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="innLabel" label="ИНН">
              <FormControl
                {...register('organization.inn', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.organization?.inn),
                })}
                type="number"
                isInvalid={errors?.organization?.inn}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="kppLabel" label="КПП">
              <FormControl
                {...register('organization.kpp', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.organization?.kpp),
                })}
                type="number"
                isInvalid={errors?.organization?.kpp}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="ogrnLabel" label="ОГРН">
              <FormControl
                {...register('organization.ogrn', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.organization?.ogrn),
                })}
                type="number"
                isInvalid={errors?.organization?.ogrn}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <h5>Адрес</h5>
      <Row className="mb-4">
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="addressTypeCodeLabel" label="Тип">
              <FormControl
                {...register('address.typeCode', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.address?.typeCode),
                })}
                type="text"
                isInvalid={errors?.address?.typeCode}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="addressStrLabel" label="Адрес">
              <FormControl
                {...register('address.addressStr', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.address?.addressStr),
                })}
                type="text"
                isInvalid={errors?.address?.addressStr}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <h5>URL интеграции</h5>
      <Row>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="tokenUrlLabel" label="Авторизации *">
              <FormControl
                {...register('token_url', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.token_url),
                })}
                type="text"
                isInvalid={errors?.token_url}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="policiesUrlLabel" label="Расчёт *">
              <FormControl
                {...register('policies_url', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.policies_url),
                })}
                type="text"
                isInvalid={errors?.policies_url}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group className="mb-3">
            <FloatingLabel controlId="installmentsUrlLabel" label="Оплата *">
              <FormControl
                {...register('installments_url', {
                  setValueAs: (v) => setValueAsText(v, defaultValues?.installments_url),
                })}
                type="text"
                isInvalid={errors?.installments_url}
                placeholder=""
                required
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>

      <FormCheck
        {...register('enabled', { setValueAs: (v) => checkSetValue(v, defaultValues?.enabled) })}
        type="checkbox"
        id="enabledCheckbox"
        label="Вкл/Выкл"
        reverse
      />
    </Form>
  )
}

export const ModalFormIntegrations = ({ isShow, onHide, selectedItemId }) => {
  const isEdit = Boolean(selectedItemId)
  const [confirmModal, setConfirmModal] = useState(false)
  const deleteIntegrationsItemMutate = useDeleteIntegrationsItem()

  const handleClickOpenConfirmDelete = () => {
    setConfirmModal(true)
  }

  const handleDeleteItem = () => {
    deleteIntegrationsItemMutate.mutate(selectedItemId, {
      onSuccess: (data) => {
        if (data?.status === 200) {
          setConfirmModal(false)
          onHide()
        }
      },
    })
  }

  if (!isShow) return null

  return (
    <>
      <Modal show={isShow} onHide={onHide}>
        <ModalBody>
          <FormIntegrations onHide={onHide} selectedItemId={selectedItemId} />

          <p>* - обязательные поля</p>
        </ModalBody>
        <ModalFooter>
          <Button type="submit" form="formIntegrations">
            Сохранить
          </Button>
          {isEdit && (
            <Button onClick={handleClickOpenConfirmDelete} variant="danger" type="button">
              Удалить
            </Button>
          )}
          <Button onClick={onHide} variant="secondary" type="button">
            Закрыть
          </Button>
        </ModalFooter>
      </Modal>

      <ConfirmDeleteModal isShow={confirmModal} onClose={setConfirmModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
