import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, Row } from 'react-bootstrap'

import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'

import { useGetInsuranceIntegrations } from '@/features/insurances/api/getInsuranceIntegrations'
import { ModalFormIntegrations } from '@/features/insurances/components/ModalFormIntegrations/ModalFormIntegrations'

const HidePass = ({ password }) => {
  const [isShowPass, setShowPass] = useState(false)

  return (
    <>
      {isShowPass && password}{' '}
      <Button className="p-0" onClick={() => setShowPass(!isShowPass)} variant="link" size="sm">
        {isShowPass ? 'скрыть' : 'показать'}
      </Button>
    </>
  )
}

const SliceLink = ({ link = '' }) => {
  const sliceLink = link.slice(0, 80)

  return (
    <a href={link} target="_blank" rel="noreferrer noopener">
      {sliceLink}
      {link.length > sliceLink.length && '...'}
    </a>
  )
}

export const Integrations = () => {
  const [isShowModalForm, setIsShowModalForm] = useState(false)
  const [selectedItemId, setSelectedItemId] = useState(null)

  const integrationsQuery = useGetInsuranceIntegrations()
  const integrations = integrationsQuery?.data?.data?.values || []

  const handleCreateItem = () => {
    setIsShowModalForm(true)
  }

  const handleEditItem = (selectedItem) => {
    setIsShowModalForm(true)
    setSelectedItemId(selectedItem.public_id)
  }

  const handleHideModal = () => {
    setIsShowModalForm(false)

    if (selectedItemId) {
      setSelectedItemId(null)
    }
  }

  return (
    <>
      <Row className="mb-5">
        <Col />
        <Col md="auto">
          <Button onClick={handleCreateItem} variant="success">
            Создать
          </Button>
        </Col>
      </Row>

      <Row className="g-4">
        {integrations?.map((item) => (
          <Col md={6} key={item.public_id}>
            <Card style={{ height: '100%' }}>
              <Card.Header>
                <Card.Title>
                  <Row>
                    <Col>
                      {item?.enabled ? <>🟢 </> : <>🔴 </>}
                      {item?.name}
                    </Col>
                    <Col md="auto">
                      <Button onClick={() => handleEditItem(item)} variant="light" size="sm">
                        изменить
                      </Button>
                    </Col>
                  </Row>
                </Card.Title>
              </Card.Header>
              <Card.Body>
                <Row className="mb-3">
                  <Col>
                    <ItemLabelValue label="Описание" value={item?.description} />
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col>
                    <ItemLabelValue label="Пользователь" value={item?.username} />
                  </Col>
                  <Col>
                    <ItemLabelValue label="Пароль" value={<HidePass password={item?.password} />} />
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col>
                    <ItemLabelValue label="Идентификатор" value={item?.client_id} />
                  </Col>
                  <Col>
                    <ItemLabelValue label="Пакет" value={item?.package_code} />
                  </Col>
                </Row>

                <h5>Компания</h5>
                <Row className="mb-3">
                  <Col>
                    <ItemLabelValue label="Короткое Имя" value={item?.organization?.shortName} />
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col>
                    <ItemLabelValue label="Полное Имя" value={item?.organization?.fullName} />
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col>
                    <ItemLabelValue label="ИНН" value={item?.organization?.inn} />
                  </Col>
                  <Col>
                    <ItemLabelValue label="КПП" value={item?.organization?.kpp} />
                  </Col>
                  <Col>
                    <ItemLabelValue label="ОГРН" value={item?.organization?.ogrn} />
                  </Col>
                </Row>

                <h5>Адрес</h5>
                <Row className="mb-4">
                  <Col md="auto">
                    <ItemLabelValue label="Тип" value={item?.address?.typeCode} />
                  </Col>
                  <Col>
                    <ItemLabelValue label="Адрес" value={item?.address?.addressStr} />
                  </Col>
                </Row>

                <h5>URL интеграции</h5>
                <Row className="mb-3">
                  <Col>
                    <ItemLabelValue label="Авторизации" value={<SliceLink link={item?.token_url} />} />
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col>
                    <ItemLabelValue label="Расчёт" value={<SliceLink link={item?.policies_url} />} />
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col>
                    <ItemLabelValue label="Оплата" value={<SliceLink link={item?.installments_url} />} />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      <ModalFormIntegrations isShow={isShowModalForm} onHide={handleHideModal} selectedItemId={selectedItemId} />
    </>
  )
}
