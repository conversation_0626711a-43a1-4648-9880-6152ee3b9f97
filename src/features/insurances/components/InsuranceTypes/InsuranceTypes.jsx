import { useState, useEffect, useMemo } from 'react'
import { Button, ButtonGroup, Col, Dropdown, Modal, Row } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import Loader from '@/components/Loader/Loader'
import PageSearch from '@/components/PageSearch/PageSearch'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { AppRoute } from '@/const'
import { useDeleteInsuranceType } from '@/features/insurances/api/deleteInsuranceType'
import { useGetInsuranceTypes } from '@/features/insurances/api/getInsuranceTypes'
import { insurancesTableData } from '@/features/insurances/insurancesScreenData'

function InsuranceTypes() {
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [licences, setLicences] = useState([]) // для отрисовки на странице
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItemId, setSelectedDeleteItemId] = useState('')

  const getInsuranceTypesQuery = useGetInsuranceTypes()
  const deleteInsuranceTypeMutation = useDeleteInsuranceType()
  const data = getInsuranceTypesQuery?.data?.data

  // Memoize insuranceTypes to prevent unnecessary re-renders
  const insuranceTypes = useMemo(() => data?.values || [], [data?.values])

  // Initialize filteredValues when data is loaded
  useEffect(() => {
    if (insuranceTypes && insuranceTypes.length > 0) {
      setFilteredValues(insuranceTypes)
    }
  }, [insuranceTypes])

  const handleConfirmDelete = (public_id) => {
    setConfirmModal(true)
    setSelectedDeleteItemId(public_id)
  }

  const handleCloseConfirm = () => {
    setConfirmModal(false)
    setSelectedDeleteItemId('')
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    deleteInsuranceTypeMutation.mutate(selectedDeleteItemId)
  }

  const returnActionsTable = (item) => {
    return (
      <td onClick={(evt) => evt.stopPropagation()}>
        <Dropdown>
          <Dropdown.Toggle as={ButtonGroup} bsPrefix=" " variant="light" id="dropdown-basic">
            <i className="bi bi-three-dots" />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item className="text-danger" as="button" onClick={() => handleConfirmDelete(item.public_id)}>
              <i className="bi bi-trash me-2" />
              Удалить
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  return (
    <Loader isLoading={false}>
      <Row className="mb-3">
        <Col>
          <PageSearch values={insuranceTypes} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <Button to={AppRoute.INSURANCE_TYPE_CREATE} as={Link} variant="success">
            <i className="bi bi-plus-circle me-2" />
            Добавить
          </Button>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <TableTemplate data={insurancesTableData} values={licences} actions={returnActionsTable} />
        </Col>
      </Row>

      <AdvancedPagination values={filteredValues} setValues={setLicences} limitValues={20} />

      <Modal show={confirmModal} onHide={handleCloseConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>Удалить страховку?</Modal.Title>
        </Modal.Header>
        <Modal.Footer>
          <Button variant="link" onClick={() => setConfirmModal(false)}>
            Отменить
          </Button>
          <Button variant="danger" onClick={handleDeleteItem}>
            Удалить
          </Button>
        </Modal.Footer>
      </Modal>
    </Loader>
  )
}

export default InsuranceTypes
