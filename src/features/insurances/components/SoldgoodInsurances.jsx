import moment from 'moment'
import { useCallback, useEffect, useState, useRef } from 'react'
import { Button, Col, Form, FormControl, Modal, Row, Card, ButtonGroup, Spinner } from 'react-bootstrap'
import { FaDownload, FaSearch, FaFilter, FaUndoAlt, FaFileExcel } from 'react-icons/fa'

import { CheckPermission } from '@/components/CheckPermission/CheckPermission'
import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import Loader from '@/components/Loader/Loader'
import Paginator from '@/components/Paginator/Paginator'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { accessConfig } from '@/accessConfig'
import { useGetAllSoldgoodInsurances } from '@/features/insurances/api/getAllSoldgoodInsurances'
import { useGetInsuranceTypes } from '@/features/insurances/api/getInsuranceTypes'
import { useGetSoldgoodInsurances } from '@/features/insurances/api/getSoldgoodInsurances'
import { useSendingSoldgoodInsurance } from '@/features/insurances/api/sendingSoldgoodInsurance'
import SearchInsurance from '@/features/insurances/components/SearchInsurance'
import SoldgoodInsuranceForm from '@/features/insurances/components/SoldgoodInsuranceForm'
import { useInsuranceSearch } from '@/features/insurances/hooks/useInsuranceSearch'
import { soldgoodInsurancesTableData } from '@/features/insurances/insurancesScreenData'

// Constants
const PAGE_LIMIT_PAGINATION = 20

const STATUS_OPTIONS = [
  { value: 'all', label: 'Все статусы' },
  { value: 'paid', label: 'Оплачен' },
  { value: 'canceled', label: 'Возврат' },
  { value: 'created', label: 'Не оплачен' },
]

const MODAL_TITLES = {
  view: 'Страховка',
  edit: 'Изменение страховки',
}

// Helper functions
const getDefaultFilterItems = () => ({
  status: 'paid',
  start_date: moment().subtract(1, 'month'),
  end_date: moment(),
  insurance: {
    public_id: 'all',
  },
})

const formatDateForInput = (date) => {
  return date ? moment(date).format('YYYY-MM-DDTHH:mm') : ''
}

const createFileName = (number, originalName, extension) => {
  return `${number || originalName}.${extension}`
}

const downloadSingleFile = (blob, fileName) => {
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Custom hooks
const useInsuranceData = () => {
  const getSoldgoodInsurancesMutation = useGetSoldgoodInsurances()
  const getAllSoldgoodInsurancesMutation = useGetAllSoldgoodInsurances()
  const getInsuranceTypeQuery = useGetInsuranceTypes()
  const sendingSoldgoodInsuranceMutate = useSendingSoldgoodInsurance()

  return {
    getAllSoldgoodInsurancesMutation,
    getInsuranceTypeQuery,
    sendingSoldgoodInsuranceMutate,
    insuranceTypes: getInsuranceTypeQuery?.data?.data?.values,
    data: getSoldgoodInsurancesMutation?.data?.data,
    isLoading: getSoldgoodInsurancesMutation.isLoading,
  }
}

const useInsuranceFilters = (initialFilters, setLicences) => {
  const [filterItems, setFilterItems] = useState(initialFilters)

  const handleFilter = useCallback((field, value) => {
    setFilterItems((prevFilters) => {
      const newFilterItems = { ...prevFilters }

      if (value === 'all' || value === '') {
        delete newFilterItems[field]
      } else {
        newFilterItems[field] = value
      }

      return newFilterItems
    })
  }, [])

  const handleStatusChange = useCallback(
    (evt) => {
      handleFilter(evt.target.name, evt.target.value)
    },
    [handleFilter]
  )

  const handleInsuranceTypeChange = useCallback(
    (evt) => {
      handleFilter('insurance', { public_id: evt.target.value })
    },
    [handleFilter]
  )

  const handleDateChange = useCallback((evt) => {
    const { name, value } = evt.target

    setFilterItems((prev) => {
      if (value === '') {
        const { [name]: _, ...newState } = prev
        return newState
      } else {
        return { ...prev, [name]: new Date(value) }
      }
    })
  }, [])

  const resetFilters = useCallback(() => {
    const defaultFilters = getDefaultFilterItems()
    setFilterItems(defaultFilters)
    setLicences([])
    // Note: We'll need to trigger fetchInsurances after this is called
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    filterItems,
    handleStatusChange,
    handleInsuranceTypeChange,
    handleDateChange,
    resetFilters,
  }
}

const usePagination = (pageLimit) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(null)

  const handlePageChange = useCallback(
    (pageNumber) => {
      const skip = pageLimit * pageNumber - pageLimit
      setCurrentPage(pageNumber)
      return skip
    },
    [pageLimit]
  )

  const resetPagination = useCallback(() => {
    setCurrentPage(1)
  }, [])

  const updateTotalPages = useCallback(
    (totalCount) => {
      setTotalPages(Math.ceil(totalCount / pageLimit))
    },
    [pageLimit]
  )

  return {
    currentPage,
    totalPages,
    handlePageChange,
    resetPagination,
    updateTotalPages,
  }
}

const usePhotoDownload = () => {
  const [isDownloadingPhotos, setIsDownloadingPhotos] = useState(false)

  const downloadPhotos = useCallback(async (filterItems, data, singleInsurance, getAllInsurancesMutation) => {
    try {
      setIsDownloadingPhotos(true)

      const allInsurancesRequestData = {
        ...filterItems,
        skip: 0,
        limit: data?.count || 1000,
      }

      return new Promise((resolve, reject) => {
        getAllInsurancesMutation.mutate(allInsurancesRequestData, {
          onSuccess: async (response) => {
            try {
              const insuranceData = singleInsurance ? [singleInsurance] : response?.data?.values || []
              const photosToDownload = insuranceData.filter((obj) => obj.info?.picture)

              if (photosToDownload.length === 0) {
                alert('Нет доступных изображений для скачивания')
                setIsDownloadingPhotos(false)
                resolve()
                return
              }

              const fetchPromises = photosToDownload.map(async (obj) => {
                if (!obj.info?.picture) return null

                const apiUrl = import.meta.env.VITE_REACT_APP_API || ''
                const fullUrl = `${apiUrl}${obj.info.picture}`
                const fileExtension = obj.info.picture.split('.').pop()
                const fileOriginalName = obj.info.picture.split('/').pop().split('.').shift()
                const fileName = createFileName(obj.info.number, fileOriginalName, fileExtension)

                try {
                  const response = await fetch(fullUrl)
                  if (!response.ok) {
                    console.error(`Ошибка при загрузке фото ${fullUrl}: ${response.status}`)
                    return null
                  }
                  const blob = await response.blob()
                  return { blob, fileName }
                } catch (error) {
                  console.error('Ошибка при загрузке фото:', error)
                  return null
                }
              })

              const photoResults = await Promise.all(fetchPromises)
              const validPhotos = photoResults.filter((item) => item !== null)

              if (validPhotos.length === 0) {
                alert('Не удалось загрузить ни одного изображения')
                setIsDownloadingPhotos(false)
                resolve()
                return
              }

              if (validPhotos.length === 1) {
                const { blob, fileName } = validPhotos[0]
                downloadSingleFile(blob, fileName)
                setIsDownloadingPhotos(false)
                resolve()
                return
              }

              // Multiple photos - create zip
              const JSZip = await import('jszip').then((module) => module.default)
              const zip = new JSZip()

              validPhotos.forEach(({ blob, fileName }) => {
                zip.file(fileName, blob)
              })

              const content = await zip.generateAsync({ type: 'blob' })
              downloadSingleFile(content, 'insurance_photos.zip')
              setIsDownloadingPhotos(false)
              resolve()
            } catch (error) {
              console.error('Ошибка при обработке данных:', error)
              alert('Произошла ошибка при обработке данных')
              setIsDownloadingPhotos(false)
              reject(error)
            }
          },
          onError: (error) => {
            console.error('Ошибка при получении данных:', error)
            alert('Произошла ошибка при получении данных о страховках')
            setIsDownloadingPhotos(false)
            reject(error)
          },
        })
      })
    } catch (error) {
      console.error('Ошибка при скачивании фотографий:', error)
      alert('Произошла ошибка при скачивании фотографий')
      setIsDownloadingPhotos(false)
      throw error
    }
  }, [])

  return {
    isDownloadingPhotos,
    downloadPhotos,
  }
}

// Components
const FilterSection = ({
  filterItems,
  insuranceTypes,
  onStatusChange,
  onInsuranceTypeChange,
  onDateChange,
  onApplyFilters,
  onResetFilters,
}) => (
  <Card className="mb-4 shadow-sm">
    <Card.Header className="bg-body-tertiary d-flex align-items-center">
      <h5 className="mb-0">
        <FaFilter className="me-2" />
        Фильтры
      </h5>
      <Button size="sm" variant="outline-secondary" className="ms-auto" onClick={onResetFilters}>
        <FaUndoAlt className="me-1" />
        Сбросить
      </Button>
    </Card.Header>
    <Card.Body>
      <Row className="g-3 align-items-end">
        <Col md={3}>
          <Form.Group>
            <Form.Label>Статус</Form.Label>
            <Form.Select
              onChange={onStatusChange}
              value={filterItems?.status || 'all'}
              name="status"
              aria-label="Фильтр по статусу"
            >
              {STATUS_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>

        <Col md={3}>
          <Form.Group>
            <Form.Label>Тип страховки</Form.Label>
            <Form.Select
              onChange={onInsuranceTypeChange}
              value={filterItems?.insurance?.public_id || 'all'}
              name="insurance_public_id"
              aria-label="Фильтр по виду страховки"
            >
              <option value="all">Все типы</option>
              {insuranceTypes?.map((item) => (
                <option value={item.public_id} key={item.public_id}>
                  {item.name}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>

        <Col md="auto">
          <Form.Group>
            <Form.Label>Период от</Form.Label>
            <FormControl
              type="datetime-local"
              name="start_date"
              onChange={onDateChange}
              value={formatDateForInput(filterItems?.start_date)}
            />
          </Form.Group>
        </Col>

        <Col md="auto">
          <Form.Group>
            <Form.Label>Период до</Form.Label>
            <FormControl
              type="datetime-local"
              name="end_date"
              onChange={onDateChange}
              value={formatDateForInput(filterItems?.end_date)}
            />
          </Form.Group>
        </Col>

        <Col md="auto">
          <Button variant="primary" onClick={onApplyFilters}>
            <FaSearch className="me-2" />
            Применить фильтр
          </Button>
        </Col>
      </Row>
    </Card.Body>
  </Card>
)

const StatisticsRow = ({ data, foundValues, filterItems, isDownloadingPhotos, onDownloadPhotos }) => (
  <Row className="mt-4">
    <Col xs={12} className="d-flex justify-content-between align-items-center">
      <div className="fs-6">
        {!foundValues?.length && (
          <>
            <span className="me-3">
              Количество: <b>{data?.count || 0}</b>
            </span>
            <span>
              Сумма: <b>{data?.total_sum || 0}</b>
            </span>
          </>
        )}
      </div>

      <CheckPermission allowedRoles={accessConfig.insurances.downloadButtonInsurance}>
        <div>
          <ButtonGroup size="md">
            <DownloadButton
              url="/api/admin/bought/insurances"
              fileName="insurances.xlsx"
              data={filterItems}
              disabled={!filterItems?.start_date || !filterItems?.end_date}
              label="Выгрузить Excel"
              icon={FaFileExcel}
            />

            <Button
              variant="outline-secondary"
              onClick={() => onDownloadPhotos()}
              disabled={!filterItems?.start_date || !filterItems?.end_date || isDownloadingPhotos}
            >
              {isDownloadingPhotos ? (
                <Spinner className="me-2" animation="grow" size="sm" />
              ) : (
                <FaDownload className="me-2" />
              )}
              Скачать фото
            </Button>
          </ButtonGroup>
        </div>
      </CheckPermission>
    </Col>
  </Row>
)

const ResultsSection = ({
  foundValues,
  data,
  isLoading,
  isSearching,
  customTableData,
  currentPage,
  totalPages,
  onClickRow,
  onPageChange,
  onResetSearch,
  renderCustomCell,
  setLicences,
  licenses,
}) => (
  <Card className="mb-4 shadow-sm">
    <Card.Header className="bg-body-tertiary">
      <Row className="align-items-center">
        <Col>
          <h5 className="mb-0">Результаты</h5>
        </Col>
      </Row>
    </Card.Header>
    <Card.Body>
      <Row className="mb-3">
        <Col>
          <SearchInsurance
            setLicences={setLicences} // This will be handled by the parent component
            resetSearch={onResetSearch}
            foundValuesLength={licenses?.length}
          />
        </Col>
      </Row>

      <Loader isLoading={isLoading || isSearching}>
        <TableTemplate
          actionRow={onClickRow}
          data={customTableData}
          values={foundValues?.length > 0 ? foundValues : data?.values}
          renderCustomCell={renderCustomCell}
        />

        {foundValues?.length === 0 && (
          <Row className="mt-4">
            <Col>
              <Paginator currentPage={currentPage} totalPages={totalPages} changePageHandler={onPageChange} />
            </Col>
          </Row>
        )}
      </Loader>
    </Card.Body>
  </Card>
)

const InsuranceModal = ({
  show,
  isView,
  selectedItem,
  title,
  onClose,
  onUpdateLicences,
  onSendToInsurance,
  onToggleView,
  setLicences,
}) => (
  <Modal show={show} onHide={onClose} size="lg">
    <Modal.Header closeButton>
      <Modal.Title>{title}</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <SoldgoodInsuranceForm
        selectedItem={selectedItem}
        isClickRow={isView}
        onUpdateLicences={onUpdateLicences}
        onCloseModal={onClose}
        setLicences={setLicences}
      />
    </Modal.Body>
    <Modal.Footer>
      {selectedItem?.info?.api_client?.success === false && (
        <Button style={{ marginRight: 'auto' }} onClick={() => onSendToInsurance(selectedItem.public_id)} type="button">
          Отправить в страховую
        </Button>
      )}
      {isView ? (
        <Button onClick={onToggleView} type="button" variant="primary">
          Изменить
        </Button>
      ) : (
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
      )}
      <Button type="button" variant="outline-secondary" onClick={onClose}>
        Закрыть
      </Button>
    </Modal.Footer>
  </Modal>
)

// Main component
function SoldgoodInsurances() {
  // State
  const [editUserPopup, setEditUserPopup] = useState(false)
  const [isView, setIsView] = useState(false)
  const [foundValues, setFoundValues] = useState([])
  const [selectedItem, setSelectedItem] = useState({})
  const [insuranceData, setInsuranceData] = useState(null) // Store fetched data
  const [allInsurances, setAllInsurances] = useState(null)

  const [licenses, setLicences] = useState(null)

  // Custom hooks
  const { getAllSoldgoodInsurancesMutation, sendingSoldgoodInsuranceMutate, insuranceTypes, isLoading } =
    useInsuranceData()

  const {
    filterItems,
    handleStatusChange,
    handleInsuranceTypeChange,
    handleDateChange,
    resetFilters: resetFiltersBase,
  } = useInsuranceFilters(getDefaultFilterItems(), setLicences)

  const { currentPage, totalPages, handlePageChange, resetPagination, updateTotalPages } =
    usePagination(PAGE_LIMIT_PAGINATION)

  const { isDownloadingPhotos, downloadPhotos } = usePhotoDownload()

  // URL search handling
  const { isSearching, clearUrlParam } = useInsuranceSearch((result, shouldOpenModal) => {
    if (Array.isArray(result)) {
      setFoundValues(result)
    } else {
      setSelectedItem(result)
      setEditUserPopup(true)
      setIsView(shouldOpenModal)
    }
  })

  // Use ref to access current filters without causing re-renders
  const currentFiltersRef = useRef(filterItems)
  currentFiltersRef.current = filterItems

  // Data fetching function - stable reference without filterItems dependency
  const fetchInsurances = useCallback(
    (skip = 0, limit = PAGE_LIMIT_PAGINATION, filters = null) => {
      // Use passed filters or current filterItems from ref
      const filtersToUse = filters || currentFiltersRef.current
      const requestData = {
        skip,
        limit,
        ...filtersToUse,
      }

      // Use direct axios call since React Query mutation is hanging
      import('@/lib/axios').then(({ axios }) => {
        axios
          .post('/api/admin/soldgood/insurances', requestData)
          .then((response) => {
            // Store the fetched data in local state
            setInsuranceData(response?.data)
            setAllInsurances(response?.data)

            if (response?.data?.values?.length > 0) {
              updateTotalPages(response.data.count)
              setFoundValues([])
            } else {
              updateTotalPages(0)
            }
          })
          .catch((error) => {
            console.error('❌ Direct API request failed:', error)
            console.error('❌ Error details:', error?.response?.data)
            console.error('❌ Error message:', error?.message)
          })
      })

      if (skip === 0) {
        resetPagination()
      }
    },
    [updateTotalPages, resetPagination]
  )

  // Use ref to prevent double initial load (StrictMode compatible)
  const hasInitialLoadedRef = useRef(false)

  useEffect(() => {
    if (licenses?.length) {
      const newInsuranceData = { ...insuranceData, values: licenses }

      setInsuranceData(newInsuranceData)
    } else {
      setInsuranceData(allInsurances)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [licenses, allInsurances])

  useEffect(() => {
    if (!hasInitialLoadedRef.current) {
      hasInitialLoadedRef.current = true
      fetchInsurances(0, PAGE_LIMIT_PAGINATION)
    }
  }, [fetchInsurances])

  // Event handlers
  const handleClosePopup = useCallback(() => {
    setEditUserPopup(false)
    setIsView(false)
    clearUrlParam()
  }, [clearUrlParam])

  const handleEditUser = useCallback((item) => {
    setSelectedItem({ ...item })
    setEditUserPopup(true)
  }, [])

  const handleClickRow = useCallback(
    (data) => {
      handleEditUser(data)
      setIsView(true)
    },
    [handleEditUser]
  )

  const handleApplyFilters = useCallback(() => {
    fetchInsurances(0, PAGE_LIMIT_PAGINATION)
    setLicences([])
  }, [fetchInsurances])

  const handlePageChangeWithFetch = useCallback(
    (pageNumber) => {
      const skip = handlePageChange(pageNumber)
      fetchInsurances(skip, PAGE_LIMIT_PAGINATION)
    },
    [handlePageChange, fetchInsurances]
  )

  const handleSendToInsurance = useCallback(
    (id) => {
      sendingSoldgoodInsuranceMutate.mutate(
        { public_id: id },
        {
          onSuccess: (res) => {
            if (res?.status === 200) {
              handleClosePopup()
              fetchInsurances()
            }
          },
        }
      )
    },
    [sendingSoldgoodInsuranceMutate, handleClosePopup, fetchInsurances]
  )

  const handleDownloadPhotos = useCallback(
    (singleInsurance) => {
      downloadPhotos(currentFiltersRef.current, insuranceData, singleInsurance, getAllSoldgoodInsurancesMutation)
    },
    [downloadPhotos, insuranceData, getAllSoldgoodInsurancesMutation]
  )

  const handleResetSearch = useCallback(() => {
    setFoundValues([])
  }, [])

  const handleToggleView = useCallback(() => {
    setIsView(false)
  }, [])

  const resetFilters = useCallback(() => {
    resetFiltersBase()
    // Use setTimeout to ensure the state update happens first
    setTimeout(() => {
      fetchInsurances(0, PAGE_LIMIT_PAGINATION)
    }, 0)
  }, [resetFiltersBase, fetchInsurances])

  // Table configuration
  const customTableData = {
    ...soldgoodInsurancesTableData,
    list: soldgoodInsurancesTableData.list.map((item) => {
      if (item.param === 'downloadPhoto') {
        return {
          ...item,
          customCell: true,
        }
      }
      return item
    }),
  }

  const renderCustomCell = useCallback(
    (param, value, item) => {
      if (param === 'downloadPhoto') {
        if (!value) return '-'

        return (
          <Button
            variant="link"
            onClick={(e) => {
              e.stopPropagation()
              handleDownloadPhotos(item)
            }}
            style={{ padding: '0' }}
          >
            <FaDownload />
          </Button>
        )
      }
      return null
    },
    [handleDownloadPhotos]
  )

  const modalTitle = isView ? MODAL_TITLES.view : MODAL_TITLES.edit

  return (
    <>
      <FilterSection
        filterItems={filterItems}
        insuranceTypes={insuranceTypes}
        onStatusChange={handleStatusChange}
        onInsuranceTypeChange={handleInsuranceTypeChange}
        onDateChange={handleDateChange}
        onApplyFilters={handleApplyFilters}
        onResetFilters={resetFilters}
      />

      <Card className="mb-4 shadow-sm">
        <Card.Body>
          <StatisticsRow
            data={insuranceData}
            foundValues={foundValues}
            filterItems={filterItems}
            isDownloadingPhotos={isDownloadingPhotos}
            onDownloadPhotos={() => handleDownloadPhotos()}
          />
        </Card.Body>
      </Card>

      <ResultsSection
        foundValues={foundValues}
        data={insuranceData}
        isLoading={isLoading}
        isSearching={isSearching}
        customTableData={customTableData}
        currentPage={currentPage}
        totalPages={totalPages}
        onClickRow={handleClickRow}
        onPageChange={handlePageChangeWithFetch}
        onResetSearch={handleResetSearch}
        renderCustomCell={renderCustomCell}
        setLicences={setLicences}
        licenses={licenses}
      />

      <InsuranceModal
        show={editUserPopup}
        isView={isView}
        selectedItem={selectedItem}
        title={modalTitle}
        onClose={handleClosePopup}
        onUpdateLicences={fetchInsurances}
        onSendToInsurance={handleSendToInsurance}
        onToggleView={handleToggleView}
        setLicences={setLicences}
      />
    </>
  )
}

export default SoldgoodInsurances
