import moment from 'moment/moment'
import { useCallback, useEffect } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Row, Spinner } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useGetStatInsurances } from '@/features/insurances/api/getStatInsurances'
import { checkSetValue } from '@/utils/forms'

export const InsuranceStatistics = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      start_date: moment().utc().subtract(1, 'months').startOf('month').format('YYYY-MM-DDTHH:mm'),
      end_date: moment().utc().startOf('month').format('YYYY-MM-DDTHH:mm'),
    },
  })

  const statInsurancesMutation = useGetStatInsurances()
  const insurances = statInsurancesMutation?.data?.data

  const handleClickSubmit = useCallback(
    (data) => {
      statInsurancesMutation.mutate(data)
    },
    [statInsurancesMutation]
  )

  useEffect(() => {
    handleSubmit(handleClickSubmit)()
  }, [handleSubmit, handleClickSubmit])

  return (
    <>
      <Form className="mb-5" onSubmit={handleSubmit(handleClickSubmit)}>
        <Row>
          <Col>
            <Form.Group>
              <FloatingLabel controlId="startDateLabel" label="От">
                <FormControl
                  {...register('start_date', {
                    setValueAs: (v) => checkSetValue(v, {}, 'datetime-local'),
                  })}
                  type="datetime-local"
                  isInvalid={errors?.start_date}
                  placeholder="От"
                />
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col>
            <Form.Group>
              <FloatingLabel controlId="endDateLabel" label="До">
                <FormControl
                  {...register('end_date', {
                    setValueAs: (v) => checkSetValue(v, {}, 'datetime-local'),
                  })}
                  type="datetime-local"
                  isInvalid={errors?.end_date}
                  placeholder="До"
                />
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col className="align-content-center" md="auto">
            <Button type="submit" size="lg" disabled={statInsurancesMutation.isLoading} style={{ minWidth: '121px' }}>
              {statInsurancesMutation.isLoading ? <Spinner animation="grow" size="sm" /> : 'Получить'}
            </Button>
          </Col>
        </Row>
      </Form>

      <Row>
        <Col>Оплаченные страховки: {insurances?.count}</Col>
      </Row>
    </>
  )
}
