import { useEffect, useRef, useState } from 'react'
import { Button, Col, Figure, FloatingLabel, Form, FormControl, FormSelect, Row } from 'react-bootstrap'
import { Link, useParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'
import PagePreloader from '@/components/PagePreloader/PagePreloader'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { AppRoute, MAX_IMG_SIZE } from '@/const'
import { useCreateInsuranceType } from '@/features/insurances/api/createInsuranceType'
import { useGetInsuranceFormats } from '@/features/insurances/api/getInsuranceFormats'
import { useGetInsuranceIntegrations } from '@/features/insurances/api/getInsuranceIntegrations'
import { useGetInsuranceTypes } from '@/features/insurances/api/getInsuranceTypes'
import { useUpdateInsuranceType } from '@/features/insurances/api/upadteInsuranceType'
import { insuranceTypeFormatsTableData } from '@/features/insurances/insurancesScreenData'
import { useGetProcessingList } from '@/features/processing/api/getProcessingList'
import { useToast } from '@/hooks/useToast'
import styles from '@/pages/ShopFormScreen/ShopFormScreen.module.scss'
import { convertBase64 } from '@/utils/common'
import { updateFormData } from '@/utils/forms'
import { getImageSrc } from '@/utils/images'

const SECONDS_IN_DAY = 86400

const ReturnLink = ({ link }) => {
  if (link && link.includes('http')) {
    return (
      <a href={link} target="_blank" rel="noreferrer">
        {link}
      </a>
    )
  }

  return (
    <a
      href={`${window.location.origin}${link}`}
      target="_blank"
      rel="noreferrer"
    >{`${window.location.origin}${link}`}</a>
  )
}

export const InsuranceTypeForm = () => {
  const [formData, setFormData] = useState({})
  const [defaultFormData, setDefaultFormData] = useState({})
  const [isViewLegalsInput, setIsViewLegalsInput] = useState(true)

  const legalsInputRef = useRef(null)

  const { public_id } = useParams()
  const openToast = useToast()

  const createInsuranceTypeMutate = useCreateInsuranceType()
  const updateInsuranceTypeMutate = useUpdateInsuranceType()
  const getInsuranceFormatsQuery = useGetInsuranceFormats(public_id)
  const integrationsQuery = useGetInsuranceIntegrations()
  const getInsuranceTypesQuery = useGetInsuranceTypes()
  const getProcessingListQuery = useGetProcessingList()
  const insuranceFormats = getInsuranceFormatsQuery?.data?.data?.values
  const processingList = getProcessingListQuery?.data?.data?.values
  const integrations = integrationsQuery?.data?.data?.values || []
  const data = getInsuranceTypesQuery?.data?.data
  const isLoading = getInsuranceTypesQuery.isLoading

  const isUpdateItem = !!public_id

  useEffect(() => {
    if (public_id && data) {
      const findItem = data.values.find((item) => item.public_id === public_id)

      if (findItem) {
        setDefaultFormData({ ...findItem })
      }
    } else if (!isUpdateItem) {
      setFormData({ active: true })
    }
  }, [public_id, data, isUpdateItem])

  useEffect(() => {
    if (Object.keys(defaultFormData).length > 0 && defaultFormData?.legals) {
      setIsViewLegalsInput(false)
    }
  }, [defaultFormData])

  const handleSubmitForm = () => {
    if (isUpdateItem) {
      updateInsuranceTypeMutate.mutate({ id: public_id, data: formData })
    } else {
      createInsuranceTypeMutate.mutate(formData)
    }
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleChangeSelect = (evt) => {
    const value = evt.target.value
    const name = evt.target.name

    if (value === 'null') {
      setFormData({ ...formData, [name]: { public_id: null } })
    } else {
      setFormData({ ...formData, [name]: { public_id: value } })
    }
  }

  const handleChangeCheckbox = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.checked })
  }

  const handleChangeTimeField = (evt) => {
    const seconds = evt.target.value * SECONDS_IN_DAY
    const name = evt.target.name

    setFormData({ ...formData, [name]: seconds })
  }

  const convertSecondsToDays = (value) => {
    return value / SECONDS_IN_DAY
  }

  const handleFileRead = async (event) => {
    const file = event.target.files[0]
    const fileSizeInKB = file.size
    const base64 = await convertBase64(file)

    const newFormData = { ...formData }

    if (base64 === '') {
      delete newFormData.logo
      setFormData({ ...newFormData })
    } else if (fileSizeInKB <= MAX_IMG_SIZE) {
      setFormData({ ...formData, logo: base64 })
    } else if (fileSizeInKB > MAX_IMG_SIZE) {
      openToast.error({ message: `Файл слишком большой: ${file.name}` })
    }
  }

  const handleDeletePictures = () => {
    const copyNewsStoryData = { ...formData }

    delete copyNewsStoryData.logo

    setFormData({ ...copyNewsStoryData })
  }

  const handleRemoveLegals = () => {
    setIsViewLegalsInput(true)

    setFormData({ ...formData, legals: null })
  }

  const handleResetInputLegals = () => {
    legalsInputRef.current.value = ''

    if (defaultFormData?.legals) {
      setFormData({ ...formData, legals: null })
    } else {
      setFormData((prevState) => {
        const { legals: _, ...newState } = prevState
        return newState
      })
    }
  }

  const handleHideLegalsInput = () => {
    setIsViewLegalsInput(false)
    legalsInputRef.current.value = ''
    setFormData((prevState) => {
      const { legals: _, ...newState } = prevState
      return newState
    })
  }

  const handleChangeLegals = async (evt) => {
    const file = evt.target.files[0]
    const base64 = await convertBase64(file)

    setFormData({ ...formData, legals: base64 })
  }

  return (
    <Layout>
      <PagePreloader isLoading={isLoading}>
        <Row className="mb-3">
          <Col>
            <Button as={Link} to={`${AppRoute.INSURANCES}/types`} variant="outline-secondary">
              <i className="bi bi-arrow-left me-2" />
              Вернуться к списку страховок
            </Button>
          </Col>
        </Row>
        <Row>
          <h3 className="mb-3">{isUpdateItem ? 'Редактирование страховки' : 'Создание страховки'}</h3>
        </Row>

        <Row className="g-3 mb-5">
          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="nameLicenceLabel" label="Название (“Start”)">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.name}
                name="name"
                type="text"
                placeholder="Название (“Start”)"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="titleLicenceLabel" label="Заголовок (“Любительская”)">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.title}
                name="title"
                type="text"
                placeholder="Заголовок (“Любительская”)"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="subtitleLicenceLabel" label="Подзаголовок">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.subtitle}
                name="subtitle"
                type="text"
                placeholder="Подзаголовок"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="descriptionLicenceLabel" label="Описание">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.description}
                name="description"
                type="text"
                placeholder="Описание"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="footnoteLicenceLabel" label="Примечание">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.footnote}
                name="footnote"
                type="text"
                placeholder="Примечание"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="publicIdLabelOrder" label="Юридическое лицо">
              <FormSelect onChange={handleChangeSelect} name="processing" aria-label="processing_public_id">
                <option>выберите один из вариантов</option>
                {processingList?.map((item) => (
                  <option
                    value={item.public_id}
                    selected={item.public_id === defaultFormData?.processing?.public_id}
                    key={item._id}
                  >
                    {item.entity}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="count_limitLicenceLabel" label="Доступное количество (лимит)">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.count_limit}
                name="count_limit"
                type="number"
                placeholder="Доступное количество"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="priceLicenceLabel" label="Стоимость (в рублях)">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.price}
                name="price"
                type="number"
                placeholder="Стоимость"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="startNumberLicenceLabel" label="Начало нумерации">
              <FormControl
                onChange={handleChangeField}
                defaultValue={defaultFormData?.start_number}
                name="start_number"
                type="number"
                placeholder="Стартовый номер"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="validityTimeLicenceLabel" label="Время действия (в днях)">
              <FormControl
                onChange={handleChangeTimeField}
                defaultValue={defaultFormData?.validity_time ? convertSecondsToDays(defaultFormData.validity_time) : ''}
                name="validity_time"
                type="number"
                placeholder="Время действия"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="activationВelayLicenceLabel" label="Задержка начала действия (в днях)">
              <FormControl
                onChange={handleChangeTimeField}
                defaultValue={
                  defaultFormData?.activation_delay ? convertSecondsToDays(defaultFormData.activation_delay) : ''
                }
                name="activation_delay"
                type="number"
                placeholder="Задержка начала действия (в днях)"
              />
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <FloatingLabel controlId="integrationLabel" label="Интеграция">
              <FormSelect onChange={handleChangeSelect} name="api_client" aria-label="api_client_public_id">
                <option value="null">Без интеграции</option>
                {integrations?.map((item) => (
                  <option
                    value={item.public_id}
                    selected={item.public_id === defaultFormData?.api_client?.public_id}
                    key={item.public_id}
                  >
                    {item.name}
                  </option>
                ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <Form.Group controlId="formFile">
              <Form.Label className={styles.fileButton}>Выбрать логотип</Form.Label>
              <Form.Control
                className="visually-hidden"
                type="file"
                onChange={handleFileRead}
                accept=".png, .jpg, .jpeg, .svg, .webp"
              />
            </Form.Group>
            <span className="small">* максимальный размер логотипа 5MB</span>
            <br />
            <span className="small">* поле с логотипом обязательно</span>
          </Col>

          {(formData?.logo || defaultFormData?.logo) && (
            <Col className="d-grid justify-content-center" md={{ offset: 3, span: 6 }}>
              <Figure>
                <Figure.Image
                  width={171}
                  alt="Предпросмотр логотипа"
                  src={getImageSrc(formData?.logo || defaultFormData?.logo)}
                />
              </Figure>
              {formData?.logo && (
                <Row>
                  <Col>
                    <Button onClick={handleDeletePictures} variant="link" size="sm">
                      удалить
                    </Button>
                  </Col>
                </Row>
              )}
            </Col>
          )}

          <Col className="mt-4" md={{ offset: 3, span: 6 }}>
            {isViewLegalsInput && (
              <Form.Group controlId="formFile" className="mb-3">
                <Row>
                  <Col md="auto">
                    <Form.Label>Условия страхования (pdf, docx) </Form.Label>
                  </Col>
                  <Col md="auto">
                    {defaultFormData?.legals && (
                      <Button onClick={handleHideLegalsInput} variant="link" size="sm">
                        отмена
                      </Button>
                    )}
                    {formData?.legals && (
                      <Button onClick={handleResetInputLegals} variant="link" size="sm">
                        очистить
                      </Button>
                    )}
                  </Col>
                </Row>
                <Form.Control
                  onChange={handleChangeLegals}
                  type="file"
                  accept=".pdf, .docx"
                  lang="ru"
                  ref={legalsInputRef}
                />
              </Form.Group>
            )}

            {!isViewLegalsInput && (
              <div className="mb-3">
                <p>
                  Условия страхования загружены:{' '}
                  <Button onClick={handleRemoveLegals} variant="link" size="sm">
                    обновить/удалить
                  </Button>
                </p>
                <ReturnLink link={defaultFormData?.legals} />
              </div>
            )}
          </Col>

          <Col md={{ offset: 3, span: 6 }}>
            <Row>
              <Col md="auto">
                <Form.Label>Сделать страховку активной:</Form.Label>
              </Col>
              <Col>
                <Form.Check
                  type="checkbox"
                  id="active"
                  label="Да"
                  name="active"
                  onChange={handleChangeCheckbox}
                  checked={formData?.active ?? defaultFormData?.active}
                />
              </Col>
            </Row>
          </Col>
          <Col md={{ offset: 3, span: 6 }}>
            <Row>
              <Col md="auto">
                <Form.Label>Сделать страховку по умолчанию:</Form.Label>
              </Col>
              <Col>
                <Form.Check
                  type="checkbox"
                  id="deafault"
                  label="Да"
                  name="default"
                  onChange={handleChangeCheckbox}
                  checked={formData?.default ?? defaultFormData?.default}
                />
              </Col>
            </Row>
          </Col>
        </Row>

        <Row className="mb-5">
          <Col className="d-grid" md={{ offset: 4, span: 4 }}>
            <Button onClick={handleSubmitForm} variant="success" size="lg" disabled={!isUpdateItem && !formData.logo}>
              Сохранить
            </Button>
          </Col>
        </Row>
      </PagePreloader>

      {public_id && (
        <>
          <h5 className="mb-3">События для которых действует страховка</h5>
          <TableTemplate values={insuranceFormats} data={insuranceTypeFormatsTableData} />
        </>
      )}
    </Layout>
  )
}
