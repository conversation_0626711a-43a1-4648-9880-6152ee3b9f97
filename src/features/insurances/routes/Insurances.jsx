import React from 'react'
import { Tab, Tabs } from 'react-bootstrap'
import { useNavigate, useParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { AppRoute } from '@/const'
import InsuranceTypes from '@/features/insurances/components/InsuranceTypes/InsuranceTypes'
import { Integrations } from '@/features/insurances/components/Integrations'
import SoldgoodInsurances from '@/features/insurances/components/SoldgoodInsurances'

const TabKey = {
  ISSUED: 'issued',
  TYPES: 'types',
  STATISTICS: 'statistics',
  INTEGRATIONS: 'integrations',
}

export const Insurances = () => {
  const { tab } = useParams()
  const navigate = useNavigate()

  const handleSelectTab = (tab) => {
    if (tab === TabKey.ISSUED) {
      navigate(AppRoute.INSURANCES)
    } else {
      navigate(`${AppRoute.INSURANCES}/${tab}`)
    }
  }

  return (
    <Layout title="Страховки">
      <Tabs className="mb-5" onSelect={handleSelectTab} activeKey={tab}>
        <Tab title="Выданные страховки" eventKey={TabKey.ISSUED}>
          {!tab && <SoldgoodInsurances />}
        </Tab>
        <Tab title="Виды страховок" eventKey={TabKey.TYPES}>
          {tab === TabKey.TYPES && <InsuranceTypes />}
        </Tab>
        <Tab title="Интеграции" eventKey={TabKey.INTEGRATIONS}>
          {tab === TabKey.INTEGRATIONS && <Integrations />}
        </Tab>
        {/*<Tab title="Статистика" eventKey={TabKey.STATISTICS}>*/}
        {/*  {tab === TabKey.STATISTICS && <InsuranceStatistics />}*/}
        {/*</Tab>*/}
      </Tabs>
    </Layout>
  )
}
