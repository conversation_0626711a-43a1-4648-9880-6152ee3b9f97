import { AppRoute } from '@/const'

export const soldgoodInsurancesTableData = {
  title: '',
  list: [
    { label: 'Номер', value: 'info.number' },
    { label: 'Страховка', value: 'info.insurance.name' },
    { label: 'Статус', value: 'status', param: 'ticketStatus' },
    { label: 'Фамилия', value: 'info.last_name' },
    { label: 'Имя', value: 'info.first_name' },
    { label: 'Отчество', value: 'info.second_name' },
    { label: 'Дата рождения', value: 'info.birth_date', param: 'date' },
    { label: 'Начало действия', value: 'info.start_date', param: 'date' },
    { label: 'Окончание действия', value: 'info.validity_date', param: 'date' },
    { label: 'Дата создания', value: 'created', param: 'date' },
    { label: 'Полис', value: 'info.api_client.success', param: 'yesOrNoEmoji' },
    { label: 'Фото', value: 'info.picture', param: 'downloadPhoto' },
  ],
}

export const insurancesTableData = {
  title: '',
  clickUrl: AppRoute.INSURANCE_TYPE_EDIT,
  clickUrlParam: 'public_id',
  list: [
    { label: 'Название', value: 'name' },
    { label: 'Заголовок', value: 'title' },
    { label: 'Подзаголовок', value: 'subtitle' },
    { label: 'Описание', value: 'description' },
    { label: 'Срок действия', value: 'validity_time', param: 'secondsToDays' },
  ],
}

export const insuranceTypeFormatsTableData = {
  title: '',
  clickUrl: AppRoute.EVENTS,
  clickUrlParam: 'event.public_id',
  list: [
    { label: '', value: 'event_type.logo', param: 'image' },
    { label: 'Тип события', value: 'event.title' },
    { label: 'Формат', value: 'title' },
    { label: 'Город', value: 'city.name_ru' },
    { label: 'Дата старта', value: 'start_time', param: 'date' },
  ],
}
