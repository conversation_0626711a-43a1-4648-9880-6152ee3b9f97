import { useCallback, useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import { useToast } from '@/hooks/useToast'

import { useSearchInsuranceByNumber } from '../api/searchInsuranceByNumber'

export const useInsuranceSearch = (onInsuranceFound) => {
  const [urlNumber, setUrlNumber] = useState(null)
  const location = useLocation()
  const navigate = useNavigate()
  const openToast = useToast()

  const clearUrlParam = useCallback(() => {
    const searchParams = new URLSearchParams(location.search)
    if (searchParams.has('number')) {
      searchParams.delete('number')
      const newUrl = `${location.pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
      navigate(newUrl, { replace: true })
    }
    setUrlNumber(null)
  }, [location.pathname, location.search, navigate])

  // Извлекаем номер из URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    const numberParam = searchParams.get('number')

    if (numberParam?.trim()) {
      const cleanNumber = numberParam.trim()

      // Валидируем номер
      if (isNaN(cleanNumber) || parseFloat(cleanNumber) <= 0 || !Number.isInteger(parseFloat(cleanNumber))) {
        openToast.error({ message: 'Номер страховки должен быть положительным целым числом' })
        clearUrlParam()
        return
      }

      setUrlNumber(cleanNumber)
    } else {
      setUrlNumber(null)
    }
  }, [location.search, clearUrlParam, openToast])

  // Выполняем поиск по номеру
  const { isLoading } = useSearchInsuranceByNumber(urlNumber, {
    onSuccess: (response) => {
      const insurances = response?.data?.values || []

      if (insurances.length === 1) {
        onInsuranceFound?.(insurances[0], true) // true = открыть модальное окно
        openToast.success({ message: `Найдена страховка с номером ${urlNumber}` })
      } else if (insurances.length > 1) {
        onInsuranceFound?.(insurances, false) // false = показать в списке
        openToast.success({
          message: `Найдено ${insurances.length} страховок с номером ${urlNumber}`,
          duration: 4000,
        })
      } else {
        openToast.error({ message: `Страховка с номером ${urlNumber} не найдена` })
        clearUrlParam()
      }
    },
    onError: (error) => {
      console.error('Ошибка при поиске страховки:', error)
      openToast.error({ message: 'Ошибка при поиске страховки. Попробуйте позже.' })
      clearUrlParam()
    },
  })

  return {
    isSearching: isLoading && !!urlNumber,
    clearUrlParam,
  }
}
