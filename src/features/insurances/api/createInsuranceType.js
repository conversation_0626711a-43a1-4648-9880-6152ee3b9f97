import { useMutation } from 'react-query'
import { useNavigate } from 'react-router-dom'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createInsuranceType = (data) => {
  return axios.post(APIRoute.ACTIONS_TYPES_INSURANCE, data)
}

export const useCreateInsuranceType = () => {
  const openToast = useToast()
  const navigate = useNavigate()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['insuranceTypes'])
        navigate(`${AppRoute.INSURANCES}/types`)
        openToast.success({ message: 'Страховка создана!' })
      }
    },
    mutationFn: createInsuranceType,
  })
}
