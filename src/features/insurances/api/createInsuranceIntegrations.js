import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createInsuranceIntegrations = (data) => {
  return axios.post(APIRoute.CRUD_INSURANCE_INTEGRATIONS, data)
}

export const useCreateInsuranceIntegrations = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['insuranceIntegrations'])
        openToast.success({ message: 'Интеграция создана!' })
      }
    },
    mutationFn: createInsuranceIntegrations,
  })
}
