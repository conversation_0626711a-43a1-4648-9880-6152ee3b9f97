import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getInsuranceFormats = (publicId) => {
  return axios.get(`${APIRoute.INSURANCE_FORMATS}/${publicId}`)
}

export const useGetInsuranceFormats = (publicId) => {
  return useQuery({
    enabled: Boolean(publicId),
    queryKey: ['insuranceFormats', publicId],
    queryFn: () => getInsuranceFormats(publicId),
  })
}
