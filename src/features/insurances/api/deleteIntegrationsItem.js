import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteIntegrationsItem = (id) => {
  return axios.delete(`${APIRoute.CRUD_INSURANCE_INTEGRATIONS}/${id}`)
}

export const useDeleteIntegrationsItem = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['insuranceIntegrations'])

        openToast.success({ message: 'Интеграция успешно удалена!' })
      }
    },
    mutationFn: deleteIntegrationsItem,
  })
}
