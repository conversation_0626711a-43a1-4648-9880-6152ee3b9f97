import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteInsuranceType = (id) => {
  return axios.delete(`${APIRoute.ACTIONS_TYPES_INSURANCE}/${id}`)
}

export const useDeleteInsuranceType = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['insuranceTypes'])

        openToast.success({ message: 'Страховка успешно удалена!' })
      }
    },
    mutationFn: deleteInsuranceType,
  })
}
