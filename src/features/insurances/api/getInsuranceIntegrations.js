import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 10

const getInsuranceIntegrations = () => {
  return axios.get(APIRoute.CRUD_INSURANCE_INTEGRATIONS)
}

export const useGetInsuranceIntegrations = () => {
  return useQuery({
    queryKey: ['insuranceIntegrations'],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: getInsuranceIntegrations,
  })
}
