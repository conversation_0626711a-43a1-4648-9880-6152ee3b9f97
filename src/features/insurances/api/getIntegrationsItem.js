import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getIntegrationsItem = (publicId) => {
  return axios.get(`${APIRoute.CRUD_INSURANCE_INTEGRATIONS}/${publicId}`)
}

export const useGetIntegrationsItem = (publicId) => {
  return useQuery({
    enabled: Boolean(publicId),
    queryKey: ['insuranceIntegrations', publicId],
    queryFn: () => getIntegrationsItem(publicId),
  })
}
