import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

export const sendingSoldgoodInsurance = (data) => {
  return axios.post(APIRoute.INSURANCE_API_CLIENT_SOLDGOODS, data)
}

export const useSendingSoldgoodInsurance = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        openToast.success({ message: 'Данные отправлены в страховую' })
      }
    },
    mutationFn: sendingSoldgoodInsurance,
  })
}
