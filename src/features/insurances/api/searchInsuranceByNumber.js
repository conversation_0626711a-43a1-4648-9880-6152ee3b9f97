import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const searchInsuranceByNumber = (number) => {
  return axios.get(`${APIRoute.SEARCH_INSURANCE_NUMBER}/${number}`)
}

export const useSearchInsuranceByNumber = (number, options = {}) => {
  return useQuery({
    queryKey: ['searchInsuranceByNumber', number],
    queryFn: () => searchInsuranceByNumber(number),
    enabled: !!number && !isNaN(number) && parseFloat(number) > 0,
    staleTime: 0, // Всегда делаем свежий запрос для поиска
    refetchOnWindowFocus: false,
    retry: 1,
    ...options,
  })
}
