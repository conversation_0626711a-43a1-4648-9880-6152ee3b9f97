import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateInsuranceIntegrations = ({ id, data }) => {
  return axios.put(`${APIRoute.CRUD_INSURANCE_INTEGRATIONS}/${id}`, data)
}

export const useUpdateInsuranceIntegrations = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['insuranceIntegrations'])
        openToast.success({ message: 'Интеграция обновлена!' })
      }
    },
    mutationFn: updateInsuranceIntegrations,
  })
}
