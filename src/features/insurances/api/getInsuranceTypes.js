import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 10

const getInsuranceTypes = () => {
  return axios.get(APIRoute.GET_TYPES_INSURANCE)
}

export const useGetInsuranceTypes = () => {
  return useQuery({
    queryKey: ['insuranceTypes'],
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryFn: getInsuranceTypes,
  })
}
