import { useMutation } from 'react-query'
import { useNavigate } from 'react-router-dom'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const updateInsuranceType = ({ id, data }) => {
  return axios.put(`${APIRoute.ACTIONS_TYPES_INSURANCE}/${id}`, data)
}

export const useUpdateInsuranceType = () => {
  const openToast = useToast()
  const navigate = useNavigate()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries(['insuranceTypes'])
        navigate(`${AppRoute.INSURANCES}/types`)
        openToast.success({ message: 'Страховка изменена!' })
      }
    },
    mutationFn: updateInsuranceType,
  })
}
