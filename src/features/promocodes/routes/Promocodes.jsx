import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON> } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'
import PagePreloader from '@/components/PagePreloader/PagePreloader'
import { Pagination } from '@/components/Pagination/Pagination'

import { Role } from '@/const'
import { useGetPromocodes } from '@/features/promocodes/api/getPromocodes'
import { Promocode } from '@/features/promocodes/components/Promocode/Promocode'
import { PromocodesLoader } from '@/features/promocodes/components/PromocodesLoader/PromocodesLoader'
import { PromocodeUsageModal } from '@/features/promocodes/components/PromocodeUsageModal/PromocodeUsageModal'
import { SearchPromocodes } from '@/features/promocodes/components/SearchPromocodes/SearchPromocodes'
import { usePagination } from '@/hooks/usePagination'
import ModalCreatePromocode from '@/pages/PromocodesScreen/components/modals/ModalCreatePromocode'
import ModalResultPromocodes from '@/pages/PromocodesScreen/components/modals/ModalResultPromocodes'
import storage from '@/utils/storage'

export const Promocodes = () => {
  const userStorage = storage.getUserObj()
  const [promocodes, setPromocodes] = useState([])

  // Проверяем наличие роли promocode
  const hasPromocodeRole = userStorage?.role?.includes(Role.PROMO) || userStorage?.role?.includes(Role.SUPERADMIN)

  const pagination = usePagination({ paginationLimit: 12, onSetSlicedValues: setPromocodes })

  const [newPromocodes, setNewPromocodes] = useState([])
  const [selectedPromocode, setSelectedPromocode] = useState({})
  const [isShowUsageModal, setIsShowUsageModal] = useState(false)
  const [generatePromocodePopup, setGeneratePromocodePopup] = useState(false)
  const [resultNewPromocodesPopup, setResultNewPromocodesPopup] = useState(false)
  const [isEditPromocode, setIsEditPromocode] = useState(null)
  const [isClonePromocode, setIsClonePromocode] = useState(false)

  const handleSetPromocodes = (data) => {
    setPromocodes(data.values)
    pagination.init({ count: data?.count })
  }

  const promocodesQuery = useGetPromocodes(pagination.skip, pagination.limit, handleSetPromocodes)

  const handleUpdate = (isStart) => {
    promocodesQuery.refetch()

    isStart && pagination.start('count')
  }

  const handleCloseGeneratePopup = () => {
    setGeneratePromocodePopup(false)
    setSelectedPromocode({})
    setIsEditPromocode(null)
    setIsClonePromocode(false)
  }

  const handleEditPromocode = (promocode) => {
    setSelectedPromocode(promocode)
    setGeneratePromocodePopup(true)
    setIsEditPromocode(true)
  }

  const handleClonePromocode = (promocode) => {
    setSelectedPromocode(promocode)
    setGeneratePromocodePopup(true)
    setIsEditPromocode(false)
    setIsClonePromocode(true)
  }

  const handleCloseResultPopup = () => {
    setResultNewPromocodesPopup(false)
  }

  const handleSetSearchedPromocodes = (values) => {
    pagination.init({ values: values })
    pagination.start()
  }

  function ButtonUsed() {
    return <Button onClick={() => setIsShowUsageModal(true)}>Использования</Button>
  }

  // Если у пользователя нет роли promo, показываем только кнопку "Создать"
  if (!hasPromocodeRole) {
    return (
      <Layout title="Промокоды">
        <Row className="justify-content-center" style={{ minHeight: '60vh' }}>
          <Col xs="auto" className="d-flex align-items-center">
            <Button variant="primary" size="lg" onClick={() => setGeneratePromocodePopup(true)}>
              <i className="bi bi-plus-lg me-2" />
              Создать промокод
            </Button>
          </Col>
        </Row>

        <ModalCreatePromocode
          show={generatePromocodePopup}
          handleCloseModal={handleCloseGeneratePopup}
          newPromocodes={setNewPromocodes}
          handleResultModal={setResultNewPromocodesPopup}
          getPromocodes={handleUpdate}
          currentPage={pagination.handlePage}
          editPromocode={selectedPromocode}
          isEditPromocode={isEditPromocode}
          isClonePromocode={isClonePromocode}
        />

        <ModalResultPromocodes
          show={resultNewPromocodesPopup}
          handleClosePopup={handleCloseResultPopup}
          newPromocodes={newPromocodes}
        />
      </Layout>
    )
  }

  return (
    <Layout title="Промокоды" onClickAddButton={() => setGeneratePromocodePopup(true)} buttons={ButtonUsed}>
      <SearchPromocodes
        onSetPromocodes={handleSetSearchedPromocodes}
        onUpdatePromocodes={handleUpdate}
        onChangePage={pagination.handlePage}
      />

      <Row className="g-3">
        <PagePreloader isLoading={promocodesQuery.isLoading} preloader={<PromocodesLoader />}>
          {promocodes?.map((item) => (
            <Col xs={12} sm={6} lg={4} key={item.code}>
              <Promocode
                promocode={item}
                onUpdatePromocodes={handleUpdate}
                onEditPromocode={handleEditPromocode}
                onClonePromocode={handleClonePromocode}
              />
            </Col>
          ))}
        </PagePreloader>
      </Row>

      <Row className="mt-4">
        <Col>
          <Pagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            count={pagination.count}
            limit={pagination.limit}
            onChangePage={pagination.handlePage}
            pageSizeOptions={[12, 24, 36, 48, 60]}
          />
        </Col>
      </Row>

      <ModalCreatePromocode
        show={generatePromocodePopup}
        handleCloseModal={handleCloseGeneratePopup}
        newPromocodes={setNewPromocodes}
        handleResultModal={setResultNewPromocodesPopup}
        getPromocodes={handleUpdate}
        currentPage={pagination.handlePage}
        editPromocode={selectedPromocode}
        isEditPromocode={isEditPromocode}
        isClonePromocode={isClonePromocode}
      />

      <ModalResultPromocodes
        show={resultNewPromocodesPopup}
        handleClosePopup={handleCloseResultPopup}
        newPromocodes={newPromocodes}
      />

      <PromocodeUsageModal isShow={isShowUsageModal} onClose={() => setIsShowUsageModal(false)} />
    </Layout>
  )
}
