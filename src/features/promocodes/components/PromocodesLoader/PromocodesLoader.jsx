import { <PERSON>, <PERSON>, Spinner } from 'react-bootstrap'

import styles from './PromocodesLoader.module.scss'

export const PromocodesLoader = () => {
  return (
    <>
      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>

      <Col xs={12} sm={6} lg={4}>
        <Card className={styles.card}>
          <Spinner className={styles.spinner} animation="grow" />
        </Card>
      </Col>
    </>
  )
}
