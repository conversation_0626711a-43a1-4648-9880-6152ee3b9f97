import { useRef } from 'react'
import { <PERSON><PERSON>, Col, Dropdown, Dropdown<PERSON>utton, FloatingLabel, Form, InputGroup, Row } from 'react-bootstrap'

import { useSearchPromocodes } from '@/features/promocodes/api/searchPromocodes'

export const SearchPromocodes = ({ onSetPromocodes, onUpdatePromocodes }) => {
  const searchRef = useRef(null)
  const searchEndDateRef = useRef(null)
  const searchStartDateRef = useRef(null)

  const searchPromocodesMutation = useSearchPromocodes(onSetPromocodes)

  const handleClickSearch = (evt) => {
    const name = evt.target.dataset.search
    let value

    if (name === 'start_date') {
      value = new Date(searchStartDateRef.current.value)
    } else if (name === 'end_date') {
      value = new Date(searchEndDateRef.current.value)
    } else {
      value = searchRef.current.value
    }

    const body = { [name]: value }

    searchPromocodesMutation.mutate(body)
  }

  const handleChaneSearchField = (evt) => {
    if (evt.target.value === '') {
      onUpdatePromocodes(true)
    }
  }

  return (
    <Row className="mb-4 g-3">
      <Col xs={12}>
        <InputGroup>
          <FloatingLabel controlId="floatingInput" label="Введите код, формат или описание">
            <Form.Control
              onChange={handleChaneSearchField}
              ref={searchRef}
              type="text"
              placeholder="Введите код, формат или описание"
            />
          </FloatingLabel>

          <DropdownButton
            variant="primary"
            title="искать по"
            id="input-group-dropdown-2"
            disabled={searchPromocodesMutation.isLoading}
          >
            <Dropdown.Item onClick={handleClickSearch} data-search="code">
              коду
            </Dropdown.Item>
            <Dropdown.Item onClick={handleClickSearch} data-search="formats">
              формату
            </Dropdown.Item>
            <Dropdown.Item onClick={handleClickSearch} data-search="description">
              описанию
            </Dropdown.Item>
          </DropdownButton>
        </InputGroup>
      </Col>

      <Col xs={12} sm={6}>
        <InputGroup>
          <FloatingLabel controlId="floatingInput" label="Дата начала действия промокода">
            <Form.Control
              onChange={handleChaneSearchField}
              ref={searchStartDateRef}
              type="datetime-local"
              placeholder="Дата начала действия промокода"
            />
          </FloatingLabel>

          <Button onClick={handleClickSearch} data-search="start_date">
            искать
          </Button>
        </InputGroup>
      </Col>

      <Col xs={12} sm={6}>
        <InputGroup>
          <FloatingLabel controlId="floatingInput" label="Дата окончания действия промокода">
            <Form.Control
              onChange={handleChaneSearchField}
              ref={searchEndDateRef}
              type="datetime-local"
              placeholder="Дата окончания действия промокода"
            />
          </FloatingLabel>

          <Button onClick={handleClickSearch} data-search="end_date">
            искать
          </Button>
        </InputGroup>
      </Col>
    </Row>
  )
}
