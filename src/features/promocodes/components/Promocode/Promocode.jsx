import moment from 'moment'
import { useState } from 'react'
import { Card, Container, Dropdown, ListGroup, Navbar, Offcanvas } from 'react-bootstrap'
import { Link, useNavigate } from 'react-router-dom'

import { CopyToClipboard } from '@/components/CopyToClipboard/CopyToClipboard'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { AppRoute } from '@/const'
import { useCheckPromocode } from '@/features/promocodes/api/checkPromocode'
import { useDeletePromocode } from '@/features/promocodes/api/deletePromocode'
import { API_URL } from '@/lib/axios'
import { getDiscountForm, getKindName, getRightNames } from '@/utils/common'
import { unixToMoment } from '@/utils/date'

const HOURS_IN_DAY = 24
const MINUTES_IN_HOUR = 60

const returnTypeIcon = (type) => {
  switch (type) {
    case 'ticket':
      return <i className="bi bi-ticket-perforated" />
    case 'product':
      return <i className="bi bi-cart-check" />
    case 'license':
      return <i className="bi bi-card-checklist" />
    case 'insurance':
      return <i className="bi bi-shield-check" />
    default:
      return null
  }
}

const returnCodeTypeName = (type) => {
  switch (type) {
    case 'ticket':
      return 'Билет'
    case 'product':
      return 'Товар'
    case 'license':
      return 'Лицензия'
    case 'insurance':
      return 'Страховка'
    default:
      return type ?? '-'
  }
}

const returnDuration = (date) => {
  const currentTime = moment()
  const differenceDate = unixToMoment(date).diff(currentTime)
  const duration = moment.duration(differenceDate, 'milliseconds')

  const days = Math.floor(duration.asDays())
  const hours = Math.floor(duration.asHours() % HOURS_IN_DAY)
  const minutes = Math.floor(duration.asMinutes() % MINUTES_IN_HOUR)
  const seconds = Math.floor(duration.asSeconds() % MINUTES_IN_HOUR)

  if (days > 0) {
    return `${days} ${getRightNames(days, 'день', 'дня', 'дней')}`
  } else if (hours > 0) {
    return `${hours} ${getRightNames(hours, 'час', 'часа', 'часов')}`
  } else if (minutes > 0) {
    return `${minutes} ${getRightNames(minutes, 'минута', 'минуты', 'минут')}`
  } else if (seconds > 0) {
    return `${seconds} ${getRightNames(seconds, 'секунда', 'секунды', 'секунд')}`
  }

  return null
}

const returnActionTime = (item) => {
  const currentTime = moment()

  if (currentTime.isSameOrBefore(unixToMoment(item.end_date))) {
    if (currentTime.isSameOrBefore(unixToMoment(item.start_date))) {
      return <span className="text-warning">Начнёт действовать через {returnDuration(item.start_date)}</span>
    } else {
      return `Действителен ещё ${returnDuration(item.end_date)}`
    }
  } else {
    return <span className="text-danger">Недействителен</span>
  }
}

export const Promocode = ({ promocode, onUpdatePromocodes, onEditPromocode, onClonePromocode, large = false }) => {
  const [isOpenNavbar, setIsOpenNavbar] = useState(false)
  const [isCodeCountTicket, setIsCountTicket] = useState(false)
  const [isCodeCountProduct, setIsCountProduct] = useState(false)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const isValidEndDate = moment().isSameOrBefore(unixToMoment(promocode.end_date))
  const isValidStartDate = moment().isSameOrAfter(unixToMoment(promocode.start_date))
  const navigate = useNavigate()

  const deletePromocodeMutation = useDeletePromocode(setConfirmDeleteModal, onUpdatePromocodes)
  const getPromocodeCount = useCheckPromocode(
    isCodeCountTicket,
    promocode.soldgood_type,
    promocode.code,
    promocode.formats?.[0]
  )
  const checkPromocodeShop = useCheckPromocode(
    isCodeCountProduct,
    promocode.soldgood_type,
    promocode.code,
    promocode.products?.[0]
  )
  const promocodeCountShop = checkPromocodeShop?.data?.data?.available_code_count
  const promocodeCountTicket = getPromocodeCount?.data?.data?.available_code_count

  const handleClickCount = (evt) => {
    evt.preventDefault()
    evt.stopPropagation()

    switch (promocode.soldgood_type) {
      case 'ticket':
        setIsCountTicket(true)
        break
      case 'product':
        setIsCountProduct(true)
        break
      default:
        setIsCountTicket(true)
        break
    }
  }

  const handleConfirmDelete = () => {
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = () => {
    deletePromocodeMutation.mutate({ code: promocode.code })
  }

  const handleOpenNavbar = (evt) => {
    evt.preventDefault()
    evt.stopPropagation()
    setIsOpenNavbar(true)
  }

  const handleCardClick = () => {
    navigate(`${AppRoute.PROMOCODE_ORDERS}/${promocode.code}`)
  }

  const handleCardKeyDown = (evt) => {
    if (evt.key === 'Enter' || evt.key === ' ') {
      evt.preventDefault()
      handleCardClick()
    }
  }

  const handleInteractiveClick = (evt) => {
    evt.stopPropagation()
  }

  return (
    <>
      <Card className="h-100">
        <Card.Body>
          <div
            className="d-flex flex-column h-100 text-decoration-none"
            onClick={handleCardClick}
            onKeyDown={handleCardKeyDown}
            role="button"
            tabIndex={0}
          >
            <div className="d-flex align-items-center gap-3 mb-3">
              <div className="d-flex align-items-center gap-2">
                {returnTypeIcon(promocode.soldgood_type)}
                <span className="fs-5 fw-medium mb-0">
                  <CopyToClipboard>{promocode.code}</CopyToClipboard>
                </span>
              </div>

              <p className="mb-0 fs-6">{getDiscountForm(promocode.discount, promocode.discount_form)}</p>

              {onEditPromocode && (
                <Dropdown className="ms-auto" onClick={handleInteractiveClick}>
                  <Dropdown.Toggle as="button" bsPrefix=" " className="border-0 bg-transparent p-0">
                    <i className="bi bi-three-dots-vertical" />
                  </Dropdown.Toggle>

                  <Dropdown.Menu>
                    <Dropdown.Item as="button" onClick={() => onEditPromocode(promocode)}>
                      <i className="bi bi-pencil me-2" />
                      Редактировать
                    </Dropdown.Item>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button" onClick={() => onClonePromocode(promocode)}>
                      <i className="bi bi-files me-2" />
                      Клонировать
                    </Dropdown.Item>
                    <Dropdown.Divider />
                    <Dropdown.Item onClick={handleConfirmDelete} className="text-danger" as="button">
                      <i className="bi bi-trash me-2" />
                      Удалить
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              )}
            </div>

            <p className="small text-muted mb-3">{promocode.description}</p>

            <div className="d-flex justify-content-between mb-3">
              <p className="mb-0 fw-medium">{promocode.active ? 'Активен' : 'Неактивен'}</p>
              <p className="mb-0 text-end text-success">{returnActionTime(promocode)}</p>
            </div>

            <div className={large ? 'row' : ''}>
              <div className={large ? 'col-12 col-lg-6' : ''}>
                <div className="row mb-3">
                  <div className="col-6">
                    <ItemLabelValue value={promocode.available_count} label="количество" />
                  </div>
                  <div className="col-6">
                    <ItemLabelValue
                      value={unixToMoment(promocode.start_date).format('D MMMM YYYY, HH:mm')}
                      label="начало действия"
                    />
                  </div>
                </div>

                <div className="row mb-3">
                  <div className="col-6">
                    <ItemLabelValue
                      value={
                        promocodeCountTicket ??
                        promocodeCountShop ?? (
                          <button
                            className="btn btn-link p-0 text-decoration-underline border-0"
                            onClick={handleClickCount}
                            disabled={
                              getPromocodeCount.isLoading ||
                              !isValidStartDate ||
                              !isValidEndDate ||
                              !promocode?.soldgood_type
                            }
                            type="button"
                          >
                            показать
                          </button>
                        )
                      }
                      label="остаток"
                    />
                  </div>
                  <div className="col-6">
                    <ItemLabelValue
                      value={unixToMoment(promocode.end_date).format('D MMMM YYYY, HH:mm')}
                      label="окончание действия"
                    />
                  </div>
                </div>
              </div>

              <div className={large ? 'col-12 col-lg-6' : ''}>
                <div className="row mb-3">
                  <div className="col-6">
                    <ItemLabelValue value={returnCodeTypeName(promocode.soldgood_type)} label="тип" />
                  </div>
                  <div className="col-6">
                    <ItemLabelValue
                      value={
                        promocode.user_public_id ? (
                          <Link
                            className="text-decoration-underline"
                            to={`/user/${promocode.user_public_id}`}
                            onClick={handleInteractiveClick}
                          >
                            {promocode.user_public_id}
                          </Link>
                        ) : (
                          '-'
                        )
                      }
                      label="пользователь"
                    />
                  </div>
                </div>

                <div className="row mb-3">
                  <div className="col-6">
                    <ItemLabelValue value={getKindName(promocode.kind)} label="вид" />
                  </div>
                </div>
              </div>
            </div>

            <hr className="mt-auto" />

            {promocode?.cities?.length > 0 && (
              <p className="mb-2">
                <i className="bi bi-geo-alt-fill me-1" />
                {promocode.cities[0]}{' '}
                {promocode.cities.length > 1 && (
                  <button
                    className="btn btn-sm btn-outline-secondary border-0 opacity-75 py-0 px-1"
                    onClick={handleOpenNavbar}
                    type="button"
                  >
                    +{promocode.cities.length - 1}{' '}
                    {getRightNames(promocode.cities.length - 1, 'город', 'города', 'городов')}
                  </button>
                )}
              </p>
            )}

            {promocode?.formats?.length > 0 && (
              <p className="mb-2">
                {promocode.formats[0]}{' '}
                {promocode.formats.length > 1 && (
                  <button
                    className="btn btn-sm btn-outline-secondary border-0 opacity-75 py-0 px-1"
                    onClick={handleOpenNavbar}
                    type="button"
                  >
                    +{promocode.formats.length - 1}{' '}
                    {getRightNames(promocode.formats.length - 1, 'формат', 'формата', 'форматов')}
                  </button>
                )}
              </p>
            )}

            {promocode?.products?.length > 0 && (
              <p className="mb-2 small">
                {promocode.products[0]}{' '}
                {promocode.products.length > 1 && (
                  <button
                    className="btn btn-sm btn-outline-secondary border-0 opacity-75 py-0 px-1"
                    style={{ fontSize: '12px' }}
                    onClick={handleOpenNavbar}
                    type="button"
                  >
                    +{promocode.products.length - 1}{' '}
                    {getRightNames(promocode.products.length - 1, 'товар', 'товара', 'товаров')}
                  </button>
                )}
              </p>
            )}

            {promocode?.licenses?.length > 0 && (
              <p className="mb-2" style={{ fontSize: '13px' }}>
                {promocode.licenses[0]}{' '}
                {promocode.licenses.length > 1 && (
                  <button
                    className="btn btn-sm btn-outline-secondary border-0 opacity-75 py-0 px-1"
                    style={{ fontSize: '12px' }}
                    onClick={handleOpenNavbar}
                    type="button"
                  >
                    +{promocode.licenses.length - 1}{' '}
                    {getRightNames(promocode.licenses.length - 1, 'лицензия', 'лицензии', 'лицензий')}
                  </button>
                )}
              </p>
            )}

            {promocode?.created_date && (
              <p className="mb-0 text-end text-muted" style={{ fontSize: '12px' }}>
                <span>{unixToMoment(promocode.created_date).format('D MMMM YYYY, HH:mm')}</span>
              </p>
            )}
          </div>
        </Card.Body>
      </Card>

      <Navbar expand={false} expanded={isOpenNavbar} className="p-0">
        <Container>
          <Navbar.Offcanvas
            id={'offcanvasNavbar-expand'}
            aria-labelledby={'offcanvasNavbarLabel-expand'}
            placement="end"
            onHide={setIsOpenNavbar}
          >
            <Offcanvas.Header closeButton>
              <Offcanvas.Title>{promocode.code}</Offcanvas.Title>
            </Offcanvas.Header>
            <Offcanvas.Body>
              {promocode?.cities?.length > 0 && (
                <>
                  <p className="mb-1">
                    {promocode.cities.length} {getRightNames(promocode.cities.length, 'город', 'города', 'городов')}:
                  </p>
                  <ListGroup className="mb-3">
                    {promocode.cities.map((item) => (
                      <ListGroup.Item key={item}>{item}</ListGroup.Item>
                    ))}
                  </ListGroup>
                </>
              )}

              {promocode?.formats?.length > 0 && (
                <>
                  <p className="mb-1">
                    {promocode.formats.length}{' '}
                    {getRightNames(promocode.formats.length, 'формат', 'формата', 'форматов')}:
                  </p>
                  <ListGroup>
                    {promocode.formats.map((item) => (
                      <ListGroup.Item key={item}>{item}</ListGroup.Item>
                    ))}
                  </ListGroup>
                </>
              )}

              {promocode?.products?.length > 0 && (
                <>
                  <p className="mb-1">
                    {promocode.products.length} {getRightNames(promocode.products.length, 'товар', 'товара', 'товаров')}
                    :
                  </p>
                  <ListGroup style={{ fontSize: '15px' }}>
                    {promocode.products.map((item) => (
                      <ListGroup.Item key={item}>
                        <a href={`${API_URL}/shop/product/${item}`} target="_blank" rel="noopener noreferrer">
                          {item}
                        </a>
                      </ListGroup.Item>
                    ))}
                  </ListGroup>
                </>
              )}

              {promocode?.licenses?.length > 0 && (
                <>
                  <p className="mb-1">
                    {promocode.licenses.length}{' '}
                    {getRightNames(promocode.licenses.length, 'лицензия', 'лицензии', 'лицензий')}:
                  </p>
                  <ListGroup style={{ fontSize: '15px' }}>
                    {promocode.licenses.map((item) => (
                      <ListGroup.Item key={item}>{item}</ListGroup.Item>
                    ))}
                  </ListGroup>
                </>
              )}
            </Offcanvas.Body>
          </Navbar.Offcanvas>
        </Container>
      </Navbar>

      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </>
  )
}
