import { useRef, useState } from 'react'
import { <PERSON>ton, ButtonGroup, Col, Floating<PERSON>abe<PERSON>, Form, Modal, Row, Spinner, Table } from 'react-bootstrap'

import { useUsageStatPromocodes } from '@/features/promocodes/api/usageStatPromocodes'
import { useToast } from '@/hooks/useToast'

const promocodesInitial = {
  not_exist_list: [],
  used_list: [],
  unused_list: [],
}

const MIN_COLUMN_WIDTH = 18

export function PromocodeUsageModal({ isShow, onClose }) {
  const textareaRef = useRef()
  const [promocodes, setPromocodes] = useState(promocodesInitial)
  const { not_exist_list, used_list, unused_list } = promocodes
  const maxLength = Math.max(not_exist_list?.length, used_list?.length, unused_list?.length)

  const usageStatPromocodes = useUsageStatPromocodes(setPromocodes)

  const openToast = useToast()

  const handleSendForm = () => {
    const codes = textareaRef.current.value
      .trim()
      .split(/[\s,]+/)
      .filter((code) => code.length > 0)

    const body = {
      promocodes: codes,
    }

    usageStatPromocodes.mutate(body)
  }

  const handleDownloadJSON = () => {
    const jsonString = `data:text/json;charset=utf-8,${encodeURIComponent(JSON.stringify(promocodes, null, 2))}`
    const link = document.createElement('a')
    link.href = jsonString
    link.download = 'usage_promocode.json'
    link.click()
  }

  const handleCloseModal = () => {
    usageStatPromocodes.reset()
    setPromocodes(promocodesInitial)
    onClose()
  }

  const getMaxColumnWidth = (array) => {
    return Math.max(...array.map((item) => (item || '').length), MIN_COLUMN_WIDTH)
  }

  const formatTableData = () => {
    const usedWidth = getMaxColumnWidth(used_list)
    const unusedWidth = getMaxColumnWidth(unused_list)
    const notExistWidth = getMaxColumnWidth(not_exist_list)

    let tableData = `Использованные${' '.repeat(
      Math.max(usedWidth - 'Использованные'.length, 0)
    )}\tНеиспользованные${' '.repeat(Math.max(unusedWidth - 'Неиспользованные'.length, 0))}\tНесуществующие\n`

    for (let i = 0; i < maxLength; i++) {
      const used = (used_list[i] || '').padEnd(usedWidth)
      const unused = (unused_list[i] || '').padEnd(unusedWidth)
      const notExist = (not_exist_list[i] || '').padEnd(notExistWidth)
      tableData += `${used}\t${unused}\t${notExist}\n`
    }

    return tableData
  }

  const handleCopyTable = () => {
    const tableData = formatTableData()
    navigator.clipboard
      .writeText(tableData)
      .then(() => {
        openToast.success({ message: 'Таблица скопирована в буфер обмена' })
      })
      .catch((err) => {
        console.error('Ошибка копирования: ', err)
      })
  }

  return (
    <Modal show={isShow} onHide={handleCloseModal} size="lg">
      <Modal.Header closeButton />
      <Modal.Body>
        <Form.Group className="mb-3">
          <FloatingLabel controlId="promocodesLabel" label="Промокоды">
            <Form.Control style={{ minHeight: '200px' }} as="textarea" ref={textareaRef} placeholder="Промокоды" />
          </FloatingLabel>
        </Form.Group>

        <Row>
          <Col>
            {maxLength > 0 && (
              <ButtonGroup>
                <Button onClick={handleDownloadJSON}>Скачать</Button>
                <Button onClick={handleCopyTable}>Копировать</Button>
              </ButtonGroup>
            )}
          </Col>
          <Col md="auto">
            <Button onClick={handleSendForm} disabled={usageStatPromocodes.isLoading}>
              {usageStatPromocodes.isLoading && <Spinner animation="grow" size="sm" />} Отправить
            </Button>
          </Col>
        </Row>

        {maxLength > 0 && (
          <Table className="mt-5">
            <thead>
              <tr>
                <th>Использованные</th>
                <th>Неиспользованные</th>
                <th>Несуществующие</th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: maxLength }).map((_, index) => (
                <tr key={index}>
                  <td>{used_list[index] || ''}</td>
                  <td>{unused_list[index] || ''}</td>
                  <td>{not_exist_list[index] || ''}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        )}
      </Modal.Body>
    </Modal>
  )
}
