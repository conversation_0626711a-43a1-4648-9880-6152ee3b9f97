import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getPromocodes = (skip, limit) => {
  return axios.get(`${APIRoute.PROMOCODE_LIST}/${skip}/${limit}`)
}

export const useGetPromocodes = (skip, limit, onSetValues) => {
  return useQuery({
    onSuccess: (data) => {
      if (data?.status === 200 && data?.data?.values?.length > 0) {
        onSetValues(data.data)
      }
    },
    queryKey: ['promocodes', skip, limit],
    queryFn: () => getPromocodes(skip, limit),
  })
}
