import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const checkPromocode = (codeType, code, formatId) => {
  return axios.get(`${APIRoute.PROMOCODE_CHECK}/${codeType}/${code}/${formatId}`)
}

export const useCheckPromocode = (isEnabled, codeType, code, formatId) => {
  return useQuery({
    enabled: isEnabled,
    queryKey: ['promocodeCount', code, formatId],
    queryFn: () => checkPromocode(codeType, code, formatId),
  })
}
