import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const deletePromocode = (data) => {
  return axios.delete(APIRoute.PROMOCODE, { data: data })
}

export const useDeletePromocode = (onClose, onUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (onUpdate) {
          onUpdate()
        }

        onClose(false)
        openToast.success({ message: 'Промокод успешно удалён!' })
      }
    },
    mutationFn: deletePromocode,
  })
}
