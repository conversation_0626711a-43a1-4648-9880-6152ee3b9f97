import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const searchPromocodes = (data) => {
  return axios.post(APIRoute.GET_ONE_PROMO, data)
}

export const useSearchPromocodes = (onSetValues) => {
  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (data?.data?.values?.length > 0) {
          onSetValues(data.data.values)
        }
      }
    },
    mutationFn: searchPromocodes,
  })
}
