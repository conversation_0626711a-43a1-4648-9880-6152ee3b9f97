import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const usageStatPromocodes = (data) => {
  return axios.post(APIRoute.STAT_USAGE_PROMO, data)
}

export const useUsageStatPromocodes = (onSetValues) => {
  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        onSetValues(data.data)
      }
    },
    mutationFn: usageStatPromocodes,
  })
}
