import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения данных промокода
 * @param {string} code - Код промокода
 * @returns {Promise} Promise с данными промокода
 */
const getPromoCode = (code) => {
  return axios.post(APIRoute.GET_ONE_PROMO, { code })
}

/**
 * Хук для получения данных промокода
 * @param {string} code - Код промокода
 * @returns {Object} Объект с данными useQuery
 */
export const useGetPromoCode = (code) => {
  return useQuery({
    queryKey: ['promoCode', code],
    queryFn: () => getPromoCode(code),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data.values?.[0] || null,
    enabled: !!code,
  })
}
