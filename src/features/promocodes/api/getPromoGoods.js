import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Функция для получения списка промо-товаров
 * @returns {Promise} Promise с данными промо-товаров
 */
const getPromoGoods = () => {
  return axios.get(APIRoute.GET_PROMO_GOODS)
}

/**
 * Хук для получения списка промо-товаров
 * @returns {Object} Объект с данными useQuery
 */
export const useGetPromoGoods = () => {
  return useQuery({
    queryKey: ['promoGoods'],
    queryFn: getPromoGoods,
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    select: (response) => response.data.values || [],
  })
}
