import { useMutation } from 'react-query'
import { useNavigate } from 'react-router-dom'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const updateNews = (data) => {
  return axios.put(APIRoute.UPDATE_NEWS, data)
}

export const useUpdateNews = () => {
  const openToast = useToast()
  const navigate = useNavigate()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('news')
        navigate(AppRoute.NEWS)
        openToast.success({ message: 'Новость обновлена!' })
      }
    },
    mutationFn: updateNews,
  })
}
