import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteNews = (publicId) => {
  return axios.delete(APIRoute.DELETE_NEWS, { data: { public_id: publicId } })
}

export const useDeleteNews = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('news')
        openToast.success({ message: 'Новость удалена!' })
      }
    },
    mutationFn: deleteNews,
  })
}
