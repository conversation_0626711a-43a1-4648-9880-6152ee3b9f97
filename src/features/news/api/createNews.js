import { useMutation } from 'react-query'
import { useNavigate } from 'react-router-dom'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const createNews = (data) => {
  return axios.post(APIRoute.POST_NEWS, data)
}

export const useCreateNews = () => {
  const openToast = useToast()
  const navigate = useNavigate()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('createNews')
    },
    onSuccess: (data) => {
      if (data?.status === 200) {
        queryClient.invalidateQueries('news')
        navigate(AppRoute.NEWS)
        openToast.success({ message: 'Новость добавлена!' })
      }
    },
    mutationFn: createNews,
  })
}
