import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Card, Container, Floating<PERSON>abel, Form, Modal, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'

import LogoLeague from '@/assets/img/logo-league.svg?react'

import { AppRoute } from '@/const'
import { checkSetValue } from '@/utils/forms'

import styles from './RegisterForm.module.scss'
import { useRegister } from '../api/register'

export const RegisterForm = () => {
  const navigate = useNavigate()
  const userRegister = useRegister()
  const [registerStatusModal, setRegisterStatusModal] = useState(false)
  const [isHidePass, setHidePass] = useState(true)

  const { register, handleSubmit } = useForm()

  useEffect(() => {
    document.title = 'Регистрация'
  }, [])

  useEffect(() => {
    if (userRegister.isSuccess) {
      setRegisterStatusModal(true)
    }
  }, [userRegister.isSuccess])

  const handleSubmitForm = (data) => {
    userRegister.mutate({ ...data })
  }

  const handleCloseModal = () => {
    setRegisterStatusModal(false)
    navigate(AppRoute.AUTH)
  }

  return (
    <Container className={styles.container}>
      <Card className={styles.jm}>
        <div className={styles.logoWrap}>
          <LogoLeague className={styles.logo} width="178" height="114" />
        </div>

        <center>
          <p className={styles.appeal}>Зарегистрируйте себе учётную запись</p>
        </center>

        <Form onSubmit={handleSubmit(handleSubmitForm)}>
          <Form.Group className="mb-3" controlId="formGroupEmail">
            <div className={styles.inputGroup}>
              <FloatingLabel controlId="labelNameReg" label="Имя">
                <Form.Control
                  {...register('name', {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, {}, 'text'),
                  })}
                  className={styles.field}
                  placeholder="Введите ваше имя"
                  autoComplete="name"
                  required
                />
              </FloatingLabel>
            </div>
            <Form.Control.Feedback type="invalid">Введите корректную почту</Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3" controlId="formGroupEmail">
            <div className={styles.inputGroup}>
              <FloatingLabel controlId="labelEmailReg" label="Электропочта">
                <Form.Control
                  {...register('email', {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, {}, 'email'),
                  })}
                  className={styles.field}
                  placeholder="<EMAIL>"
                  autoComplete="email"
                  required
                />
              </FloatingLabel>
            </div>
            <Form.Control.Feedback type="invalid">Введите корректную почту</Form.Control.Feedback>
          </Form.Group>

          <Form.Group className={`${styles.formGroupPassword} mb-3`} controlId="formGroupPassword">
            <div className={styles.inputGroup}>
              <FloatingLabel controlId="labelPasswordReg" label="Пароль">
                <Form.Control
                  {...register('password', {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, {}, 'text'),
                  })}
                  className={styles.field}
                  type={isHidePass ? 'password' : 'text'}
                  placeholder="Придумайте пароль"
                  autoComplete="new-password"
                  required
                />
              </FloatingLabel>
              <button
                onClick={() => setHidePass(!isHidePass)}
                className={`${styles.btnTogglePass} ${isHidePass ? '' : styles.btnTogglePassShow}`}
                type="button"
              />
            </div>
            <Form.Control.Feedback type="invalid">Заполните поле ввода пароля</Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="d-flex justify-content-center mb-5" as={Row}>
            <Button className={styles.btn} type="submit">
              Зарегистрироваться
            </Button>
          </Form.Group>

          <Form.Group className="d-flex justify-content-center" as={Row}>
            <button onClick={() => navigate(AppRoute.AUTH)} className={styles.btnSecond} type="button">
              Войти
            </button>
          </Form.Group>
        </Form>
      </Card>

      <Modal show={registerStatusModal} onHide={handleCloseModal}>
        <Modal.Header closeButton>
          <Modal.Title>Проверьте почту</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Вы успешно зарегистрировались, осталось подтвердить почту!</p>
          <p>Для подтверждения перейдите по ссылке, указанной в письме (письмо может быть в спаме)</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Войти
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  )
}
