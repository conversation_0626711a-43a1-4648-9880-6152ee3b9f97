import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Card, Container, FloatingLabel, Form, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'

import LogoLeague from '@/assets/img/logo-league.svg?react'

import Toasts from '@/components/Toasts/Toasts'

import { AppRoute } from '@/const'
import { checkSetValue } from '@/utils/forms'

import styles from './LoginForm.module.scss'
import { useLogin } from '../api/login'

export const LoginForm = () => {
  const login = useLogin()
  const navigate = useNavigate()

  const [isResetPassword, setResetPassword] = useState(false)
  const [isHidePass, setHidePass] = useState(true)
  const [isWrongAuth, setIsWrongAuth] = useState(false)
  const { register, handleSubmit } = useForm()

  useEffect(() => {
    document.title = 'Авторизация'
  }, [])

  useEffect(() => {
    if (login.isError && login?.error?.response?.status === 400) {
      setIsWrongAuth(true)
    }
  }, [login.isError, login?.error?.response?.status])

  const handleSubmitLogin = (data) => {
    login.mutate({ ...data })
  }

  const handleResetPass = () => {
    setResetPassword(!isResetPassword)
  }

  return (
    <Container className={styles.container}>
      <Card className={styles.jm}>
        <div className={styles.logoWrap}>
          <LogoLeague className={styles.logo} width="178" height="114" />
        </div>
        <center>
          <p className={styles.appeal}>
            {isResetPassword
              ? 'Для восстановления пароля введите ваш электронный адрес, на вашу почту придёт инструкция по восстановлению пароля'
              : 'Войдите в свою учётную запись'}
          </p>
        </center>
        <Form onSubmit={handleSubmit(handleSubmitLogin)}>
          <Form.Group controlId="formGroupEmail">
            <div className={styles.inputGroup}>
              <FloatingLabel controlId="labelEmail" label="Электропочта">
                <Form.Control
                  {...register('email', {
                    required: true,
                    setValueAs: (v) => checkSetValue(v, {}, 'email'),
                  })}
                  onFocus={() => setIsWrongAuth(false)}
                  className={`${styles.field} ${isWrongAuth ? styles.fieldError : ''}`}
                  placeholder="<EMAIL>"
                  autoComplete="email"
                  required
                />
              </FloatingLabel>

              {isWrongAuth && <span className={styles.formControlFeedback}>Неверный логин или пароль</span>}
            </div>
          </Form.Group>

          {!isResetPassword && (
            <Form.Group className={styles.formGroupPassword} controlId="formGroupPassword">
              <div className={styles.inputGroup}>
                <FloatingLabel controlId="labelPassword" label="Пароль">
                  <Form.Control
                    {...register('password', {
                      required: true,
                      setValueAs: (v) => checkSetValue(v, {}, 'text'),
                    })}
                    type={isHidePass ? 'password' : 'text'}
                    className={`${styles.field} ${isWrongAuth ? styles.fieldError : ''}`}
                    onFocus={() => setIsWrongAuth(false)}
                    placeholder="********"
                    autoComplete="current-password"
                    required
                  />
                </FloatingLabel>
                {isWrongAuth && <span className={styles.formControlFeedback}>Неверный логин или пароль</span>}
                <button
                  onClick={() => setHidePass(!isHidePass)}
                  className={`${styles.btnTogglePass} ${isHidePass ? '' : styles.btnTogglePassShow}`}
                  type="button"
                />
              </div>
            </Form.Group>
          )}
          <Form.Group className="d-flex justify-content-center mb-5" as={Row}>
            <Button className={styles.btn} type="submit">
              {isResetPassword ? 'Отправить' : 'Войти'}
            </Button>
          </Form.Group>
          <Form.Group className="d-flex justify-content-center" as={Row}>
            {isResetPassword ? (
              <button onClick={handleResetPass} className={styles.btnSecond} type="button">
                Войти
              </button>
            ) : (
              <button onClick={() => navigate(AppRoute.REGISTER)} className={styles.btnSecond} type="button">
                Нет аккаунта?
              </button>
            )}
          </Form.Group>
        </Form>
      </Card>

      <Toasts />
    </Container>
  )
}
