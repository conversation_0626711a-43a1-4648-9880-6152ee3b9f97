.container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.jm {
  margin-bottom: 0;
  padding: 32px;
  width: 100%;
  max-width: 400px;
  background-color: #ffffff;
  box-shadow:
    0 1px 0 0 rgb(221, 227, 238),
    0 0 0 1px rgb(232, 234, 244);
}

.logoWrap {
  margin: 0 auto;
  margin-bottom: 32px;
}

.logo {
  & path {
    fill: black;
  }
}

.appeal {
  font-weight: 500;
}

.field {
  margin-bottom: 20px;
  padding-left: 42px;
  background-color: #f6f6f6;
  border: none;
}

.fieldError {
  border-color: #dc3545;
}

.formControlFeedback {
  position: absolute;
  top: 90%;
  left: 0;
  font-size: 13px;
  color: #dc3545;
}

.formGroupPassword {
  position: relative;
}

.inputGroup {
  position: relative;
}

.btnTogglePass {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 45px;
  background-image: url('../../../assets/img/icons/icon-pass-hid.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  border: none;
}

.btnTogglePassShow {
  background-image: url('../../../assets/img/icons/icon-pass-view.svg');
}

.btn {
  padding-left: 50px;
  padding-right: 50px;
  width: max-content;
  border: none;
  box-shadow: 0 3px 10px rgba(66, 194, 255, 0.4);
}

.btnSecond {
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.5);
  background-color: transparent;
  border: none;

  &:hover {
    color: rgba(0, 0, 0, 1);
  }
}

.btnForget {
  position: absolute;
  top: 0;
  right: 0;
}
