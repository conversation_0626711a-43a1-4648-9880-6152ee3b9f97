import { useMutation } from 'react-query'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import { APIRoute, AppRoute, Role } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'
import { addUser } from '@/store/slices/user'
import storage from '@/utils/storage'

export const loginWithEmailAndPassword = (data) => {
  return axios.put(APIRoute.LOGIN, data)
}

export const useLogin = ({ config } = {}) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const openToast = useToast()

  return useMutation({
    onMutate: async (login) => {
      await queryClient.cancelQueries('login')
      queryClient.setQueryData('login', login.data)
    },
    onSuccess: (data) => {
      if ([Role.ADMIN, Role.SUPERADMIN].some((el) => data.data.user.role.includes(el))) {
        storage.setToken(data.data.token)
        storage.setUserObj(data.data.user)
        dispatch(addUser(data.data.user))
        navigate(AppRoute.PROFILE)
      } else {
        openToast.error({ message: 'Недостаточно прав для авторизации' })
      }
    },
    ...config,
    mutationFn: loginWithEmailAndPassword,
  })
}
