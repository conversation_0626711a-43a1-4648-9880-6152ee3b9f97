import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

export const registerWithEmailAndPassword = (data) => {
  return axios.post(APIRoute.REGISTER, data)
}

export const useRegister = ({ config } = {}) => {
  return useMutation({
    onMutate: async (register) => {
      await queryClient.cancelQueries('register')
      queryClient.setQueryData('register', register.data)
    },
    ...config,
    mutationFn: registerWithEmailAndPassword,
  })
}
