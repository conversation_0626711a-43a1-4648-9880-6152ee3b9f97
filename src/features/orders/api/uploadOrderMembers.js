import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const uploadOrderMembers = ({ orderPublicId, formData }) => {
  return axios.postForm(`${APIRoute.UPLOAD_MEMBERS_ORDER}/${orderPublicId}`, formData)
}

export const useUploadOrderMembers = (onClose, onUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (onUpdate) {
          onUpdate()
        }

        onClose(false)
        openToast.success({ message: 'Данные участников загружены!' })
      }
    },
    mutationFn: uploadOrderMembers,
  })
}
