import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

const getUserOrders = (publicId) => {
  return axios.get(`${APIRoute.USER_ORDERS}/${publicId}`)
}

export const useGetUserOrders = (publicId, enabled = true) => {
  return useQuery({
    enabled: Boolean(publicId) && enabled,
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    queryKey: ['userOrders', publicId],
    queryFn: () => getUserOrders(publicId),
  })
}
