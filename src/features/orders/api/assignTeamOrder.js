import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const assignTeamOrder = (data) => {
  return axios.post(APIRoute.ASSIGN_CORP_TEAMS, data)
}

export const useAssignTeamOrder = ({ config } = {}, onClose, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }
        onClose(false)
        openToast.success({ message: 'Команда успешно присвоена!' })
      }
    },
    ...config,
    mutationFn: assignTeamOrder,
  })
}
