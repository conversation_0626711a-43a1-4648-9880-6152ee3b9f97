import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getCompanyOrders = (companyPublicId) => axios.get(`${APIRoute.GET_COMPANY_ORDERS}/${companyPublicId}`)

export const useGetCompanyOrders = (companyPublicId) => {
  return useQuery({
    enabled: Boolean(companyPublicId),
    queryKey: ['companyOrders', companyPublicId],
    queryFn: () => getCompanyOrders(companyPublicId),
  })
}
