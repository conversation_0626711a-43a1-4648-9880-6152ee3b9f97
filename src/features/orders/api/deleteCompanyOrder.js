import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const deleteCompanyOrder = (data) => {
  return axios.delete(APIRoute.CREATE_PAYMENT, { data: data })
}

export const useDeleteCompanyOrder = ({ companyPublicId, onUpdate } = {}) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (companyPublicId) {
          queryClient.invalidateQueries(['companyOrders', companyPublicId])
        } else if (onUpdate) {
          onUpdate()
        }

        openToast.success({ message: 'Заказ был удалён!' })
      }
    },
    mutationFn: deleteCompanyOrder,
  })
}
