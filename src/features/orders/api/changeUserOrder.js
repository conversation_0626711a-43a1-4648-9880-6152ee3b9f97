import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const changeUserOrder = (data) => {
  return axios.post(APIRoute.CHANGE_USER_ORDER, data)
}

export const useChangeUserOrder = ({ config } = {}, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onMutate: async () => {
      await queryClient.cancelQueries('changeUserOrder')
    },
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        openToast.success({ message: 'Заказ успешно перенесён!' })
      }
    },
    ...config,
    mutationFn: changeUserOrder,
  })
}
