import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getPromocodeOrders = (code) => {
  return code ? axios.get(`${APIRoute.GET_PROMOCODE_ORDERS}/${code}`) : undefined
}

export const useGetPromocodeOrders = ({ config } = {}, code) => {
  return useQuery({
    ...config,
    enabled: code?.length > 0,
    queryKey: ['promocodeOrders', code],
    queryFn: () => getPromocodeOrders(code),
  })
}
