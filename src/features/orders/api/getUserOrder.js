import { useQuery } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

const getUser = (userPublicId) => {
  return userPublicId ? axios.get(`${APIRoute.USER_INFO}/${userPublicId}`) : undefined
}

export const useGetUserOrder = ({ config } = {}, userPublicId) => {
  return useQuery({
    ...config,
    enabled: userPublicId?.length > 0,
    queryKey: ['userOrder', userPublicId],
    queryFn: () => getUser(userPublicId),
  })
}
