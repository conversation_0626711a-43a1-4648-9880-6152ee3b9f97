import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'

const getOrder = (url) => {
  return axios.get(url)
}

export const useGetOrder = () => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data.data) {
        // Trigger user order query manually using queryClient instead of useState
        queryClient.prefetchQuery(['userOrder', data.data.user_public_id], () =>
          axios.get(`${APIRoute.USER_INFO}/${data.data.user_public_id}`)
        )
      } else {
        openToast.error({ message: 'Заказ не найден' })
      }
    },
    mutationFn: getOrder,
  })
}
