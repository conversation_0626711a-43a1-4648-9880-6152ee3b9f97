import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'

const deleteOrder = (publicId) => {
  return axios.delete(`${APIRoute.DELETE_ORDER}/${publicId}`)
}

export const useDeleteOrder = ({ config } = {}, handleUpdate) => {
  const openToast = useToast()

  return useMutation({
    onSuccess: (data) => {
      if (data?.status === 200) {
        if (handleUpdate) {
          handleUpdate()
        }

        openToast.success({ message: 'Заказ был удалён!' })
      }
    },
    ...config,
    mutationFn: deleteOrder,
  })
}
