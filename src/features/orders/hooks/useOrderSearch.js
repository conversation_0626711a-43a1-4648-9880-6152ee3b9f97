import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'

import { APIRoute, Kind } from '@/const'
import { useToast } from '@/hooks/useToast'

import { useDeleteCompanyOrder } from '../api/deleteCompanyOrder'
import { useDeleteOrder } from '../api/deleteOrder'
import { useGetOrder } from '../api/getOrder'

// Constants
export const ORDER_ID_REGEX = /^[A-Z0-9]+$/

/**
 * Хук для определения поддержки буфера обмена
 */
export const useClipboardSupport = () => {
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    const userAgent = navigator.userAgent
    // Показываем кнопку только в Chrome и браузерах на его движке (кроме Edge)
    setIsSupported(userAgent.includes('Chrome') && !userAgent.includes('Edg'))
  }, [])

  return isSupported
}

/**
 * Основной хук для поиска заказов с защитой от повторных запросов
 */
export const useOrderSearch = () => {
  const [searchId, setSearchId] = useState('')
  const [lastSearchUrl, setLastSearchUrl] = useState('')
  const getOrderMutation = useGetOrder()

  // Защита от повторных запросов к тому же URL
  const searchOrder = useCallback(
    (url) => {
      if (url && url !== lastSearchUrl) {
        getOrderMutation.mutate(url)
        setLastSearchUrl(url)
      } else if (url === lastSearchUrl) {
        // Если URL тот же, просто повторяем запрос без изменения состояния
        getOrderMutation.mutate(url)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [lastSearchUrl, getOrderMutation.mutate]
  )

  const resetSearch = useCallback(() => {
    getOrderMutation.reset()
    setLastSearchUrl('')
  }, [getOrderMutation])

  // Стабильные функции для предотвращения пересоздания колбеков
  const stableSetSearchId = useCallback((id) => setSearchId(id), [])

  return {
    searchId,
    setSearchId: stableSetSearchId,
    lastSearchUrl,
    searchOrder,
    resetSearch,
    orderData: getOrderMutation.data?.data,
    isLoading: getOrderMutation.isLoading,
    isError: getOrderMutation.isError,
  }
}

/**
 * Хук для работы с URL параметрами и предотвращения повторных загрузок
 */
export const useUrlParams = () => {
  const { public_id, start_number } = useParams()
  const loadedUrlRef = useRef('')

  // Создаем стабильный URL для предотвращения повторных загрузок
  const currentUrl = useMemo(() => {
    if (public_id && start_number) {
      return `${APIRoute.ORDER_INFO_TICKET}/${public_id}/${start_number}`
    } else if (public_id) {
      return `${APIRoute.ORDER_INFO}/${public_id}`
    }
    return ''
  }, [public_id, start_number])

  const shouldLoadFromUrl = currentUrl && currentUrl !== loadedUrlRef.current

  const markUrlAsLoaded = useCallback(() => {
    loadedUrlRef.current = currentUrl
  }, [currentUrl])

  return {
    public_id,
    start_number,
    currentUrl,
    shouldLoadFromUrl,
    markUrlAsLoaded,
  }
}

/**
 * Хук для работы с буфером обмена с валидацией
 */
export const useClipboardPaste = (onPaste) => {
  const openToast = useToast()

  return useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText()
      const formattedId = text.trim().replaceAll(' ', '')

      if (!formattedId) {
        openToast.warning({ message: 'Буфер обмена пуст' })
        return
      }

      if (!ORDER_ID_REGEX.test(formattedId)) {
        openToast.warning({
          title: 'Неверный формат',
          message: 'Идентификатор заказа может содержать только заглавные латинские буквы и цифры.',
        })
        return
      }

      onPaste(formattedId)
    } catch (err) {
      console.error('Не удалось получить доступ к буферу обмена:', err)
      openToast.error({ message: 'Не удалось получить доступ к буферу обмена' })
    }
  }, [onPaste, openToast])
}

/**
 * Хук для удаления заказов с учетом типа заказа
 */
export const useOrderDeletion = (onDeleteSuccess) => {
  const deleteOrderMutation = useDeleteOrder()
  const deleteCompanyOrderMutation = useDeleteCompanyOrder()

  const deleteOrder = useCallback(
    (order) => {
      const onSuccess = (data) => {
        if (data?.status === 200) {
          onDeleteSuccess?.()
        }
      }

      if (order?.kind === Kind.CORPORATE) {
        deleteCompanyOrderMutation.mutate({ public_id: order.public_id }, { onSuccess })
      } else {
        deleteOrderMutation.mutate(order.public_id, { onSuccess })
      }
    },
    [deleteOrderMutation, deleteCompanyOrderMutation, onDeleteSuccess]
  )

  return { deleteOrder }
}
