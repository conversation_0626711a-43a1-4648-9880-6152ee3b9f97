# Order Search Hooks

## Описание

Этот модуль содержит оптимизированные хуки для компонента `OrderSearch`, которые решают проблемы с бесконечными циклами запросов и улучшают производительность.

## Хуки

### `useOrderSearch()`

Основной хук для поиска заказов с защитой от повторных запросов:

- **Защита от дублирования**: Предотвращает повторные запросы к тому же URL
- **Стабильные зависимости**: Использует `mutate` напрямую для избежания циклов
- **Централизованное состояние**: Объединяет данные заказа, состояние загрузки и ошибки

### `useUrlParams()`

Хук для работы с URL параметрами:

- **Мемоизация URL**: Создает стабильный URL через `useMemo`
- **Защита от повторных загрузок**: Использует `useRef` для отслеживания загруженных URL
- **Контроль загрузки**: Определяет, нужно ли загружать данные из URL

### `useClipboardSupport()`

Определяет поддержку буфера обмена в браузере:

- **Безопасная проверка**: Проверяет User-Agent для определения поддержки
- **Оптимизация**: Выполняется только один раз при монтировании

### `useClipboardPaste(onPaste)`

Обработка вставки из буфера обмена:

- **Валидация**: Проверяет формат ID заказа
- **Обработка ошибок**: Показывает понятные сообщения пользователю
- **Стабильные зависимости**: Избегает лишних пересоздания функции

### `useOrderDeletion(onDeleteSuccess)`

Хук для удаления заказов:

- **Полиморфизм**: Автоматически выбирает правильный API для типа заказа
- **Обратные вызовы**: Вызывает колбек при успешном удалении
- **Оптимизированные зависимости**: Избегает ненужных пересозданий

## Преимущества рефакторинга

1. **Устранение бесконечных циклов**: Удалены сложные зависимости в `useCallback`
2. **Лучшая производительность**: Мемоизация и контроль ре-рендеров
3. **Читаемость кода**: Логика разделена на специализированные хуки
4. **Повторное использование**: Хуки можно использовать в других компонентах
5. **Тестируемость**: Каждый хук можно тестировать отдельно
6. **Безопасность**: Защита от повторных запросов и некорректного поведения

## Использование

```javascript
import {
  useOrderSearch,
  useUrlParams,
  useClipboardSupport,
  useClipboardPaste,
  useOrderDeletion,
} from '../hooks/useOrderSearch'

const MyComponent = () => {
  const orderSearch = useOrderSearch()
  const urlParams = useUrlParams()
  const clipboardSupported = useClipboardSupport()

  // Логика компонента...
}
```
