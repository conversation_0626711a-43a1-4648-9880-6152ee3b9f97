import { useCallback, useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON>, But<PERSON>, Card, Col, Container, Form, Row, Tab, Tabs } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'
import PageTitle from '@/components/PageTitle/PageTitle'

import { APIRoute, AppRoute } from '@/const'
import { Order } from '@/features/orders'
import { UserInfo } from '@/features/user/components/UserInfo/UserInfo'
import { queryClient } from '@/lib/react-query'

import SearchNumberForm from '../components/SearchNumberForm'
import {
  useClipboardSupport,
  useOrderSearch,
  useUrlParams,
  useClipboardPaste,
  useOrderDeletion,
} from '../hooks/useOrderSearch'

// Constants
const TABS = {
  ORDER_ID: 'orderIdTab',
  ORDER_NUMBER: 'orderNumberTab',
}

// Main component
export const OrderSearch = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState(TABS.ORDER_ID)

  const clipboardSupported = useClipboardSupport()
  const urlParams = useUrlParams()
  const orderSearch = useOrderSearch()

  // Извлекаем функции из хуков для стабильных зависимостей
  const { resetSearch, searchOrder, setSearchId, searchId, lastSearchUrl, orderData, isLoading, isError } = orderSearch
  const { shouldLoadFromUrl, currentUrl, start_number, markUrlAsLoaded, public_id } = urlParams

  // Получение данных пользователя (мемоизировано)
  const userData = useMemo(
    () => queryClient.getQueryData(['userOrder', orderData?.user_public_id])?.data,
    [orderData?.user_public_id]
  )

  // Обработка удаления заказа
  const handleDeleteSuccess = useCallback(() => {
    resetSearch()
    if (public_id) {
      navigate(AppRoute.ORDERS)
    }
  }, [resetSearch, public_id, navigate])

  const { deleteOrder } = useOrderDeletion(handleDeleteSuccess)

  // Загрузка заказа из URL при изменении параметров
  useEffect(() => {
    if (shouldLoadFromUrl) {
      if (start_number) {
        setActiveTab(TABS.ORDER_NUMBER)
      }
      searchOrder(currentUrl)
      markUrlAsLoaded()
    }
  }, [shouldLoadFromUrl, currentUrl, start_number, searchOrder, markUrlAsLoaded])

  // Обработчики поиска
  const handleOrderIdSearch = useCallback(
    (orderId) => {
      const url = `${APIRoute.ORDER_INFO}/${orderId}`
      searchOrder(url)
    },
    [searchOrder]
  )

  const handleTicketSearch = useCallback(
    (type, format, number) => {
      if (type === 'order') {
        handleOrderIdSearch(searchId)
      } else if (type === 'ticket') {
        const url = `${APIRoute.ORDER_INFO_TICKET}/${format}/${number}`
        searchOrder(url)
      }
    },
    [handleOrderIdSearch, searchId, searchOrder]
  )

  // Обработчики формы
  const handleInputChange = useCallback(
    (evt) => {
      const formattedId = evt.target.value.replaceAll(' ', '')
      setSearchId(formattedId)
    },
    [setSearchId]
  )

  const handleFormSubmit = useCallback(
    (e) => {
      e.preventDefault()
      if (searchId) {
        handleOrderIdSearch(searchId)
      }
    },
    [searchId, handleOrderIdSearch]
  )

  const handleKeyDown = useCallback(
    (evt) => {
      if (evt.key === 'Enter' && searchId) {
        evt.preventDefault()
        handleOrderIdSearch(searchId)
      }
    },
    [searchId, handleOrderIdSearch]
  )

  // Обработчик вставки из буфера обмена
  const handleClipboardPaste = useClipboardPaste((pastedId) => {
    setSearchId(pastedId)
    handleOrderIdSearch(pastedId)
  })

  // Обработчики заказа
  const handleUpdateOrder = useCallback(() => {
    if (lastSearchUrl) {
      searchOrder(lastSearchUrl)
    }
  }, [searchOrder, lastSearchUrl])

  const handleDeleteOrder = useCallback(() => {
    if (orderData) {
      deleteOrder(orderData)
    }
  }, [orderData, deleteOrder])

  // Вычисляемые значения
  const isEmptyResponse = orderData && Object.keys(orderData).length === 0
  const showOrderData = orderData && Object.keys(orderData).length > 0

  return (
    <Layout>
      <Container>
        <Row className="justify-content-center mt-4 mb-4">
          <Col md="auto">
            <PageTitle title="Поиск заказа" />
          </Col>
        </Row>

        <Card className="mb-4 shadow-sm">
          <Card.Body>
            <Tabs activeKey={activeTab} onSelect={setActiveTab} id="order-search-tabs" className="mb-3">
              <Tab eventKey={TABS.ORDER_ID} title="По идентификатору">
                <Form className="mb-3" onSubmit={handleFormSubmit}>
                  <Form.Group className="d-flex">
                    <Form.Control
                      type="search"
                      placeholder="Введите идентификатор заказа"
                      value={searchId}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyDown}
                      className="me-2"
                    />
                    {clipboardSupported && (
                      <Button
                        onClick={handleClipboardPaste}
                        variant="outline-secondary"
                        title="Вставить и найти"
                        className="me-2"
                        type="button"
                      >
                        <i className="bi bi-clipboard" />
                      </Button>
                    )}
                    <Button variant="primary" type="submit" disabled={!searchId}>
                      <i className="bi bi-search" />
                    </Button>
                  </Form.Group>
                </Form>
              </Tab>

              <Tab eventKey={TABS.ORDER_NUMBER} title="По номеру билета">
                <SearchNumberForm onSearch={handleTicketSearch} />
              </Tab>
            </Tabs>
          </Card.Body>
        </Card>

        {isLoading && (
          <Alert variant="info" className="text-center">
            Загрузка данных...
          </Alert>
        )}

        {isEmptyResponse && (
          <Alert variant="warning" className="text-center">
            Заказ не найден
          </Alert>
        )}

        {isError && (
          <Alert variant="danger" className="text-center">
            Произошла ошибка при поиске заказа. Попробуйте позже.
          </Alert>
        )}

        {showOrderData && (
          <>
            <Card className="mb-4 shadow-sm">
              <Card.Body>
                <Order order={orderData} onUpdateOrders={handleUpdateOrder} onDeleteOrder={handleDeleteOrder} />
              </Card.Body>
            </Card>

            <Card className="mb-4 shadow-sm">
              <Card.Header as="h5">Пользователь</Card.Header>
              <Card.Body>
                {userData ? <UserInfo user={userData} /> : <Alert variant="secondary">Нет данных о пользователе</Alert>}
              </Card.Body>
            </Card>
          </>
        )}
      </Container>
    </Layout>
  )
}
