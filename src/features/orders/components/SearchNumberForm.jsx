import { useRef, useState } from 'react'
import { Button, Col, FloatingLabel, FormControl, FormSelect, InputGroup, Row } from 'react-bootstrap'

import { useGetEvents } from '@/features/events'
import { useGetEventFormats } from '@/features/events/api/getEventFormats'
import { unixToMoment } from '@/utils/date'

function SearchNumberForm({ onSearch }) {
  const [format, setFormat] = useState('')
  const [number, setNumber] = useState('')
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [selectedCityId, setSelectedCityId] = useState('')
  const [isSelectedCity, setIsSelectedCity] = useState(false)

  const getEventsQuery = useGetEvents()
  const getEventFormatsQuery = useGetEventFormats({}, selectedCityId)

  const citySelectRef = useRef(null)

  const handleChangeEvent = (evt) => {
    const events = getEventsQuery?.data?.data?.values
    const eventId = evt.target.value
    const event = events.find((event) => event.public_id === eventId)

    setIsSelectedCity(false)
    citySelectRef.current.selectedIndex = 0

    if (event) {
      setSelectedEvent(event)
    } else {
      setSelectedEvent(null)
      setIsSelectedCity(false)
      setIsSelectedCity('')
    }
  }

  const handleChangeCity = (evt) => {
    const eventCityId = evt.target.value

    if (eventCityId.length !== 0) {
      setIsSelectedCity(true)
      setSelectedCityId(eventCityId)
    } else {
      setIsSelectedCity(false)
      setSelectedCityId('')
    }
  }

  const handleSelectFormat = (evt) => {
    const formatId = evt.target.value

    setFormat(formatId)
  }

  return (
    <>
      <Row className="g-3 mb-3">
        <Col md={3}>
          <FloatingLabel controlId="eventLabelTicket" label="Событие">
            <FormSelect onChange={handleChangeEvent} name="event_public_id" aria-label="Событие">
              <option value="">выберите один из вариантов</option>
              {getEventsQuery?.data?.data?.values.map((event) => (
                <option value={event.public_id} key={event.public_id}>
                  {event.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={3}>
          <FloatingLabel controlId="cityLabelTicket" label="Город">
            <FormSelect
              onChange={handleChangeCity}
              ref={citySelectRef}
              name="city_id"
              aria-label="Город"
              disabled={!selectedEvent}
            >
              <option value="">{selectedEvent ? 'выберите один из вариантов' : 'сначала выберите событие'}</option>
              {selectedEvent?.event_city.map((city) => (
                <option value={city.public_id} key={city.public_id}>
                  {city.city.name_ru} — {unixToMoment(city.start_time).format('DD.MM.YYYY')}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={3}>
          <FloatingLabel controlId="formatLabelTicket" label="Формат">
            <FormSelect
              onChange={handleSelectFormat}
              value={format || ''}
              name="format_public_id"
              aria-label="Формат"
              disabled={!isSelectedCity}
            >
              <option value="">
                {!selectedEvent && !isSelectedCity && 'выберите событие и город'}
                {selectedEvent && !isSelectedCity && 'выберите город'}
                {selectedEvent && isSelectedCity && 'выберите один из вариантов'}
              </option>
              {getEventFormatsQuery?.data?.data?.values?.map((format) => (
                <option value={format.public_id} key={format.public_id}>
                  {format.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={3}>
          <InputGroup>
            <FormControl
              onChange={(e) => setNumber(e.target.value)}
              name="ticket_count"
              type="number"
              placeholder="Номер билета"
            />
            <Button onClick={() => onSearch('ticket', format, number)} variant="primary" type="button">
              <i className="bi bi-search" />
            </Button>
          </InputGroup>
        </Col>
      </Row>
    </>
  )
}

export default SearchNumberForm
