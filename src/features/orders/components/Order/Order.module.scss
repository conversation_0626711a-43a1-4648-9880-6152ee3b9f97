@import '../../../../assets/styles/variables';

.main {
  margin-bottom: 12px;
}

.card {
  position: relative;
}

.cardTop {
  position: relative;

  display: grid;
  grid-template-columns: 1fr auto 24px;
  gap: 16px;

  &::after {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);

    display: block;
    width: 80%;
    height: 1px;

    background-color: var(--bs-border-color-translucent);

    content: '';
  }
}

.orderId {
  margin-top: 16px;
}

.link {
  color: var(--bs-link-color);
  text-decoration: underline;

  &:hover {
    color: var(--bs-link-hover-color);
  }
}

.mainInfo {
  padding-top: 16px;
}

.item {
  display: grid;
}

.itemLabel {
  font-size: 12px;
  color: var(--bs-secondary-color);
}

.sourcesInfo {
  position: relative;
  padding-top: 16px;

  font-size: 11px;
  color: var(--bs-secondary-color);

  &::before {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);

    display: block;
    width: 80%;
    height: 1px;

    background-color: var(--bs-border-color-translucent);

    content: '';
  }
}

.ip,
.agentUser {
  margin-bottom: 6px;
}

.dropDown {
  position: absolute;
  top: 16px;
  right: 14px;
}

.dropDownToggle {
  cursor: pointer;
}

.iconMore {
  & path {
    fill: var(--bs-secondary-color);
  }
}

.danger,
.danger:hover {
  color: var(--bs-danger);
}

.field {
  background-color: var(--bs-secondary-bg);
  border: 1px solid var(--bs-border-color);
  color: var(--bs-body-color);

  &:focus {
    background-color: var(--bs-body-bg);
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
  }
}

@media (min-width: $grid-breakpoint-sm) {
  .mainInfo {
    display: grid;
    align-content: start;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (min-width: $grid-breakpoint-md) {
  .card {
    display: grid;
    grid-template-columns: auto 1fr;
    column-gap: 36px;
  }

  .cardTop {
    padding-right: 36px;

    display: block;

    border-bottom: none;

    &::after {
      left: auto;
      right: 0;
      top: 50%;
      bottom: auto;
      transform: translateY(-50%);

      width: 1px;
      height: 70%;
    }
  }

  .orderId {
    text-align: center;
  }

  .sourcesInfo {
    display: grid;
    grid-template-columns: 100px auto;
    grid-column: 1 / -1;

    &::before {
      width: 95%;
    }
  }

  .ip,
  .agentUser {
    margin-bottom: 0;
  }
}
