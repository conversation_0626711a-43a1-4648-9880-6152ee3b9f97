import { createRef, useCallback, useEffect, useState } from 'react'
import { Button, ButtonGroup, Card, Col, Dropdown, FloatingLabel, Form, FormControl, Modal, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { Link } from 'react-router-dom'

import IconMoreVertical from '@/assets/img/icons/icon-more-vertical.svg?react'

import { CostStatus } from '@/components/CostStatus'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import Loader from '@/components/Loader/Loader'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { Kind } from '@/const'
import { UpdateOrdersTicketsContextProvider } from '@/features/orders'
import { useDeleteCompanyOrder } from '@/features/orders/api/deleteCompanyOrder'
import { ChangeTeamModal } from '@/features/orders/components/ChangeTeamModal'
import { Soldgoods } from '@/features/tickets'
import { getKindName } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

import styles from './Order.module.scss'
import { useChangeUserOrder } from '../../api/changeUserOrder'
import { useDeleteOrder } from '../../api/deleteOrder'
import { useUploadOrderMembers } from '../../api/uploadOrderMembers'

export const Order = ({ order, tickets, teams, onDeleteOrder, onUpdateOrders, onUpdateTickets, isLoadingTickets }) => {
  const [file, setFile] = useState(null)
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const [isOpenModalUserChange, setIsOpenModalUserChange] = useState(false)
  const [isOpenModalChangeTeam, setIsOpenModalChangeTeam] = useState(false)
  const [isOpenModalUploadMembers, setIsOpenModalUploadMembers] = useState(false)

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm()

  const deleteOrderMutation = useDeleteOrder({}, onUpdateOrders)
  const changeUserOrderMutation = useChangeUserOrder({}, onUpdateOrders)
  const uploadOrderMembersMutation = useUploadOrderMembers(setIsOpenModalUploadMembers, onUpdateOrders)
  const deleteCompanyOrderMutation = useDeleteCompanyOrder({ onUpdate: onUpdateOrders })

  const fileMembersRef = createRef(null)

  const handleCloseModalUserChange = useCallback(() => {
    setIsOpenModalUserChange(false)
    reset()
  }, [reset])

  useEffect(() => {
    if (changeUserOrderMutation.isSuccess) {
      handleCloseModalUserChange()
    }
  }, [changeUserOrderMutation.isSuccess, handleCloseModalUserChange])

  const onSubmit = (data) => {
    const body = {
      ...data,
      order: {
        public_id: order.public_id,
      },
    }

    changeUserOrderMutation.mutate({ ...body })
  }

  const handleSubmitUploadMembers = (evt) => {
    evt.preventDefault()

    const formData = new FormData()
    formData.append('file', file)

    uploadOrderMembersMutation.mutate({ orderPublicId: order.public_id, formData })
  }

  const handleSelectFile = (evt) => {
    setFile(evt.target.files[0])
  }

  const handleCloseModalUploadMembers = () => {
    setIsOpenModalUploadMembers(false)
    setFile(null)
  }

  const handleConfirmDelete = () => {
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = () => {
    if (onDeleteOrder) {
      onDeleteOrder(order)
    } else if (order?.kind === Kind.CORPORATE) {
      deleteCompanyOrderMutation.mutate({ public_id: order.public_id })
    } else {
      deleteOrderMutation.mutate(order.public_id)
    }

    setConfirmDeleteModal(false)
  }

  if (!order) return null

  return (
    <UpdateOrdersTicketsContextProvider
      value={{ handleUpdateOrders: onUpdateOrders, handleUpdateTickets: onUpdateTickets }}
    >
      <Card className={styles.main}>
        <Card.Body className={styles.card}>
          <div className={styles.cardTop}>
            <CostStatus cost={order.amount} status={order.status} />
            <p className={styles.orderId}>
              <Link className={styles.link} to={`/orders/${order.public_id}`}>
                {order.public_id}
              </Link>
            </p>
          </div>

          <div className={styles.mainInfo}>
            <ItemLabelValue
              value={
                <Link className={styles.link} to={`/user/${order.user_public_id}`}>
                  {order.user_public_id}
                </Link>
              }
              label="пользователь"
            />
            <ItemLabelValue value={unixToMoment(order.created).format('D MMMM YYYY, HH:mm')} label="дата создания" />
            <ItemLabelValue value={order.email} label="почта" />
            <ItemLabelValue value={getKindName(order.kind)} label="Тип" />
            {(order?.kind === Kind.CORPORATE || order?.kind === Kind.PARTNER) && (
              <>
                <ItemLabelValue value={order.company_name} label="компания" />
                <ItemLabelValue value={order.description} label="описание" />
              </>
            )}
          </div>

          <div className={styles.sourcesInfo}>
            <p className={styles.ip}>{order.sources_info?.ip}</p>
            <p className={styles.agentUser}>{order.sources_info?.user_agent}</p>
          </div>

          <Dropdown className={styles.dropDown} drop="start">
            <Dropdown.Toggle as={ButtonGroup} bsPrefix={styles.dropDownToggle} variant="success" id="dropdown-basic">
              <IconMoreVertical className={styles.iconMore} />
            </Dropdown.Toggle>

            <Dropdown.Menu>
              <Dropdown.Item as="button" onClick={() => setIsOpenModalUserChange(true)}>
                <i className="bi bi-person me-2" />
                Сменить пользователя
              </Dropdown.Item>
              <Dropdown.Item as="button" onClick={() => setIsOpenModalUploadMembers(true)}>
                <i className="bi bi-upload me-2" />
                Загрузить участников
              </Dropdown.Item>
              {teams?.length > 0 && (
                <Dropdown.Item as="button" onClick={() => setIsOpenModalChangeTeam(true)}>
                  <i className="bi bi-people me-2" />
                  Присвоить команду
                </Dropdown.Item>
              )}
              <Dropdown.Divider />
              <Dropdown.Item className="text-danger" as="button" onClick={handleConfirmDelete}>
                <i className="bi bi-trash me-2" />
                Удалить
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Card.Body>
      </Card>

      <Loader isLoading={isLoadingTickets} text="загрузка товаров">
        <Soldgoods
          soldgoods={order?.soldgoods || order?.soldgood || tickets}
          teams={teams}
          orderUserId={order?.user_public_id}
        />
      </Loader>

      <Modal show={isOpenModalUserChange} onHide={handleCloseModalUserChange} size="sm">
        <Modal.Header closeButton />
        <Modal.Body>
          <Form onSubmit={handleSubmit(onSubmit)} id="form">
            <Row>
              <Col md={12}>
                <Form.Group>
                  <FloatingLabel controlId={'titleInput'} label="Идентификатор пользователя">
                    <FormControl
                      className={styles.field}
                      {...register('user.public_id', {
                        required: true,
                        setValueAs: (v) => checkSetValue(v, undefined, 'text'),
                      })}
                      type="text"
                      isInvalid={errors?.user?.public_id}
                      placeholder="Идентификатор пользователя"
                    />
                  </FloatingLabel>
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button type="submit" variant="success" form="form">
            Сохранить
          </Button>
          <Button onClick={handleCloseModalUserChange} variant="outline-secondary">
            Отмена
          </Button>
        </Modal.Footer>
      </Modal>

      <Modal show={isOpenModalUploadMembers} onHide={handleCloseModalUploadMembers}>
        <Modal.Header closeButton />
        <Modal.Body>
          <Form onSubmit={handleSubmitUploadMembers} id="uploadMembersForm">
            <Row>
              <Col md={12}>
                <Form.Group>
                  <FormControl
                    onChange={handleSelectFile}
                    type="file"
                    name="file"
                    id={`fileMembers${order.public_id}`}
                    ref={fileMembersRef}
                    accept=".xlsx"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="submit"
            variant="success"
            form="uploadMembersForm"
            disabled={uploadOrderMembersMutation.isLoading}
          >
            {uploadOrderMembersMutation.isLoading ? 'Загрузка...' : 'Сохранить'}
          </Button>
          <Button onClick={handleCloseModalUploadMembers} variant="outline-secondary">
            Отмена
          </Button>
        </Modal.Footer>
      </Modal>

      <ChangeTeamModal
        teams={teams}
        isShow={isOpenModalChangeTeam}
        orderPublicId={order?.public_id}
        onClose={setIsOpenModalChangeTeam}
      />

      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </UpdateOrdersTicketsContextProvider>
  )
}
