import { useContext } from 'react'
import { Button, FloatingLabel, Form, FormControl, Modal } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { UpdateOrdersTicketsContext } from '@/features/orders'
import { useAssignTeamOrder } from '@/features/orders/api/assignTeamOrder'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'

const ChangeTeamForm = ({ orderPublicId, teams, onClose }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const { handleUpdateTickets } = useContext(UpdateOrdersTicketsContext)

  const changeTeamMutation = useAssignTeamOrder({}, onClose, handleUpdateTickets)

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)

    filteredData.order_public_id = orderPublicId

    if (Object.keys(filteredData).length > 0) {
      changeTeamMutation.mutate({ ...filteredData })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Form.Group>
        <FloatingLabel controlId="teamLabel" label="Команда">
          <FormControl
            as="select"
            {...register('team.public_id', {
              required: true,
              setValueAs: (v) => checkSetValue(v, '', 'text'),
            })}
            type="text"
            isInvalid={errors?.team?.public_id}
            placeholder="Команда"
          >
            <option value="">Выберите один из вариантов</option>
            {teams?.map((team) => (
              <option value={team.public_id} key={team.public_id}>
                {team.name}
              </option>
            ))}
          </FormControl>
        </FloatingLabel>
      </Form.Group>
    </Form>
  )
}

export const ChangeTeamModal = ({ isShow, onClose, orderPublicId, teams }) => {
  return (
    <Modal show={isShow} onHide={() => onClose(false)}>
      <Modal.Header closeButton />
      <Modal.Body>
        <ChangeTeamForm teams={teams} orderPublicId={orderPublicId} onClose={onClose} />
      </Modal.Body>
      <Modal.Footer>
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
        <Button onClick={() => onClose(false)} variant="outline-secondary">
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
