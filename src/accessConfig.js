import { Role } from '@/const'

export const roles = {
  [Role.USER]: 'Пользователь',
  [Role.ADMIN]: 'Админ',
  [Role.VOLUNTEER]: 'Волонтёр',
  [Role.SUPERVOLUNTEER]: 'Суперволонтёр',
  [Role.PROMO]: 'Промокоды',
  [Role.ORDER]: 'Заказы',
  [Role.BANNERS]: 'Банеры',
  [Role.BRANCHES]: 'Отделения',
  [Role.CALENDAR]: 'Календарь',
  [Role.CLICKER]: 'Ссылки',
  [Role.CLUBS]: 'Клубы',
  [Role.COMMUNICATION]: 'Обратная связь',
  [Role.COMPANY]: 'Корп и партнеры',
  [Role.CREATE_ORDER]: 'Создание заказа',
  [Role.EVENTS]: 'События',
  [Role.EVENT_TYPE]: 'Типы событий',
  [Role.PAGES]: 'Страницы',
  [Role.MEMBERS]: 'Спортсмены',
  [Role.NEWS]: 'Новости',
  [Role.USERS]: 'Пользователи',
  [Role.OLYMPIC]: 'Олимпийский резерв',
  [Role.RESULTS]: 'Результаты',
  [Role.ONLINE]: 'Онлайн результаты',
  [Role.SHOP]: 'Магазин',
  [Role.DOCUMENT]: 'Документы',
  [Role.MANAGEMENT]: 'Менеджмент',
  [Role.INSURANCE]: 'Страховки',
  [Role.SUPPORT]: 'Поддержка',
  [Role.OFFLINE]: 'Оффлайн',
  [Role.XLSX_TICKETS]: 'Выгрузка участников',
  [Role.XLSX_SHIRTS]: 'Выгрузка по футболкам',
  [Role.PROCESSING]: 'Процессинг',
  [Role.EVENTS_LIMITED]: 'Отдельное событие',
}

export const accessConfig = {
  menuDropdown: [Role.ADMIN, Role.SUPERADMIN],
  promocodes: {
    route: [Role.SUPERADMIN, Role.PROMO, Role.EVENTS_LIMITED],
  },
  promocodeScreen: {
    route: [Role.SUPERADMIN, Role.PROMO],
  },
  events: {
    route: [Role.EVENTS, Role.SUPERADMIN],
  },
  eventScreen: {
    route: [Role.EVENTS, Role.SUPERADMIN],
    selectAllCities: [Role.SUPERADMIN],
    downloadInsurance: [Role.SUPERADMIN, Role.XLSX_TICKETS],
    downloadShirts: [Role.SUPERADMIN, Role.XLSX_SHIRTS],
  },
  orders: {
    route: [Role.ORDER, Role.SUPERADMIN],
  },
  orderInfoScreen: {
    route: [Role.ORDER, Role.SUPERADMIN],
  },
  news: {
    route: [Role.NEWS, Role.SUPERADMIN],
  },
  newsEditor: {
    route: [Role.NEWS, Role.SUPERADMIN],
  },
  newsEditorStory: {
    route: [Role.NEWS, Role.SUPERADMIN],
  },
  users: {
    route: [Role.USERS, Role.SUPERADMIN],
    actions: [Role.SUPERADMIN],
  },
  userScreen: {
    route: [Role.USERS, Role.SUPERADMIN],
  },
  profile: {
    route: [Role.ADMIN, Role.SUPERADMIN],
    editButton: [Role.SUPERADMIN],
  },
  customOrder: {
    route: [Role.COMPANY, Role.SUPERADMIN],
  },
  corp: {
    route: [Role.COMPANY, Role.SUPERADMIN],
  },
  company: {
    route: [Role.COMPANY, Role.SUPERADMIN],
    createOrderButton: [Role.CREATE_ORDER, Role.SUPERADMIN],
  },
  createCustomOrder: {
    route: [Role.COMPANY, Role.SUPERADMIN],
  },
  corpForm: {
    route: [Role.CREATE_ORDER, Role.SUPERADMIN],
  },
  corpFormItem: {
    route: [Role.CREATE_ORDER, Role.SUPERADMIN],
  },
  supplier: {
    route: [Role.SUPERADMIN],
  },
  createSupplierScreen: {
    route: [Role.SUPERADMIN],
  },
  onlineResults: {
    route: [Role.SUPERADMIN, Role.VOLUNTEER, Role.SUPERVOLUNTEER, Role.ONLINE],
  },
  results: {
    route: [Role.RESULTS, Role.SUPERADMIN],
  },
  resultsTable: {
    route: [Role.RESULTS, Role.SUPERADMIN],
  },
  resultsCreate: {
    route: [Role.RESULTS, Role.SUPERADMIN],
  },
  resultsEdit: {
    route: [Role.RESULTS, Role.SUPERADMIN],
  },
  shop: {
    route: [Role.SHOP, Role.SUPERADMIN],
  },
  shopForm: {
    route: [Role.SHOP, Role.SUPERADMIN],
  },
  shopFormItem: {
    route: [Role.SHOP, Role.SUPERADMIN],
  },
  documents: {
    route: [Role.SUPERADMIN, Role.DOCUMENT],
  },
  documentsForm: {
    route: [Role.SUPERADMIN, Role.DOCUMENT],
  },
  banners: {
    route: [Role.BANNERS, Role.SUPERADMIN],
  },
  bannersForm: {
    route: [Role.BANNERS, Role.SUPERADMIN],
  },
  bannersFormItem: {
    route: [Role.BANNERS, Role.SUPERADMIN],
  },
  settings: {
    route: [Role.SUPERADMIN],
  },
  settingsForm: {
    route: [Role.SUPERADMIN],
  },
  settingsFormItem: {
    route: [Role.SUPERADMIN],
  },
  clubs: {
    route: [Role.CLUBS, Role.SUPERADMIN],
  },
  club: {
    route: [Role.CLUBS, Role.SUPERADMIN],
  },
  links: {
    route: [Role.CLICKER, Role.SUPERADMIN],
  },
  athletes: {
    route: [Role.MEMBERS, Role.SUPERADMIN],
  },
  athleteTeams: {
    route: [Role.MEMBERS, Role.SUPERADMIN],
  },
  pages: {
    route: [Role.PAGES, Role.SUPERADMIN],
  },
  pagesFrom: {
    route: [Role.PAGES, Role.SUPERADMIN],
  },
  pagesFromItem: {
    route: [Role.PAGES, Role.SUPERADMIN],
  },
  calendar: {
    route: [Role.CALENDAR, Role.SUPERADMIN],
  },
  branches: {
    route: [Role.BRANCHES, Role.SUPERADMIN],
  },
  management: {
    route: [Role.MANAGEMENT, Role.SUPERADMIN],
  },
  insurances: {
    route: [Role.INSURANCE, Role.SUPERADMIN],
    downloadButtonInsurance: [Role.INSURANCE, Role.SUPERADMIN],
  },
  olympicReserve: {
    route: [Role.OLYMPIC, Role.SUPERADMIN],
  },
  offline: {
    route: [Role.OFFLINE, Role.SUPERADMIN],
  },
  eventForm: {
    route: [Role.EVENTS, Role.SUPERADMIN],
  },
  processing: {
    route: [Role.PROCESSING, Role.SUPERADMIN],
  },
  polls: {
    route: [Role.SUPERADMIN],
  },
  universities: {
    route: [Role.SUPERADMIN],
  },
  ticket: {
    route: [Role.ORDER, Role.SUPERADMIN],
  },
  root: {
    route: [Role.ADMIN, Role.SUPERADMIN],
  },
}
