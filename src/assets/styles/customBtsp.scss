@import 'bootstrap/scss/bootstrap';

// Иконки в боковом меню должны наследовать цвет активного пункта
.nav-link {
  /* отменяем любые глобальные переопределения fill/stroke — иконки сами управляют цветом по теме */

  &.active {
    /* У активного пункта меняем цвет только тем элементам, в которых он действительно задан */

    /* 1. Заливаем элементы, у которых есть атрибут fill (кроме fill="none") */
    & .bi [fill]:not([fill='none']) {
      fill: currentColor !important;
    }

    /* 2. Обновляем stroke там, где он задан */
    & .bi [stroke] {
      stroke: currentColor !important;
    }
  }
}
