@import 'variables';

@mixin mainTitle {
  font-size: 28px;
  font-weight: 400;
  color: var(--bs-body-color);
}

@mixin pseudo {
  content: '';
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 1rem;
  height: 1rem;
}

@mixin scrollbar {
  //For Chrome, Edge, Safari
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--bs-secondary-color);
    border-radius: 6px;
  }
}

@mixin addButton {
  padding-left: 40px;
  padding-right: 22px;

  font-size: 14px;
  color: var(--bs-body-color);
  line-height: 1.7;
  text-transform: uppercase;

  background-color: var(--bs-success);
  background-image: url('@/assets/img/icons/icon-add.svg');
  background-repeat: no-repeat;
  background-position: center left 20px;
  border: none;

  &:hover {
    background-color: var(--bs-success-hover, var(--bs-success));
    filter: brightness(0.9);
  }
}

@mixin smallBtn {
  padding: 0;

  text-decoration: underline;
  color: var(--bs-secondary-color);

  background: transparent;
  border: none;

  &:hover:not(:disabled),
  &:active:not(:disabled) {
    color: var(--bs-primary);
  }

  &:disabled {
    text-decoration: none;
    color: var(--bs-secondary-color);
    opacity: 0.75;
  }
}
