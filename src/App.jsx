import { useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import 'bootstrap-icons/font/bootstrap-icons.css'

import './App.scss'
import { AppRoute } from '@/const'
import storage from '@/utils/storage'

import { useToast } from './hooks/useToast'
import { axios } from './lib/axios'
import { AppRoutes } from './routes'

function App() {
  const openToast = useToast()
  const user = storage.getUserObj()
  const auth = storage.getToken()

  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userObj')
    window.location.replace(`${window.location.origin}/manager${AppRoute.AUTH}`)
  }

  // для старых аккаунтов у которых роли не списком
  useEffect(() => {
    if (typeof user?.role === 'string') {
      logout()
    }
    if (auth && !user?.public_id) {
      logout()
    }
  }, [auth, user?.public_id, user?.role])

  axios.interceptors.response.use(
    (response) => {
      return response
    },
    (error) => {
      if (error?.response?.status === 400) {
        openToast.error({ title: true, message: error.response.data.message, duration: 0 })
      } else if (
        error?.response?.status === 403 ||
        (error?.response?.status === 401 && localStorage.getItem('token'))
      ) {
        logout()
      } else if (error?.response?.status === 422) {
        openToast.error({ title: true, message: 'Проверьте данные', duration: 0 })
      }

      return error
    }
  )

  return (
    <BrowserRouter basename="/manager">
      <AppRoutes />
    </BrowserRouter>
  )
}

export default App
