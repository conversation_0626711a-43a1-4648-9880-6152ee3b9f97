import { isBoolean } from 'lodash'
import { useDispatch } from 'react-redux'

import { addToasts } from '@/store/slices/app'

const getTitle = (title, type) => {
  if (isBoolean(title) && title) {
    if (type === 'info') {
      return 'Инфо'
    } else if (type === 'success') {
      return 'Успех'
    } else if (type === 'warning') {
      return 'Важно'
    } else if (type === 'error') {
      return 'Ошибка'
    }
  } else if (!isBoolean(title) && title) {
    return title
  }

  return undefined
}

const useToast = () => {
  const dispatch = useDispatch()

  const openToast = ({ type, title, message, duration }) => {
    const newToast = {
      id: Date.now(),
      type,
      title,
      message,
      duration: duration ?? 3000,
    }

    dispatch(addToasts(newToast))
  }

  const info = (params) => {
    openToast({ ...params, type: 'info', title: getTitle(params.title, 'info') })
  }

  const success = (params) => {
    openToast({ ...params, type: 'success', title: getTitle(params.title, 'success') })
  }

  const warning = (params) => {
    openToast({ ...params, type: 'warning', title: getTitle(params.title, 'warning') })
  }

  const error = (params) => {
    openToast({ ...params, type: 'error', title: getTitle(params.title, 'error') })
  }

  return { info, success, warning, error }
}

export { useToast }
