import { useState } from 'react'

export const usePagination = (params) => {
  const { paginationLimit = 10, onSetSlicedValues } = params

  const [skip, setSkip] = useState(0)
  const [count, setCount] = useState(0)
  const [values, setValues] = useState([])
  const [limit, setLimit] = useState(paginationLimit)
  const [totalPages, setTotalPages] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [slicedValues, setSlicedValues] = useState([])

  const handlePage = (pageNumber, newLimit = limit) => {
    setCurrentPage(pageNumber)

    if (values?.length === 0) {
      const nextSkip = newLimit * pageNumber - newLimit

      setSkip(nextSkip)

      if (newLimit !== limit) {
        setLimit(newLimit)
        setTotalPages(Math.ceil(count / newLimit))
      }
    } else {
      handleSetValues(values, pageNumber, newLimit)
    }
  }

  const start = (type) => {
    setCurrentPage(1)

    if (type === 'count') {
      setSkip(0)
    }
  }

  const init = ({ count, values }) => {
    if (count) {
      handleSetCount(count)
    } else if (values) {
      handleSetValues(values, 1)
    }
  }

  const handleSetCount = (newCount) => {
    if (newCount !== count) {
      setCount(newCount)
      setTotalPages(Math.ceil(newCount / limit))
    }

    if (values?.length > 0) {
      setValues([])
      setSlicedValues([])
    }
  }

  const handleSetValues = (values, pageNumber = currentPage, newLimit = limit) => {
    const offset = (pageNumber - 1) * newLimit
    const current_values = values.slice(offset, offset + newLimit)
    const valuesLength = values?.length

    setTotalPages(Math.ceil(valuesLength / newLimit))
    setCount(valuesLength)

    setSlicedValues([...current_values])
    onSetSlicedValues([...current_values])

    setValues([...values])
  }

  return {
    limit,
    skip,
    count,
    currentPage,
    setCurrentPage,
    totalPages,
    slicedValues,
    handlePage,
    init,
    start,
  }
}
