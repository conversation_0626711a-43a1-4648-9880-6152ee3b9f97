import moment from 'moment'

import { unixToMoment } from '@/utils/date'

export const updateFormData = (evt, formData, setFormData, defaultFormData) => {
  const type = evt.target.type
  const name = evt.target.name

  const value = type === 'number' ? +evt.target.value : evt.target.value
  const newFormData = { ...formData }

  if (value === '' || value === 'none' || value === defaultFormData[name]) {
    delete newFormData[name]
    setFormData({ ...newFormData })
  } else if (type === 'date') {
    setFormData({ ...formData, [name]: new Date(value) })
  } else if (name === 'public_name') {
    const regex = /^[a-z0-9_]*$/

    if (regex.test(value)) {
      setFormData({ ...formData, [name]: value })
    }
  } else {
    setFormData({ ...formData, [name]: value })
  }
}

export const checkSetValue = (value, defaultValue, type) => {
  const matchDefaultValue = (v) => {
    return v !== defaultValue ? v : undefined
  }

  const matchDefaultDate = () => {
    return value !== unixToMoment(defaultValue).format('YYYY-MM-DD') ? moment(value).utc().toISOString() : undefined
  }

  if (value !== '') {
    if (type === 'number') {
      return matchDefaultValue(+value)
    } else if (type === 'date' || type === 'datetime-local') {
      return matchDefaultDate()
    } else {
      return matchDefaultValue(value)
    }
  }
  return defaultValue && defaultValue !== value ? value : undefined
}

export const checkSetValueBranches = (value, defaultValue, type, name) => {
  const matchDefaultValue = (v) => {
    return v !== defaultValue ? v : ''
  }

  const matchDefaultDate = () => {
    return value !== unixToMoment(defaultValue).format('YYYY-MM-DD') ? moment(value).utc().toISOString() : ''
  }

  if (name === 'phone') {
    return value === '' ? null : matchDefaultValue(value)
  } else if (name === 'email') {
    return value === '' ? null : matchDefaultValue(value)
  } else if (name === 'link') {
    return value === '' ? null : matchDefaultValue(value)
  } else if (value !== '') {
    if (type === 'number') {
      return matchDefaultValue(+value)
    } else if (type === 'date' || type === 'datetime-local') {
      return matchDefaultDate()
    } else {
      return matchDefaultValue(value)
    }
  }
  return ''
}
