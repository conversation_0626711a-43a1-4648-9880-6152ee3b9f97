const storage = {
  getToken: () => {
    return window.localStorage.getItem('token')
  },
  setToken: (token) => {
    window.localStorage.setItem('token', token)
  },
  clearToken: () => {
    window.localStorage.removeItem('token')
  },
  getUserObj: () => {
    return JSON.parse(window.localStorage.getItem('userObj'))
  },
  setUserObj: (data) => {
    window.localStorage.setItem('userObj', JSON.stringify(data))
  },
}

export default storage
