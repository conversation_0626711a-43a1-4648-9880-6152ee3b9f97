import imagePlaceholder from '../assets/img/image-error-300.png'

export const getImageSrc = (url) => {
  if (url?.slice(0, 4) === 'http' || url?.slice(0, 4) === 'data') {
    return url
  } else if (url?.slice(0, 1) === '/') {
    return `${import.meta.env.VITE_REACT_APP_API}${url}`
  } else if (url === undefined || url === '') {
    return imagePlaceholder
  } else {
    return `${import.meta.env.VITE_REACT_APP_API}/${url}`
  }
}

export const checkAndSetImgUrl = (evt) => {
  evt.target.src = imagePlaceholder
}
