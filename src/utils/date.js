import moment from 'moment-timezone'
import 'moment/locale/ru'

const TIME_FORMAT = import.meta.env.VITE_REACT_APP_TIMEFORMAT

const getFormatTime = (time, timezone) => {
  const timeTz = unixToMoment(time).tz(timezone)
  return `${timeTz.format('DD.MM.YYYY')}`
}

/**
 * Converts timestamp to Moment.js object based on TIME_FORMAT
 * @param {number} timestamp Unix timestamp in seconds or ISO string
 * @returns {moment.Moment} Moment.js object
 */
export const unixToMoment = (timestamp) => {
  if (TIME_FORMAT === 'timestamp') {
    return moment.unix(timestamp)
  } else if (TIME_FORMAT === 'iso') {
    return moment.utc(timestamp)
  }
  // Default to ISO if no format specified
  return moment.utc(timestamp)
}

export { getFormatTime }
