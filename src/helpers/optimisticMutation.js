import { useMutation } from 'react-query'

import { useToast } from '@/hooks/useToast'
import { queryClient } from '@/lib/react-query'

export const useOptimisticMutation = ({
  queryKey,
  mutationFn,
  successMessage,
  errorMessage,
  onMutate: customOnMutate,
  onSuccess: customOnSuccess,
}) => {
  const openToast = useToast()

  return useMutation({
    mutationFn,
    onMutate: async (variables) => {
      await queryClient.cancelQueries(queryKey)

      const previousData = queryClient.getQueryData(queryKey)

      // Если передана кастомная логика onMutate, запускаем её
      if (customOnMutate) {
        customOnMutate(variables, previousData)
      }

      return { previousData }
    },
    onSuccess: (response, variables) => {
      if (customOnSuccess) {
        customOnSuccess(response, variables)
      }

      if (response?.status === 200) {
        openToast.success({ message: successMessage })
      }
    },
    onError: (context) => {
      if (context?.previousData) {
        queryClient.setQueryData(queryKey, context.previousData)
      }

      openToast.error({ message: errorMessage })
    },
  })
}
