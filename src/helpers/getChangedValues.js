export const getChangedValues = (dirtyFields, allValues, defaultValues = {}) => {
  // Если поле конечное и помечено как dirty, просто возвращаем его значение
  if (dirtyFields === true) return allValues

  const initialAccumulator = Array.isArray(allValues) ? [] : {}

  // Вспомогательная функция для глубокого сравнения массивов
  const arraysEqual = (arr1, arr2) => {
    if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false
    if (arr1.length !== arr2.length) return false
    return arr1.every((item, index) => {
      if (typeof item === 'object' && item !== null && typeof arr2[index] === 'object' && arr2[index] !== null) {
        return JSON.stringify(item) === JSON.stringify(arr2[index])
      }
      return item === arr2[index]
    })
  }

  // Иначе рекурсивно обходим дерево dirtyFields
  return Object.entries(dirtyFields).reduce((acc, [key, value]) => {
    if (value === true) {
      // leaf-узел
      acc[key] = allValues[key]
    } else {
      // Проверяем, является ли текущее значение массивом
      const currentValue = allValues?.[key]
      const defaultValue = defaultValues?.[key]

      if (Array.isArray(currentValue)) {
        // Для массивов проверяем, действительно ли они изменились
        if (!arraysEqual(currentValue, defaultValue)) {
          acc[key] = currentValue
        }
        // Если массивы одинаковые, не включаем их в результат
      } else {
        // вложенный объект – углубляемся
        const nested = getChangedValues(value, currentValue ?? {}, defaultValue ?? {})
        if (Object.keys(nested).length) acc[key] = nested
      }
    }

    return acc
  }, initialAccumulator)
}
