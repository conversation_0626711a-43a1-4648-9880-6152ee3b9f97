import { Navigate } from 'react-router-dom'

import { accessConfig } from '@/accessConfig'
import { AppRoute } from '@/const'
import { Links } from '@/features/links/routes'
import { OrderSearch } from '@/features/orders/routes'
import { lazyImport } from '@/utils/lazyImport'

// Lazy load large page components
const { default: AthletesScreen } = lazyImport(() => import('@/pages/AthletesScreen/AthletesScreen'), 'default')
const { default: BannersFormScreen } = lazyImport(
  () => import('@/pages/BannersFormScreen/BannersFormScreen'),
  'default'
)
const { default: BannersScreen } = lazyImport(() => import('@/pages/BannersScreen/BannersScreen'), 'default')
const { default: BranchesScreen } = lazyImport(() => import('@/pages/BranchesScreen/BranchesScreen'), 'default')
const { default: CalendarScreen } = lazyImport(() => import('@/pages/CalendarScreen/CalendarScreen'), 'default')
const { default: ClubScreen } = lazyImport(() => import('@/pages/ClubScreen/ClubScreen'), 'default')
const { default: ClubsScreen } = lazyImport(() => import('@/pages/ClubsScreen/ClubsScreen'), 'default')
const { default: CorpScreen } = lazyImport(() => import('@/pages/CorpScreen/CorpScreen'), 'default')
const { default: CompanyScreen } = lazyImport(
  () => import('@/pages/CorpScreen/innerScreens/CompanyScreen/CompanyScreen'),
  'default'
)
const { default: CreateCorpScreen } = lazyImport(() => import('@/pages/CreateCorpScreen/CreateCorpScreen'), 'default')
const { default: CreateOrderScreen } = lazyImport(
  () => import('@/pages/CreateOrderScreen/CreateOrderScreen'),
  'default'
)
const { default: CreateSupplierScreen } = lazyImport(
  () => import('@/pages/CreateSupplierScreen/CreateSupplierScreen'),
  'default'
)
const { default: CustomOrderScreen } = lazyImport(
  () => import('@/pages/CustomOrderScreen/CustomOrderScreen'),
  'default'
)
const { default: DocumentsFormScreen } = lazyImport(
  () => import('@/pages/DocumentsFormScreen/DocumentsFormScreen'),
  'default'
)
const { default: DocumentsScreen } = lazyImport(() => import('@/pages/DocumentsScreen/DocumentsScreen'), 'default')
const { default: EventScreen } = lazyImport(() => import('@/pages/EventScreen/EventScreen'), 'default')
const { default: EventsScreen } = lazyImport(() => import('@/pages/EventsScreen/EventsScreen'), 'default')
const { default: NewsEditorScreen } = lazyImport(() => import('@/pages/NewsEditorScreen/NewsEditorScreen'), 'default')
const { default: NewsScreen } = lazyImport(() => import('@/pages/NewsScreen/NewsScreen'), 'default')
const { default: OnlineResultsScreen } = lazyImport(
  () => import('@/pages/OnlineResultsScreen/OnlineResultsScreen'),
  'default'
)
const { default: PagesFormScreen } = lazyImport(
  () => import('@/pages/PagesScreen/innerScreens/PagesFormScreen/PagesFormScreen'),
  'default'
)
const { default: PagesScreen } = lazyImport(() => import('@/pages/PagesScreen/PagesScreen'), 'default')
const { default: PromocodeScreen } = lazyImport(() => import('@/pages/PromocodeScreen/PromocodeScreen'), 'default')
const { default: ResultsCreateScreen } = lazyImport(
  () => import('@/pages/ResultsCreateScreen/ResultsCreateScreen'),
  'default'
)
const { default: ResultsEditScreen } = lazyImport(
  () => import('@/pages/ResultsEditScreen/ResultsEditScreen'),
  'default'
)
const { default: ResultsScreen } = lazyImport(() => import('@/pages/ResultsScreen/ResultsScreen'), 'default')
const { ResultsScreenTable } = lazyImport(
  () => import('@/pages/ResultsScreen/ResultsScreenTable/ResultsScreenTable'),
  'ResultsScreenTable'
)
const { default: SettingsOutsideFormScreen } = lazyImport(
  () => import('@/pages/SettingsOutsideFormScreen/SettingsOutsideFormScreen'),
  'default'
)
const { default: SettingsOutsideScreen } = lazyImport(
  () => import('@/pages/SettingsOutsideScreen/SettingsOutsideScreen'),
  'default'
)
const { default: ShopFormScreen } = lazyImport(() => import('@/pages/ShopFormScreen/ShopFormScreen'), 'default')
const { default: ShopScreen } = lazyImport(() => import('@/pages/ShopScreen/ShopScreen'), 'default')
const { default: SupplierScreen } = lazyImport(() => import('@/pages/SupplierScreen/SupplierScreen'), 'default')
const { TeamsScreen } = lazyImport(() => import('@/pages/TeamsScreen/TeamsScreen'), 'TeamsScreen')
const { default: UsersScreen } = lazyImport(() => import('@/pages/UsersScreen/UsersScreen'), 'default')

import App from './protected'

const { Profile } = lazyImport(() => import('@/features/user'), 'Profile')
const { Management } = lazyImport(() => import('@/features/management'), 'Management')
const { Promocodes } = lazyImport(() => import('@/features/promocodes'), 'Promocodes')
const { Insurances } = lazyImport(() => import('@/features/insurances'), 'Insurances')
const { InsuranceTypeForm } = lazyImport(() => import('@/features/insurances'), 'InsuranceTypeForm')
const { OlympicReserve } = lazyImport(() => import('@/features/olympicReserve'), 'OlympicReserve')
const { Offline } = lazyImport(() => import('@/features/offline'), 'Offline')
const { EventFormPage } = lazyImport(() => import('@/features/events'), 'EventFormPage')
const { ProcessingScreen } = lazyImport(() => import('@/features/processing'), 'ProcessingScreen')
const { SoldgoodPage } = lazyImport(() => import('@/features/tickets'), 'SoldgoodPage')
const { CityFormatsPage } = lazyImport(() => import('@/features/events'), 'CityFormatsPage')
const { CityFormatPage } = lazyImport(() => import('@/features/events'), 'CityFormatPage')
const { PollsList } = lazyImport(() => import('@/features/polls'), 'PollsList')
const { PollDetails } = lazyImport(() => import('@/features/polls'), 'PollDetails')
const { Universities } = lazyImport(() => import('@/features/universities'), 'Universities')

export const protectedRoutes = [
  {
    element: <App />,
    children: [
      {
        path: AppRoute.PROMOCODES,
        element: <Promocodes />,
        allowedRoles: accessConfig.promocodes.route,
      },
      {
        path: AppRoute.EVENTS,
        element: <EventsScreen />,
        allowedRoles: accessConfig.events.route,
      },
      {
        path: AppRoute.ORDERS,
        element: <OrderSearch />,
        allowedRoles: accessConfig.orders.route,
      },
      {
        path: AppRoute.NEWS,
        element: <NewsScreen />,
        allowedRoles: accessConfig.news.route,
      },
      {
        path: AppRoute.USERS,
        element: <UsersScreen />,
        allowedRoles: accessConfig.users.route,
      },
      {
        path: AppRoute.CUSTOM_ORDER,
        element: <CustomOrderScreen />,
        allowedRoles: accessConfig.customOrder.route,
      },
      {
        path: AppRoute.CORP,
        element: <CorpScreen />,
        allowedRoles: accessConfig.corp.route,
      },
      {
        path: AppRoute.COMPANY,
        element: <CompanyScreen />,
        allowedRoles: accessConfig.company.route,
      },
      {
        path: AppRoute.SUPPLIER,
        element: <SupplierScreen />,
        allowedRoles: accessConfig.supplier.route,
      },
      {
        path: AppRoute.ONLINE_RESULTS,
        element: <OnlineResultsScreen />,
        allowedRoles: accessConfig.onlineResults.route,
      },
      {
        path: AppRoute.RESULTS,
        element: <ResultsScreen />,
        allowedRoles: accessConfig.results.route,
      },
      {
        path: AppRoute.RESULTS_TABLE,
        element: <ResultsScreenTable />,
        allowedRoles: accessConfig.resultsTable.route,
      },
      {
        path: AppRoute.RESULTS_TABLE_EDIT,
        element: <ResultsEditScreen />,
        allowedRoles: accessConfig.resultsTable.route,
      },
      {
        path: AppRoute.RESULTS_CREATE,
        element: <ResultsCreateScreen />,
        allowedRoles: accessConfig.resultsCreate.route,
      },
      {
        path: AppRoute.RESULTS_EDIT,
        element: <ResultsCreateScreen />,
        allowedRoles: accessConfig.resultsEdit.route,
      },
      {
        path: AppRoute.PROMOCODE_SCREEN,
        element: <PromocodeScreen />,
        allowedRoles: accessConfig.promocodeScreen.route,
      },
      {
        path: AppRoute.ORDER_INFO_SCREEN,
        element: <OrderSearch />,
        allowedRoles: accessConfig.orderInfoScreen.route,
      },
      {
        path: AppRoute.ORDER_INFO_START_NUMBER,
        element: <OrderSearch />,
        allowedRoles: accessConfig.orderInfoScreen.route,
      },
      {
        path: AppRoute.NEWS_EDITOR,
        element: <NewsEditorScreen />,
        allowedRoles: accessConfig.newsEditor.route,
      },
      {
        path: AppRoute.NEWS_EDITOR_STORY,
        element: <NewsEditorScreen />,
        allowedRoles: accessConfig.newsEditorStory.route,
      },
      {
        path: AppRoute.CREATE_CUSTOM_ORDER,
        element: <CreateOrderScreen />,
        allowedRoles: accessConfig.createCustomOrder.route,
      },
      {
        path: AppRoute.CORP_FORM,
        element: <CreateCorpScreen />,
        allowedRoles: accessConfig.corpForm.route,
      },
      {
        path: AppRoute.CORP_FORM_ITEM,
        element: <CreateCorpScreen />,
        allowedRoles: accessConfig.corpFormItem.route,
      },
      {
        path: AppRoute.CREATE_SUPPLIER_SCREEN,
        element: <CreateSupplierScreen />,
        allowedRoles: accessConfig.createSupplierScreen.route,
      },
      {
        path: AppRoute.USER_SCREEN,
        element: <Profile />,
        allowedRoles: accessConfig.userScreen.route,
      },
      {
        path: AppRoute.PROFILE,
        element: <Profile />,
        allowedRoles: accessConfig.profile.route,
      },
      {
        path: AppRoute.EVENT_SCREEN,
        element: <EventScreen />,
        allowedRoles: accessConfig.eventScreen.route,
      },
      {
        path: AppRoute.SHOP,
        element: <ShopScreen />,
        allowedRoles: accessConfig.shop.route,
      },
      {
        path: AppRoute.SHOP_FORM,
        element: <ShopFormScreen />,
        allowedRoles: accessConfig.shopForm.route,
      },
      {
        path: AppRoute.SHOP_FORM_ITEM,
        element: <ShopFormScreen />,
        allowedRoles: accessConfig.shopFormItem.route,
      },
      {
        path: AppRoute.ATHLETES,
        element: <AthletesScreen />,
        allowedRoles: accessConfig.athletes.route,
      },
      {
        path: AppRoute.TEAMS,
        element: <TeamsScreen />,
        allowedRoles: accessConfig.athleteTeams.route,
      },
      {
        path: AppRoute.PAGES,
        element: <PagesScreen />,
        allowedRoles: accessConfig.pages.route,
      },
      {
        path: AppRoute.PAGES_FORM,
        element: <PagesFormScreen />,
        allowedRoles: accessConfig.pagesFrom.route,
      },
      {
        path: AppRoute.PAGES_FORM_ITEM,
        element: <PagesFormScreen />,
        allowedRoles: accessConfig.pagesFromItem.route,
      },
      {
        path: AppRoute.DOCUMENTS,
        element: <DocumentsScreen />,
        allowedRoles: accessConfig.documents.route,
      },
      {
        path: AppRoute.DOCUMENTS_FORM,
        element: <DocumentsFormScreen />,
        allowedRoles: accessConfig.documentsForm.route,
      },
      {
        path: AppRoute.BANNERS,
        element: <BannersScreen />,
        allowedRoles: accessConfig.banners.route,
      },
      {
        path: AppRoute.BANNERS_FORM,
        element: <BannersFormScreen />,
        allowedRoles: accessConfig.bannersForm.route,
      },
      {
        path: AppRoute.BANNERS_FORM_ITEM,
        element: <BannersFormScreen />,
        allowedRoles: accessConfig.bannersFormItem.route,
      },
      {
        path: AppRoute.CALENDAR,
        element: <CalendarScreen />,
        allowedRoles: accessConfig.calendar.route,
      },
      {
        path: AppRoute.SETTINGS,
        element: <SettingsOutsideScreen />,
        allowedRoles: accessConfig.settings.route,
      },
      {
        path: AppRoute.SETTINGS_FORM,
        element: <SettingsOutsideFormScreen />,
        allowedRoles: accessConfig.settingsForm.route,
      },
      {
        path: AppRoute.SETTINGS_FORM_ITEM,
        element: <SettingsOutsideFormScreen />,
        allowedRoles: accessConfig.settingsFormItem.route,
      },
      {
        path: AppRoute.CLUBS,
        element: <ClubsScreen />,
        allowedRoles: accessConfig.clubs.route,
      },
      {
        path: AppRoute.CLUB,
        element: <ClubScreen />,
        allowedRoles: accessConfig.club.route,
      },
      {
        path: AppRoute.BRANCHES,
        element: <BranchesScreen />,
        allowedRoles: accessConfig.branches.route,
      },
      {
        path: AppRoute.LINKS,
        element: <Links />,
        allowedRoles: accessConfig.links.route,
      },
      {
        path: AppRoute.MANAGEMENT,
        element: <Management />,
        allowedRoles: accessConfig.management.route,
      },
      {
        path: AppRoute.INSURANCES,
        element: <Insurances />,
        allowedRoles: accessConfig.insurances.route,
      },
      {
        path: AppRoute.INSURANCES_TAB,
        element: <Insurances />,
        allowedRoles: accessConfig.insurances.route,
      },
      {
        path: AppRoute.INSURANCE_TYPE_CREATE,
        element: <InsuranceTypeForm />,
        allowedRoles: accessConfig.insurances.route,
      },
      {
        path: AppRoute.INSURANCE_TYPE_EDIT_ID,
        element: <InsuranceTypeForm />,
        allowedRoles: accessConfig.insurances.route,
      },
      {
        path: AppRoute.OLYMPIC_RESERVE,
        element: <OlympicReserve />,
        allowedRoles: accessConfig.olympicReserve.route,
      },
      {
        path: AppRoute.OFFLINE,
        element: <Offline />,
        allowedRoles: accessConfig.offline.route,
      },
      {
        path: AppRoute.EVENTS_CREATE,
        element: <EventFormPage />,
        allowedRoles: accessConfig.eventForm.route,
      },
      {
        path: AppRoute.EVENTS_FORM_ITEM,
        element: <EventFormPage />,
        allowedRoles: accessConfig.eventForm.route,
      },
      {
        path: AppRoute.CITY_FORMATS,
        element: <CityFormatsPage />,
        allowedRoles: accessConfig.eventScreen.route,
      },
      {
        path: AppRoute.CITY_FORMATS_STANDALONE,
        element: <CityFormatsPage />,
        allowedRoles: accessConfig.eventScreen.route,
      },
      {
        path: AppRoute.CITY_FORMAT,
        element: <CityFormatPage />,
        allowedRoles: accessConfig.eventScreen.route,
      },
      {
        path: AppRoute.PROCESSING,
        element: <ProcessingScreen />,
        allowedRoles: accessConfig.processing.route,
      },
      {
        path: AppRoute.POLLS,
        element: <PollsList />,
        allowedRoles: accessConfig.polls.route,
      },
      {
        path: AppRoute.POLL,
        element: <PollDetails />,
        allowedRoles: accessConfig.polls.route,
      },
      {
        path: AppRoute.UNIVERSITIES,
        element: <Universities />,
        allowedRoles: accessConfig.universities.route,
      },
      // TODO: удалить в будущем
      // оставлен для обратной совместимости, актульный SOLDGOOD
      {
        path: AppRoute.TICKET,
        element: <SoldgoodPage />,
        allowedRoles: accessConfig.ticket.route,
      },
      {
        path: AppRoute.SOLDGOOD,
        element: <SoldgoodPage />,
        allowedRoles: accessConfig.ticket.route,
      },
      {
        path: AppRoute.ROOT,
        element: <Navigate to={AppRoute.PROFILE} />,
        allowedRoles: accessConfig.root.route,
      },
    ],
  },
]
