import React from 'react'
import { useRoutes } from 'react-router-dom'

import { FillingAdminData } from '@/components/FillingAdminData/FillingAdminData'

import storage from '@/utils/storage'

import { protectedRoutes } from './protectedRoutes'
import { publicRoutes } from './public'

const filterAllowedRoles = (routes) => {
  const user = storage.getUserObj()

  routes[0].children = routes[0].children.filter((item) => item?.allowedRoles?.some((el) => user?.role?.includes(el)))

  return routes
}

export const AppRoutes = () => {
  const auth = storage.getToken()

  const routes = auth ? filterAllowedRoles(protectedRoutes) : publicRoutes
  const element = useRoutes([...routes])

  return (
    <>
      {element}

      {auth && <FillingAdminData />}
    </>
  )
}
