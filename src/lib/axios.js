import Axios from 'axios'

import storage from '@/utils/storage'

export const API_URL = import.meta.env.VITE_REACT_APP_API
const REQUEST_TIMEOUT = 60000

function authRequestInterceptor(config) {
  const token = storage.getToken()
  if (token) {
    config.headers.authorization = `${token}`
  }
  config.headers.Accept = 'application/json'
  return config
}

export const axios = Axios.create({
  baseURL: API_URL,
  timeout: REQUEST_TIMEOUT,
})

axios.interceptors.request.use(authRequestInterceptor)

// Export as api for backward compatibility
export const api = axios
