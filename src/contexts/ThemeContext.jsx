import { createContext, useContext, useEffect, useState } from 'react'

const THEME_STORAGE_KEY = 'admin-theme'
const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto',
}

const ThemeContext = createContext({
  theme: THEMES.AUTO,
  effectiveTheme: THEMES.LIGHT,
  setTheme: () => {},
  themes: THEMES,
})

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme должен использоваться внутри ThemeProvider')
  }
  return context
}

const getSystemTheme = () => {
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? THEMES.DARK : THEMES.LIGHT
}

const getStoredTheme = () => {
  try {
    return localStorage.getItem(THEME_STORAGE_KEY) || THEMES.AUTO
  } catch {
    return THEMES.AUTO
  }
}

const setStoredTheme = (theme) => {
  try {
    localStorage.setItem(THEME_STORAGE_KEY, theme)
  } catch {
    // Игнорируем ошибки при записи в localStorage
  }
}

export const ThemeProvider = ({ children }) => {
  const [theme, setThemeState] = useState(getStoredTheme)
  const [systemTheme, setSystemTheme] = useState(getSystemTheme)

  // Определяем эффективную тему (ту что фактически применяется)
  const effectiveTheme = theme === THEMES.AUTO ? systemTheme : theme

  // Слушаем изменения системной темы
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const handleChange = (e) => {
      setSystemTheme(e.matches ? THEMES.DARK : THEMES.LIGHT)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  // Применяем тему к HTML элементу
  useEffect(() => {
    const htmlElement = document.documentElement

    if (effectiveTheme === THEMES.DARK) {
      htmlElement.setAttribute('data-bs-theme', 'dark')
    } else {
      htmlElement.setAttribute('data-bs-theme', 'light')
    }

    // Также добавляем класс для кастомных стилей при необходимости
    htmlElement.classList.toggle('theme-dark', effectiveTheme === THEMES.DARK)
    htmlElement.classList.toggle('theme-light', effectiveTheme === THEMES.LIGHT)
  }, [effectiveTheme])

  const setTheme = (newTheme) => {
    if (Object.values(THEMES).includes(newTheme)) {
      setThemeState(newTheme)
      setStoredTheme(newTheme)
    }
  }

  return (
    <ThemeContext.Provider
      value={{
        theme,
        effectiveTheme,
        setTheme,
        themes: THEMES,
      }}
    >
      {children}
    </ThemeContext.Provider>
  )
}

export { THEMES }
