import { createSlice } from '@reduxjs/toolkit'

import { NameSpace } from '../nameSpace'

export const appSlice = createSlice({
  name: NameSpace.APP,
  initialState: {
    toasts: [],
  },
  reducers: {
    addToasts: (state, action) => {
      state.toasts.push(action.payload)
    },
    removeToast: (state, action) => {
      state.toasts = state.toasts.filter((toast) => toast.id !== action.payload)
    },
  },
})

export const { addToasts, removeToast } = appSlice.actions

export const getToasts = (state) => state[NameSpace.APP].toasts

export default appSlice.reducer
