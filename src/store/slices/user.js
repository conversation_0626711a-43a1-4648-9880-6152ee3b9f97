import { createSlice } from '@reduxjs/toolkit'

import storage from '@/utils/storage'

import { NameSpace } from '../nameSpace'

export const userSlice = createSlice({
  name: NameSpace.USER,
  initialState: {
    user: storage.getUserObj() || {},
  },
  reducers: {
    addUser: (state, action) => {
      state.user = action.payload
    },
  },
})

export const { addUser } = userSlice.actions

export const getUser = (state) => state[NameSpace.USER].user

export default userSlice.reducer
