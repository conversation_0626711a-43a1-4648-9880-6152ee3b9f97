import { createSlice } from '@reduxjs/toolkit'

import { NameSpace } from '@/store/nameSpace'

export const linksSlice = createSlice({
  name: NameSpace.LINKS,
  initialState: {
    isHrcl: false,
  },
  reducers: {
    addStatusHrcl: (state, action) => {
      state.isHrcl = action.payload
    },
  },
})

export const { addStatusHrcl } = linksSlice.actions

export const getIsHrcl = (state) => state[NameSpace.LINKS].isHrcl

export default linksSlice.reducer
