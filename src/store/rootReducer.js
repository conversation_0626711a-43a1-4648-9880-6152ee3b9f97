import { combineReducers } from '@reduxjs/toolkit'

import { NameSpace } from './nameSpace'
import appReducer from './slices/app'
import linksReducer from './slices/links'
import userReducer from './slices/user'
import { supplier } from './supplier/supplier'

export default combineReducers({
  [NameSpace.APP]: appReducer,
  [NameSpace.USER]: userReducer,
  [NameSpace.SUPPLIER]: supplier,
  [NameSpace.LINKS]: linksReducer,
})
