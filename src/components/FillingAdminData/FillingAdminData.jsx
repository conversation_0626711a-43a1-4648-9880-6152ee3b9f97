import { useEffect, useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Modal, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useUpdateUserData } from '@/features/user/api/updateUserData'
import { removeEmptyString } from '@/utils/common'
import { checkSetValue } from '@/utils/forms'
import storage from '@/utils/storage'

export const FillingAdminData = () => {
  const user = storage.getUserObj()
  const [isShow, setIsShow] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: user?.name,
      last_name: user?.last_name,
    },
  })

  const updateUserDataMutation = useUpdateUserData()

  useEffect(() => {
    if (user?.name?.length < 2 || user?.last_name?.length < 2) {
      setIsShow(true)
    } else {
      setIsShow(false)
    }
  }, [user])

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)

    if (Object.keys(filteredData).length > 0) {
      updateUserDataMutation.mutate(filteredData)
    }
  }

  return (
    <Modal show={isShow}>
      <Modal.Body>
        <p>Чтобы продолжить укажите имя и фамилию.</p>

        <Form onSubmit={handleSubmit(onSubmit)} id="form">
          <Row>
            <Col className="mb-3" xs={12}>
              <Form.Group>
                <FloatingLabel controlId={'nameInput'} label="Имя">
                  <FormControl
                    {...register('name', {
                      required: true,
                      setValueAs: (v) => checkSetValue(v, {}, 'text'),
                      minLength: 2,
                    })}
                    type="text"
                    isInvalid={errors?.name}
                    placeholder="Имя"
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col xs={12}>
              <Form.Group>
                <FloatingLabel controlId={'lastNameInput'} label="Фамилия">
                  <FormControl
                    {...register('last_name', {
                      required: true,
                      setValueAs: (v) => checkSetValue(v, {}, 'text'),
                      minLength: 2,
                    })}
                    type="text"
                    isInvalid={errors?.last_name}
                    placeholder="Фамилия"
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>
          </Row>
        </Form>
      </Modal.Body>

      <Modal.Footer>
        <Button type="submit" variant="success" form="form">
          Сохранить
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
