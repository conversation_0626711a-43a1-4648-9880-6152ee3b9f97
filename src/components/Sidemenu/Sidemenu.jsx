import { useEffect, useState } from 'react'
import { NavDropdown } from 'react-bootstrap'
import { Link, NavLink, useNavigate } from 'react-router-dom'

import { CheckPermission } from '@/components/CheckPermission/CheckPermission'
import ThemeSwitcher from '@/components/ThemeSwitcher/ThemeSwitcher'
import IconOffline from '@/components/ui/icons/IconOffline'

import { accessConfig } from '@/accessConfig'
import { AppRoute, Role } from '@/const'
import { useTheme } from '@/contexts/ThemeContext'
import { menuItems } from '@/menuItems'
import { isAccessPermissions } from '@/utils/common'

import IconArrowLeft from '../../assets/img/icons/icon-arrow-left.svg?react'
import UserPic from '../../assets/img/icons/icon-user2.svg?react'
import LogoLiga from '../../assets/img/logo-league.svg?react'
import Header from '../Header/Header'
import Toasts from '../Toasts/Toasts'

const getInfoDropdown = (user) => {
  const MAX_LENGTH_NAME = 15
  const PERMISSIBLE_LENGTH = 13
  const name = user.name
  const email = user.email

  if (user && name !== null) {
    if (name?.length > MAX_LENGTH_NAME) {
      return name.slice(0, PERMISSIBLE_LENGTH) + '...'
    } else {
      return name
    }
  } else if (user && email !== null) {
    if (email?.length > MAX_LENGTH_NAME) {
      return email.slice(0, PERMISSIBLE_LENGTH) + '...'
    } else {
      return email
    }
  }

  return 'Администратор'
}

function Sidemenu({ children }) {
  const navigate = useNavigate()
  const user = JSON.parse(localStorage.getItem('userObj'))
  const [isCollapsed, setIsCollapsed] = useState(true)
  const { effectiveTheme, themes } = useTheme()

  useEffect(() => {
    function handleResize() {
      const sideMenuStatus = localStorage.getItem('sidemenu')

      if (window.innerWidth < 1200) setIsCollapsed(true)
      if (window.innerWidth > 1200) {
        if (sideMenuStatus && sideMenuStatus === 'open') {
          setIsCollapsed(false)
        } else if (sideMenuStatus && sideMenuStatus === 'close') {
          setIsCollapsed(true)
        } else {
          setIsCollapsed(false)
        }
      }
    }

    window.addEventListener('resize', handleResize)

    handleResize()

    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userObj')
    navigate(AppRoute.AUTH)
  }

  const handleToggleSideMenu = () => {
    setIsCollapsed(!isCollapsed)

    if (window.innerWidth > 1200) {
      localStorage.setItem('sidemenu', !isCollapsed ? 'close' : 'open')
    }
  }

  const sidebarClasses = [
    'd-flex',
    'flex-column',
    'flex-shrink-0',
    'p-3',
    'bg-body',
    'border-end',
    'position-fixed',
    'top-0',
    'start-0',
    'h-100',
    'overflow-auto',
    'd-none',
    'd-lg-flex',
  ].join(' ')

  const sidebarWidth = isCollapsed ? '5rem' : '17.5rem'
  const linkColorClass = effectiveTheme === themes.DARK ? 'link-light' : 'link-dark'

  return (
    <>
      <Header />

      <div className="d-flex">
        {/* Sidebar */}
        <div
          className={sidebarClasses}
          style={{
            width: sidebarWidth,
            zIndex: 1000,
            transition: 'width 0.3s ease',
          }}
        >
          {/* Logo */}
          <div className="d-flex justify-content-center">
            <LogoLiga
              style={{
                filter: effectiveTheme === themes.DARK ? 'invert(0)' : 'invert(1)',
              }}
              width={isCollapsed ? '32' : '80'}
              height={isCollapsed ? '18' : '57'}
            />
          </div>

          <hr className="my-3" />

          {/* Navigation Menu */}
          <ul className="nav nav-pills flex-column mb-auto">
            {menuItems.map(
              (link) =>
                isAccessPermissions(link.allowedRoles, user.role) && (
                  <li key={link.id} className="nav-item mb-1">
                    <NavLink
                      to={link.path}
                      className={({ isActive }) =>
                        `nav-link d-flex align-items-center ${isActive ? 'active link-white' : linkColorClass} ${isCollapsed ? 'justify-content-center px-2' : 'px-3'}`
                      }
                    >
                      <link.icon className="bi me-2" width="20" height="20" />
                      {!isCollapsed && <span>{link.label}</span>}
                    </NavLink>
                  </li>
                )
            )}

            {window.location.hostname !== 'heroleague.ru' &&
              isAccessPermissions([Role.OFFLINE, Role.SUPERADMIN], user.role) && (
                <li className="nav-item mb-1">
                  <NavLink
                    to={AppRoute.OFFLINE}
                    className={({ isActive }) =>
                      `nav-link d-flex align-items-center ${isActive ? 'active link-white' : linkColorClass} ${isCollapsed ? 'justify-content-center px-2' : 'px-3'}`
                    }
                  >
                    <IconOffline className="bi me-2" width="20" height="20" />
                    {!isCollapsed && <span>Оффлайн</span>}
                  </NavLink>
                </li>
              )}
          </ul>

          <hr className="my-3" />

          {/* Theme Switcher */}
          <div className="mb-3">
            <ThemeSwitcher isCollapsed={isCollapsed} />
          </div>

          <hr className="my-3" />

          {/* User Info */}
          <div className={`d-flex align-items-center ${isCollapsed ? 'justify-content-center' : ''}`}>
            <Link to={AppRoute.PROFILE} className="text-decoration-none">
              <div
                className="bg-body-secondary rounded-circle d-flex align-items-center justify-content-center me-2"
                style={{ width: '32px', height: '32px' }}
              >
                <UserPic width="16" height="16" />
              </div>
            </Link>
            {!isCollapsed && (
              <div className="flex-grow-1">
                <NavDropdown title={getInfoDropdown(user)} style={{ textTransform: 'capitalize' }} id="user-dropdown">
                  <CheckPermission allowedRoles={accessConfig.menuDropdown}>
                    <NavDropdown.Item onClick={() => navigate(AppRoute.PROFILE)}>Профиль</NavDropdown.Item>
                    <NavDropdown.Divider />
                  </CheckPermission>
                  <NavDropdown.Item onClick={logout}>Выйти</NavDropdown.Item>
                </NavDropdown>
              </div>
            )}
          </div>

          <hr className="my-3" />

          {/* Toggle Button */}
          <button
            className="btn btn-outline-secondary d-flex align-items-center w-100"
            onClick={handleToggleSideMenu}
            type="button"
          >
            <IconArrowLeft
              className="bi"
              width="16"
              height="16"
              style={{
                transition: 'transform 0.3s ease',
                transform: isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
              }}
            />
            {!isCollapsed && <span className="ms-2">Свернуть меню</span>}
          </button>
        </div>

        {/* Main Content */}
        <main
          className="flex-grow-1 overflow-auto"
          style={{
            marginLeft: sidebarWidth,
            transition: 'margin-left 0.3s ease',
            minHeight: '100vh',
          }}
        >
          {children}
        </main>
      </div>

      <Toasts />
    </>
  )
}

export default Sidemenu
