import React from 'react'
import { Col, Figure, Form, FormGroup, Row } from 'react-bootstrap'

import { MAX_IMG_SIZE } from '@/const'
import { useToast } from '@/hooks/useToast'
import { convertBase64 } from '@/utils/common'
import { getImageSrc } from '@/utils/images'

import styles from '../../pages/AthletesScreen/components/Participants/Participants.module.scss'

function SingleImageForm({ formData, defaultFormData, onChangeFormData, isEdit }) {
  const openToast = useToast()

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    const fileSizeInB = file.size
    const base64 = await convertBase64(file)

    const newNewsStoryData = { ...formData }

    if (base64 === '') {
      newNewsStoryData.pivture = ''
      onChangeFormData({ ...newNewsStoryData })
    } else if (fileSizeInB <= MAX_IMG_SIZE) {
      onChangeFormData({ ...formData, picture: base64 })
    } else if (fileSizeInB > MAX_IMG_SIZE) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
      evt.target.value = ''
    }
  }

  return (
    <FormGroup controlId="nameForm">
      <Row className="mb-3">
        {(formData.picture || defaultFormData.picture) && (
          <Col className="d-grid justify-content-center mb-2" md={12}>
            <Figure className="m-0">
              <Figure.Image
                className={styles.image}
                width={95}
                alt="171x180"
                src={getImageSrc(formData.picture || defaultFormData.picture)}
              />
            </Figure>
          </Col>
        )}

        {!isEdit && (
          <Col className="mb-1">
            <Form.Group controlId="formPictureFile">
              <Form.Control
                className={`${styles.fileField} visually-hidden`}
                type="file"
                onChange={handleFileRead}
                name="picture"
                accept=".png, .svg"
                placeholder=" "
                required
              />
              <Form.Label
                className={`${styles.fileButton} ${
                  formData.picture || defaultFormData.picture ? styles.addedImage : ''
                }`}
              >
                {formData.picture || defaultFormData.picture
                  ? 'Заменить фотографию (png или svg)'
                  : 'Выберите фотографию (png или svg)'}
              </Form.Label>
            </Form.Group>
          </Col>
        )}
      </Row>
    </FormGroup>
  )
}

export default SingleImageForm
