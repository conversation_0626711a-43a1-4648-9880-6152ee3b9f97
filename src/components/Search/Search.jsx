import { useRef } from 'react'
import { Col, Dropdown, DropdownButton, FormControl, InputGroup, Row } from 'react-bootstrap'

import { useSearchUser } from '@/features/user/api/searchUser'

import { toLowerCaseEmail } from '../../utils/common'

function Search({ foundData, onResetSearch, totalPages }) {
  const { mutate: searchUser } = useSearchUser()
  const searchRef = useRef()

  const handleClickSearch = (evt) => {
    const name = evt.target.dataset.search
    const value = name === 'email' ? toLowerCaseEmail(searchRef.current.value) : searchRef.current.value

    const body = { [name]: value }

    searchUser(body, {
      onSuccess: (response) => {
        if (response.status === 200) {
          foundData([response.data])
          totalPages(0)
        }
      },
    })
  }

  const handleChangeSearch = (evt) => {
    if (evt.target.value.length === 0) {
      onResetSearch(0, 10)
    }
  }

  return (
    <>
      <Row className="mb-3">
        <Col>
          <InputGroup>
            <InputGroup.Text>
              <i className="bi bi-search" />
            </InputGroup.Text>
            <FormControl
              placeholder="Введите почту, номер телефона или public id пользователя"
              aria-label="Введите почту, номер телефона или public id пользователя"
              aria-describedby="basic-addon2"
              ref={searchRef}
              onChange={handleChangeSearch}
            />

            <DropdownButton variant="outline-secondary" title="Искать по" id="input-group-dropdown-2">
              <Dropdown.Item onClick={handleClickSearch} data-search="email">
                почте
              </Dropdown.Item>
              <Dropdown.Item onClick={handleClickSearch} data-search="phone">
                телефону
              </Dropdown.Item>
              <Dropdown.Item onClick={handleClickSearch} data-search="public_id">
                public id
              </Dropdown.Item>
            </DropdownButton>
          </InputGroup>
        </Col>
      </Row>
    </>
  )
}

export default Search
