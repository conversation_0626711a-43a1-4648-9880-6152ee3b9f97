import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Card, Container } from 'react-bootstrap'

export const ErrorFallback = ({ error, resetErrorBoundary }) => {
  const isDev = import.meta.env.MODE === 'development'

  return (
    <Container className="py-5">
      <Card className="shadow-sm">
        <Card.Header className="bg-danger text-white">
          <h2>Произошла ошибка в приложении</h2>
        </Card.Header>
        <Card.Body>
          <Alert variant="danger">
            <p>
              <strong>Сообщение ошибки:</strong> {error.message || 'Неизвестная ошибка'}
            </p>
          </Alert>

          {isDev && (
            <div className="mb-4">
              <h5>
                Техническая информация <Badge bg="secondary">Только в режиме разработки</Badge>
              </h5>
              <pre className="bg-body-tertiary p-3 border rounded" style={{ maxHeight: '200px', overflow: 'auto' }}>
                {error.stack}
              </pre>
            </div>
          )}

          <div className="d-flex gap-2">
            <Button variant="primary" onClick={resetErrorBoundary}>
              Попробовать снова
            </Button>
            <Button variant="outline-secondary" href="/manager">
              На главную
            </Button>
            {isDev && (
              <Button variant="outline-info" onClick={() => console.error(error)}>
                Вывести в консоль
              </Button>
            )}
          </div>
        </Card.Body>
      </Card>
    </Container>
  )
}
