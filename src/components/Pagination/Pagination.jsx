import classNames from 'classnames/bind'
import { useCallback, useEffect, useState } from 'react'
import { Col, Form, Row } from 'react-bootstrap'

import Paginator from '@/components/Paginator/Paginator'

import styles from './Pagination.module.scss'

const cx = classNames.bind(styles)

export const Pagination = ({
  currentPage,
  totalPages,
  onChangePage,
  count,
  limit,
  pageSizeOptions = [],
  isShowSize = true,
}) => {
  const [fromCount, setFromCount] = useState(0)
  const [paginationLimit, setPaginationLimit] = useState(limit)
  const [toCount, setToCount] = useState(currentPage * 12)

  const paginatorContainerClassNames = cx({
    col: true,
    paginatorContainer: !isShowSize,
  })

  const pageCounterItems = useCallback(
    (pageNum = currentPage, limitNum = paginationLimit) => {
      setFromCount((pageNum || 1) * limitNum - limitNum + 1)

      if (pageNum < totalPages && count > limitNum) {
        setToCount(pageNum * limitNum)
      } else {
        setToCount(count)
      }
    },
    [currentPage, paginationLimit, totalPages, count]
  )

  useEffect(() => {
    isShowSize && pageCounterItems(currentPage, limit)
  }, [count, currentPage, isShowSize, limit, pageCounterItems])

  const handleChangePage = (page) => {
    onChangePage(page)

    isShowSize && pageCounterItems(page)
  }

  const handleChangePageLimit = (evt) => {
    const limit = +evt.target.value
    setPaginationLimit(limit)

    onChangePage(1, limit)

    isShowSize && pageCounterItems(1, limit)
  }

  return (
    <Row className={`${styles.paginationContainer} text-muted`}>
      {pageSizeOptions.length > 0 && (
        <Col className={styles.col} xs={12} sm={6} md={'auto'}>
          Показывать по
          <Form.Select className={styles.select} value={paginationLimit} onChange={handleChangePageLimit}>
            {pageSizeOptions.map((item) => (
              <option value={item} key={item}>
                {item}
              </option>
            ))}
          </Form.Select>
        </Col>
      )}

      {isShowSize && (
        <Col className={`${styles.col} ${styles.sizes}`} xs={12} sm={6} md={'auto'}>
          Показано с {fromCount === 0 ? 1 : fromCount} по {toCount} из {count}
        </Col>
      )}

      <Col className={paginatorContainerClassNames} xs={12} md={'auto'}>
        <Paginator totalPages={totalPages} currentPage={currentPage} changePageHandler={handleChangePage} />
      </Col>
    </Row>
  )
}
