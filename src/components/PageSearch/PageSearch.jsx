import { useEffect, useRef } from 'react'
import { FormControl, InputGroup } from 'react-bootstrap'

/**
 * Компонент предоставляет поле поиска по переданным данным и синхронизирует
 * отфильтрованный список с родительским состоянием.
 *
 * @param {Array}  values      Массив исходных элементов для поиска и отображения.
 * @param {Function} setValues Функция, обновляющая массив, используемый родительским
 *                             компонентом для отображения элементов.
 * @param {string} [placeholder='Поиск'] Текст-плейсхолдер для поля ввода.
 */
function PageSearch({ values, setValues, placeholder = 'Поиск' }) {
  const initializedRef = useRef(false)

  // При монтировании компонента и при изменении входных данных выполняется
  // синхронизация с родительским состоянием.
  useEffect(() => {
    if (!initializedRef.current && Array.isArray(values) && values.length > 0) {
      setValues([...values])
      initializedRef.current = true
    }
  }, [values, setValues])

  const handleChangeSearch = (evt) => {
    const searchTerm = evt.target.value.toLowerCase()

    if (searchTerm === '') {
      // Reset to original values when search is empty
      setValues(values)
    } else {
      // Filter values based on search term
      const filteredValues = values.filter((item) => {
        const itemName = JSON.stringify(item).toLowerCase()
        return itemName.includes(searchTerm)
      })
      setValues([...filteredValues])
    }
  }

  return (
    <InputGroup>
      <InputGroup.Text>
        <i className="bi bi-search" />
      </InputGroup.Text>
      <FormControl
        placeholder={placeholder}
        aria-label={placeholder}
        aria-describedby="basic-addon2"
        onChange={handleChangeSearch}
      />
    </InputGroup>
  )
}

export default PageSearch
