import classNames from 'classnames/bind'

import { getTicketStatus } from '@/utils/common'

import styles from './CostStatus.module.scss'

const cx = classNames.bind(styles)

export const CostStatus = ({ cost, status }) => {
  const priceWrapClasses = cx({
    costWrap: true,
    costWrapPaid: status === 'paid',
    costWrapCreated: status === 'created',
    costWrapDanger: status === 'canceled' || status === 'partially_canceled' || status === 'deleted',
  })

  return (
    <p className={priceWrapClasses}>
      <span className={styles.cost}>{cost?.toLocaleString()} &#8381;</span>
      <span className={styles.status}>{getTicketStatus(status)}</span>
    </p>
  )
}
