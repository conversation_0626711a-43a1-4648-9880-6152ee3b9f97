import PhotoSwipeLightbox from 'photoswipe/lightbox'
import { useEffect } from 'react'

function PhotoViewer({ children, className }) {
  useEffect(() => {
    let lightbox = new PhotoSwipeLightbox({
      gallery: '#' + 'product-card-gallery',
      children: 'a',
      pswpModule: () => import('photoswipe'),
    })

    lightbox.addFilter('itemData', (itemData) => {
      return {
        src: itemData.element.href,
        width: itemData.element.children[0].naturalWidth,
        height: itemData.element.children[0].naturalHeight,
      }
    })
    lightbox.init()

    return () => {
      lightbox.destroy()
      lightbox = null
    }
  }, [])

  return (
    <div className={`${className || ''} pswp-gallery`} id={'product-card-gallery'}>
      {children}
    </div>
  )
}

export default PhotoViewer
