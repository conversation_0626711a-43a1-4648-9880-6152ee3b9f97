import Quill from 'quill'
import { useLayoutEffect, useEffect, useRef } from 'react'
import 'quill/dist/quill.snow.css'

const QuillEditor = ({ value, onChange, placeholder, theme = 'snow', readOnly = false }) => {
  const containerRef = useRef(null)
  const quillRef = useRef(null)
  const onChangeRef = useRef(onChange)
  const isSilent = useRef(false)
  const initialValueRef = useRef(value || '')

  useLayoutEffect(() => {
    onChangeRef.current = onChange
  })

  // Keep latest onChange in ref to avoid stale closures
  useEffect(() => {
    onChangeRef.current = onChange
  }, [onChange])

  // Keep latest value in a ref for mount initialization without adding it to deps
  useEffect(() => {
    initialValueRef.current = value || ''
  }, [value])

  useEffect(() => {
    const container = containerRef.current
    const editorContainer = container.appendChild(container.ownerDocument.createElement('div'))
    const quill = new Quill(editorContainer, {
      theme,
      readOnly,
      placeholder,
      modules: {
        toolbar: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ list: 'ordered' }, { list: 'bullet' }],
          [{ align: [] }],
          ['link', 'image'],
          ['clean'],
        ],
      },
    })

    quillRef.current = quill

    // Initialize with current value
    const initialHTML = initialValueRef.current
    if (initialHTML !== quill.root.innerHTML) {
      isSilent.current = true
      quill.clipboard.dangerouslyPasteHTML(initialHTML)
    }

    quill.on(Quill.events.TEXT_CHANGE, () => {
      if (!isSilent.current) {
        onChangeRef.current?.(quill.root.innerHTML)
      }
      isSilent.current = false
    })

    return () => {
      quillRef.current = null
      container.innerHTML = ''
    }
  }, [theme, readOnly, placeholder])

  // Sync value prop to editor
  useEffect(() => {
    const quill = quillRef.current
    if (!quill) return

    const editorHTML = quill.root.innerHTML
    const valueHTML = value || ''

    if (valueHTML !== editorHTML) {
      isSilent.current = true
      quill.clipboard.dangerouslyPasteHTML(valueHTML)
    }
  }, [value])

  return <div ref={containerRef} />
}

export default QuillEditor
