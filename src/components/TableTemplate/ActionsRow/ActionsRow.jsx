import { Button, Col, OverlayTrigger, Row } from 'react-bootstrap'

import { renderBtnTooltip } from '@/utils/tooltips.jsx'

function ActionsRow({ item, onClickEdit, onClickDelete }) {
  return (
    <td className="align-middle">
      <Row onClick={(evt) => evt.stopPropagation()} className="justify-content-center g-1">
        {onClickEdit && (
          <Col xs="auto">
            <OverlayTrigger
              placement="left"
              delay={{ show: 250, hide: 400 }}
              overlay={(evt) => renderBtnTooltip(evt, 'редактировать')}
            >
              <Button
                onClick={() => onClickEdit(item)}
                variant="outline-secondary"
                type="button"
                size="sm"
                className="d-flex align-items-center border-0"
              >
                <i className="bi bi-pencil" />
              </Button>
            </OverlayTrigger>
          </Col>
        )}
        {onClickDelete && (
          <Col xs="auto">
            <OverlayTrigger
              placement="left"
              delay={{ show: 250, hide: 400 }}
              overlay={(evt) => renderBtnTooltip(evt, 'удалить')}
            >
              <Button
                onClick={() => onClickDelete(item)}
                variant="outline-danger"
                type="button"
                size="sm"
                className="d-flex align-items-center border-0"
              >
                <i className="bi bi-trash" />
              </Button>
            </OverlayTrigger>
          </Col>
        )}
      </Row>
    </td>
  )
}

export default ActionsRow
