.cardWrapper {
  position: static;

  overflow: auto;
}

.title {
  color: #646d8f;
}

.tablee {
  border-collapse: separate;
  border-spacing: 0 5px;

  & thead {
    position: sticky;
    color: #b4bac7;
    text-transform: uppercase;
    z-index: 10;
  }

  & td {
    font-weight: 500;
    vertical-align: middle;
    border: none;
  }

  & thead td {
    font-weight: 400;
  }

  & tr {
    margin-bottom: 5px;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 5px;
  }

  & tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

.td {
  display: table-cell;
  vertical-align: middle;
}

.pictureContainer {
  width: max-content;

  background-color: rgba(100, 109, 143, 0.05);
}

.imageLink {
  position: relative;

  display: block;
  width: 48px;
  height: 48px;

  &:not(:first-child) {
    display: none;
  }

  &:hover::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    display: block;

    background-color: rgba(255, 255, 255, 0.5);
    background-image: url('../../assets/img/icons/icon-zoom.svg');
    background-repeat: no-repeat;
    background-position: center;

    content: '';
    z-index: 10;
  }
}

.picture {
  width: 100%;
  height: 100%;

  object-fit: cover;

  border-radius: 4px;
}

.imageWrapper {
  width: 48px;
  height: 48px;
}

.image {
  width: 100%;
  height: 100%;

  object-fit: cover;

  border-radius: 4px;
}
