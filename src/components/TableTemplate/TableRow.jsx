import React, { memo } from 'react'
import { But<PERSON> } from 'react-bootstrap'
import { <PERSON> } from 'react-router-dom'

import PhotoViewer from '@/components/PhotoViewer/PhotoViewer'

import {
  formatPhoneNumber,
  getDescCode,
  getDiscountForm,
  getRightNames,
  getSex,
  getTicketStatus,
  recompose,
  secondsToDays,
  times,
} from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

import styles from './TableTemplate.module.scss'

const returnImage = (image) => {
  let path = image

  if (typeof image === 'object') {
    path = image.path
  }

  if (!path || path === '') {
    return (
      <div className={styles.imageWrapper}>
        <img className={styles.image} src={getImageSrc(path)} alt="" />
      </div>
    )
  }

  return (
    <div onClick={(evt) => evt.stopPropagation()}>
      <PhotoViewer className={styles.pictureContainer}>
        <a
          href={getImageSrc(path)}
          data-pswp-width={800}
          data-pswp-height={300}
          target="_blank"
          rel="noreferrer"
          className={styles.imageLink}
        >
          <img className={styles.picture} src={getImageSrc(path)} alt="" />
        </a>
      </PhotoViewer>
    </div>
  )
}

const TableRow = memo(
  ({
    value,
    data,
    actions,
    isClickable,
    actionRow,
    onClickRow,
    renderCustomCell,
    setImageModal,
    setImageModalShow,
  }) => {
    const handleRowClick = () => {
      if (onClickRow) {
        onClickRow(value)
      }
    }

    const switchTd = (param, cellValue, item, el) => {
      const timezone = item?.timezone || 'Europe/Moscow'

      if (renderCustomCell && el?.customCell) {
        const customCell = renderCustomCell(param, cellValue, item)
        if (customCell !== null) {
          return customCell
        }
      }

      if (el?.customCell && typeof el.customCell === 'function') {
        const customCell = el.customCell(cellValue, item)
        if (customCell !== null && customCell !== undefined) {
          return customCell
        }
      }

      if (Array.isArray(cellValue) && !el?.customCell) {
        return cellValue.map((item, index) => (
          <span key={index}>
            {typeof item === 'object' ? JSON.stringify(item) : item} <br />
          </span>
        ))
      }

      switch (param) {
        case 'ordersLink':
          return <Link to={`/orders/${cellValue}`}>{cellValue}</Link>
        case 'userLink':
          return <Link to={`/user/${cellValue}`}>{cellValue}</Link>
        case 'date':
          return cellValue ? times.getFullDate(cellValue) : '-'
        case 'dateTs':
          return cellValue ? unixToMoment(cellValue).tz(timezone).format('DD/MM/YYYY, HH:mm') : '-'
        case 'dateTime':
          return (
            <>
              {times.getFullDate(cellValue)}, {times.getTime(cellValue)}
            </>
          )
        case 'gender':
          return cellValue ? getSex(cellValue) : '-'
        case 'ticketStatus':
          return getTicketStatus(cellValue)
        case 'public':
          return (
            <div
              style={{
                border: '1px solid transparent',
                borderRadius: '100%',
                width: '15px',
                height: '15px',
                backgroundColor: cellValue ? 'green' : 'red',
              }}
            >
              {cellValue}
            </div>
          )
        case 'yesOrNo':
          return cellValue ? 'Да' : 'Нет'
        case 'yesOrNoEmoji':
          return cellValue ? '🟢' : '🔴'
        case 'discountForm':
          return getDiscountForm(cellValue, item.discount_form)
        case 'vat':
          return getDescCode('vat', cellValue)
        case 'image':
          return returnImage(cellValue)
        case 'imageLink':
          return (
            <Button
              onClick={() => {
                setImageModal(cellValue)
                setImageModalShow(true)
              }}
              variant="link"
            >
              открыть
            </Button>
          )
        case 'phones':
          return cellValue ? formatPhoneNumber(cellValue) : '-'
        case 'rightNamesParticipants':
          return cellValue ? getRightNames(cellValue, 'участник', 'участника', 'участников') : '-'
        case 'rightNamesTickets':
          return cellValue ? getRightNames(cellValue, 'билет', 'билета', 'билетов') : '-'
        case 'rightNamesOrders':
          return cellValue ? getRightNames(cellValue, 'заказ', 'заказа', 'заказов') : '-'
        case 'money':
          return cellValue ? `${cellValue.toLocaleString()} ₽` : '-'
        case 'copy':
          return (
            <div
              style={{
                padding: '8px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                cursor: 'pointer',
                wordBreak: 'break-all',
              }}
              onClick={(e) => {
                e.stopPropagation()
                navigator.clipboard.writeText(cellValue)
              }}
            >
              {cellValue}
            </div>
          )
        case 'link':
          return (
            <Link
              to={el.linkTo ? recompose(item, el.linkTo) : '#'}
              target={el.linkTarget || '_self'}
              onClick={(e) => e.stopPropagation()}
            >
              {cellValue}
            </Link>
          )
        case 'secondsToDays':
          return cellValue ? secondsToDays(cellValue) : '-'
        default:
          if (cellValue === null || cellValue === undefined) {
            return '-'
          }
          if (typeof cellValue === 'object') {
            return Array.isArray(cellValue) ? cellValue.join(', ') : JSON.stringify(cellValue)
          }
          return String(cellValue)
      }
    }

    return (
      <tr onClick={handleRowClick} style={{ cursor: isClickable || actionRow ? 'pointer' : 'default' }}>
        {data.list.map((item) => (
          <td className={styles.td} key={item.value}>
            {switchTd(item.param, recompose(value, item.value), value, item)}
          </td>
        ))}
        {actions && actions(value)}
      </tr>
    )
  }
)

TableRow.displayName = 'TableRow'

export default TableRow
