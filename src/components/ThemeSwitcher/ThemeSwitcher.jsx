import { ButtonGroup, ToggleButton } from 'react-bootstrap'

import { useTheme } from '@/contexts/ThemeContext'

const ThemeSwitcher = ({ isCollapsed = false }) => {
  const { theme, setTheme, themes } = useTheme()

  const themeOptions = [
    {
      value: themes.LIGHT,
      icon: 'bi-sun',
      ariaLabel: 'Светлая тема',
    },
    {
      value: themes.DARK,
      icon: 'bi-moon',
      ariaLabel: 'Тёмная тема',
    },
    {
      value: themes.AUTO,
      icon: 'bi-circle-half',
      ariaLabel: 'Автоматическая тема',
    },
  ]

  if (isCollapsed) {
    return (
      <div className="mb-3 px-2">
        <div className="d-flex flex-column gap-2">
          {themeOptions.map((option) => (
            <ToggleButton
              key={option.value}
              id={`theme-${option.value}`}
              type="radio"
              variant="outline-secondary"
              name="theme"
              value={option.value}
              checked={theme === option.value}
              onChange={() => setTheme(option.value)}
              className="p-2 d-flex justify-content-center align-items-center"
              style={{ width: '32px', height: '32px', fontSize: '16px' }}
              aria-label={option.ariaLabel}
              title={option.ariaLabel}
            >
              <i className={option.icon} aria-hidden="true" />
            </ToggleButton>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-3 px-3">
      <div className="mb-2">
        <small className="text-muted text-uppercase fw-medium" style={{ fontSize: '12px', letterSpacing: '0.5px' }}>
          ТЕМА
        </small>
      </div>
      <ButtonGroup size="sm" className="w-100">
        {themeOptions.map((option) => (
          <ToggleButton
            key={option.value}
            id={`theme-${option.value}`}
            type="radio"
            variant="outline-secondary"
            name="theme"
            value={option.value}
            checked={theme === option.value}
            onChange={() => setTheme(option.value)}
            className="d-flex justify-content-center align-items-center"
            style={{ minWidth: '32px', height: '32px', fontSize: '16px' }}
            aria-label={option.ariaLabel}
            title={option.ariaLabel}
          >
            <i className={option.icon} aria-hidden="true" />
          </ToggleButton>
        ))}
      </ButtonGroup>
    </div>
  )
}

export default ThemeSwitcher
