import { useTheme } from '@/contexts/ThemeContext'

const IconPolls = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="none" viewBox="0 0 24 24" {...props}>
      <g
        stroke={effectiveTheme === themes.LIGHT ? 'black' : 'white'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
        <path d="M7 8h6M7 12h10M7 16h8" />
      </g>
    </svg>
  )
}

export default IconPolls
