import { useTheme } from '@/contexts/ThemeContext'

const IconOffline = (props) => {
  const { theme } = useTheme()

  const fillColor = theme === 'dark' ? 'white' : 'black'

  return (
    <svg width={20} height={20} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M16.5002 18.8335L14.2918 16.6668H5.41683C4.13905 16.6668 3.05572 16.2224 2.16683 15.3335C1.27794 14.4446 0.833496 13.3613 0.833496 12.0835C0.833496 11.0168 1.16405 10.0668 1.82516 9.2335C2.48627 8.39461 3.33627 7.8585 4.37516 7.62516C4.41961 7.51405 4.46127 7.40572 4.50016 7.30016C4.55572 7.19461 4.59738 7.08072 4.62516 6.9585L1.16683 3.50016L2.3335 2.3335L17.6668 17.6668M5.41683 15.0002H12.6252L5.91683 8.29183C5.88905 8.44738 5.86961 8.60016 5.8585 8.75016C5.84183 8.87794 5.8335 9.01683 5.8335 9.16683H5.41683C4.61127 9.16683 3.92516 9.45294 3.3585 10.0252C2.78627 10.5918 2.50016 11.2779 2.50016 12.0835C2.50016 12.8891 2.78627 13.5835 3.3585 14.1668C3.92516 14.7224 4.61127 15.0002 5.41683 15.0002ZM18.0002 15.6252L16.7918 14.4585C17.0307 14.2641 17.2085 14.0391 17.3252 13.7835C17.4418 13.5279 17.5002 13.2391 17.5002 12.9168C17.5002 12.3335 17.2974 11.8418 16.8918 11.4418C16.4918 11.0363 16.0002 10.8335 15.4168 10.8335H14.1668V9.16683C14.1668 8.01683 13.7613 7.0335 12.9502 6.21683C12.1391 5.40572 11.1557 5.00016 10.0002 5.00016C9.62794 5.00016 9.26683 5.04461 8.91683 5.1335C8.56683 5.22794 8.2335 5.37238 7.91683 5.56683L6.7085 4.3585C7.19738 4.02516 7.71405 3.76961 8.2585 3.59183C8.8085 3.41961 9.38905 3.3335 10.0002 3.3335C11.6279 3.3335 13.0057 3.90016 14.1335 5.0335C15.2668 6.16127 15.8335 7.53905 15.8335 9.16683C16.7946 9.27794 17.5891 9.69461 18.2168 10.4168C18.8502 11.1279 19.1668 11.9613 19.1668 12.9168C19.1668 13.4724 19.064 13.9752 18.8585 14.4252C18.6474 14.8918 18.3613 15.2918 18.0002 15.6252Z"
        fill={fillColor}
      />
    </svg>
  )
}

export default IconOffline
