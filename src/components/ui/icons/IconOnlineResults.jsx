import { useTheme } from '@/contexts/ThemeContext'

const IconOnlineResults = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={18}
      height={18}
      fill="none"
      version="1.1"
      viewBox="0 0 512 512"
      {...props}
    >
      <g fill={effectiveTheme === themes.LIGHT ? 'black' : 'white'}>
        <path d="M 363.31 0.00 L 367.06 0.00 C 410.44 3.07 425.90 57.08 390.11 82.35 Q 389.53 82.76 390.22 82.91 Q 409.19 87.27 427.23 94.28 C 435.88 97.65 437.93 102.88 437.99 112.08 Q 438.12 131.91 437.83 151.74 C 437.65 163.90 432.73 169.98 420.19 169.99 Q 365.83 170.01 311.48 170.00 C 298.03 170.00 292.01 165.34 291.98 151.54 Q 291.94 131.59 292.26 111.63 C 292.38 104.08 294.01 98.13 300.95 95.21 Q 319.86 87.23 339.89 82.91 Q 340.54 82.77 340.00 82.37 C 304.35 55.90 319.33 4.26 363.31 0.00 Z M 380.01 45.00 A 14.92 14.92 0.0 0 0 365.09 30.08 A 14.92 14.92 0.0 0 0 350.17 45.00 A 14.92 14.92 0.0 0 0 365.09 59.92 A 14.92 14.92 0.0 0 0 380.01 45.00 Z M 321.96 139.45 A 0.55 0.55 0.0 0 0 322.51 140.00 L 407.37 140.00 A 0.63 0.62 0.0 0 0 408.00 139.38 L 408.00 119.53 A 0.75 0.74 10.1 0 0 407.51 118.83 Q 394.35 114.02 380.56 111.59 A 0.46 0.45 8.1 0 0 380.02 111.99 C 378.11 130.57 351.95 130.46 350.17 111.83 A 0.35 0.35 0.0 0 0 349.76 111.52 Q 335.90 114.04 322.64 118.77 A 0.95 0.95 0.0 0 0 322.02 119.66 L 321.96 139.45 Z" />
        <path d="M 121.95 216.07 C 108.43 207.30 101.10 191.36 102.32 175.66 C 104.56 146.76 132.59 127.90 160.25 135.99 C 196.00 146.44 203.37 195.09 172.43 216.07 Q 171.34 216.80 172.62 217.11 Q 191.32 221.50 209.23 228.29 C 217.81 231.54 219.94 236.94 219.99 246.01 Q 220.18 277.11 219.80 288.50 C 219.43 299.86 211.87 303.99 201.71 303.99 Q 146.85 304.02 91.97 303.99 C 79.20 303.98 74.01 298.03 73.98 285.54 Q 73.94 265.69 74.25 245.85 C 74.41 235.91 76.75 231.66 85.65 228.09 Q 101.80 221.61 118.77 217.59 Q 120.19 217.26 121.66 217.18 Q 123.52 217.09 121.95 216.07 Z M 162.01 178.99 A 14.92 14.92 0.0 0 0 147.09 164.07 A 14.92 14.92 0.0 0 0 132.17 178.99 A 14.92 14.92 0.0 0 0 147.09 193.91 A 14.92 14.92 0.0 0 0 162.01 178.99 Z M 189.71 252.89 Q 176.47 248.06 162.59 245.60 A 0.33 0.32 -79.9 0 0 162.22 245.86 C 158.22 264.77 134.83 264.55 132.05 246.00 A 0.48 0.48 0.0 0 0 131.49 245.60 Q 117.83 248.08 104.74 252.73 A 1.11 1.11 0.0 0 0 104.01 253.76 L 103.97 273.61 A 0.39 0.39 0.0 0 0 104.36 274.00 L 189.37 274.00 A 0.63 0.62 0.0 0 0 190.00 273.38 L 190.00 253.30 A 0.45 0.43 10.7 0 0 189.71 252.89 Z" />
        <path d="M 445.19 512.00 L 284.44 512.00 C 274.26 510.05 271.01 503.35 271.00 493.64 Q 271.00 356.49 271.00 219.35 C 271.00 206.17 275.77 200.01 289.37 200.01 Q 364.51 200.00 439.66 200.00 C 452.81 200.00 458.99 204.78 459.00 218.36 Q 459.00 354.95 459.00 491.54 C 459.00 502.49 456.88 510.00 445.19 512.00 Z M 429.00 230.57 A 0.57 0.57 0.0 0 0 428.43 230.00 L 301.57 230.00 A 0.57 0.57 0.0 0 0 301.00 230.57 L 301.00 481.43 A 0.57 0.57 0.0 0 0 301.57 482.00 L 428.43 482.00 A 0.57 0.57 0.0 0 0 429.00 481.43 L 429.00 230.57 Z" />
        <path d="M 227.44 512.00 L 66.69 512.00 C 56.35 510.22 53.01 503.43 53.01 493.66 Q 52.99 423.50 53.00 353.34 C 53.00 340.20 57.77 334.01 71.35 334.01 Q 146.50 334.00 221.65 334.00 C 235.29 334.00 240.99 339.04 240.99 352.89 Q 241.00 422.51 241.00 492.13 C 241.00 502.70 238.61 509.90 227.44 512.00 Z M 211.00 364.68 A 0.68 0.68 0.0 0 0 210.32 364.00 L 83.68 364.00 A 0.68 0.68 0.0 0 0 83.00 364.68 L 83.00 481.32 A 0.68 0.68 0.0 0 0 83.68 482.00 L 210.32 482.00 A 0.68 0.68 0.0 0 0 211.00 481.32 L 211.00 364.68 Z" />
      </g>
    </svg>
  )
}

export default IconOnlineResults
