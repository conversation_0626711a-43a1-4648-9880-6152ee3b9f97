import { useTheme } from '@/contexts/ThemeContext'

const IconNews = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="none" viewBox="0 0 16 16" {...props}>
      <g fill={effectiveTheme === themes.LIGHT ? 'black' : 'white'}>
        <path d="M3.5 5C3.36739 5 3.24021 5.05268 3.14645 5.14645C3.05268 5.24021 3 5.36739 3 5.5C3 5.63261 3.05268 5.75979 3.14645 5.85355C3.24021 5.94732 3.36739 6 3.5 6H10.5C10.6326 6 10.7598 5.94732 10.8536 5.85355C10.9473 5.75979 11 5.63261 11 5.5C11 5.36739 10.9473 5.24021 10.8536 5.14645C10.7598 5.05268 10.6326 5 10.5 5H3.5ZM3.5 7C3.36739 7 3.24021 7.05268 3.14645 7.14645C3.05268 7.24021 3 7.36739 3 7.5V9.5C3 9.63261 3.05268 9.75979 3.14645 9.85355C3.24021 9.94732 3.36739 10 3.5 10H5.5C5.63261 10 5.75979 9.94732 5.85355 9.85355C5.94732 9.75979 6 9.63261 6 9.5V7.5C6 7.36739 5.94732 7.24021 5.85355 7.14645C5.75979 7.05268 5.63261 7 5.5 7H3.5ZM4 9V8H5V9H4ZM7.5 7C7.36739 7 7.24021 7.05268 7.14645 7.14645C7.05268 7.24021 7 7.36739 7 7.5C7 7.63261 7.05268 7.75979 7.14645 7.85355C7.24021 7.94732 7.36739 8 7.5 8H10.5C10.6326 8 10.7598 7.94732 10.8536 7.85355C10.9473 7.75979 11 7.63261 11 7.5C11 7.36739 10.9473 7.24021 10.8536 7.14645C10.7598 7.05268 10.6326 7 10.5 7H7.5ZM7.5 9C7.36739 9 7.24021 9.05268 7.14645 9.14645C7.05268 9.24021 7 9.36739 7 9.5C7 9.63261 7.05268 9.75979 7.14645 9.85355C7.24021 9.94732 7.36739 10 7.5 10H10.5C10.6326 10 10.7598 9.94732 10.8536 9.85355C10.9473 9.75979 11 9.63261 11 9.5C11 9.36739 10.9473 9.24021 10.8536 9.14645C10.7598 9.05268 10.6326 9 10.5 9H7.5ZM1 4C1 3.46957 1.21071 2.96086 1.58579 2.58579C1.96086 2.21071 2.46957 2 3 2H11C11.5304 2 12.0391 2.21071 12.4142 2.58579C12.7893 2.96086 13 3.46957 13 4C13.5304 4 14.0391 4.21071 14.4142 4.58579C14.7893 4.96086 15 5.46957 15 6V10.5C15 11.163 14.7366 11.7989 14.2678 12.2678C13.7989 12.7366 13.163 13 12.5 13H3.5C2.83696 13 2.20107 12.7366 1.73223 12.2678C1.26339 11.7989 1 11.163 1 10.5V4ZM12.5 10.5C12.3674 10.5 12.2402 10.4473 12.1464 10.3536C12.0527 10.2598 12 10.1326 12 10V4C12 3.73478 11.8946 3.48043 11.7071 3.29289C11.5196 3.10536 11.2652 3 11 3H3C2.73478 3 2.48043 3.10536 2.29289 3.29289C2.10536 3.48043 2 3.73478 2 4V10.5C2 10.8978 2.15804 11.2794 2.43934 11.5607C2.72064 11.842 3.10218 12 3.5 12H12.5C12.8978 12 13.2794 11.842 13.5607 11.5607C13.842 11.2794 14 10.8978 14 10.5V6C14 5.73478 13.8946 5.48043 13.7071 5.29289C13.5196 5.10536 13.2652 5 13 5V10C13 10.1326 12.9473 10.2598 12.8536 10.3536C12.7598 10.4473 12.6326 10.5 12.5 10.5Z" />
      </g>
    </svg>
  )
}

export default IconNews
