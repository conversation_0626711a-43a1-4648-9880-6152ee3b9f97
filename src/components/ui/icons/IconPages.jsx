import { useTheme } from '@/contexts/ThemeContext'

const IconPages = (props) => {
  const { theme } = useTheme()

  const fillColor = theme === 'dark' ? 'white' : 'black'

  return (
    <svg width={18} height={18} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M5.25 13.5H10.5M5.25 10.5H6M5.25 7.5H7.5M5.25 1.5H12.375L15.75 4.875V14.25"
        stroke={fillColor}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.25 15.375V4.875C2.25 4.57663 2.36853 4.29048 2.5795 4.0795C2.79048 3.86853 3.07663 3.75 3.375 3.75H10.689C10.8083 3.75011 10.9227 3.79758 11.007 3.882L13.368 6.243C13.41 6.28493 13.4433 6.33474 13.4659 6.38957C13.4886 6.4444 13.5001 6.50317 13.5 6.5625V15.375C13.5 15.6734 13.3815 15.9595 13.1705 16.1705C12.9595 16.3815 12.6734 16.5 12.375 16.5H3.375C3.07663 16.5 2.79048 16.3815 2.5795 16.1705C2.36853 15.9595 2.25 15.6734 2.25 15.375Z"
        stroke={fillColor}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5 6.30002V4.01552C10.5001 3.96307 10.5157 3.91183 10.5449 3.86826C10.5741 3.82468 10.6155 3.79073 10.664 3.77067C10.7124 3.75062 10.7657 3.74536 10.8172 3.75556C10.8686 3.76577 10.9159 3.79098 10.953 3.82802L13.422 6.29702C13.4594 6.334 13.4849 6.38129 13.4954 6.43286C13.5058 6.48442 13.5006 6.53792 13.4805 6.58652C13.4604 6.63512 13.4262 6.67664 13.3824 6.70575C13.3386 6.73487 13.2871 6.75028 13.2345 6.75002H10.95C10.8307 6.75002 10.7162 6.70261 10.6318 6.61821C10.5474 6.53382 10.5 6.41936 10.5 6.30002V6.30002Z"
        fill={fillColor}
        stroke={fillColor}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default IconPages
