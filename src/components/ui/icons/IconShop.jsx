import { useTheme } from '@/contexts/ThemeContext'

const IconShop = (props) => {
  const { theme } = useTheme()

  const fillColor = theme === 'dark' ? 'white' : 'black'

  return (
    <svg xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve" viewBox="0 0 32 32" {...props}>
      <path
        fill={fillColor}
        d="m31.4 11.2-3-3.998a2.967 2.967 0 0 0-.4-.423V2a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v4.78a2.945 2.945 0 0 0-.4.42L.601 11.199A3.013 3.013 0 0 0 0 13v1c0 1.654 1.346 3 3 3v13a2 2 0 0 0 2 2h22a2 2 0 0 0 2-2V17c1.654 0 3-1.346 3-3v-1c0-.646-.213-1.285-.6-1.8zM26 2v4H6V2h20zM10.193 15H6.004l4-7h2.189l-2 7zm3.041-7H15.5v7h-4.266l2-7zM16.5 8h2.266l2 7H16.5V8zm3.305 0h2.189l4 7h-4.189l-2-7zM2 14v-1c0-.217.07-.427.2-.6l3-4A1 1 0 0 1 6 8h2.852l-4 7H3a1 1 0 0 1-1-1zm18 16h-7.5V20H20v10zm7 0h-6V20a1 1 0 0 0-1-1h-7.5a1 1 0 0 0-1 1v10H5V17h22v13zm3-16a1 1 0 0 1-1 1h-1.854l-4-7H26c.314 0 .611.148.799.4l3 4A.992.992 0 0 1 30 13v1z"
      />
    </svg>
  )
}

export default IconShop
