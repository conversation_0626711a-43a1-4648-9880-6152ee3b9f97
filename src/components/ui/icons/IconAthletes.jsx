import { useTheme } from '@/contexts/ThemeContext'

const IconAthletes = (props) => {
  const { theme } = useTheme()

  const fillColor = theme === 'dark' ? 'white' : 'black'

  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" {...props}>
      <path
        fill={fillColor}
        d="M62.06 0h3.75q21.23 2.34 30.38 19.08c9.77 17.87 4.17 35.14-10.57 48.04a.31.31 0 0 0 .08.52q38.06 15.63 42.3 56.92V128h-9.81q-1.31-29.38-25.28-45.57a.43.42 31 0 0-.61.15q-6.15 11.56-17 18.9a1.14 1.14 0 0 0-.19 1.72c8.66 9.34 2.04 22.85-9.67 24.8h-2.75c-12.08-1.84-18.39-15.54-9.64-25.04a.89.88-51.7 0 0-.15-1.33Q41.93 94.22 35.69 82.6a.41.41 0 0 0-.59-.15c-13.52 8.87-22.51 22.45-24.54 38.56q-.44 3.49-.87 6.99H0v-3.56q4.29-41.26 42.32-56.76a.32.32 0 0 0 .09-.53q-1.77-1.59-3.52-3.19C26.23 52.38 23.68 34.08 31.7 19.25Q40.8 2.43 62.06 0Zm28.86 37a26.93 26.93 0 0 0-26.93-26.93A26.93 26.93 0 0 0 37.06 37a26.93 26.93 0 0 0 26.93 26.93A26.93 26.93 0 0 0 90.92 37Zm-8.23 40.37q-18.68-6.58-37.37-.02a.7.69 69.4 0 0-.41.92c3.12 7.54 11.73 14.43 18.61 18.5a.97.95-44.5 0 0 .95 0q11.3-6.46 18.29-17.39a1.69 1.68 60.1 0 0 .27-1.01l-.03-.59a.47.46-82.5 0 0-.31-.41ZM68.93 113a4.94 4.94 0 0 0-4.94-4.94 4.94 4.94 0 0 0-4.94 4.94 4.94 4.94 0 0 0 4.94 4.94 4.94 4.94 0 0 0 4.94-4.94Z"
      />
    </svg>
  )
}

export default IconAthletes
