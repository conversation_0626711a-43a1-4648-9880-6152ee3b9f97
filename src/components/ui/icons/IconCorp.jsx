import { useTheme } from '@/contexts/ThemeContext'

const IconCorp = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="none" viewBox="0 0 16 16" {...props}>
      <g stroke={effectiveTheme === themes.LIGHT ? 'black' : 'white'}>
        <path
          d="M10 4C10 4 10 2 8 2C6 2 6 4 6 4M14.5 8.5V14H1.5V8.5H14.5ZM1 4H15V8C15 8 12 10 8 10C4 10 1 8 1 8V4ZM8 11V9V11Z"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export default IconCorp
