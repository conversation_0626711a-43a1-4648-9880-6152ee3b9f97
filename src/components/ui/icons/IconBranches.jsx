import { useTheme } from '@/contexts/ThemeContext'

const IconBranches = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="none" viewBox="0 0 18 18" {...props}>
      <g stroke={effectiveTheme === themes.LIGHT ? 'black' : 'white'}>
        <path
          d="M9 12.375V5.625M3 12L5.25 9.375H12.7402L15 12M3.75 3.375H14.25V5.625H3.75V3.375ZM1.5 12.375H4.5V15.375H1.5V12.375ZM7.5 12.375H10.5V15.375H7.5V12.375ZM13.5 12.375H16.5V15.375H13.5V12.375Z"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export default IconBranches
