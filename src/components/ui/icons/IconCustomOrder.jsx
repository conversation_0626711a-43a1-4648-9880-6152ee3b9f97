import { useTheme } from '@/contexts/ThemeContext'

const IconCustomOrder = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={18}
      height={18}
      fill="none"
      style={{ enableBackground: 'new 0 0 48 48' }}
      version="1.1"
      viewBox="0 0 48 48"
      xmlSpace="preserve"
      {...props}
    >
      <g
        fill={effectiveTheme === themes.LIGHT ? 'black' : 'white'}
        stroke={effectiveTheme === themes.LIGHT ? 'black' : 'white'}
      >
        <path d="M37,43c0,0.6-0.4,1-1,1H12c-0.6,0-1-0.4-1-1V5c0-0.6,0.4-1,1-1h13V2H12c-1.7,0-3,1.3-3,3v38c0,1.7,1.3,3,3,3h24   c1.7,0,3-1.3,3-3V16h-2V43z" />
        <polygon points="33,8 33,2 31,2 31,8 25,8 25,10 31,10 31,16 33,16 33,10 39,10 39,8  " />
        <rect height="2" width="10" x="17" y="19" />
        <rect height="2" width="14" x="17" y="27" />
        <rect height="2" width="10" x="17" y="35" />
      </g>
    </svg>
  )
}

export default IconCustomOrder
