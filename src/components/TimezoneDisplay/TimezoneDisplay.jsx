import moment from 'moment-timezone'
import { useState, useEffect } from 'react'

export const TimezoneDisplay = ({ timezone, className = '' }) => {
  const [currentTime, setCurrentTime] = useState(() => {
    if (!timezone) return 'N/A'
    return moment().tz(timezone).format('HH:mm:ss')
  })

  // Обновление времени каждую секунду
  useEffect(() => {
    if (!timezone) {
      setCurrentTime('N/A')
      return
    }

    const updateTime = () => {
      const timezonedMoment = moment().tz(timezone)
      if (timezonedMoment.isValid()) {
        setCurrentTime(timezonedMoment.format('HH:mm:ss'))
      } else {
        setCurrentTime('Invalid timezone')
      }
    }

    // Обновляем время сразу
    updateTime()

    // Устанавливаем интервал для обновления каждую секунду
    const interval = setInterval(updateTime, 1000)

    // Очищаем интервал при размонтировании
    return () => clearInterval(interval)
  }, [timezone])

  return (
    <div className={`text-muted small d-flex flex-wrap align-items-center gap-3 ${className}`}>
      <div>
        <i className="bi bi-globe me-1" />
        Часовой пояс: <strong>{timezone || 'Не указан'}</strong>
      </div>
      <div>
        <i className="bi bi-clock me-1" />
        Текущее время: <strong>{currentTime}</strong>
      </div>
    </div>
  )
}
