import useAxios from 'axios-hooks'
import { useState } from 'react'
import { Accordion, Card, Col, ListGroup, Row, Table, Button } from 'react-bootstrap'

import { APIRoute } from '../../const'
import { getSex, times } from '../../utils/common'

function UserCard({ user }) {
  const [authorizationHistory, setAuthorizationHistory] = useState([])
  const [, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  const getAuthorizationHistory = () => {
    api({ url: `${APIRoute.USER_AUTH_HISTORY}/${user.public_id}` }).then((r) => {
      if (r.data.values.length > 0) {
        setAuthorizationHistory(r.data.values)
      }
    })
  }

  return (
    <Card className="mb-2" style={{ width: '100%' }}>
      <Card.Header>Информация о пользователе</Card.Header>
      <Card.Body>
        <Row className="mb-4">
          <Col>
            <h6>Основная информация</h6>
            <ListGroup>
              <ListGroup.Item>
                <b>Имя:</b> {user.name}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Фамилия:</b> {user.last_name}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Отчество:</b> {user.second_name}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Дата рождения:</b> {user.birth_date}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Пол:</b> {getSex(user.gender)}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Роль:</b> {user.role}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>By payment:</b> {user.by_payment ? 'Да' : 'Нет'}
              </ListGroup.Item>
            </ListGroup>
          </Col>
          <Col>
            <h6>Контакты</h6>
            <ListGroup>
              <ListGroup.Item>
                <b>Телефон:</b> {user.phone}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Email:</b> {user.email}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Email код:</b> {user.email_code}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Email подтверждён:</b> {user.verified_email ? 'Да' : 'Нет'}
              </ListGroup.Item>
            </ListGroup>
          </Col>
          <Col>
            <h6>Идентификаторы</h6>
            <ListGroup>
              <ListGroup.Item>
                <b>Id:</b> {user.id}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Public id:</b> {user.public_id}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Facebook id:</b> {user.facebook_id}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Google id:</b> {user.google_id}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Vk id:</b> {user.vk_id}
              </ListGroup.Item>
            </ListGroup>
          </Col>
        </Row>
        <Row className="mb-4">
          <Col>
            <h6>Экстренный контакт</h6>
            <ListGroup>
              <ListGroup.Item>
                <b>Имя и Фамилия:</b> {user.extra_contact}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Телефон контакта:</b> {user.contact_phone}
              </ListGroup.Item>
              <ListGroup.Item>
                <b>Отношения:</b> {user.type_relate}
              </ListGroup.Item>
            </ListGroup>
          </Col>
        </Row>
        <Row>
          <Col>
            <Accordion>
              <Accordion.Item eventKey="0">
                <Accordion.Header onClick={getAuthorizationHistory}>История авторизаций</Accordion.Header>
                <Accordion.Body className="pt-3" eventKey="0">
                  <Row className="mb-4">
                    <Col>
                      {authorizationHistory.length > 0 ? (
                        <Table striped bordered hover>
                          <thead>
                            <tr>
                              <th>#</th>
                              <th>Город</th>
                              <th>Страна</th>
                              <th>Метод</th>
                              <th>ОС</th>
                              <th>Браузер</th>
                              <th>Дата авторизации</th>
                            </tr>
                          </thead>
                          <tbody>
                            {authorizationHistory.map((item, index) => (
                              <tr key={index + 1}>
                                <td>{index + 1}</td>
                                <td>{item.city}</td>
                                <td>{item.country}</td>
                                <td>{item.method}</td>
                                <td>{item.os}</td>
                                <td>{item.browser}</td>
                                <td>
                                  {times.getFullDate(item.temp)}, {times.getTime(item.temp)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </Table>
                      ) : (
                        <p>Нет истории</p>
                      )}
                    </Col>
                  </Row>
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  )
}

export default UserCard
