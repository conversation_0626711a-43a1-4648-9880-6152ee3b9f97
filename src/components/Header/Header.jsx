import { useState } from 'react'
import { Container, Nav, Navbar, NavDropdown, Offcanvas } from 'react-bootstrap'
import { NavLink, useNavigate } from 'react-router-dom'

import { CheckPermission } from '@/components/CheckPermission/CheckPermission'

import { accessConfig } from '@/accessConfig'
import { AppRoute, Role } from '@/const'
import { menuItems } from '@/menuItems'
import { isAccessPermissions } from '@/utils/common'

import styles from './Header.module.scss'

const getInfoDropdown = (user) => {
  if (user && user.name !== null) return user.name
  else if (user && user.email !== null) return user.email
  return 'Администратор'
}

function Header() {
  const navigate = useNavigate()
  const user = JSON.parse(localStorage.getItem('userObj'))
  const [isOffcanvasOpen, setIsOffcanvasOpen] = useState(false)

  const handleNavLinkClick = () => {
    setIsOffcanvasOpen(false)
  }

  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('userObj')
    setIsOffcanvasOpen(false)
    navigate(AppRoute.AUTH)
  }

  return (
    <>
      <Navbar
        className={`${styles.header} mb-4`}
        sticky="top"
        expand={false}
        expanded={isOffcanvasOpen}
        onToggle={setIsOffcanvasOpen}
      >
        <Container fluid>
          <Navbar.Toggle aria-controls="offcanvasNavbar-expand-false" />
          <Navbar.Offcanvas
            id="offcanvasNavbar-expand-false"
            aria-labelledby="offcanvasNavbarLabel-expand-false"
            placement="start"
          >
            <Offcanvas.Header closeButton>
              <Offcanvas.Title id="offcanvasNavbarLabel-expand-false">Меню</Offcanvas.Title>
            </Offcanvas.Header>
            <Offcanvas.Body>
              <Nav className="me-auto">
                {menuItems.map(
                  (link) =>
                    isAccessPermissions(link.allowedRoles, user.role) && (
                      <Nav.Link
                        className={styles.navLink}
                        as={NavLink}
                        to={link.path}
                        key={link.id}
                        onClick={handleNavLinkClick}
                      >
                        {link.label}
                      </Nav.Link>
                    )
                )}

                {window.location.hostname !== 'heroleague.ru' &&
                  isAccessPermissions([Role.OFFLINE, Role.SUPERADMIN], user.role) && (
                    <Nav.Link
                      className={styles.navLink}
                      as={NavLink}
                      to={AppRoute.OFFLINE}
                      onClick={handleNavLinkClick}
                    >
                      Оффлайн
                    </Nav.Link>
                  )}
              </Nav>
            </Offcanvas.Body>
          </Navbar.Offcanvas>
          <NavDropdown className={styles.dropdown} title={`${getInfoDropdown(user)}`} id="basic-nav-dropdown">
            <CheckPermission allowedRoles={accessConfig.menuDropdown}>
              <NavDropdown.Item onClick={() => navigate(AppRoute.PROFILE)}>Профиль</NavDropdown.Item>
              <NavDropdown.Divider />
            </CheckPermission>
            <NavDropdown.Item onClick={logout}>Выйти</NavDropdown.Item>
          </NavDropdown>
        </Container>
      </Navbar>
    </>
  )
}

export default Header
