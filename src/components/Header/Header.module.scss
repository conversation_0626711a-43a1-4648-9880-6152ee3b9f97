.header {
  display: none;
  background-color: #ffffff;
  box-shadow: 0 0 0 1px rgb(232, 234, 244);
}

.logo path {
  fill: #000000;
}

.navLink:global(.active) {
  background-color: rgba(100, 109, 143, 0.05);
  border-radius: 4px;
}

.dropdown {
  position: absolute;
  top: 26px;
  right: 12px;
  transform: translateY(-50%);
  width: 160px;
  text-align: right;
}

.alert {
  position: fixed;
  right: 12px;
  width: max-content;
  z-index: 15;
}

@media (max-width: 992px) {
  .dropdown {
    min-width: auto;
    text-align: left;
  }
}

@media (max-width: 1024px) {
  .header {
    display: block;
  }

  .navLink {
    padding: 8px 12px;
  }
}
