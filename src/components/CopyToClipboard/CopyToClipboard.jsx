import { useState } from 'react'
import { OverlayTrigger, Tooltip } from 'react-bootstrap'

export const CopyToClipboard = ({ children }) => {
  const [clipboardText, setClipboardText] = useState('Скопировать')

  const handleCopyToClipboard = (data) => {
    navigator.clipboard.writeText(data).then(
      () => {
        setClipboardText('Скопировано')
      },
      () => {
        setClipboardText('Не удалось скопировать')
      }
    )

    setTimeout(() => setClipboardText('Скопировать'), 800)
  }

  return (
    <OverlayTrigger
      placement="top"
      delay={{ show: 150, hide: 200 }}
      overlay={<Tooltip id="button-tooltip-2">{clipboardText}</Tooltip>}
    >
      <span
        onClick={(evt) => {
          evt.preventDefault()
          handleCopyToClipboard(children)
        }}
        style={{ borderBottom: '1px dotted #000', cursor: 'pointer' }}
      >
        {children}
      </span>
    </OverlayTrigger>
  )
}
