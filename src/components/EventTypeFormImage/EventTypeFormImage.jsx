import { createRef, useEffect, useState } from 'react'
import { But<PERSON>, Col, Figure, FloatingLabel, Form, FormControl, FormSelect, Row } from 'react-bootstrap'

import { MAX_IMG_SIZE } from '@/const'

import styles from './EventTypeFormImage.module.scss'
import { imageSizes } from './eventTypeFormImageData'
import { useToast } from '../../hooks/useToast'
import { checkUploadedImage, convertBase64 } from '../../utils/common'
import { updateFormData } from '../../utils/forms'
import { getImageSrc } from '../../utils/images'

function EventTypeFormImage({ field, outsideFormData, defaultFormData = {}, handleOutsideFormData }) {
  const [formData, setFormData] = useState({})

  const desktopImgInput = createRef()
  const mobileImgInput = createRef()

  const openToast = useToast()

  useEffect(() => {
    const newOutsideFormData = { ...outsideFormData }

    if (Object.keys(formData).length === 0) {
      if (field in newOutsideFormData) {
        delete newOutsideFormData[field]
        handleOutsideFormData(newOutsideFormData)
      }
      return
    }

    if (JSON.stringify(newOutsideFormData[field] || null) !== JSON.stringify(formData)) {
      handleOutsideFormData({ ...outsideFormData, [field]: { ...formData } })
    }
  }, [formData, field, handleOutsideFormData, outsideFormData])

  const handleFileRead = async (evt) => {
    const name = evt.target.name
    const file = evt.target.files[0]
    const fileSizeInKB = file.size
    const base64 = await convertBase64(file)

    const newFormData = { ...formData }

    if (base64 === '') {
      delete newFormData[name]
      setFormData({ ...newFormData })
    } else if (fileSizeInKB <= MAX_IMG_SIZE) {
      if (
        imageSizes[field] &&
        imageSizes[field][name] &&
        (await checkUploadedImage(file, imageSizes[field][name].width, imageSizes[field][name].height))
      ) {
        setFormData({ ...formData, [name]: base64 })
      } else if (
        imageSizes[field] &&
        imageSizes[field][name] &&
        !(await checkUploadedImage(file, imageSizes[field][name].width, imageSizes[field][name].height))
      ) {
        openToast.error({
          title: true,
          message: `Выберите изображение с разрешением ${imageSizes[field][name].width}х${imageSizes[field][name].height}`,
          duration: 6000,
        })
      }
    } else if (fileSizeInKB > MAX_IMG_SIZE) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
    }
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleChangeToggle = (evt) => {
    const name = evt.target.name
    const value = evt.target.checked

    setFormData({ ...formData, [name]: value })
  }

  const handleDeletePictures = (fieldImg) => {
    const newFormData = { ...formData }

    delete newFormData[fieldImg]

    setFormData({ ...newFormData })

    if (fieldImg === 'picture_main') desktopImgInput.current.value = ''
    else if (fieldImg === 'picture_small') mobileImgInput.current.value = ''
  }

  return (
    <Row className="mb-3 g-3">
      <Col md={{ offset: 3, span: 6 }}>
        <h4>{imageSizes[field].label}</h4>
      </Col>

      <Col md={{ offset: 3, span: 6 }}>
        <FloatingLabel controlId="linkLabel" label="Ссылка">
          <FormControl
            onChange={handleChangeField}
            defaultValue={defaultFormData?.link}
            name="link"
            type="text"
            placeholder="Ссылка"
          />
        </FloatingLabel>
      </Col>

      {field === 'project' && (
        <Col md={{ offset: 3, span: 6 }}>
          <FloatingLabel controlId={`${field}SizeImageLabel`} label="Размер">
            <FormSelect
              onChange={handleChangeField}
              value={formData?.size || defaultFormData?.size}
              name="size"
              aria-label="Размер"
            >
              <option value="none">выберите один из вариантов</option>
              <option value="small">Маленький</option>
              <option value="Medium">Средний</option>
              <option value="large">Большой</option>
            </FormSelect>
          </FloatingLabel>
        </Col>
      )}

      <Col md={{ offset: 3, span: 3 }}>
        <FloatingLabel controlId="priorityNumberLabel" label="Приоритет">
          <FormControl
            onChange={handleChangeField}
            value={formData?.priority_number ?? defaultFormData?.priority_number}
            name="priority_number"
            type="number"
            placeholder="Приоритет"
          />
        </FloatingLabel>
      </Col>

      <Col className="d-flex align-items-center" md={3}>
        <Form.Check
          onChange={handleChangeToggle}
          checked={formData?.public ?? defaultFormData?.public}
          name="public"
          type="switch"
          id="public-switch"
          label="Отображать на странице"
        />
      </Col>

      <Col className="mb-3" md={{ offset: 3, span: 3 }}>
        <Form.Group controlId={`${field}DesktopFile`}>
          <Form.Control
            className={`${styles.fileInput} visually-hidden`}
            type="file"
            onChange={handleFileRead}
            name="picture_main"
            ref={desktopImgInput}
          />
          <Form.Label className={styles.fileButton}>Основная картинка</Form.Label>
        </Form.Group>
        <small className="text-muted">
          <i>
            размер изображения: {imageSizes[field].picture_main.width}x{imageSizes[field].picture_main.height}
          </i>
        </small>
      </Col>

      <Col className="mb-3" md={3}>
        <Form.Group controlId={`${field}MobileFile`}>
          <Form.Control
            className={`${styles.fileInput} visually-hidden`}
            type="file"
            onChange={handleFileRead}
            name="picture_small"
            ref={mobileImgInput}
          />
          <Form.Label className={styles.fileButton}>Мобильная картинка, если есть</Form.Label>
        </Form.Group>
        <small className="text-muted">
          <i>
            размер изображения: {imageSizes[field].picture_small.width}x{imageSizes[field].picture_small.height}
          </i>
        </small>
      </Col>

      <Col md={{ offset: 3, span: 6 }}>
        <Row className="mb-5">
          <Col className="d-grid justify-content-center" md={6}>
            {(formData?.picture_main || defaultFormData?.picture_main) && (
              <>
                <Figure className={`${styles.imgWrap} mb-0`}>
                  <Figure.Image
                    className="mb-0"
                    width={260}
                    alt="Основная картинка"
                    src={getImageSrc(formData?.picture_main || defaultFormData?.picture_main)}
                  />
                </Figure>
                {formData?.picture_main && (
                  <Row>
                    <Col>
                      <Button onClick={() => handleDeletePictures('picture_main')} variant="link" size="sm">
                        удалить
                      </Button>
                    </Col>
                  </Row>
                )}
              </>
            )}
          </Col>
          <Col className="d-grid justify-content-center" md={6}>
            {(formData?.picture_small || defaultFormData?.picture_small) && (
              <>
                <Figure className={`${styles.imgWrap} mb-0`}>
                  <Figure.Image
                    className="mb-0"
                    width={260}
                    alt="Мобильная картинка"
                    src={getImageSrc(formData?.picture_small || defaultFormData?.picture_small)}
                  />
                </Figure>
                {formData?.picture_small && (
                  <Row>
                    <Col>
                      <Button onClick={() => handleDeletePictures('picture_small')} variant="link" size="sm">
                        удалить
                      </Button>
                    </Col>
                  </Row>
                )}
              </>
            )}
          </Col>
        </Row>
      </Col>
    </Row>
  )
}

export default EventTypeFormImage
