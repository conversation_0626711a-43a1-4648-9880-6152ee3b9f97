import { useCallback, useEffect, useState } from 'react'
import { Col, Form, Row } from 'react-bootstrap'

import styles from './AdvancedPagination.module.scss'
import Paginator from '../Paginator/Paginator'

function AdvancedPagination({ values, setValues, limitValues = 10 }) {
  const [filteredValues, setFilteredValues] = useState([])
  const [fromCount, setFromCount] = useState(0)
  const [toCount, setToCount] = useState(0)
  const [paginationLimit, setPaginationLimit] = useState(10)
  const [totalPages, setTotalPages] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)

  const handleClickPage = useCallback(
    (pageNumber, limit = paginationLimit) => {
      if (!values || !Array.isArray(values)) return

      const offset = (pageNumber - 1) * limit
      const current_values = values.slice(offset, offset + limit)

      setFilteredValues([...current_values])
      setCurrentPage(pageNumber)
      setValues([...current_values])

      // Inline page counter calculation to avoid dependency loop
      setFromCount(pageNumber * limit - limit + 1)
      setToCount(pageNumber * limit)
    },
    [values, setValues, paginationLimit]
  )

  useEffect(() => {
    if (values && Array.isArray(values) && values.length > 0) {
      setFilteredValues([...values])
      setTotalPages(Math.ceil(values.length / limitValues))
      setPaginationLimit(limitValues)

      // Initialize first page without callback dependency
      const current_values = values.slice(0, limitValues)
      setFilteredValues([...current_values])
      setCurrentPage(1)
      setValues([...current_values])
      setFromCount(1)
      setToCount(limitValues)
    } else {
      setFilteredValues([])
      setCurrentPage(1)
      setFromCount(0)
      setToCount(0)
    }
  }, [values, limitValues]) // eslint-disable-line react-hooks/exhaustive-deps

  const handleChangePageLimit = useCallback(
    (evt) => {
      if (!values || !Array.isArray(values)) return

      const limit = +evt.target.value
      setPaginationLimit(limit)
      setTotalPages(Math.ceil(values.length / limit))

      handleClickPage(currentPage, limit)
    },
    [values, currentPage, handleClickPage]
  )

  return (
    <Row className={`${styles.paginationContainer} text-muted d-flex align-items-center`}>
      <Col className="d-flex align-items-center">
        {values && Array.isArray(values) && values.length > toCount && (
          <>
            Показывать по
            <Form.Select className={styles.select} value={paginationLimit} onChange={handleChangePageLimit}>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={40}>40</option>
              <option value={60}>60</option>
              <option value={80}>80</option>
              <option value={100}>100</option>
            </Form.Select>
          </>
        )}
      </Col>
      <Col md="auto">
        {filteredValues.length > 0 && values && Array.isArray(values) && (
          <>
            Показано с {fromCount === 0 ? 1 : fromCount} по {toCount > values.length ? values.length : toCount} из{' '}
            {values.length}
          </>
        )}
      </Col>
      <Col md="auto">
        {filteredValues.length > 0 && (
          <Paginator totalPages={totalPages} currentPage={currentPage} changePageHandler={handleClickPage} />
        )}
      </Col>
    </Row>
  )
}

export default AdvancedPagination
