import { useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>, Container, Row } from 'react-bootstrap'
import { useLocation } from 'react-router-dom'

import { menuItems } from '@/menuItems'
import { isAccessPermissions } from '@/utils/common'
import storage from '@/utils/storage'

import PageTitle from '../PageTitle/PageTitle'

function Layout({
  children,
  title,
  documentTitle,
  onClickAddButton,
  href,
  titleAbsolute,
  addButtonPermissions = [],
  buttons,
}) {
  const location = useLocation()
  const user = storage.getUserObj()

  useEffect(() => {
    document.title = documentTitle || (location && findRoute(location)) || 'Админка'
  }, [documentTitle, location])

  const findRoute = (location) => {
    const rout = menuItems.find((link) => link.path === location.pathname)
    return rout?.label || null
  }

  return (
    <Container className="pt-4 pb-4" fluid>
      <Row className="d-flex justify-content-between">
        {title && (
          <Col className={titleAbsolute ? 'position-absolute' : ''} xs="auto">
            <PageTitle>{title}</PageTitle>
          </Col>
        )}
        <Col />
        {buttons && <Col xs="auto">{buttons()}</Col>}
        {(onClickAddButton || href) &&
          (addButtonPermissions.length === 0 || isAccessPermissions(addButtonPermissions, user?.role)) && (
            <Col xs="auto">
              <Button variant="success" onClick={onClickAddButton} href={href || undefined}>
                <i className="bi bi-plus-circle me-2" />
                Добавить
              </Button>
            </Col>
          )}
      </Row>

      {children}
    </Container>
  )
}

export default Layout
