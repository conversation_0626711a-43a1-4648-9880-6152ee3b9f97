import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState } from 'react'
import { Button, ButtonGroup, Card, Col, ListGroup, Row, Tab, Table, Tabs } from 'react-bootstrap'
import { Link, useNavigate } from 'react-router-dom'
import 'photoswipe/style.css'

import EventPromocodes from '@/components/EventInfo/components/EventPromocodes/EventPromocodes'
import FormatStatCard from '@/components/EventInfo/components/FormatStatCard'
import LocationStatistics from '@/components/EventInfo/components/LocationStatistics/LocationStatistics'
import PhotoViewer from '@/components/PhotoViewer/PhotoViewer'

import { AppRoute, Kind } from '@/const'
import { useTheme } from '@/contexts/ThemeContext'
import { useGetEventCities } from '@/features/events/api/getEventCities'
import EventDownloads from '@/pages/EventScreen/components/EventDownloads/EventDownloads'
import { eventCityData } from '@/pages/EventsScreen/eventsScreenData'
import { getImageSrc } from '@/utils/images'

import EventsChart from './components/EventsChart'
import EventsChartClusters from './components/EventsChartClusters'
import StatisticCards from './components/StatisticCards'
import TabComparison from './components/TabComparison'
import styles from './EventInfo.module.scss'
import iconGoods from '../../assets/img/icons/icon-goods.svg?react'
import iconRefund from '../../assets/img/icons/icon-refund.svg?react'
import Empty from '../Empty/Empty'
import Loader from '../Loader/Loader'
import TableTemplate from '../TableTemplate/TableTemplate'

const TabKey = {
  COMMON: 'common',
  PROMOCODES: 'tabPromocodes',
  FORMATS: 'tabFormats',
  REFUND: 'tabRefund',
  PRODUCTS: 'tabProducts',
  CHARTS: 'tabCharts',
  CLUSTER: 'CLUSTER',
  LOCATION: 'LOCATION',
  COMPARISON: 'COMPARISON',
}

function EventInfo({ item }) {
  const { effectiveTheme, themes } = useTheme()
  const [{ loading }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )
  const [{ loading: isLoadingCityStat }, apiCityStatistics] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )
  const navigate = useNavigate()
  const [refund, setRefund] = useState({})
  const [products, setProducts] = useState({})
  const [eventPromocodes, setEventPromocodes] = useState([])
  const [isActiveCharts, setIsActiveCharts] = useState(false)
  const [isActiveChartsClusters, setIsActiveChartsClusters] = useState(false)
  const [statisticsKind, setStatisticsKind] = useState('all')
  const [dataEvent, setDataEvent] = useState(null)
  const [formatsTab, setFormatsTab] = useState(null)
  const [activeStatTab, setActiveStatTab] = useState(TabKey.COMMON)

  const getEventCitiesQuery = useGetEventCities(item.public_id)
  const eventCities = getEventCitiesQuery?.data?.data?.values

  const getEventStatistics = useCallback(
    (eventId, kind) => {
      api({ url: `/api/payment/statistics/${kind}/${eventId}` }).then((r) => {
        if (r.data && ((Object.keys(r.data).length !== 0 && r.data.constructor === Object) || r.data.length > 0)) {
          setDataEvent(r.data)
        }
      })
    },
    [api]
  )

  useEffect(() => {
    item && getEventStatistics(item.public_id, 'all')
  }, [getEventStatistics, item])

  const getStatistics = (tab, id, kind = statisticsKind) => {
    setIsActiveCharts(false)
    setActiveStatTab(tab)

    if (tab === TabKey.COMMON) {
      getEventStatistics(item.public_id, kind)
    } else if (tab === TabKey.PROMOCODES) {
      api({ url: `/api/payment/promocode/statistics/event/${id}/${kind}` }).then((r) => {
        if (r.status === 200) {
          setEventPromocodes(r.data.values)
        }
      })
    } else if (tab === TabKey.FORMATS) {
      getCitiesStatistics(id, kind)
    } else if (tab === TabKey.REFUND) {
      api({ url: `/api/refund/statistics/${id}` }).then((r) => {
        if (r.status === 200) {
          setRefund(r.data)
        }
      })
    } else if (tab === TabKey.PRODUCTS) {
      api({ url: `/api/admin/products/${id}` }).then((r) => {
        if (r.status === 200) {
          setProducts(r.data.values)
        }
      })
    } else if (tab === TabKey.CHARTS) {
      setIsActiveCharts(true)
    } else if (tab === TabKey.CLUSTER) {
      setIsActiveChartsClusters(true)
    }
  }

  const handleChangeStatisticsKind = (kind) => {
    getStatistics(activeStatTab, item.public_id, kind)
    setStatisticsKind(kind)
  }

  const getCitiesStatistics = (id, kind) => {
    apiCityStatistics({ url: `/api/payment/statistics/cities/${kind}/${id}` }).then((r) => {
      if (r.status === 200) {
        setFormatsTab(r.data)
      }
    })
  }

  const handleNavigateToCityFormats = (city) => {
    navigate(`/city-formats/${item.public_id}/${city.public_id}`)
  }

  const KindButtons = () => {
    return (
      <ButtonGroup className="mb-3 flex-wrap" size="sm">
        <Button
          onClick={() => handleChangeStatisticsKind('all')}
          variant={statisticsKind === 'all' ? 'primary' : 'outline-primary'}
        >
          Все
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.ATHLETE)}
          variant={statisticsKind === Kind.ATHLETE ? 'primary' : 'outline-primary'}
        >
          Физики
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.CORPORATE)}
          variant={statisticsKind === Kind.CORPORATE ? 'primary' : 'outline-primary'}
        >
          Корпы
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.PARTNER)}
          variant={statisticsKind === Kind.PARTNER ? 'primary' : 'outline-primary'}
        >
          Партнёры
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.BRAND)}
          variant={statisticsKind === Kind.BRAND ? 'primary' : 'outline-primary'}
        >
          Бренд
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.SPORT)}
          variant={statisticsKind === Kind.SPORT ? 'primary' : 'outline-primary'}
        >
          Спорт
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.PRODUCTS)}
          variant={statisticsKind === Kind.PRODUCTS ? 'primary' : 'outline-primary'}
        >
          Продукты
        </Button>
        <Button
          onClick={() => handleChangeStatisticsKind(Kind.OTHER)}
          variant={statisticsKind === Kind.OTHER ? 'primary' : 'outline-primary'}
        >
          Другие
        </Button>
      </ButtonGroup>
    )
  }

  if (!item || Object.keys(item).length === 0) {
    return (
      <div className={styles.empty}>
        <p className="mb-4">Нет информации о событии</p>
        <Button href="/manager/events" variant="outline-secondary">
          К списку событий
        </Button>
      </div>
    )
  }

  return (
    <>
      <Card className="mb-4 border-0 shadow-sm">
        <Card.Header className="border-0 py-4">
          <Row className="g-3 align-items-center">
            <Col xs={12} sm="auto" className="text-center text-sm-start">
              <div className="d-inline-block position-relative">
                <img
                  className="rounded"
                  src={getImageSrc(item.event_type.logo)}
                  alt="Логотип события"
                  style={{
                    width: '70px',
                    height: '70px',
                    objectFit: 'contain',
                    backgroundColor: effectiveTheme === themes.DARK ? '#2d3748' : '#160e4d',
                    padding: '8px',
                  }}
                />
              </div>
            </Col>

            <Col xs={12} sm className="text-center text-sm-start">
              <div>
                <h1 className="mb-2 fs-3 fw-bold">{item.title}</h1>
                <div className="d-flex flex-wrap gap-2 justify-content-center justify-content-sm-start align-items-center">
                  <Button
                    to={`${AppRoute.EVENTS_EDIT}/${item.public_id}`}
                    variant="outline-primary"
                    size="sm"
                    as={Link}
                    className="d-flex align-items-center gap-1"
                  >
                    <i className="bi bi-pencil"></i>
                    Редактировать
                  </Button>
                </div>
              </div>
            </Col>

            <Col xs={12} sm="auto" className="text-center text-sm-end">
              <div className="d-flex gap-2 justify-content-center justify-content-sm-end">
                <Button
                  to={AppRoute.EVENTS}
                  variant="outline-secondary"
                  as={Link}
                  size="sm"
                  className="d-flex align-items-center gap-1"
                >
                  <i className="bi bi-list"></i>
                  Список событий
                </Button>
              </div>
            </Col>
          </Row>
        </Card.Header>
      </Card>

      <Row className="g-3 mb-4">
        <Col xs={12} lg={8}>
          <Card className="h-100 shadow-sm border-0">
            <Card.Header className="bg-primary text-white">
              <Card.Title className="mb-0 fw-semibold">
                <i className="bi bi-info-circle me-2"></i>
                Основная информация
              </Card.Title>
            </Card.Header>
            <Card.Body className="p-0">
              <ListGroup variant="flush">
                <ListGroup.Item className="py-3">
                  <Row className="align-items-center">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Заголовок:
                    </Col>
                    <Col xs={12} sm={8}>
                      <span className="fw-medium">{item.title}</span>
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3">
                  <Row className="align-items-center">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Подзаголовок:
                    </Col>
                    <Col xs={12} sm={8}>
                      <span>{item.title_above}</span>
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3">
                  <Row className="align-items-center">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Ссылка:
                    </Col>
                    <Col xs={12} sm={8}>
                      <a
                        href={item.external_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-decoration-none text-primary"
                      >
                        <i className="bi bi-link-45deg me-1"></i>
                        {item.external_url}
                      </a>
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3">
                  <Row className="align-items-center">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Публичный ID:
                    </Col>
                    <Col xs={12} sm={8}>
                      <code className="bg-body-tertiary px-2 py-1 rounded">{item.public_id}</code>
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3">
                  <Row className="align-items-center">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Промо видео:
                    </Col>
                    <Col xs={12} sm={8}>
                      {item.video_promo ? (
                        <a
                          href={item.video_promo}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-decoration-none text-primary"
                        >
                          <i className="bi bi-play-circle me-1"></i>
                          {item.video_promo}
                        </a>
                      ) : (
                        <span className="text-muted">Не указано</span>
                      )}
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3">
                  <Row className="align-items-center">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Слайдер:
                    </Col>
                    <Col xs={12} sm={8}>
                      <span>{item.slider || 'Не указан'}</span>
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3">
                  <Row className="align-items-start">
                    <Col xs={12} sm={4} className="text-muted fw-medium mb-2 mb-sm-0">
                      Описание:
                    </Col>
                    <Col xs={12} sm={8}>
                      <p className="mb-0 text-break">{item.description || 'Описание отсутствует'}</p>
                    </Col>
                  </Row>
                </ListGroup.Item>
                <ListGroup.Item className="py-3 bg-body-tertiary">
                  <Row className="text-center text-sm-start">
                    <Col key="enabled" xs={12} sm={4} className="mb-3 mb-sm-0">
                      <div className="d-flex align-items-center justify-content-center justify-content-sm-start">
                        <i
                          className={`bi ${item.enabled ? 'bi-check-circle-fill text-success' : 'bi-x-circle-fill text-danger'} me-2`}
                        ></i>
                        <div>
                          <div className="text-muted small">Активно</div>
                          <div className="fw-medium">{item.enabled ? 'Да' : 'Нет'}</div>
                        </div>
                      </div>
                    </Col>
                    <Col key="public" xs={12} sm={4} className="mb-3 mb-sm-0">
                      <div className="d-flex align-items-center justify-content-center justify-content-sm-start">
                        <i
                          className={`bi ${item.public ? 'bi-eye-fill text-info' : 'bi-eye-slash-fill text-secondary'} me-2`}
                        ></i>
                        <div>
                          <div className="text-muted small">Публичное</div>
                          <div className="fw-medium">{item.public ? 'Да' : 'Нет'}</div>
                        </div>
                      </div>
                    </Col>
                    <Col key="cancel" xs={12} sm={4}>
                      <div className="d-flex align-items-center justify-content-center justify-content-sm-start">
                        <i
                          className={`bi ${item.cancel ? 'bi-arrow-counterclockwise text-warning' : 'bi-ban text-danger'} me-2`}
                        ></i>
                        <div>
                          <div className="text-muted small">Возвраты</div>
                          <div className="fw-medium">{item.cancel ? 'Да' : 'Нет'}</div>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </ListGroup.Item>
              </ListGroup>
            </Card.Body>
          </Card>
        </Col>

        <Col xs={12} lg={4}>
          <Row className="g-3 h-100">
            <Col xs={12}>
              <Card className="h-100 shadow-sm border-0">
                <Card.Header className="bg-success text-white">
                  <Card.Title className="mb-0 fw-semibold">
                    <i className="bi bi-tag me-2"></i>
                    Тип мероприятия
                  </Card.Title>
                </Card.Header>
                <Card.Body>
                  <div className="text-center mb-3">
                    <img
                      src={getImageSrc(item.event_type.logo)}
                      alt="Логотип типа мероприятия"
                      className="rounded"
                      style={{
                        width: '80px',
                        height: '80px',
                        objectFit: 'contain',
                        backgroundColor: effectiveTheme === themes.DARK ? '#2d3748' : '#160e4d',
                        padding: '8px',
                      }}
                    />
                  </div>
                  <div className="text-center mb-3">
                    <h6 className="fw-bold text-primary">{item.event_type.title}</h6>
                    <p className="text-muted small mb-0">{item.event_type.description}</p>
                  </div>
                  <div className="border-top pt-3">
                    <Row className="g-2 small">
                      <Col key="id" xs={6}>
                        <div className="text-muted">ID:</div>
                        <code className="small">{item.event_type.public_id}</code>
                      </Col>
                      <Col key="promo" xs={6}>
                        <div className="text-muted">Промо видео:</div>
                        {item.event_type.video_promo ? (
                          <a
                            href={item.event_type.video_promo}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-decoration-none small"
                          >
                            <i className="bi bi-play-circle"></i> Смотреть
                          </a>
                        ) : (
                          <span className="text-muted small">Нет</span>
                        )}
                      </Col>
                    </Row>
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col xs={12}>
              <Card className="h-100 shadow-sm border-0">
                <Card.Header className="bg-warning">
                  <Card.Title className="mb-0 fw-semibold text-dark">
                    <i className="bi bi-image me-2"></i>
                    Баннеры
                  </Card.Title>
                </Card.Header>
                <Card.Body>
                  <PhotoViewer>
                    <Row className="g-3">
                      <Col key="desktop" xs={6}>
                        <div className="text-center">
                          <div className="text-muted small mb-2">Десктоп</div>
                          {item.banners.landing_page.desktop_picture ? (
                            <a
                              href={getImageSrc(item.banners.landing_page.desktop_picture)}
                              data-pswp-width={1200}
                              data-pswp-height={800}
                              className={`d-block`}
                            >
                              <img
                                src={getImageSrc(item.banners.landing_page.desktop_picture)}
                                alt="Десктоп баннер"
                                className={`img-fluid rounded border`}
                                style={{
                                  height: '80px',
                                  width: '100%',
                                  objectFit: 'cover',
                                  cursor: 'pointer',
                                }}
                              />
                              <small className="text-primary mt-1 d-block">Нажмите для увеличения</small>
                            </a>
                          ) : (
                            <div
                              className="border rounded bg-body-secondary d-flex align-items-center justify-content-center text-muted"
                              style={{ height: '80px' }}
                            >
                              <i className="bi bi-image-alt me-2"></i>
                              <small>Нет изображения</small>
                            </div>
                          )}
                        </div>
                      </Col>
                      <Col key="mobile" xs={6}>
                        <div className="text-center">
                          <div className="text-muted small mb-2">Мобильный</div>
                          {item.banners.landing_page.mobile_picture ? (
                            <a
                              href={getImageSrc(item.banners.landing_page.mobile_picture)}
                              data-pswp-width={800}
                              data-pswp-height={1200}
                              className={`d-block`}
                            >
                              <img
                                src={getImageSrc(item.banners.landing_page.mobile_picture)}
                                alt="Мобильный баннер"
                                className={`img-fluid rounded border`}
                                style={{
                                  height: '80px',
                                  width: '100%',
                                  objectFit: 'cover',
                                  cursor: 'pointer',
                                }}
                              />
                              <small className="text-primary mt-1 d-block">Нажмите для увеличения</small>
                            </a>
                          ) : (
                            <div
                              className="border rounded bg-body-secondary d-flex align-items-center justify-content-center text-muted"
                              style={{ height: '80px' }}
                            >
                              <i className="bi bi-phone-fill me-2"></i>
                              <small>Нет изображения</small>
                            </div>
                          )}
                        </div>
                      </Col>
                    </Row>
                  </PhotoViewer>
                  <div className="border-top pt-3 mt-3">
                    <Row className="g-2">
                      <Col key="text-color" xs={6}>
                        <div className="text-muted small">Цвет текста:</div>
                        <div className="d-flex align-items-center">
                          <div
                            className="rounded-circle border me-2"
                            style={{
                              width: '20px',
                              height: '20px',
                              backgroundColor: item.banners.landing_page.text_color || '#000000',
                            }}
                          ></div>
                          <small className="font-monospace">{item.banners.landing_page.text_color || '#000000'}</small>
                        </div>
                      </Col>
                      <Col key="button-color" xs={6}>
                        <div className="text-muted small">Цвет кнопки:</div>
                        <div className="d-flex align-items-center">
                          <div
                            className="rounded-circle border me-2"
                            style={{
                              width: '20px',
                              height: '20px',
                              backgroundColor: item.banners.landing_page.button_color || '#007bff',
                            }}
                          ></div>
                          <small className="font-monospace">
                            {item.banners.landing_page.button_color || '#007bff'}
                          </small>
                        </div>
                      </Col>
                    </Row>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>

      <Row className="mb-4">
        <Col>
          <Card className="shadow-sm border-0">
            <Card.Header className="bg-info text-white">
              <Card.Title className="mb-0 fw-semibold">
                <i className="bi bi-download me-2"></i>
                Загрузки
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <EventDownloads event={item} cities={eventCities} />
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <TableTemplate data={eventCityData} values={eventCities} actionRow={handleNavigateToCityFormats} />
        </Col>
      </Row>

      <>
        <h5 className="mb-3">Статистика</h5>

        <Tabs
          className="mb-3"
          defaultActiveKey={TabKey.COMMON}
          id="event-statistics"
          onSelect={(e) => getStatistics(e, item.public_id)}
        >
          <Tab eventKey={TabKey.COMMON} title="Общая">
            <KindButtons />

            <Loader isLoading={loading}>
              <StatisticCards data={dataEvent} />
            </Loader>
          </Tab>
          <Tab eventKey={TabKey.FORMATS} title="По форматам">
            <KindButtons />

            <Loader isLoading={isLoadingCityStat}>
              {formatsTab !== null ? (
                <Row className="mb-5">
                  {formatsTab?.cities?.map(
                    (city) =>
                      city?.formats.length > 0 && (
                        <Col className="mb-4" key={city.id} sm={6}>
                          <FormatStatCard
                            item={item}
                            city={city}
                            eventCities={eventCities}
                            statisticsKind={statisticsKind}
                          />
                        </Col>
                      )
                  )}
                </Row>
              ) : (
                'Нет статистики по форматам'
              )}
              <center>
                <a href="/$$$" target="_blank" rel="noopener">
                  改善
                </a>
              </center>
            </Loader>
          </Tab>
          <Tab eventKey={TabKey.PROMOCODES} title="По промокодам">
            <KindButtons />

            <Loader isLoading={loading}>
              {eventPromocodes.length !== 0 ? <EventPromocodes promocodes={eventPromocodes} /> : <p>Нет промокодов</p>}
            </Loader>
          </Tab>
          <Tab eventKey={TabKey.REFUND} title="По возвратам">
            <KindButtons />

            <Row className="mb-5">
              <Loader isLoading={loading}>
                {refund.cities ? (
                  refund.cities.map(
                    (city) =>
                      city.formats.length > 0 && (
                        <Col className="mb-4" key={city.public_id} sm={4}>
                          <Card>
                            <Card.Header>
                              <Card.Title>{city.name_ru}</Card.Title>
                            </Card.Header>
                            <Card.Body>
                              <Table>
                                <thead>
                                  <tr>
                                    <th>Формат</th>
                                    <th>Билеты</th>
                                    <th>Сумма &#8381;</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {city.formats.map((item) => (
                                    <tr key={item.title}>
                                      <td>{item.title}</td>
                                      <td>{item.tickets_count}</td>
                                      <td>{Math.trunc(item.common_sum)}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </Table>
                            </Card.Body>
                          </Card>
                        </Col>
                      )
                  )
                ) : (
                  <Empty icon={iconRefund} label="Возвратов нет" />
                )}
              </Loader>
            </Row>
          </Tab>
          <Tab eventKey={TabKey.PRODUCTS} title="По товарам">
            <KindButtons />

            <Loader isLoading={loading}>
              {products.length > 0 ? (
                <ListGroup>
                  {products.map((product) => (
                    <ListGroup.Item key={product.public_id}>
                      <Row>
                        <Col key="title">Заголовок: {product.title}</Col>
                        <Col key="count">Количество: {product.count}</Col>
                      </Row>
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              ) : (
                <Empty icon={iconGoods} label="Дополнительных товаров нет" />
              )}
            </Loader>
          </Tab>
          <Tab eventKey={TabKey.CHARTS} title="Графики">
            <KindButtons />

            {isActiveCharts && (
              <EventsChart eventPublicId={item.public_id} cities={eventCities} kind={statisticsKind} />
            )}
          </Tab>
          <Tab eventKey={TabKey.CLUSTER} title="Кластеры">
            <KindButtons />

            {isActiveChartsClusters && (
              <EventsChartClusters eventPublicId={item.public_id} cities={eventCities} kind={statisticsKind} />
            )}
          </Tab>
          <Tab eventKey={TabKey.LOCATION} title="География">
            <LocationStatistics cities={eventCities} />
          </Tab>
          <Tab eventKey={TabKey.COMPARISON} title="Сравнение">
            <TabComparison eventType={item?.event_type?.public_id} />
          </Tab>
        </Tabs>
      </>
    </>
  )
}

export default EventInfo
