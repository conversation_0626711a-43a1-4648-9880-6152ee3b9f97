import React from 'react'
import { Card, Col, Row } from 'react-bootstrap'

import styles from './StatisticCards.module.scss'
import iconChart from '../../../assets/img/icons/icon-bar-chart.svg?react'
import Empty from '../../Empty/Empty'

function StatisticCards({ data }) {
  return data ? (
    <Row className="g-3">
      <Col sm="auto">
        <Card className={styles.card}>
          <Card.Title>{data.tickets_count.toLocaleString()}</Card.Title>
          <Card.Text className="text-muted">Билеты</Card.Text>
        </Card>
      </Col>
      <Col sm="auto">
        <Card className={`${styles.card} ${styles.card2}`}>
          <Card.Title>{Math.round(data.common_sum).toLocaleString()}</Card.Title>
          <Card.Text className="text-muted">Сумма &#8381;</Card.Text>
        </Card>
      </Col>
      <Col sm="auto">
        <Card className={`${styles.card} ${styles.card3} ${styles.cardPercents}`}>
          <Card.Title className={styles.counter}>{data.registered_count.toLocaleString()}</Card.Title>
          <Card.Title className={styles.percents}>
            {((data.registered_count / data.tickets_count) * 100).toFixed(2)}%
          </Card.Title>
          <Card.Text className="text-muted">С данными</Card.Text>
        </Card>
      </Col>
      <Col sm="auto">
        <Card className={`${styles.card} ${styles.card4}  ${styles.cardPercents}`}>
          <Card.Title className={styles.counter}>{data.insured_count.toLocaleString()}</Card.Title>
          <Card.Title className={styles.percents}>
            {((data.insured_count / data.tickets_count) * 100).toFixed(2)}%
          </Card.Title>
          <Card.Text className="text-muted">Со страховкой</Card.Text>
        </Card>
      </Col>
      <Col sm={'auto'}>
        <Card className={`${styles.card} ${styles.card5} ${styles.cardPercents}`}>
          <Card.Title className={styles.counter}>{data.offline_registered.toLocaleString()}</Card.Title>
          <Card.Title className={styles.percents}>
            {((data.offline_registered / data.tickets_count) * 100).toFixed(2)}%
          </Card.Title>
          <Card.Text className="text-muted">Зарегистрировано на мероприятии</Card.Text>
        </Card>
      </Col>
    </Row>
  ) : (
    <Empty icon={iconChart} label="Статистики нет" />
  )
}

export default StatisticCards
