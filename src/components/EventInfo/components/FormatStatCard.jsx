import useAxios from 'axios-hooks'
import { useState } from 'react'
import { Button, ButtonGroup, Card, Table } from 'react-bootstrap'

import { Kind } from '@/const'
import { getFormatTime } from '@/utils/date'

function FormatStatCard({ item, city, eventCities, statisticsKind }) {
  const [selectedTab, setSelectedTab] = useState('sales')

  const [{ data }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  const getCityDate = (eventCityId) => {
    const city = item && eventCities.find((city) => city.public_id === eventCityId)

    if (city) {
      return `— ${getFormatTime(city.start_time, city.timezone)}`
    } else {
      return ''
    }
  }

  const returnLimits = (limits) => {
    switch (statisticsKind) {
      case Kind.ATHLETE:
        return limits?.athlete
      case Kind.CORPORATE:
        return limits?.corporate
      case Kind.PARTNER:
        return limits?.partner
      case Kind.BRAND:
        return limits?.brand
      case Kind.SPORT:
        return limits?.sport
      case Kind.OTHER:
        return limits?.other
      default:
        return (
          (limits?.athlete || 0) +
          (limits?.corporate || 0) +
          (limits?.partner || 0) +
          (limits?.brand || 0) +
          (limits?.sport || 0) +
          (limits?.other || 0)
        )
    }
  }

  const handleClickParticipation = () => {
    setSelectedTab('participation')

    api({
      url: `/api/volunteer/tickets/statistics/${item.public_id}/${city.public_id}/${statisticsKind}`,
    })
  }

  return (
    <Card style={{ minWidth: '390px', height: '100%' }}>
      <Card.Header>
        <Card.Title>
          {city.name_ru} {getCityDate(city.public_id)}
        </Card.Title>
      </Card.Header>
      <Card.Body>
        <ButtonGroup>
          <Button
            onClick={() => setSelectedTab('sales')}
            variant={selectedTab === 'sales' ? 'primary' : 'outline-primary'}
            size="sm"
          >
            Продажи
          </Button>
          <Button
            onClick={handleClickParticipation}
            variant={selectedTab === 'participation' ? 'primary' : 'outline-primary'}
            size="sm"
          >
            Участие
          </Button>
        </ButtonGroup>
        {selectedTab === 'sales' && city.formats.length > 0 && (
          <Table>
            <thead>
              <tr>
                <th>Формат</th>
                <th>Билеты</th>
                <th>Осталось</th>
                <th>Сумма &#8381;</th>
              </tr>
            </thead>
            <tbody>
              {city.formats.map((item) => (
                <tr key={item.title}>
                  <td
                    style={{
                      backgroundColor: `${returnLimits(item?.limits) - item.tickets_count < 1 ? '#ff7070' : ''}`,
                    }}
                  >
                    {item.title}
                  </td>
                  <td
                    style={{
                      backgroundColor: `${returnLimits(item?.limits) - item.tickets_count < 1 ? '#ff7070' : ''}`,
                    }}
                  >
                    {item.tickets_count}
                  </td>
                  <td
                    style={{
                      backgroundColor: `${returnLimits(item?.limits) - item.tickets_count < 1 ? '#ff7070' : ''}`,
                    }}
                  >
                    {returnLimits(item?.limits) - item.tickets_count} / {returnLimits(item?.limits)}
                  </td>
                  <td
                    style={{
                      backgroundColor: `${returnLimits(item?.limits) - item.tickets_count < 1 ? '#ff7070' : ''}`,
                    }}
                  >
                    {Math.trunc(item.common_sum)}
                  </td>
                </tr>
              ))}
              <tr>
                <td style={{ textAlign: 'right' }}>
                  <b>Итого</b>
                </td>
                <td>{city.formats.length > 0 && city.formats.reduce((a, b) => a + b.tickets_count, 0)}</td>
                <td>
                  {city.formats.length > 0 &&
                    city.formats.reduce((a, b) => a + (returnLimits(b?.limits) - b.tickets_count), 0)}{' '}
                  / {city.formats.length > 0 && city.formats.reduce((a, b) => a + returnLimits(b?.limits), 0)}
                </td>
                <td>{city.formats.length > 0 && Math.trunc(city.formats.reduce((a, b) => a + b.common_sum, 0))}</td>
              </tr>
            </tbody>
          </Table>
        )}

        {selectedTab === 'participation' && data?.cities?.length > 0 && (
          <Table>
            <thead>
              <tr>
                <th>Формат</th>
                <th>Участников</th>
                <th>Зарегистрировано</th>
              </tr>
            </thead>
            <tbody>
              {data.cities[0].formats.map((item) => (
                <tr key={item.title}>
                  <td>{item.title}</td>
                  <td>{item.tickets_count}</td>
                  <td>{item.offline_registered}</td>
                </tr>
              ))}
              <tr>
                <td style={{ textAlign: 'right' }}>
                  <b>Итого</b>
                </td>
                <td>
                  {data.cities[0].formats.length > 0 && data.cities[0].formats.reduce((a, b) => a + b.tickets_count, 0)}
                </td>
                <td>
                  {data.cities[0].formats.length > 0 &&
                    data.cities[0].formats.reduce((a, b) => a + b.offline_registered, 0)}
                </td>
              </tr>
            </tbody>
          </Table>
        )}
      </Card.Body>
    </Card>
  )
}

export default FormatStatCard
