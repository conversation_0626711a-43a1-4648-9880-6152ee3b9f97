import useAxios from 'axios-hooks'
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap'

function DownloadButton({
  url,
  eventCityPublicId,
  fileName,
  label = 'Выгрузить .xlsx',
  params,
  data,
  disabled = false,
  className = '',
  variant = 'outline-secondary',
  icon: Icon = null,
}) {
  const [{ loading }, api] = useAxios(
    {
      url: url,
      method: 'GET',
    },
    { manual: true }
  )

  const downloadFile = () => {
    const urlApi = eventCityPublicId ? `${url}/${eventCityPublicId}?` : url
    const urlParams = params ? new URLSearchParams(params) : ''

    let config

    if (data) {
      config = {
        url: urlApi + urlParams,
        method: 'POST',
        responseType: 'blob',
        data: data,
      }
    } else {
      config = {
        url: urlApi + urlParams,
        method: 'GET',
        responseType: 'blob',
      }
    }

    api({ ...config }).then((response) => {
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
    })
  }

  return (
    <Button className={className} onClick={downloadFile} type="button" disabled={loading || disabled} variant={variant}>
      {loading && <Spinner className="me-2" animation="grow" size="sm" />}
      {!loading && (Icon ? <Icon className="me-2" /> : <i className="bi bi-download me-2" />)}
      {label}
    </Button>
  )
}

export default DownloadButton
