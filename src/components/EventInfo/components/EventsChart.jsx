import useAxios from 'axios-hooks'
import moment from 'moment'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Alert, Button, Col, FloatingLabel, FormControl, FormSelect, Row } from 'react-bootstrap'

import { useGetEventFormats } from '@/features/events/api/getEventFormats'

import DownloadStatTickets from './DownloadStatTickets/DownloadStatTickets'
import LineChart from './LineChart'
import { APIRoute } from '../../../const'
import { getFormatTime } from '../../../utils/date'
import Loader from '../../Loader/Loader'

function EventsChart({ eventPublicId, cities, kind = 'all' }) {
  const [{ loading }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )
  const [formats, setFormats] = useState([])
  const [formData, setFormData] = useState({
    event_city: {
      public_id: cities[0].public_id,
    },
    start_date: moment().subtract(1, 'month'),
    end_date: moment(),
    kind: kind,
  })
  const [datesTickets, setDatesTickets] = useState(null)
  const [toggleState, setToggleState] = useState('count')
  const [isAllCities, setIsAllCities] = useState(false)
  const [isSales, setIsSales] = useState(true)
  const [currentCityId, setCurrentCityId] = useState(cities[0].public_id)
  const { data: eventFormatsData } = useGetEventFormats({}, currentCityId)
  const formDataRef = useRef(formData)

  // Update ref when formData changes
  useEffect(() => {
    formDataRef.current = formData
  }, [formData])

  const getTickets = useCallback(
    (body, url) => {
      api({ url: url, method: 'POST', data: body }).then((r) => {
        r.status === 200 && setDatesTickets(r.data.values)
      })
    },
    [api]
  )

  const getDatesTickets = useCallback(
    (data) => {
      const body = { ...(data || formDataRef.current), kind: kind }
      const url = APIRoute.DATES_TICKETS

      getTickets(body, url)
      setIsSales(true)
    },
    [kind, getTickets]
  )

  useEffect(() => {
    if (eventFormatsData?.data?.values?.length > 0) {
      const values = eventFormatsData.data.values
      const data = {
        ...formDataRef.current,
        event_format: {
          public_id: values[0].public_id,
        },
      }
      setFormats(values)
      setFormData({ ...data })

      // Call getTickets directly to avoid dependency loop
      const body = { ...data, kind: kind }
      const url = APIRoute.DATES_TICKETS
      getTickets(body, url)
      setIsSales(true)
    }
  }, [eventFormatsData, kind, getTickets])

  const handleChangeCity = (evt) => {
    const eventCityId = evt.target.value
    const optionText = evt.target.selectedOptions[0].text

    const newFormData = { ...formData }

    delete newFormData.event_format
    setFormats([])

    if (optionText !== 'Все') {
      delete newFormData.event_public_id

      setFormData({ ...newFormData, event_city: { public_id: eventCityId } })
      setCurrentCityId(eventCityId)
    } else {
      delete newFormData.event_city

      setFormData({ ...newFormData, event_public_id: eventCityId })
      setIsAllCities(true)
      setCurrentCityId('')
    }
  }

  const handleChangeFormat = (evt) => {
    const formatId = evt.target.value
    const newFormData = { ...formData }

    if (formatId !== 'all') {
      setFormData({ ...formData, event_format: { public_id: formatId } })
    } else {
      delete newFormData.event_format
      setFormData({ ...newFormData })
    }
  }

  const getRefundDatesTickets = () => {
    const body = { ...formData, kind: kind }
    const url = APIRoute.REFUND_DATES_TICKETS

    getTickets(body, url)
    setIsSales(false)
  }

  const handleChangeDate = (evt) => {
    setFormData({ ...formData, [evt.target.name]: new Date(evt.target.value) })
  }

  const generateCityOptions = (cities) => {
    const cityOptions = []

    cities.forEach((item) => {
      cityOptions.push({
        id: item.public_id,
        address: `${item.address} — ${getFormatTime(item.start_time, item.timezone)}`,
      })
    })

    return cityOptions
  }

  return (
    <>
      <Row className="g-3 mb-5">
        <Col md={6}>
          <FloatingLabel controlId="publicIdLabelCity" label="Выбор города">
            <FormSelect
              onChange={handleChangeCity}
              value={formData?.event_city?.public_id || formData?.event_public_id}
              name="processing_public_id"
              aria-label="processing_public_id"
            >
              <option value={eventPublicId}>Все</option>
              {cities &&
                generateCityOptions(cities).map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.address}
                  </option>
                ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="publicIdLabelCity" label="Выбор формата">
            <FormSelect
              onChange={handleChangeFormat}
              value={formData?.event_format?.public_id}
              name="processing_public_id"
              aria-label="processing_public_id"
              disabled={isAllCities}
            >
              <option value="all">Все форматы</option>
              {formats.length > 0 &&
                formats.map((item) => (
                  <option value={item.public_id} key={item.public_id}>
                    {item.title}
                  </option>
                ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="quantityLabelStartTime" label="От">
            <FormControl
              onChange={handleChangeDate}
              defaultValue={moment(formData.start_date).format('YYYY-MM-DDTHH:mm:ss')}
              name="start_date"
              type="datetime-local"
              placeholder="от"
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="quantityLabelEndTime" label="До">
            <FormControl
              onChange={handleChangeDate}
              defaultValue={moment(formData.end_date).format('YYYY-MM-DDTHH:mm:ss')}
              name="end_date"
              type="datetime-local"
              placeholder="до"
            />
          </FloatingLabel>
        </Col>

        <Col md="auto">
          <Button onClick={() => getDatesTickets()} type="button" disabled={loading}>
            Продажи
          </Button>
        </Col>
        <Col md="1">
          <DownloadStatTickets data={formData} url={APIRoute.GET_EXCEL_DATES_TICKETS} prefix="sales_" />
        </Col>

        <Col md="auto">
          <Button onClick={() => getRefundDatesTickets()} type="button" disabled={loading}>
            Возвраты
          </Button>
        </Col>
        <Col md="auto">
          <DownloadStatTickets data={formData} url={APIRoute.GET_EXCEL_REFUND_DATES_TICKETS} prefix="refund_" />
        </Col>
      </Row>

      <Loader isLoading={loading}>
        {datesTickets && datesTickets.length > 0 && (
          <Row>
            <Col md={12}>
              <Button
                variant={toggleState === 'count' ? 'primary' : 'light'}
                type="button"
                onClick={() => setToggleState('count')}
              >
                Количество {isSales ? 'продаж' : 'возвратов'}
              </Button>{' '}
              <Button
                variant={toggleState === 'cost' ? 'primary' : 'light'}
                type="button"
                onClick={() => setToggleState('cost')}
              >
                Стоимость {isSales ? 'продаж' : 'возвратов'}
              </Button>
            </Col>
            <Col>
              <LineChart eventData={datesTickets} toggle={toggleState} />
            </Col>
          </Row>
        )}
      </Loader>

      {datesTickets && datesTickets.length === 0 && <Alert variant="warning">За выбранный период нет данных!</Alert>}
    </>
  )
}

export default EventsChart
