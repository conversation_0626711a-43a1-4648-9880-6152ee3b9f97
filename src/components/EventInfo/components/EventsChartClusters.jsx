import useAxios from 'axios-hooks'
import moment from 'moment'
import { useEffect, useRef, useState } from 'react'
import { Col, FloatingLabel, FormSelect, Row } from 'react-bootstrap'

import { useGetEventFormats } from '@/features/events/api/getEventFormats'

import { ChartPie } from './ChartPie'
import { APIRoute } from '../../../const'
import { getFormatTime } from '../../../utils/date'
import Loader from '../../Loader/Loader'

function EventsChartClusters({ cities, kind = 'all' }) {
  const [formats, setFormats] = useState([])
  const [formData, setFormData] = useState({
    event_city: {
      public_id: cities[0].public_id,
    },
    start_date: moment().subtract(1, 'month'),
    end_date: moment(),
    kind: kind,
  })

  const [currentCityId, setCurrentCityId] = useState(cities[0].public_id)
  const { data: eventFormatsData } = useGetEventFormats({}, currentCityId)
  const formDataRef = useRef(formData)

  // Update ref when formData changes
  useEffect(() => {
    formDataRef.current = formData
  }, [formData])

  const [statistics, apiStatistics] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  useEffect(() => {
    if (eventFormatsData?.data?.values?.length > 0) {
      const values = eventFormatsData.data.values
      const data = {
        ...formDataRef.current,
        event_format: {
          public_id: values[0].public_id,
        },
      }
      setFormats(values)
      setFormData({ ...data })
    }
  }, [eventFormatsData, kind])

  const handleChangeCity = (evt) => {
    const eventCityId = evt.target.value

    const newFormData = { ...formData }

    delete newFormData.event_format
    setFormats([])

    delete newFormData.event_public_id

    setFormData({ ...newFormData, event_city: { public_id: eventCityId } })
    setCurrentCityId(eventCityId)
  }

  useEffect(() => {
    if (formats?.length) {
      apiStatistics({ url: `${APIRoute.EVENT_CLUSTER_STATISTICS}/${formats[0]?.public_id}/${kind}` })
    }
  }, [formats, apiStatistics, kind])

  const handleChangeFormat = (evt) => {
    const formatId = evt.target.value

    apiStatistics({ url: `${APIRoute.EVENT_CLUSTER_STATISTICS}/${formatId}/${kind}` })
    setFormData({ ...formData, event_format: { public_id: formatId } })
  }

  const generateCityOptions = (cities) => {
    const cityOptions = []

    cities.forEach((item) => {
      cityOptions.push({
        id: item.public_id,
        address: `${item.address} — ${getFormatTime(item.start_time, item.timezone)}`,
      })
    })

    return cityOptions
  }

  return (
    <>
      <Row className="g-3 mb-5">
        <Row style={{ position: 'relative', zIndex: 1 }} className="g-3 mb-5">
          <Col md={6}>
            <FloatingLabel controlId="publicIdLabelCity" label="Выбор города">
              <FormSelect
                onChange={handleChangeCity}
                value={formData?.event_city?.public_id || formData?.event_public_id}
                name="processing_public_id"
                aria-label="processing_public_id"
              >
                {cities &&
                  generateCityOptions(cities).map((item) => (
                    <option value={item.id} key={item.id}>
                      {item.address}
                    </option>
                  ))}
              </FormSelect>
            </FloatingLabel>
          </Col>

          <Col md={6}>
            <FloatingLabel controlId="publicIdLabelCity" label="Выбор формата">
              <FormSelect
                onChange={handleChangeFormat}
                value={formData?.event_format?.public_id}
                name="processing_public_id"
                aria-label="processing_public_id"
              >
                {formats.length > 0 &&
                  formats.map((item) => (
                    <option value={item.public_id} key={item.public_id}>
                      {item.title}
                    </option>
                  ))}
              </FormSelect>
            </FloatingLabel>
          </Col>
        </Row>
      </Row>
      <Loader isLoading={statistics?.loading}>
        <Row>
          <Col md={12}>
            <ChartPie statistics={statistics?.data?.values} />
          </Col>
        </Row>
      </Loader>
    </>
  )
}

export default EventsChartClusters
