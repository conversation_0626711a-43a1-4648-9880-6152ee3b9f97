@import '../../../assets/styles/mixins';

.card {
  position: relative;
  padding: 12px;
  padding-right: 60px;
  min-width: 204px;

  box-shadow: none;

  &::before {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);

    display: block;
    width: 43px;
    height: 43px;

    background-color: rgba(211, 22, 30, 0.08);
    background-image: url('../../../assets/img/icons/icon-tickets.svg');
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 50%;

    content: '';
  }
}

.card2 {
  &::before {
    background-color: rgba(57, 157, 251, 0.08);
    background-image: url('../../../assets/img/icons/icon-money-calculator.svg');
  }
}

.card3 {
  &::before {
    background-color: rgba(100, 109, 143, 0.08);
    background-image: url('../../../assets/img/icons/icon-user-data.svg');
  }
}

.card4 {
  &::before {
    background-color: rgba(34, 176, 125, 0.08);
    background-image: url('../../../assets/img/icons/icon-shield-plus.svg');
  }
}

.card5 {
  &::before {
    background-color: rgba(181, 72, 198, 0.08);
    background-image: url('../../../assets/img/icons/icon-user-check.svg');
  }
}

.cardPercents {
  .percents {
    display: none;
  }

  &:hover {
    .counter {
      display: none;
    }

    .percents {
      display: block;
    }
  }
}

.chart {
  width: 2000px;
  height: 2000px;
}
