import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from 'chart.js'
import { useEffect, useState } from 'react'
import { Pie } from 'react-chartjs-2'

ChartJS.register(ArcElement, Tooltip, Legend)

export function ChartPie({ statistics }) {
  const [labels, setLabels] = useState([])
  const [dataValues, setDataValues] = useState([])
  const [percentages, setPercentages] = useState([])

  useEffect(() => {
    const newLabels = []
    const newDataValues = []
    const newPercentages = []

    if (statistics?.length) {
      statistics
        .sort((a, b) => b.count - a.count)
        .forEach((el) => {
          newLabels.push(`${el?.title}, ${el?.count}, ${el?.percent}%`)
          newDataValues.push(el?.count)
          newPercentages.push(el?.percent)
        })
    }

    setLabels(newLabels)
    setDataValues(newDataValues)
    setPercentages(newPercentages)
  }, [statistics])

  const data = {
    labels: labels,
    percentages: percentages,
    datasets: [
      {
        label: 'Количество',
        data: dataValues,
        backgroundColor: [
          'rgba(255, 99, 132, 0.2)',
          'rgba(54, 162, 235, 0.2)',
          'rgba(255, 206, 86, 0.2)',
          'rgba(75, 192, 192, 0.2)',
          'rgba(153, 102, 255, 0.2)',
          'rgba(255, 159, 64, 0.2)',
          'rgba(12, 122, 64, 0.2)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
          'rgba(12, 122, 64, 1)',
        ],
        borderWidth: 2,
        hoverOffset: 1,
      },
    ],
  }

  const options = {
    plugins: {
      legend: { position: 'right' },
      tooltip: {
        callbacks: {
          label: function (tooltipItem) {
            const label = data.labels[tooltipItem.dataIndex]
            const value = tooltipItem.raw
            const percentage = data.percentages[tooltipItem.dataIndex]
            return [`${label}`, `Количество: ${value} (${percentage}%)`]
          },
        },
      },
    },
    aspectRatio: 2 / 1,
  }

  return (
    <div>
      <Pie data={data} style={{ margin: 0 }} options={options} />
    </div>
  )
}
