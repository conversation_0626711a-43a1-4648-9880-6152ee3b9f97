import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js'
import { useEffect, useState } from 'react'
import { Line } from 'react-chartjs-2'

import styles from './StatisticCards.module.scss'

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Filler, Legend)

const colors = {
  borderColor: '#C8ECCC',
  backgroundColor: '#F0FAF1',
  pointBorderColor: '#AAA',
  pointBackgroundColor: '#FEF1F1',
}

const LineChart = ({ eventData, toggle }) => {
  const [data, setData] = useState({
    datasets: [],
  })
  const [maxTicks, setMaxTicks] = useState(0)
  const [stepSize, setStepSize] = useState(1)

  useEffect(() => {
    const labels = eventData.map((item) => item._id)

    if (eventData.length > 0 && toggle === 'count') {
      const dataCount = eventData.map((item) => item.inday_count)
      const maxCount = Math.round(Math.max(...dataCount) * 1.5)

      setData({
        labels: labels,
        datasets: [
          {
            label: 'Количество',
            data: dataCount,
            ...colors,
            fill: true,
          },
        ],
      })
      setMaxTicks(maxCount)
      if (maxCount >= 10) setStepSize(Math.floor(maxCount / 10))
      else setStepSize(1)
    } else if (eventData.length > 0 && toggle === 'cost') {
      const dataCount = eventData.map((item) => item.inday_cost)
      const maxCount = Math.round(Math.max(...dataCount) * 1.5)

      setData({
        labels: labels,
        datasets: [
          {
            label: 'Стоимость',
            data: dataCount,
            ...colors,
            fill: true,
          },
        ],
      })
      setMaxTicks(maxCount)

      if (maxCount >= 10) setStepSize(Math.floor(maxCount / 10))
      else setStepSize(1)
    }
  }, [eventData, toggle])

  const options = {
    scales: {
      y: {
        max: maxTicks,
        beginAtZero: true,
      },
    },
    ticks: { stepSize },
  }

  if (eventData.length === 0) return null

  return <Line className={styles.chart} data={data} options={options} />
}

export default LineChart
