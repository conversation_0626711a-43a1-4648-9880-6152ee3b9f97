import { useState, useMemo } from 'react'
import { Al<PERSON>, Button, Card, Col, Form, Row, Spinner, Table, Badge, Collapse } from 'react-bootstrap'
import { useQueries } from 'react-query'

import { APIRoute } from '@/const'
import { useDownloadUniqueUsersByCitiesXlsx } from '@/features/events/api/downloadUniqueUsersByCitiesXlsx'
import { useGetEventArchive } from '@/features/events/api/getEventArchive'
import { useGetUniqueUsersByCities } from '@/features/events/api/getUniqueUsersByCities'
import { axios } from '@/lib/axios'

/**
 * Компонент для сравнения участников по городам из архивных событий
 *
 * Функциональность:
 * - Загружает архивные события, отфильтрованные по типу
 * - Позволяет выбрать несколько событий через селект
 * - Отображает события в сворачиваемых карточках с кнопками удаления
 * - Группирует города по событиям в сворачиваемых списках
 * - Фильтрация городов по тексту (название, адрес, дата)
 * - Получает статистику уникальных пользователей по выбранным городам
 * - Позволяет скачать список неучаствовавших пользователей
 *
 * UX особенности:
 * - Карточки событий можно сворачивать/разворачивать кликом на заголовок
 * - Кнопка удаления события находится в заголовке карточки
 * - Иконки chevron показывают состояние сворачивания
 * - Поиск по городам с показом количества найденных результатов
 * - Быстрая очистка поискового запроса
 *
 * @param {Object} props - Пропсы компонента
 * @param {string} props.eventType - ID типа события для фильтрации
 * @returns {JSX.Element} Компонент сравнения городов
 */
function TabCitiesComparison({ eventType }) {
  const [selectedEvents, setSelectedEvents] = useState([])
  const [selectedCities, setSelectedCities] = useState([])
  const [uniqueUsersStats, setUniqueUsersStats] = useState([])
  const [selectedTargetCity, setSelectedTargetCity] = useState('')
  const [collapsedEvents, setCollapsedEvents] = useState({})
  const [citySearchText, setCitySearchText] = useState('')

  // Запрос на получение архивных событий с фильтрацией по типу события
  const { data: eventsResponse, isLoading: isLoadingEvents } = useGetEventArchive({
    event_type_id: eventType,
  })

  // Фильтрация событий по типу (дополнительная проверка на клиенте)
  const filteredEvents = useMemo(() => {
    const archiveEvents = eventsResponse?.data?.values || []
    const filtered = archiveEvents?.filter((event) => event.event_type.public_id === eventType) || []
    return [...filtered].reverse()
  }, [eventsResponse?.data?.values, eventType])

  // Получаем города для всех выбранных событий с помощью useQueries
  const citiesQueries = useQueries(
    selectedEvents.map((eventId) => ({
      queryKey: ['eventCities', eventId],
      queryFn: () => axios.get(`${APIRoute.GET_EVENT_CITIES}/${eventId}`),
      enabled: !!eventId,
      staleTime: 5 * 60 * 1000,
    }))
  )

  // Объединяем все города с информацией о событии
  const allCitiesWithEvents = useMemo(() => {
    const result = []
    citiesQueries.forEach((query, index) => {
      const eventId = selectedEvents[index]
      if (query.data?.data?.values) {
        const event = filteredEvents.find((e) => e.public_id === eventId)
        query.data.data.values.forEach((city) => {
          result.push({
            ...city,
            eventId,
            eventTitle: event?.title || eventId,
          })
        })
      }
    })
    return result
  }, [citiesQueries, selectedEvents, filteredEvents])

  // Группируем города по событиям с фильтрацией по поисковому тексту
  const citiesByEvents = useMemo(() => {
    const grouped = {}
    allCitiesWithEvents.forEach((city) => {
      // Фильтрация по поисковому тексту
      const searchLower = citySearchText.toLowerCase()
      const cityName = city.city?.name_ru?.toLowerCase() || ''
      const cityAddress = city.address?.toLowerCase() || ''
      const cityDate = new Date(city.start_time).toLocaleDateString()

      const matchesSearch =
        !citySearchText ||
        cityName.includes(searchLower) ||
        cityAddress.includes(searchLower) ||
        cityDate.includes(searchLower)

      if (matchesSearch) {
        if (!grouped[city.eventId]) {
          grouped[city.eventId] = {
            event: filteredEvents.find((e) => e.public_id === city.eventId),
            cities: [],
          }
        }
        grouped[city.eventId].cities.push(city)
      }
    })
    return grouped
  }, [allCitiesWithEvents, filteredEvents, citySearchText])

  // Мутация для получения статистики уникальных пользователей по городам
  const { mutate: fetchUniqueUsersStats, isLoading: isLoadingStats } = useGetUniqueUsersByCities()

  // Мутация для скачивания XLSX
  const { mutate: downloadXlsx, isLoading: isDownloading } = useDownloadUniqueUsersByCitiesXlsx()

  // Обработчик выбора события
  const handleEventChange = (eventId) => {
    if (eventId && !selectedEvents.includes(eventId)) {
      setSelectedEvents((prev) => [...prev, eventId])
      // Сбрасываем статистику при изменении событий
      setUniqueUsersStats([])
      setSelectedTargetCity('')
    }
  }

  // Обработчик удаления события
  const handleRemoveEvent = (eventId) => {
    setSelectedEvents((prev) => prev.filter((id) => id !== eventId))
    // Удаляем выбранные города для этого события
    setSelectedCities((prev) =>
      prev.filter((cityId) => {
        const city = allCitiesWithEvents.find((c) => c.public_id === cityId)
        return city?.eventId !== eventId
      })
    )
    // Убираем событие из состояния сворачивания
    setCollapsedEvents((prev) => {
      const { [eventId]: _, ...rest } = prev
      return rest
    })
    // Сбрасываем статистику
    setUniqueUsersStats([])
    setSelectedTargetCity('')
  }

  // Обработчик переключения сворачивания события
  const handleToggleCollapse = (eventId) => {
    setCollapsedEvents((prev) => ({
      ...prev,
      [eventId]: !prev[eventId],
    }))
  }

  // Обработчик изменения выбранных городов
  const handleCityToggle = (cityId) => {
    setSelectedCities((prev) => {
      const isSelected = prev.includes(cityId)

      if (isSelected) {
        return prev.filter((id) => id !== cityId)
      } else {
        return [...prev, cityId]
      }
    })
  }

  // Обработчик изменения целевого города
  const handleTargetCityChange = (cityId) => {
    setSelectedTargetCity(cityId)
  }

  // Обработчик запроса статистики
  const handleRequestStatistics = () => {
    if (selectedCities.length > 0) {
      setUniqueUsersStats([]) // Очищаем предыдущие результаты
      fetchUniqueUsersStats(
        { event_city_id_list: selectedCities },
        {
          onSuccess: (response) => {
            setUniqueUsersStats(response?.data || [])
          },
          onError: (error) => {
            console.error('Ошибка при получении статистики:', error)
            setUniqueUsersStats([])
          },
        }
      )
    }
  }

  // Обработчик скачивания файла
  const handleDownload = () => {
    if (selectedCities.length > 0 && selectedTargetCity) {
      downloadXlsx({
        event_city_id_list: selectedCities,
        target_event_city_id: selectedTargetCity,
      })
    }
  }

  return (
    <div>
      <Card className="mb-4">
        <Card.Header>
          <h5>Сравнение участников по городам</h5>
        </Card.Header>
        <Card.Body>
          {isLoadingEvents ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
            </div>
          ) : filteredEvents?.length === 0 ? (
            <Alert variant="info">Архивные события не найдены для данного типа события</Alert>
          ) : (
            <>
              <Row className="mb-4">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label>Добавить событие:</Form.Label>
                    <Form.Select onChange={(e) => handleEventChange(e.target.value)} value="">
                      <option value="">Выберите событие</option>
                      {filteredEvents
                        .filter((event) => !selectedEvents.includes(event.public_id))
                        .map((event) => (
                          <option key={event.public_id} value={event.public_id}>
                            {event.title} - {event.public_id}
                          </option>
                        ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                {selectedEvents.length > 0 && (
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>Поиск по городам:</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Поиск по названию города, адресу или дате..."
                        value={citySearchText}
                        onChange={(e) => setCitySearchText(e.target.value)}
                      />
                      {citySearchText && (
                        <Form.Text className="text-muted">
                          <i className="bi bi-search me-1"></i>
                          &quot;{citySearchText}&quot;
                          <Button
                            variant="link"
                            size="sm"
                            className="p-0 ms-2 text-decoration-none"
                            onClick={() => setCitySearchText('')}
                          >
                            Очистить
                          </Button>
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                )}
              </Row>

              {/* Отображение городов, сгруппированных по событиям */}
              {Object.keys(citiesByEvents).length > 0 && (
                <>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <span>Выберите города для сравнения:</span>
                    {citySearchText && (
                      <small className="text-muted">
                        Найдено городов:{' '}
                        {Object.values(citiesByEvents).reduce((total, { cities }) => total + cities.length, 0)}
                      </small>
                    )}
                  </div>
                  {Object.entries(citiesByEvents).map(([eventId, { event, cities }]) => {
                    const isCollapsed = collapsedEvents[eventId]
                    return (
                      <Card key={eventId} className="mb-3">
                        <Card.Header className="py-2">
                          <div className="d-flex justify-content-between align-items-center">
                            <h6
                              className="mb-0 flex-grow-1 cursor-pointer"
                              onClick={() => handleToggleCollapse(eventId)}
                              style={{ cursor: 'pointer' }}
                            >
                              <i className={`bi ${isCollapsed ? 'bi-chevron-right' : 'bi-chevron-down'} me-2`}></i>
                              {event?.title} — {event?.public_id}
                            </h6>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleRemoveEvent(eventId)}
                              title="Удалить событие"
                            >
                              ×
                            </Button>
                          </div>
                        </Card.Header>
                        <Collapse in={!isCollapsed}>
                          <Card.Body className="py-3">
                            <Row>
                              {cities.map((city) => (
                                <Col lg={4} md={6} key={city.public_id} className="mb-2">
                                  <Form.Check
                                    type="checkbox"
                                    id={`city-${city.public_id}`}
                                    label={`${city.city.name_ru} - ${new Date(city.start_time).toLocaleDateString()}`}
                                    checked={selectedCities.includes(city.public_id)}
                                    onChange={() => handleCityToggle(city.public_id)}
                                  />
                                </Col>
                              ))}
                            </Row>
                          </Card.Body>
                        </Collapse>
                      </Card>
                    )
                  })}
                </>
              )}

              {selectedEvents.length > 0 && Object.keys(citiesByEvents).length === 0 && (
                <Alert variant="info">
                  {citySearchText
                    ? `По запросу "${citySearchText}" городов не найдено. Попробуйте изменить поисковый запрос.`
                    : 'У выбранных событий нет доступных городов'}
                </Alert>
              )}

              {selectedCities.length > 0 && (
                <div className="text-center mb-4">
                  <Button variant="primary" onClick={handleRequestStatistics} disabled={isLoadingStats}>
                    {isLoadingStats ? (
                      <>
                        <Spinner as="span" animation="border" size="sm" className="me-2" />
                        Получение статистики...
                      </>
                    ) : (
                      'Получить статистику'
                    )}
                  </Button>
                </div>
              )}

              {uniqueUsersStats.length > 0 && (
                <>
                  <Table hover responsive className="mt-3">
                    <thead>
                      <tr>
                        <th>Город</th>
                        <th>Событие</th>
                        <th>Количество уникальных участников</th>
                        <th>Выбрать город</th>
                      </tr>
                    </thead>
                    <tbody>
                      {uniqueUsersStats.map((stat) => {
                        const statCity = allCitiesWithEvents.find((c) => c.public_id === stat.event_city)
                        const cityName = statCity?.city?.name_ru || statCity?.address || stat.event_city
                        const eventTitle = statCity?.eventTitle || 'Неизвестное событие'
                        return (
                          <tr key={stat.event_city} onClick={() => handleTargetCityChange(stat.event_city)}>
                            <td>{`${cityName} (${stat.event_city})`}</td>
                            <td>{eventTitle}</td>
                            <td>{stat.count}</td>
                            <td>
                              <Form.Check
                                type="radio"
                                name="targetCity"
                                id={`target-${stat.event_city}`}
                                checked={selectedTargetCity === stat.event_city}
                                onChange={() => handleTargetCityChange(stat.event_city)}
                              />
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </Table>

                  <div className="mt-3 text-center">
                    <Button variant="success" onClick={handleDownload} disabled={!selectedTargetCity || isDownloading}>
                      {isDownloading ? (
                        <>
                          <Spinner as="span" animation="border" size="sm" className="me-2" />
                          Загрузка...
                        </>
                      ) : (
                        'Скачать список неучаствовавших'
                      )}
                    </Button>
                  </div>
                </>
              )}

              {selectedEvents.length === 0 && <Alert variant="info">Добавьте события для начала работы</Alert>}

              {selectedEvents.length > 0 && selectedCities.length === 0 && Object.keys(citiesByEvents).length > 0 && (
                <Alert variant="info">Выберите хотя бы один город для сравнения</Alert>
              )}

              {uniqueUsersStats.length === 0 && !isLoadingStats && selectedCities.length > 0 && (
                <Alert variant="info">Для получения статистики нажмите кнопку &quot;Получить статистику&quot;</Alert>
              )}
            </>
          )}
        </Card.Body>
      </Card>
    </div>
  )
}

export default TabCitiesComparison
