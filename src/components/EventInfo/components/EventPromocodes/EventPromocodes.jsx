import { useState } from 'react'
import { Card, CardBody, Table } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import PageSearch from '@/components/PageSearch/PageSearch'

import { getDiscountForm } from '@/utils/common'
import { unixToMoment } from '@/utils/date'

import styles from './EventPromocodes.module.scss'

function SortButton({ sortConfig, onClick, sortKey, children }) {
  return (
    <button
      className={`${styles.sortBtn} ${
        sortConfig.key === sortKey
          ? sortConfig.direction === 'ascending'
            ? styles.sortBtnAsc
            : styles.sortBtnDesc
          : styles.sortBtnDefault
      }`}
      onClick={() => onClick(sortKey)}
      type="button"
    >
      {children}
    </button>
  )
}

const initialSortConfig = { key: null, direction: 'ascending' }

function EventPromocodes({ promocodes = [] }) {
  const [sortedPromocodes, setSortedPromocodes] = useState(promocodes)
  const [sortConfig, setSortConfig] = useState(initialSortConfig)

  const sortData = (key) => {
    let direction = 'ascending'
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending'
    }
    const sortedData = [...sortedPromocodes].sort((a, b) => {
      if (key === 'created_date' || key === 'using_count' || key === 'discount') {
        return direction === 'ascending' ? a[key] - b[key] : b[key] - a[key]
      }
      if (a[key] < b[key]) {
        return direction === 'ascending' ? -1 : 1
      }
      if (a[key] > b[key]) {
        return direction === 'ascending' ? 1 : -1
      }
      return 0
    })
    setSortedPromocodes(sortedData)
    setSortConfig({ key, direction })
  }

  const handleSetSearchedPromocodes = (values) => {
    setSortedPromocodes(values)
    setSortConfig(initialSortConfig)
  }

  if (promocodes?.length < 1) return null

  return (
    <>
      <div className="mb-3">
        <PageSearch values={promocodes} setValues={handleSetSearchedPromocodes} />
      </div>

      <Card>
        <CardBody>
          <Table>
            <thead>
              <tr>
                <th>
                  <SortButton sortConfig={sortConfig} sortKey="promocode" onClick={sortData}>
                    Код ({sortedPromocodes.length})
                  </SortButton>
                </th>
                <th>
                  <SortButton sortConfig={sortConfig} sortKey="using_count" onClick={sortData}>
                    Использований
                  </SortButton>
                </th>
                <th>
                  <SortButton sortConfig={sortConfig} sortKey="discount" onClick={sortData}>
                    Скидка
                  </SortButton>
                </th>
                <th>
                  <SortButton sortConfig={sortConfig} sortKey="description" onClick={sortData}>
                    Описание
                  </SortButton>
                </th>
                <th>
                  <SortButton sortConfig={sortConfig} sortKey="created_date" onClick={sortData}>
                    Дата создания
                  </SortButton>
                </th>
                <th>
                  <SortButton sortConfig={sortConfig} sortKey="user_public_id" onClick={sortData}>
                    Юзер
                  </SortButton>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedPromocodes.map((item) => (
                <tr key={item.promocode}>
                  <td>{item.promocode}</td>
                  <td>{item.using_count}</td>
                  <td>{getDiscountForm(item.discount, item.discount_form)}</td>
                  <td>{item.description}</td>
                  <td>{unixToMoment(item.created_date).format('DD.MM.YYYY')}</td>
                  <td>
                    <Link to={`/user/${item.user_public_id}`}>{item.user_public_id}</Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </CardBody>
      </Card>
    </>
  )
}

export default EventPromocodes
