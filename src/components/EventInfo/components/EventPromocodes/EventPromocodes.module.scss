.sortBtn {
  position: relative;
  padding: 0;
  font-weight: 500;

  background: none;
  border: none;

  &::after {
    position: absolute;
    top: 55%;
    right: -10px;
    transform: translateY(-50%);

    width: 10px;
    height: 10px;

    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;

    content: '';
  }
}

.sortBtnDefault {
  &::after {
    right: -5px;

    width: 9px;
    height: 9px;

    background-image: url('../../../../assets/img/icons/icon-sort.svg');
  }
}

.sortBtnAsc {
  &::after {
    background-image: url('../../../../assets/img/icons/icon-ascending.svg');
  }
}

.sortBtnDesc {
  &::after {
    background-image: url('../../../../assets/img/icons/icon-descending.svg');
  }
}
