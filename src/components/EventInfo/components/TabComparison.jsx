import { useState, useMemo } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Row, Spinner, Table, Nav, Tab } from 'react-bootstrap'

import { useDownloadUniqueUsersXlsx } from '@/features/events/api/downloadUniqueUsersXlsx'
import { useGetEventArchive } from '@/features/events/api/getEventArchive'
import { useGetEvents } from '@/features/events/api/getEvents'
import { useGetUniqueUsersStatistics } from '@/features/events/api/getUniqueUsersStatistics'

import TabCitiesComparison from './TabCitiesComparison'

function TabComparison({ eventType }) {
  const [selectedEvents, setSelectedEvents] = useState([])
  const [uniqueUsersStats, setUniqueUsersStats] = useState([])
  const [selectedTargetEvent, setSelectedTargetEvent] = useState('')

  // Запрос на получение архивных событий и активных событий
  const { data: eventsResponse, isLoading: isLoadingEvents } = useGetEventArchive()
  const { data: activeEventsResponse, isLoading: isLoadingActiveEvents } = useGetEvents()

  const archiveEvents = eventsResponse?.data?.values || []
  const activeEvents = activeEventsResponse?.data?.values || []

  // Объединение и дедупликация событий
  const allEvents = [...(archiveEvents || []), ...(activeEvents || [])].reduce((acc, current) => {
    const x = acc.find((item) => item.public_id === current.public_id)
    if (!x) {
      return acc.concat([current])
    } else {
      return acc
    }
  }, [])

  // Фильтрация событий по типу
  const filteredEvents = useMemo(() => {
    const filtered = allEvents?.filter((event) => event.event_type.public_id === eventType) || []
    return [...filtered].reverse()
  }, [allEvents, eventType])

  // Мутация для получения статистики уникальных пользователей
  const { mutate: fetchUniqueUsersStats, isLoading: isLoadingStats } = useGetUniqueUsersStatistics()

  // Мутация для скачивания XLSX
  const { mutate: downloadXlsx, isLoading: isDownloading } = useDownloadUniqueUsersXlsx()

  // Обработчики изменения выбранных событий
  const handleEventToggle = (eventId) => {
    setSelectedEvents((prev) => {
      const isSelected = prev.includes(eventId)

      if (isSelected) {
        return prev.filter((id) => id !== eventId)
      } else {
        return [...prev, eventId]
      }
    })
  }

  // Обработчик изменения целевого события
  const handleTargetEventChange = (eventId) => {
    setSelectedTargetEvent(eventId)
  }

  // Обработчик запроса статистики
  const handleRequestStatistics = () => {
    if (selectedEvents.length > 0) {
      fetchUniqueUsersStats(
        { event_public_id_list: selectedEvents },
        {
          onSuccess: (response) => {
            setUniqueUsersStats(response?.data || [])
          },
        }
      )
    }
  }

  // Обработчик скачивания файла
  const handleDownload = () => {
    if (selectedEvents.length > 0) {
      downloadXlsx({
        event_public_id_list: selectedEvents,
        target_event_id: selectedTargetEvent,
      })
    }
  }

  const isLoading = isLoadingEvents || isLoadingActiveEvents

  return (
    <div>
      <Card className="mb-4">
        <Card.Header>
          <h5>Сравнение участников</h5>
        </Card.Header>
        <Card.Body>
          <Tab.Container defaultActiveKey="events">
            <Nav variant="tabs" className="mb-3">
              <Nav.Item>
                <Nav.Link eventKey="events">По событиям</Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link eventKey="cities">По городам</Nav.Link>
              </Nav.Item>
            </Nav>

            <Tab.Content>
              <Tab.Pane eventKey="events">
                {isLoading ? (
                  <div className="text-center py-4">
                    <Spinner animation="border" variant="primary" />
                  </div>
                ) : filteredEvents?.length === 0 ? (
                  <Alert variant="info">События не найдены или не удалось определить тип текущего события</Alert>
                ) : (
                  <>
                    <p>Выберите события для сравнения:</p>
                    <Row className="mb-4">
                      {filteredEvents.map((event) => (
                        <Col lg={4} md={6} key={event.public_id} className="mb-2">
                          <Form.Check
                            type="checkbox"
                            id={`event-${event.public_id}`}
                            label={`${event.title} - ${event.public_id}`}
                            checked={selectedEvents.includes(event.public_id)}
                            onChange={() => handleEventToggle(event.public_id)}
                          />
                        </Col>
                      ))}
                    </Row>

                    <div className="text-center mb-4">
                      <Button
                        variant="primary"
                        onClick={handleRequestStatistics}
                        disabled={selectedEvents.length === 0 || isLoadingStats}
                      >
                        {isLoadingStats ? (
                          <>
                            <Spinner as="span" animation="border" size="sm" className="me-2" />
                            Получение статистики...
                          </>
                        ) : (
                          'Получить статистику'
                        )}
                      </Button>
                    </div>

                    {uniqueUsersStats.length > 0 && (
                      <>
                        <Table hover responsive className="mt-3">
                          <thead>
                            <tr>
                              <th>Событие</th>
                              <th>Количество уникальных участников</th>
                              <th>Выбрать событие</th>
                            </tr>
                          </thead>
                          <tbody>
                            {uniqueUsersStats.map((stat) => {
                              const statEvent = filteredEvents.find((e) => e.public_id === stat.event) || stat.event
                              return (
                                <tr key={stat.event} onClick={() => handleTargetEventChange(stat.event)}>
                                  <td>{`${statEvent.title} (${statEvent.public_id})`}</td>
                                  <td>{stat.count}</td>
                                  <td>
                                    <Form.Check
                                      type="radio"
                                      name="targetEvent"
                                      id={`target-${stat.event}`}
                                      checked={selectedTargetEvent === stat.event}
                                      onChange={() => handleTargetEventChange(stat.event)}
                                    />
                                  </td>
                                </tr>
                              )
                            })}
                          </tbody>
                        </Table>

                        <div className="mt-3 text-center">
                          <Button
                            variant="success"
                            onClick={handleDownload}
                            disabled={!selectedTargetEvent || isDownloading}
                          >
                            {isDownloading ? (
                              <>
                                <Spinner as="span" animation="border" size="sm" className="me-2" />
                                Загрузка...
                              </>
                            ) : (
                              'Скачать список неучаствовавших'
                            )}
                          </Button>
                        </div>
                      </>
                    )}

                    {uniqueUsersStats.length === 0 && !isLoadingStats && selectedEvents.length > 0 && (
                      <Alert variant="info">
                        Для получения статистики нажмите кнопку &quot;Получить статистику&quot;
                      </Alert>
                    )}

                    {selectedEvents.length === 0 && (
                      <Alert variant="info">Выберите хотя бы одно событие для сравнения</Alert>
                    )}
                  </>
                )}
              </Tab.Pane>

              <Tab.Pane eventKey="cities">
                <TabCitiesComparison eventType={eventType} />
              </Tab.Pane>
            </Tab.Content>
          </Tab.Container>
        </Card.Body>
      </Card>
    </div>
  )
}

export default TabComparison
