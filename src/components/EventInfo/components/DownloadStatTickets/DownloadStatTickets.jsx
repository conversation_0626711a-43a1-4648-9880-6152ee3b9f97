import useAxios from 'axios-hooks'
import fileDownload from 'js-file-download'
import { But<PERSON>, Spinner } from 'react-bootstrap'

function DownloadStatTickets({ data, url, prefix }) {
  const [{ loading }, api] = useAxios(
    {
      method: 'POST',
    },
    { manual: true }
  )

  const downloadFile = () => {
    const body = data

    delete data.event_format

    api({
      url: url,
      data: body,
      responseType: 'blob',
    }).then((response) => {
      fileDownload(response.data, `${prefix}${data?.event_city?.public_id || data.event_public_id}.xlsx`)
    })
  }

  return (
    <Button onClick={downloadFile} type="button" variant="success" disabled={loading}>
      {loading ? <Spinner animation="grow" size="sm" /> : <i className="bi bi-download" />}
    </Button>
  )
}

export default DownloadStatTickets
