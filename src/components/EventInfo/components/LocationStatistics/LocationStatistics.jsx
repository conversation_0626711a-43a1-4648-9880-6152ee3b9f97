import useAxios from 'axios-hooks'
import { useState } from 'react'
import TreeView, { flattenTree } from 'react-accessible-treeview'
import { Badge, Col, Floating<PERSON>abel, FormSelect, Row, Card } from 'react-bootstrap'

import Loader from '@/components/Loader/Loader'

import { APIRoute } from '@/const'
import { getFormatTime } from '@/utils/date'

import styles from './LocationStatistics.module.scss'

function LocationStatistics({ cities }) {
  const [selectedCity, setSelectedCity] = useState('')
  const [locationsTree, setLocationsTree] = useState({})

  const [{ loading }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  const handleChangeCity = (evt) => {
    const city = evt.target.value

    setSelectedCity(city)

    if (city.length > 0) {
      api({ url: `${APIRoute.EVENT_LOCATION_STATISTICS}/${city}` }).then((res) => {
        if (res?.data?.values?.length > 0) {
          setLocationsTree(generateLocationsTree(res.data?.values))
        } else {
          setLocationsTree({})
        }
      })
    }
  }

  const generateCityOptions = (cities) => {
    const cityOptions = []

    cities.forEach((item) => {
      cityOptions.push({
        id: item.public_id,
        address: `${item.address} — ${getFormatTime(item.start_time, item.timezone)}`,
      })
    })

    return cityOptions
  }

  const generateLocationsTree = (locations) => {
    locations.sort((a, b) => b.stat - a.stat)

    locations.forEach((region) => {
      region.city_list.sort((a, b) => b.stat - a.stat)
    })

    const tree = {
      name: '',
      children: locations.map((item) => ({
        name: item.name_ru,
        metadata: {
          stat: item.stat,
        },
        children: item.city_list.map((city) => ({
          name: `${city.name_ru}: `,
          metadata: {
            stat: city.stat,
          },
        })),
      })),
    }

    return tree
  }

  const dataLocations = flattenTree(locationsTree)

  return (
    <Card className="mb-4">
      <Card.Body className={styles.cardBody}>
        <Row className="mb-4">
          <Col md={6} className="mx-auto">
            <FloatingLabel controlId="publicIdLabelCity" label="Выбор города">
              <FormSelect
                onChange={handleChangeCity}
                name="processing_public_id"
                aria-label="processing_public_id"
                className={styles.citySelect}
              >
                <option value="">Выберите город</option>
                {cities &&
                  generateCityOptions(cities).map((item) => (
                    <option value={item.id} key={item.id}>
                      {item.address}
                    </option>
                  ))}
              </FormSelect>
            </FloatingLabel>
          </Col>
        </Row>

        <Loader isLoading={loading}>
          {Object.keys(locationsTree).length > 0 ? (
            <Row>
              <Col md={6} className="mx-auto">
                <div className={`${styles.treeWrap} bg-body border rounded`}>
                  <TreeView
                    className={styles.basic}
                    data={dataLocations}
                    nodeRenderer={({
                      element,
                      isBranch,
                      isExpanded,
                      isDisabled,
                      getNodeProps,
                      level,
                      handleExpand,
                    }) => {
                      return (
                        <div
                          {...getNodeProps({ onClick: handleExpand })}
                          style={{
                            marginLeft: 40 * (level - 1),
                            opacity: isDisabled ? 0.5 : 1,
                          }}
                        >
                          <div
                            className={`${styles.item} bg-body text-body border-start border-3 border-secondary shadow-sm`}
                          >
                            {isBranch && (
                              <span className={`${styles.itemArrow} text-muted`}>{isExpanded ? '▼ ' : '► '}</span>
                            )}
                            <span className="name">
                              {element.name}{' '}
                              {Object.prototype.hasOwnProperty.call(element?.metadata, 'stat') && (
                                <Badge bg="primary" pill className="ms-2">
                                  {element.metadata.stat}
                                </Badge>
                              )}
                            </span>
                          </div>
                        </div>
                      )
                    }}
                  />
                </div>
              </Col>
            </Row>
          ) : (
            selectedCity !== '' && (
              <Row>
                <Col md={6} className="mx-auto">
                  <div className={`${styles.noData} bg-body-secondary text-body-secondary border rounded`}>
                    Нет данных для отображения
                  </div>
                </Col>
              </Row>
            )
          )}
        </Loader>
      </Card.Body>
    </Card>
  )
}

export default LocationStatistics
