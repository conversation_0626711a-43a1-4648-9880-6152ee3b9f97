.basic {
  list-style: none;
  margin: 0;
  padding-left: 0;
}

.basic :global .tree-node,
.basic :global .tree-node-group {
  list-style: none;
  margin: 0;
  padding: 0;
}

.basic :global .tree-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.basic :global .tree-branch-wrapper {
  margin-bottom: 10px;
}

.cardBody {
  padding: 1.5rem;
}

.treeWrap {
  padding: 15px;
  margin: 0 auto;
}

.item {
  margin-bottom: 6px;
  padding: 10px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;

  &:hover {
    transform: translateX(2px);
    border-left-color: var(--bs-primary) !important;
  }
}

.itemArrow {
  display: inline-block;
  min-width: 20px;
  font-size: 10px;
}

.citySelect {
  transition: all 0.2s ease;

  &:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
  }
}

.noData {
  padding: 20px;
  text-align: center;
  border-radius: 6px;
  font-style: italic;
}
