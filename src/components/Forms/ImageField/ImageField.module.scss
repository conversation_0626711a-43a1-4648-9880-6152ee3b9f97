.image {
  margin: 0;
  border-radius: 4px;
}

.fileButton {
  margin: 0 auto;
  padding: 12px;
  display: grid;
  justify-content: center;
  align-content: end;
  max-width: 325px;
  width: 100%;
  min-height: 100px;
  background-color: #f5f7f9;
  background-image: url('../../../assets/img/icons/icon-photo-camera.svg');
  background-repeat: no-repeat;
  background-position: center 10px;
  background-size: 50px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: darken(#ffffff, 4);
  }
}

.addedImage {
  padding: 6px;
  min-height: 0;
  background-position: 16.5% center;
  background-size: auto;
}

.fieldTitle {
  font-size: 18px;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 12px;
  text-align: center;
  position: relative;
  padding-bottom: 10px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(to right, #4776e6, #8e54e9);
    border-radius: 3px;
  }
}

.imageFieldContainer {
  margin-bottom: 1rem;
  width: 100%;
}

.imagePreviewContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.imagePreview {
  position: relative;
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  min-width: 150px;
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }
}

.previewImage {
  max-width: 100%;
  height: auto;
  max-height: 250px;
  object-fit: contain;
  margin: 0;
  border-radius: 12px;
  min-width: 100px;
  min-height: 80px;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 5;

  &.visible {
    opacity: 1;
  }
}

.imageActions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 10px;
  width: 100%;
  max-width: 200px;
  min-width: 120px;
}

.deleteButton {
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 4px 10px rgba(255, 65, 108, 0.3);
  font-size: 14px;
}

.replaceButton {
  background: #4776e6;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 10px rgba(71, 118, 230, 0.3);
  font-size: 14px;

  &:hover {
    background: darken(#4776e6, 4);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(71, 118, 230, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(71, 118, 230, 0.4);
  }
}

.uploadContainer {
  display: flex;
  justify-content: center;
  width: 100%;
}

.formGroupFull {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.uploadButtonWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 350px;
  min-height: 180px;
  padding: 25px;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  border: 2px dashed #ced4da;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);

  &:hover,
  &.hovered {
    border-color: #6c757d;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  }

  &.dragging {
    border-color: #2196f3;
    border-width: 3px;
    box-shadow: 0 10px 25px rgba(33, 150, 243, 0.2);
    transform: scale(1.03);
  }
}

.uploadIconContainer {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70px;
  height: 70px;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.uploadIcon {
  color: #2196f3;
  transition: all 0.3s ease;

  &.draggingIcon {
    transform: rotate(180deg);
    color: #1976d2;
  }
}

.uploadTextContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 5px;
}

.uploadTitle {
  font-size: 16px;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 5px;
}

.uploadSubtitle {
  font-size: 14px;
  color: #6c757d;
  text-align: center;
}

.acceptedFormats {
  margin-top: 10px;
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 5px 10px;
  border-radius: 4px;
}
