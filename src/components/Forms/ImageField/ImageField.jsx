import { useState, useRef, useCallback, useEffect } from 'react'
import { Button, Figure, Form, FormGroup } from 'react-bootstrap'

import styles from './ImageField.module.scss'
import { MAX_IMG_SIZE } from '../../../const'
import { useToast } from '../../../hooks/useToast'
import { convertBase64 } from '../../../utils/common'
import { getImageSrc } from '../../../utils/images'

function ImageField({
  imagePath,
  setValue,
  fieldName,
  unregister,
  isClickRow,
  fieldId = '',
  accept = '.png, .jpg, .jpeg, .webp. svg',
  title = '',
}) {
  const [image, setImage] = useState(undefined)
  const [isHovered, setIsHovered] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isButtonHovered, setIsButtonHovered] = useState(false)
  const [isSmallImage, setIsSmallImage] = useState(false)
  const fileInputRef = useRef(null)
  const imageRef = useRef(null)
  const dragCounter = useRef(0)
  const openToast = useToast()

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    if (!file) return

    const fileSizeInB = file.size
    const base64 = await convertBase64(file)

    if (base64 === '') {
      unregister(fieldName)
      setImage(undefined)
    } else if (fileSizeInB <= MAX_IMG_SIZE) {
      setValue(fieldName, base64, { shouldDirty: true })
      setImage(base64)
    } else if (fileSizeInB > MAX_IMG_SIZE) {
      openToast.error({ message: `Файл слишком большой: ${file.name}`, duration: 6000 })
      evt.target.value = ''
    }
  }

  const handleDeletePictures = () => {
    unregister(fieldName)
    setImage(undefined)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDragEnter = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    dragCounter.current += 1
    if (dragCounter.current === 1) {
      setIsDragging(true)
    }
  }, [])

  const handleDragLeave = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    dragCounter.current -= 1
    if (dragCounter.current === 0) {
      setIsDragging(false)
    }
  }, [])

  const handleDragOver = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback(
    async (e) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragging(false)
      dragCounter.current = 0

      const files = e.dataTransfer.files
      if (files && files.length > 0) {
        const file = files[0]

        // Проверка типа файла
        const acceptedTypes = accept.split(',').map((type) => type.trim().replace('.', ''))
        const fileType = file.name.split('.').pop().toLowerCase()

        if (!acceptedTypes.includes(fileType)) {
          openToast.error({
            message: `Неподдерживаемый формат файла. Пожалуйста, используйте: ${accept}`,
            duration: 6000,
          })
          return
        }

        const fileSizeInB = file.size
        const base64 = await convertBase64(file)

        if (base64 === '') {
          unregister(fieldName)
          setImage(undefined)
        } else if (fileSizeInB <= MAX_IMG_SIZE) {
          setValue(fieldName, base64)
          setImage(base64)
        } else if (fileSizeInB > MAX_IMG_SIZE) {
          openToast.error({ message: `Файл слишком большой: ${file.name}`, duration: 6000 })
        }
      }
    },
    [accept, fieldName, openToast, setValue, unregister]
  )

  const hasImage = image || imagePath

  // Проверка размера изображения после загрузки
  const checkImageSize = useCallback(() => {
    if (imageRef.current) {
      const { width, height } = imageRef.current.getBoundingClientRect()
      setIsSmallImage(width < 150 || height < 100)
    }
  }, [])

  useEffect(() => {
    if (hasImage) {
      // Время на загрузку изображения
      const timer = setTimeout(checkImageSize, 300)
      return () => clearTimeout(timer)
    }
  }, [hasImage, checkImageSize])

  return (
    <FormGroup controlId={`${fieldName}nameForm${fieldId}`}>
      {title && <div className={styles.fieldTitle}>{title}</div>}
      <div className={styles.imageFieldContainer}>
        {hasImage ? (
          <div className={styles.imagePreviewContainer}>
            <Figure
              className={styles.imagePreview}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <Figure.Image
                className={styles.previewImage}
                src={getImageSrc(image || imagePath)}
                alt="Предпросмотр изображения"
                ref={imageRef}
                onLoad={checkImageSize}
              />
              {!isClickRow && (
                <div className={`${styles.imageOverlay} ${isHovered || isSmallImage ? styles.visible : ''}`}>
                  <div className={styles.imageActions}>
                    {image && (
                      <Button onClick={handleDeletePictures} variant="danger" size="sm" className={styles.deleteButton}>
                        Удалить
                      </Button>
                    )}
                    <label className={styles.replaceButton}>
                      Заменить
                      <Form.Control
                        className="visually-hidden"
                        type="file"
                        onChange={handleFileRead}
                        name={`${fieldName}${fieldId}`}
                        accept={accept}
                        ref={fileInputRef}
                      />
                    </label>
                  </div>
                </div>
              )}
            </Figure>
          </div>
        ) : (
          <div className={styles.uploadContainer}>
            <Form.Group controlId={`${fieldName}formPictureFile${fieldId}`} className={styles.formGroupFull}>
              <div
                className={`${styles.uploadButtonWrapper} ${isDragging ? styles.dragging : ''} ${isButtonHovered ? styles.hovered : ''}`}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
                onMouseEnter={() => setIsButtonHovered(true)}
                onMouseLeave={() => setIsButtonHovered(false)}
              >
                <div className={styles.uploadIconContainer}>
                  <div className={`${styles.uploadIcon} ${isDragging ? styles.draggingIcon : ''}`}>
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor" />
                      <path
                        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
                <div className={styles.uploadTextContainer}>
                  <span className={styles.uploadTitle}>
                    {isDragging ? 'Отпустите файл здесь' : 'Выберите изображение'}
                  </span>
                  <span className={styles.uploadSubtitle}>или перетащите файл в эту область</span>
                </div>
                <Form.Control
                  className="visually-hidden"
                  type="file"
                  onChange={handleFileRead}
                  name={`${fieldName}${fieldId}`}
                  accept={accept}
                  ref={fileInputRef}
                />
              </div>
              <div className={styles.acceptedFormats}>
                Поддерживаемые форматы: {accept.replace(/\./g, ' ').replace(/,/g, '').toUpperCase()}
              </div>
            </Form.Group>
          </div>
        )}
      </div>
    </FormGroup>
  )
}

export default ImageField
