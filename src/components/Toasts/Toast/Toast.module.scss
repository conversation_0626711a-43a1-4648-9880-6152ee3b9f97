@import '../../../assets/styles/variables';

.toast {
  position: relative;
  margin-bottom: 16px;
  padding-left: 6px;

  display: grid;
  grid-template-columns: 50px 1fr 30px;
  width: 351px;

  background-color: #ffffff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  border-radius: 4px;

  &::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;

    width: 6px;

    background-color: #42c2ff;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;

    content: '';
  }
}

.success {
  &::before {
    background-color: #47c9a2;
  }
}

.warning {
  &::before {
    background-color: #fbc005;
  }
}

.error {
  &::before {
    background-color: #fb5757;
  }
}

.iconWrapper {
  display: grid;
  place-items: center;
}

.iconSuccess {
  & path {
    fill: #47c9a2;
  }
}

.content {
  padding: 8px 0;
  padding-right: 5px;
}

.title {
  margin-bottom: 0;

  font-size: 16px;
  color: #495057;
}

.description {
  margin: 0;

  width: 223px;

  font-size: 14px;
  color: #adb5bd;
  overflow-wrap: break-word;
}

.button {
  height: 100%;
  width: 100%;

  font-size: 0;

  background-color: transparent;
  background-image: url('../../../assets/img/icons/icon-close.svg');
  background-repeat: no-repeat;
  background-position: top 8px right 8px;
  border: 0;
}

@media (min-width: $grid-breakpoint-sm) {
  .toast {
    grid-template-columns: 70px 1fr 100px;
    width: 405px;

    box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
  }

  .content {
    padding: 14px 0;

    border-right: 1px solid #eef0f3;
  }

  .title {
    font-size: 18px;
  }

  .button {
    font-size: 12px;
    letter-spacing: 1px;
    color: #ced4da;
    text-transform: uppercase;

    background: none;
  }
}
