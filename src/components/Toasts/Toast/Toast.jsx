import { useTimeout } from '@/hooks/useTimeout'

import styles from './Toast.module.scss'
import IconSuccess from '../../../assets/img/icons/icon-check.svg?react'
import IconError from '../../../assets/img/icons/icon-error.svg?react'
import IconInfo from '../../../assets/img/icons/icon-info.svg?react'
import IconWarning from '../../../assets/img/icons/icon-warning.svg?react'

const getIcon = (type) => {
  switch (type) {
    case 'info':
      return <IconInfo className={styles.icon} width="30" height="30" />
    case 'success':
      return <IconSuccess className={`${styles.icon} ${styles.iconSuccess}`} width="30" height="30" />
    case 'warning':
      return <IconWarning className={styles.icon} width="30" height="30" />
    case 'error':
      return <IconError className={styles.icon} width="30" height="30" />
    default:
      return <IconInfo className={styles.icon} width="30" height="30" />
  }
}

function Toast({ type, title, message, onClose, duration }) {
  useTimeout(onClose, duration)

  return (
    <section className={`${styles.toast} ${styles[type]}`} role="alert">
      <div className={styles.iconWrapper}>{getIcon(type)}</div>

      <div className={styles.content}>
        <h3 className={styles.title}>{title}</h3>
        <p className={styles.description}>{message}</p>
      </div>

      <div>
        <button onClick={onClose} className={styles.button} type="button">
          Закрыть
        </button>
      </div>
    </section>
  )
}

export default Toast
