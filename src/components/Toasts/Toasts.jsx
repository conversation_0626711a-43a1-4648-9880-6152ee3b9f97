import { useCallback, useMemo, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { getToasts, removeToast } from '@/store/slices/app'

import Toast from './Toast/Toast'
import styles from './Toasts.module.scss'

function Toasts() {
  const dispatch = useDispatch()

  const toasts = useSelector(getToasts)

  const handleDeleteToast = useCallback(
    (id) => {
      dispatch(removeToast(id))
    },
    [dispatch]
  )

  const toastsMap = useMemo(
    () =>
      toasts.map((toast) => (
        <Toast
          type={toast.type}
          title={toast.title}
          message={toast.message}
          index={toast.id}
          onClose={() => handleDeleteToast(toast.id)}
          duration={toast.duration}
          key={toast.id}
        />
      )),
    [toasts, handleDeleteToast]
  )

  const listRef = useRef(null)

  return (
    toasts.length > 0 && (
      <div className={styles.toasts} ref={listRef}>
        {toastsMap}
      </div>
    )
  )
}

export default Toasts
