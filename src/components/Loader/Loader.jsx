import { Col, Row, Spinner } from 'react-bootstrap'

function Loader({ isLoading, children, text = 'загрузка данных' }) {
  if (isLoading) {
    return (
      <Row className="d-grid justify-content-center">
        <Col key="spinner" className="d-grid justify-content-center">
          <Spinner animation="border" />
        </Col>
        <Col key="text">{text}</Col>
      </Row>
    )
  } else if (!children) {
    return null
  } else {
    return children
  }
}

export default Loader
