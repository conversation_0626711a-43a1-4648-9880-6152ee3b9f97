import { Modal, ModalBody, ModalTitle } from 'react-bootstrap'
import <PERSON>dalHeader from 'react-bootstrap/ModalHeader'

import styles from './SuccessModal.module.scss'
import IconSuccess from '../../../assets/img/icons/icon-check.svg?react'

function SuccessModal({ children, show, handleCloseModal, description }) {
  const handleClose = () => {
    handleCloseModal(false)
  }

  return (
    <Modal className={styles.modal} show={show} onHide={handleClose}>
      <ModalHeader className={styles.modalHeader} closeButton>
        <div className={styles.headerInner}>
          <IconSuccess width="64" height="64" />
        </div>
      </ModalHeader>

      <ModalBody>
        <ModalTitle className={styles.title}>Успех!</ModalTitle>
        <p className={styles.text}>{description}</p>
        {children}
      </ModalBody>
    </Modal>
  )
}

export default SuccessModal
