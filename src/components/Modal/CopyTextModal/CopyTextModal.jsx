import { useEffect, useState } from 'react'

import styles from './CopyTextModal.module.scss'

function CopyTextModal({ evt }) {
  const [popup, setPopup] = useState(false)

  useEffect(() => {
    if (evt) copyText(evt)
  }, [evt])

  const copyText = (evt) => {
    evt.preventDefault()
    const target = evt.target

    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(`${target.innerText}`)
        .then(() => {
          setPopup(true)
          popupHidden()
        })
        .catch((err) => console.error(err))
    } else {
      alert(
        'Ваш браузер не поддерживает функцию быстрого копирования содержимого, скопируйте промокод вручную' +
          target.innerText
      )
    }

    function popupHidden() {
      setTimeout(() => {
        setPopup(false)
      }, 1000)
    }
  }

  if (!popup) return null

  return (
    <section className={styles.popup}>
      <div>
        <p className={styles.text}>Скопировано</p>
      </div>
    </section>
  )
}

export default CopyTextModal
