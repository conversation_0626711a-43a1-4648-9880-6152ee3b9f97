import React from 'react'
import { <PERSON><PERSON>, Mo<PERSON> } from 'react-bootstrap'

import styles from './confirm-modal.module.scss'
import IconRefund from '../../../assets/img/icons/icon-refund2.svg?react'
import IconTrash from '../../../assets/img/icons/icon-trash.svg?react'

const returnBody = (variant) => {
  switch (variant) {
    case 'delete':
      return (
        <>
          <p className={`${styles.iconWrap} ${styles.iconWrapDelete}`}>
            <IconTrash className={styles.icon} />
          </p>
          <p className={styles.text}>Вы уверены, что хотите удалить? Это действие невозможно будет отменить.</p>
        </>
      )
    case 'refund':
      return (
        <>
          <p className={`${styles.iconWrap} ${styles.iconWrapRefund}`}>
            <IconRefund width={18} height={18} className={styles.icon} />
          </p>
          <p className={styles.text}>Вы уверены, что хотите сделать возврат?</p>
        </>
      )
  }
}

function ConfirmModal({ isShow, onClose, onUpdateSelectedItem, onConfirmAction, variant = 'delete' }) {
  const handleCloseConfirm = () => {
    onClose(false)
    onUpdateSelectedItem('')
  }

  return (
    <Modal show={isShow} onHide={handleCloseConfirm} size="sm">
      <Modal.Header closeButton>{/*<Modal.Title>{text}</Modal.Title>*/}</Modal.Header>
      <Modal.Body className={styles.modalBody}>{returnBody(variant)}</Modal.Body>
      <Modal.Footer className="d-flex justify-content-center">
        <Button variant="outline-secondary" onClick={handleCloseConfirm}>
          Отменить
        </Button>
        <Button variant="danger" onClick={onConfirmAction}>
          {variant === 'delete' ? 'Удалить' : 'Возврат'}
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default ConfirmModal
