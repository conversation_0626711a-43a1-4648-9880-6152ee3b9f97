import React from 'react'
import { But<PERSON>, Modal } from 'react-bootstrap'

import styles from './ConfirmDeleteModal.module.scss'
import IconTrash from '../../../assets/img/icons/icon-trash.svg?react'

function ConfirmDeleteModal({ isShow, onClose, onUpdateSelectedDeleteItem, onDeleteItem }) {
  const handleCloseConfirm = () => {
    onClose(false)

    if (onUpdateSelectedDeleteItem) {
      onUpdateSelectedDeleteItem('')
    }
  }

  return (
    <Modal show={isShow} onHide={handleCloseConfirm} size="sm">
      <Modal.Header closeButton></Modal.Header>
      <Modal.Body className={styles.modalBody}>
        <p className={styles.iconWrap}>
          <IconTrash className={styles.icon} />
        </p>
        <p className={styles.text}>Вы уверены, что хотите удалить? Это действие невозможно будет отменить.</p>
      </Modal.Body>
      <Modal.Footer>
        <Button className={styles.btn} variant="link" onClick={handleCloseConfirm}>
          Отмена
        </Button>
        <Button className={`${styles.btn} ${styles.btnDanger}`} variant="link" onClick={onDeleteItem}>
          Удалить
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default ConfirmDeleteModal
