import { useState, useEffect } from 'react'
import { Button, Modal, FormCheck } from 'react-bootstrap'

import { unixToMoment } from '@/utils/date'

const CitySelectionModal = ({ show, onHide, cities = [], selectedCities = [], onConfirm }) => {
  const [localSelectedCities, setLocalSelectedCities] = useState([])

  useEffect(() => {
    setLocalSelectedCities(selectedCities)
  }, [selectedCities, show])

  const handleCityToggle = (cityId) => {
    setLocalSelectedCities((prev) => (prev.includes(cityId) ? prev.filter((id) => id !== cityId) : [...prev, cityId]))
  }

  const handleConfirm = () => {
    onConfirm(localSelectedCities)
    onHide()
  }

  const handleSelectAll = () => {
    setLocalSelectedCities(cities.map((city) => city.public_id))
  }

  const handleDeselectAll = () => {
    setLocalSelectedCities([])
  }

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Выбор городов</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="mb-3">
          <Button variant="outline-primary" size="sm" className="me-2" onClick={handleSelectAll}>
            Выбрать все
          </Button>
          <Button variant="outline-secondary" size="sm" onClick={handleDeselectAll}>
            Снять все
          </Button>
        </div>
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {cities.map((city) => (
            <FormCheck
              key={city.public_id}
              type="checkbox"
              id={`city-${city.public_id}`}
              label={`${city.city.name_ru} — ${unixToMoment(city.start_time).tz(city.timezone).format('DD.MM.YYYY')}`}
              checked={localSelectedCities.includes(city.public_id)}
              onChange={() => handleCityToggle(city.public_id)}
              className="mb-2"
            />
          ))}
        </div>
        {cities.length === 0 && <div className="text-muted text-center py-3">Нет доступных городов</div>}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="success" onClick={handleConfirm}>
          Подтвердить ({localSelectedCities.length})
        </Button>
        <Button variant="outline-secondary" onClick={onHide}>
          Отмена
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default CitySelectionModal
