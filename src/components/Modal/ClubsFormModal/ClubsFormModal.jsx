import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'

import { APIRoute } from '@/const'
import { useGetCountryRegions } from '@/features/cities/api/getCountryRegions'
import { useGetRegionCities } from '@/features/cities/api/getRegionCities'
import { useToast } from '@/hooks/useToast'
import { Tag } from '@/pages/ShopScreen/components/Delivery/Delivery'
import { updateFormData } from '@/utils/forms'

import SingleImageForm from '../../SingleImageForm/SingleImageForm'

const MOSCOW_REGION_ID = 524894
const MOSCOW_CITY_ID = 524901

function ClubsFormModal({ isShowModal, onCloseModal, selectedItem, clubPublicId, isViewForm, onUpdateClubs }) {
  const [formData, setFormData] = useState({})
  const [validated, setValidated] = useState(false)
  const [selectedRegion, setSelectedRegion] = useState(0)
  const [eventTypes, setEventTypes] = useState([])

  const openToast = useToast()

  const { data: regionsData } = useGetCountryRegions('RU')
  const regions = regionsData?.data?.values || []

  const { data: citiesData } = useGetRegionCities(selectedRegion)
  const regionCities = citiesData?.data?.values || []

  const [, api] = useAxios(
    {
      url: APIRoute.GET_REGION_RU,
      method: 'GET',
    },
    { manual: true }
  )
  const [, apiEventTypes] = useAxios(
    {
      url: APIRoute.ACTIONS_EVENT_TYPE,
      method: 'GET',
    },
    { manual: true }
  )

  const getEventTypes = useCallback(() => {
    apiEventTypes().then((r) => {
      setEventTypes(r?.data?.values)
    })
  }, [apiEventTypes])

  useEffect(() => {
    if (isShowModal && Object.keys(selectedItem)?.length === 0) {
      setFormData({
        public: true,
        location: {
          id: MOSCOW_CITY_ID,
        },
      })
      setSelectedRegion(MOSCOW_REGION_ID)
    } else if (isShowModal && Object.keys(selectedItem)?.length > 0 && selectedItem?.city?.region_id) {
      setSelectedRegion(selectedItem.city.region_id)
    }
  }, [isShowModal, selectedItem])

  useEffect(() => {
    if (isShowModal) {
      getEventTypes()
    }
  }, [isShowModal, getEventTypes])

  const handleChangeRegion = (evt) => {
    const id = +evt.target.value
    const newFormData = { ...formData }

    setSelectedRegion(id)
    delete newFormData.location
    setFormData({ ...newFormData })
  }

  const handleChangeCity = (evt) => {
    const value = +evt.target.value
    const newFormData = { ...formData }

    if (value === 0 || value === selectedItem?.location?.id) {
      delete newFormData.location
      setFormData({ ...newFormData })
    } else {
      setFormData({ ...formData, location: { id: value } })
    }
  }

  const handleChangeEventType = (evt) => {
    const value = evt.target.value

    if (value !== '') {
      if (selectedItem?.event_type?.length > 0 && !formData?.event_type) {
        setFormData((prev) => ({
          ...prev,
          event_type: [
            ...(selectedItem?.event_type?.map((item) => ({ public_id: item.public_id })) || []),
            { public_id: value },
          ],
        }))
      } else if ('event_type' in formData) {
        setFormData((prev) => ({
          ...prev,
          event_type: [...(prev?.event_type || []), { public_id: value }],
        }))
      } else {
        setFormData((prev) => ({ ...prev, event_type: [{ public_id: value }] }))
      }
    }
  }

  const handleDelSelectedType = (type) => {
    let filteredTypes = []

    if (selectedItem?.event_type?.length > 0 && !formData?.event_type) {
      filteredTypes = selectedItem.event_type
        .map((item) => ({ public_id: item.public_id }))
        .filter((item) => item.public_id !== type.public_id)
    } else if ('event_type' in formData) {
      filteredTypes = formData.event_type.filter((item) => item.public_id !== type.public_id)
    }

    setFormData((prev) => ({ ...prev, event_type: filteredTypes }))
  }

  const handleSubmitForm = (evt) => {
    const form = evt.currentTarget

    evt.preventDefault()
    evt.stopPropagation()

    if (Object.keys(selectedItem)?.length > 0 || form.checkValidity() === true) {
      const method = Object.keys(selectedItem)?.length > 0 ? 'PUT' : 'POST'
      const toastText = Object.keys(selectedItem)?.length > 0 ? 'Клуб изменён' : 'Клуб создан'
      const url =
        Object.keys(selectedItem)?.length > 0 ? `${APIRoute.ACTIONS_CLUB}/${clubPublicId}` : APIRoute.ACTIONS_CLUB

      api({ url: url, method: method, data: formData }).then(() => {
        onUpdateClubs()
        handleClickCloseModal()
        openToast.success({ message: toastText })
      })
    }

    setValidated(true)
  }

  const handleChangeCheckbox = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.checked })
  }

  const handleClickCloseModal = () => {
    setFormData({})
    setValidated(false)
    setSelectedRegion(0)
    onCloseModal(false)
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, selectedItem)
  }

  const returnEventName = (id) => {
    const foundEvent = eventTypes.find((item) => item.public_id === id)

    return foundEvent?.title ?? id
  }

  const isDisabledEventType = (item) => {
    if (selectedItem?.event_type?.length > 0 && !formData?.event_type) {
      return (selectedItem.event_type || []).map((item) => item.public_id).includes(item.public_id)
    } else if ('event_type' in formData) {
      return (formData.event_type || []).map((item) => item.public_id).includes(item.public_id)
    }

    return false
  }

  const SelectedEventTypes = ({ formDataTypes, selectedItemTypes }) => {
    const types = formDataTypes ?? selectedItemTypes

    return (
      <Col md={12}>
        {types?.length > 0 && (
          <p style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            <span>
              Выбранные типы события:{' '}
              {(types || []).map((type) => (
                <Tag onClick={() => handleDelSelectedType(type)} key={type.public_id}>
                  {returnEventName(type.public_id)}
                </Tag>
              ))}
            </span>
          </p>
        )}
      </Col>
    )
  }

  const checkRequiredSelectEvent = () => {
    if (Object.keys(selectedItem)?.length > 0) {
      if (!formData?.event_type) {
        return false
      } else if (formData?.event_type?.length === 0) {
        return true
      }
    } else if (!formData?.event_type || formData?.event_type?.length === 0) {
      return true
    }
  }

  const isRequiredSelectEvent = checkRequiredSelectEvent()

  return (
    <Modal show={isShowModal} onHide={handleClickCloseModal}>
      <Modal.Header closeButton>
        <Modal.Title>{Object.keys(selectedItem).length > 0 ? 'Редактирование' : 'Создание клуба'}</Modal.Title>
      </Modal.Header>
      <Form noValidate validated={validated} onSubmit={handleSubmitForm}>
        <Modal.Body>
          <SingleImageForm
            formData={formData}
            defaultFormData={selectedItem}
            onChangeFormData={setFormData}
            isEdit={isViewForm}
          />

          <Row className="g-3 mb-3">
            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="titleLabel" label="Название">
                  <FormControl
                    onChange={handleChangeField}
                    name="title"
                    type="text"
                    defaultValue={selectedItem?.title}
                    placeholder="Название"
                    disabled={isViewForm}
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="descriptionLabel" label="Описание">
                  <FormControl
                    onChange={handleChangeField}
                    name="description"
                    type="text"
                    defaultValue={selectedItem?.description}
                    placeholder="Описание"
                    disabled={isViewForm}
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="public_nameLabel" label="Короткий адрес">
                  <FormControl
                    onChange={handleChangeField}
                    name="public_name"
                    type="text"
                    defaultValue={selectedItem?.public_name}
                    value={formData?.public_name}
                    placeholder="Короткий адрес"
                    disabled={isViewForm}
                    required
                  />
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="regionsLabel" label="Регион">
                  <FormControl
                    as="select"
                    name="region"
                    placeholder="Регион"
                    onChange={handleChangeRegion}
                    // defaultValue={selectedItem?.region?.region_id}
                    disabled={isViewForm}
                    required
                  >
                    {/*<option value="">Выберите регион</option>*/}
                    {(regions || []).map((item) => (
                      <option
                        value={item.id}
                        key={item.id}
                        selected={item.id === selectedRegion || item.id === selectedItem?.city?.region_id}
                      >
                        {item.name_ru}
                      </option>
                    ))}
                  </FormControl>
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="locationLabel" label="Город">
                  <FormControl
                    as="select"
                    name="location"
                    placeholder="Город"
                    onChange={handleChangeCity}
                    // defaultValue={selectedItem?.location?.id}
                    // disabled={isViewForm || selectedRegion === 0}
                    required
                  >
                    <option value="">Выберите город</option>
                    {(regionCities || []).map((item) => (
                      <option
                        value={item.id}
                        key={item.id}
                        selected={item.id === formData?.city?.id || item.id === selectedItem?.city?.id}
                      >
                        {item.name_ru}
                      </option>
                    ))}
                  </FormControl>
                </FloatingLabel>
              </Form.Group>
            </Col>

            <Col md={12}>
              <Form.Group>
                <FloatingLabel controlId="eventTypeLabel" label="Тип события">
                  <FormControl
                    as="select"
                    name="eventType"
                    value=""
                    placeholder="Тип события"
                    onChange={handleChangeEventType}
                    disabled={isViewForm}
                    required={isRequiredSelectEvent}
                  >
                    <option value="">Выберите тип события</option>
                    {(eventTypes || []).map((item) => (
                      <option value={item.public_id} key={item.public_id} disabled={isDisabledEventType(item)}>
                        {item.title}
                      </option>
                    ))}
                  </FormControl>
                </FloatingLabel>
              </Form.Group>
            </Col>

            <SelectedEventTypes formDataTypes={formData?.event_type} selectedItemTypes={selectedItem?.event_type} />

            <Col md={12}>
              <FormGroup controlId="publicForm">
                <Row>
                  <Col md="auto">
                    <Form.Label>Сделать клуб публичным:</Form.Label>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      id="public"
                      label="Да"
                      onChange={handleChangeCheckbox}
                      checked={formData?.public ?? selectedItem?.public}
                    />
                  </Col>
                </Row>
              </FormGroup>
            </Col>
            <Col md={12}>
              <FormGroup controlId="priorityForm">
                <Row>
                  <Col md="auto">
                    <Form.Label>Приоритетный:</Form.Label>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      id="priority"
                      label="Да"
                      onChange={handleChangeCheckbox}
                      checked={formData?.priority ?? selectedItem?.priority}
                    />
                  </Col>
                </Row>
              </FormGroup>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="success" type="submit">
            {Object.keys(selectedItem).length > 0 ? 'Сохранить' : 'Создать'}
          </Button>
          <Button onClick={handleClickCloseModal} variant="outline-secondary" type="button">
            Закрыть
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  )
}

export default ClubsFormModal
