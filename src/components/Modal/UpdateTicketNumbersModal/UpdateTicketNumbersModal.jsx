import { Modal, Button } from 'react-bootstrap'

export const UpdateTicketNumbersModal = ({ show, onHide, onConfirm, isLoading }) => {
  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Подтверждение действия</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>У всех участников заново сформируется номер билета, вы действительно хотите продолжить?</p>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide} disabled={isLoading}>
          Нет
        </Button>
        <Button variant="primary" onClick={onConfirm} disabled={isLoading}>
          {isLoading ? 'Обновление...' : 'Да'}
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
