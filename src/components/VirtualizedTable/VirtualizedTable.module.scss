.virtualizedTable {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

.tableHeader {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.headerCell {
  padding: 12px 8px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  font-size: 0.875rem;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;

  &:last-child {
    border-right: none;
  }
}

.tableBody {
  background-color: #fff;
}

.tableRow {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  background-color: #fff;
  transition: background-color 0.15s ease-in-out;
  cursor: pointer;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.tableCell {
  padding: 12px 8px;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  word-break: break-word;
  font-size: 0.875rem;

  &:last-child {
    border-right: none;
  }
}
