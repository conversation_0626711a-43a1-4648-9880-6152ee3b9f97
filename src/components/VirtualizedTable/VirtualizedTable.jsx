import React, { memo, useMemo } from 'react'
import { Card, Table } from 'react-bootstrap'
import { FixedSizeList as List } from 'react-window'

import styles from './VirtualizedTable.module.scss'

const VirtualizedTableRow = memo(({ index, style, data }) => {
  const { rows, columns, actions, onRowClick } = data
  const row = rows[index]

  return (
    <div style={style}>
      <div className={styles.tableRow} onClick={() => onRowClick?.(row)}>
        {columns.map((column, columnIndex) => (
          <div key={columnIndex} className={styles.tableCell} style={{ width: column.width }}>
            {column.render ? column.render(row[column.key], row, index) : row[column.key] || '-'}
          </div>
        ))}
        {actions && (
          <div className={styles.tableCell} style={{ width: '100px' }}>
            {actions(row)}
          </div>
        )}
      </div>
    </div>
  )
})

VirtualizedTableRow.displayName = 'VirtualizedTableRow'

const VirtualizedTable = ({
  data,
  columns,
  height = 400,
  rowHeight = 50,
  actions,
  onRowClick,
  title,
  className = '',
}) => {
  const itemData = useMemo(
    () => ({
      rows: data,
      columns,
      actions,
      onRowClick,
    }),
    [data, columns, actions, onRowClick]
  )

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        {title && (
          <Card.Header>
            <Card.Title>{title}</Card.Title>
          </Card.Header>
        )}
        <Card.Body>
          <p>Нет данных для отображения</p>
        </Card.Body>
      </Card>
    )
  }

  return (
    <Card className={className}>
      {title && (
        <Card.Header>
          <Card.Title>{title}</Card.Title>
        </Card.Header>
      )}
      <Card.Body>
        <div className={styles.virtualizedTable}>
          {/* Table Header */}
          <div className={styles.tableHeader}>
            {columns.map((column, index) => (
              <div key={index} className={styles.headerCell} style={{ width: column.width }}>
                {column.label}
              </div>
            ))}
            {actions && (
              <div className={styles.headerCell} style={{ width: '100px' }}>
                Действия
              </div>
            )}
          </div>

          {/* Virtualized Table Body */}
          <List
            height={height}
            itemCount={data.length}
            itemSize={rowHeight}
            itemData={itemData}
            className={styles.tableBody}
          >
            {VirtualizedTableRow}
          </List>
        </div>
      </Card.Body>
    </Card>
  )
}

export default memo(VirtualizedTable)
