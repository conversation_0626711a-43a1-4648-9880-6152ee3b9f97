import { useEffect } from 'react'

import styles from './PagePreloader.module.scss'
import { pagePreloader } from './preloader'

function PagePreloader({ isLoading, children, preloader }) {
  useEffect(() => {
    isLoading && !preloader && pagePreloader()
  }, [isLoading, preloader])

  if (isLoading) {
    if (preloader) {
      return preloader
    } else {
      return (
        <div className={styles.canvasCont}>
          <canvas width="600" height="450" />
        </div>
      )
    }
  } else if (!children) {
    return null
  } else {
    return children
  }
}

export default PagePreloader
