import { memo } from 'react'
import { Col, ListGroup, Row } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import { times, getSex, getTicketStatus, formatPhoneNumber, getStatusCode } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

const ListItem = memo(({ item, xsLabel, xsValue, event }) => {
  // Early return if required props are missing
  if (!item || !event) {
    return null
  }
  // Early return if required props are missing
  if (!item || !event) {
    return null
  }
  const recompose = (obj, string) => {
    if (!obj || !string) return undefined

    const parts = string.split('.')
    const newObj = obj[parts[0]]

    if (parts[1]) {
      // If there are more parts to traverse but current object is undefined/null, return undefined
      if (newObj === undefined || newObj === null) {
        return undefined
      }
      parts.splice(0, 1)
      const newString = parts.join('.')
      return recompose(newObj, newString)
    }
    return newObj
  }

  const switchValueCol = (item) => {
    if (!item || !item.value) return '-'

    const value = recompose(event, item.value)

    switch (item.param) {
      case 'date':
        return value ? times.getFullDate(value) : '-'
      case 'gender':
        return value ? getSex(value) : '-'
      case 'ticketStatus':
        return getTicketStatus(value)
      case 'link':
        return (
          <Link to={value} target="_blank">
            {value}
          </Link>
        )
      case 'dateTime':
        return (
          <>
            {times.getFullDate(value)}, {times.getTime(value)}
          </>
        )
      case 'image':
        return value ? (
          <img src={getImageSrc(value)} alt="" style={{ maxWidth: '100px', maxHeight: '100px', objectFit: 'cover' }} />
        ) : (
          '-'
        )
      case 'phoneNumber':
        return value ? formatPhoneNumber(value) : '-'
      case 'statusCode':
        return getStatusCode(value)
      case 'dateTs':
        return value ? unixToMoment(value).format('DD/MM/YYYY, HH:mm') : '-'
      case 'yesOrNo':
        return value ? 'Да' : 'Нет'
      case 'money':
        return value ? `${value.toLocaleString()} ₽` : '-'
      case 'array':
        return Array.isArray(value) ? value.join(', ') : value || '-'
      default:
        return value || '-'
    }
  }

  return (
    <ListGroup.Item className="border-0">
      <Row>
        <Col xs={12} sm={xsLabel}>
          <span className="text-muted">{item.label}:</span>
        </Col>
        <Col xs={12} sm={xsValue}>
          {switchValueCol(item)}
        </Col>
      </Row>
    </ListGroup.Item>
  )
})

ListItem.displayName = 'ListItem'

export default ListItem
