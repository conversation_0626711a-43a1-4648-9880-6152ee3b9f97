import { Card, ListGroup } from 'react-bootstrap'

import ListItem from './ListItem'

const ListCard = ({ data, event, xsLabel = 3, xsValue = 9, moreRows = null, actions }) => {
  if (!event) return null

  return (
    <Card>
      {(data.title.length > 0 || data.subTitle.length > 0) && (
        <Card.Header>
          <Card.Title>{data.title}</Card.Title>
          <Card.Subtitle>{data.subTitle}</Card.Subtitle>
        </Card.Header>
      )}
      <ListGroup className="mb-2">
        {data.list.map((item) => (
          <ListItem key={item.label} item={item} xsLabel={xsLabel} xsValue={xsValue} event={event} />
        ))}
        {moreRows && moreRows()}
        {actions && <ListGroup.Item className="border-0 d-flex justify-content-end">{actions()}</ListGroup.Item>}
      </ListGroup>
    </Card>
  )
}

export default ListCard
