import { configureStore } from '@reduxjs/toolkit'
import { configure } from 'axios-hooks'
import { LRUCache } from 'lru-cache'
import React from 'react'
import { ErrorBoundary } from 'react-error-boundary'
import { HelmetProvider } from 'react-helmet-async'
import { QueryClientProvider } from 'react-query'
import { ReactQueryDevtools } from 'react-query/devtools'
import { Provider } from 'react-redux'

import { ErrorFallback } from '@/components/ErrorFallback/ErrorFallback'

import { ThemeProvider } from '@/contexts/ThemeContext'
import { axios } from '@/lib/axios'
import { queryClient } from '@/lib/react-query'
import { redirect } from '@/store/middlewares/redirect'
import rootReducer from '@/store/rootReducer'

const cache = new LRUCache({ max: 10 })

configure({ axios, cache })

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: {
        extraArgument: axios,
      },
    }).concat(redirect),
})

export const AppProvider = ({ children }) => {
  const handleError = (error, info) => {
    console.error('Произошла ошибка:', error)
    console.error('Информация о компоненте:', info)
  }

  return (
    <React.Suspense fallback="Loading...">
      <ErrorBoundary FallbackComponent={ErrorFallback} onError={handleError} resetKeys={[]}>
        <ThemeProvider>
          <HelmetProvider>
            <QueryClientProvider client={queryClient}>
              {import.meta.env.MODE !== 'test' && <ReactQueryDevtools />}
              <Provider store={store}>{children}</Provider>
            </QueryClientProvider>
          </HelmetProvider>
        </ThemeProvider>
      </ErrorBoundary>
    </React.Suspense>
  )
}
