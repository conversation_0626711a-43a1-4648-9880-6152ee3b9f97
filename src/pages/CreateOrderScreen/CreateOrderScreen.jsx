import useAxios from 'axios-hooks'
import { useRef, useState } from 'react'
import { Alert, Button, Col, FormCheck, Nav, Row, Tab } from 'react-bootstrap'

import styles from './CreateOrderScreen.module.scss'
import OrdersForm from './OrdersForm'
import Layout from '../../components/Layout/Layout'
import SuccessModal from '../../components/Modal/SuccessModal/SuccessModal'
import { APIRoute } from '../../const'
import { API_URL } from '../../lib/axios'

const initItem = {
  name: 'Позиция 1',
  price: '',
  qty: '',
  taxmode: '',
  vat: '',
  payattr: '',
  lineattr: '',
}

function CreateOrderScreen() {
  const [, api] = useAxios(
    {
      url: APIRoute.CREATE_CUSTOM_ORDER,
      method: 'POST',
    },
    { manual: true }
  )
  const [formData, setFormData] = useState({
    processing_public_id: '',
    items: [{ ...initItem }],
  })
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [isDangerAlert, setIsDangerAlert] = useState(false)
  const [createdOrder, setCreatedOrder] = useState(null)
  const [isCheckApproval, setIsCheckApproval] = useState(false)
  const [isCheckAgreement, setIsCheckAgreement] = useState(false)

  const orderLinkRef = useRef(null)

  const handleCreateOrder = async () => {
    setIsDangerAlert(false)

    api({ data: formData }).then((response) => {
      if (response.status === 200) {
        setCreatedOrder(response.data.public_id)
        setShowSuccessModal(true)
        resetForm()
      } else if (response.status === 400 || response.status === 422) {
        setIsDangerAlert(true)
      }
    })
  }

  const handleAddItem = () => {
    const index = formData.items.length + 1
    const newItem = { ...initItem, name: `Позиция ${index}` }

    const newItems = [...formData.items]
    newItems.push(newItem)

    setFormData({ ...formData, items: [...newItems] })
  }

  const resetForm = () => {
    setFormData({
      processing_public_id: '',
      email: '',
      items: [{ ...initItem }],
    })
  }

  return (
    <Layout title="Создание произвольного заказа">
      <Row>
        <Col className="d-grid" md={2}>
          <Button className="mb-3" onClick={handleAddItem} variant="outline-success">
            Добавить позицию
          </Button>
        </Col>
        <Col md={10}>
          {isDangerAlert ? (
            <Alert variant="danger" onClose={() => setIsDangerAlert(false)} dismissible>
              Что-то пошло не так, заказ не был создан
            </Alert>
          ) : (
            <Alert variant="light">Все поля обязательны для заполнения</Alert>
          )}
        </Col>
      </Row>

      <Tab.Container id="left-tabs-example" defaultActiveKey="0">
        <Row>
          <Col sm={2}>
            <Nav variant="pills" className="flex-column">
              {formData.items.map((order, index) => (
                <Nav.Item key={index}>
                  <Nav.Link eventKey={`${index}`}>{order.name}</Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
          </Col>
          <Col sm={10}>
            <Tab.Content>
              {formData.items.map((order, index) => (
                <Tab.Pane eventKey={`${index}`} key={index}>
                  <OrdersForm formData={formData} onChangeField={setFormData} indexForm={index} />
                </Tab.Pane>
              ))}
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      <Row className="mt-4 mb-4">
        <Col md={{ span: 10, offset: 2 }}>
          <FormCheck
            type="checkbox"
            id="chekApproval"
            label="Наименование, НДС и цены согласованы с Главным Бухгалтером"
            onChange={(evt) => setIsCheckApproval(evt.target.checked)}
          />
        </Col>
        <Col md={{ span: 10, offset: 2 }}>
          <FormCheck
            type="checkbox"
            id="chekAgreement"
            label="Соглашаюсь с ответственностью в случае неверного формирования заказа"
            onChange={(evt) => setIsCheckAgreement(evt.target.checked)}
          />
        </Col>
      </Row>

      <Row className="justify-content-md-center mb-5">
        <Col className="d-grid" md={{ span: 3, offset: 3 }}>
          <Button
            onClick={handleCreateOrder}
            variant="primary"
            type="button"
            size="lg"
            disabled={!isCheckApproval || !isCheckAgreement}
          >
            Создать заказ
          </Button>
        </Col>
        <Col className="d-grid" md={{ span: 4, offset: 2 }}>
          <Button onClick={resetForm} variant="outline-secondary">
            Сбросить все формы и удалить позиции
          </Button>
        </Col>
      </Row>

      <SuccessModal
        show={showSuccessModal}
        handleCloseModal={setShowSuccessModal}
        description="Заказ успешно был создан."
      >
        <div className={styles.linkWrapper}>
          <p>Ссылка на оплату</p>
          <a
            className={styles.orderLink}
            ref={orderLinkRef}
            href={`${API_URL}/api/random/payment/${createdOrder}`}
            target="_blank"
            rel="noreferrer"
          >{`${API_URL}/api/random/payment/${createdOrder}`}</a>
        </div>
      </SuccessModal>

      {/*<CopyTextModal evt={copiedText} />*/}
    </Layout>
  )
}

export default CreateOrderScreen
