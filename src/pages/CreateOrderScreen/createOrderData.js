const vat = [
  {
    code: -1,
    desc: 'не облагается НДС',
  },
  {
    code: 0,
    desc: 'облагается НДС по ставке 0 %',
  },
  {
    code: 5,
    desc: 'облагается НДС по ставке 5 %',
  },
  {
    code: 7,
    desc: 'облагается НДС по ставке 7 %',
  },
  {
    code: 10,
    desc: 'облагается НДС по ставке 10 %',
  },
  {
    code: 20,
    desc: 'облагается НДС по ставке 20 %',
  },
  {
    code: 105,
    desc: 'облагается НДС по ставке 5/105',
  },
  {
    code: 107,
    desc: 'облагается НДС по ставке 7/107',
  },
  {
    code: 110,
    desc: 'облагается НДС по ставке 10/110',
  },
  {
    code: 120,
    desc: 'облагается НДС по ставке 20/120',
  },
]

const taxmode = [
  {
    code: 0,
    desc: 'Общая система налогообложения',
  },
  {
    code: 1,
    desc: 'Упрощённая система налогообложения (Доход)',
  },
  {
    code: 2,
    desc: 'Упрощённая СН (Доход минус Расход)',
  },
  {
    code: 3,
    desc: 'Единый налог на вмененный доход',
  },
  {
    code: 4,
    desc: 'Единый сельскохозяйственный налог',
  },
  {
    code: 5,
    desc: 'Патентная система налогообложения',
  },
]

const payattr = [
  {
    code: 1,
    desc: 'Полная предварительная оплата до момента передачи предмета расчёта',
  },
  {
    code: 2,
    desc: 'Частичная предварительная оплата до момента передачи предмета расчёта',
  },
  {
    code: 3,
    desc: 'Аванс',
  },
  {
    code: 4,
    desc: 'Полная оплата, в том числе с учётом аванса (предварительной оплаты) в момент передачи предмета расчёта',
  },
  {
    code: 5,
    desc: 'Частичная оплата предмета расчёта в момент его передачи с по-следующей оплатой в кредит',
  },
  {
    code: 6,
    desc: 'Передача предмета расчёта без его оплаты в момент его передачи с последующей оплатой в кредит',
  },
  {
    code: 7,
    desc: 'Оплата предмета расчёта после его передачи с оплатой в кредит (оплата кредита)',
  },
]

const lineattr = [
  {
    code: 1,
    desc: 'о реализуемом товаре, за исключением подакцизного товара (наименование и иные сведения, описывающие товар)',
  },
  {
    code: 2,
    desc: 'о реализуемом подакцизном товаре (наименование и иные сведения, описывающие товар)',
  },
  {
    code: 3,
    desc: 'о выполняемой работе (наименование и иные сведения, описывающие работу)',
  },
  {
    code: 4,
    desc: 'об оказываемой услуге (наименование и иные сведения, описывающие услугу)',
  },
  {
    code: 5,
    desc: 'о приёме ставок при осуществлении деятельности по проведению азартных игр',
  },
  {
    code: 6,
    desc: 'о выплате денежных средств в виде выигрыша при осуществлении деятельности по проведению азартных игр',
  },
  {
    code: 7,
    desc: `о приёме денежных средств при реализации лотерейных билетов, электронных лотерейных билетов, приёме лотерейных 
    ставок при осуществлении деятельности по проведению лотерей`,
  },
  {
    code: 8,
    desc: 'о выплате денежных средств в виде выигрыша при осуществлении деятельности по проведению лотерей ',
  },
  {
    code: 9,
    desc: 'о предоставлении прав на использование результатов интеллектуальной деятельности или средств индивидуализации',
  },
  {
    code: 10,
    desc: `0 об авансе, задатке, предоплате, кредите, взносе в счёт оплаты, пени, штрафе, вознаграждении, бонусе и ином 
    аналогичном предмете расчёта`,
  },
  {
    code: 11,
    desc: `о вознаграждении пользователя, являющегося платёжным агентом (субагентом), банковским платёжным агентом (субагентом), 
    комиссионером, поверенным или иным агентом`,
  },
  {
    code: 12,
    desc: 'о предмете расчёта, состоящем из предметов, каждому из которых может быть присвоено значение от «1» до «11»',
  },
  {
    code: 13,
    desc: 'о предмете расчёта, не относящемуся к предметам расчёта, которым может быть присвоено значение от «1» до «12» и от «14» до «18»',
  },
  {
    code: 14,
    desc: 'о передаче имущественных прав',
  },
  {
    code: 15,
    desc: 'о внереализационном доходе',
  },
  {
    code: 16,
    desc: `о суммах расходов, уменьшающих сумму налога (авансовых платежей) в соответствии с пунктом 3.1 статьи 346.21 
    Налогового кодекса Российской Федерации`,
  },
  {
    code: 17,
    desc: 'о суммах уплаченного торгового сбора',
  },
  {
    code: 18,
    desc: 'о курортном сборе',
  },
  {
    code: 19,
    desc: 'о залоге',
  },
]

export { vat, taxmode, payattr, lineattr }
