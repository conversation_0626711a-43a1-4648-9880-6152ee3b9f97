import React from 'react'
import { Button, Col, FloatingLabel, FormControl, FormSelect, Row } from 'react-bootstrap'

import { useGetProcessingList } from '@/features/processing/api/getProcessingList'

import { lineattr, payattr, taxmode, vat } from './createOrderData'
import OrderOptions from './OrderOptions'

function OrdersForm({ formData, onChangeField, indexForm }) {
  const getProcessingListQuery = useGetProcessingList()
  const processingList = getProcessingListQuery?.data?.data?.values

  const handleChangeField = (evt) => {
    const name = evt.target.name
    const value =
      name === 'name' || name === 'processing_public_id' || name === 'email'
        ? evt.target.value
        : Number(evt.target.value)

    if (name === 'processing_public_id' || name === 'email') {
      onChangeField({ ...formData, [name]: value })
    } else {
      const newItems = formData.items.map((item, itemIndex) => {
        if (itemIndex === indexForm) {
          return { ...item, [name]: value }
        } else {
          return item
        }
      })

      onChangeField({ ...formData, items: [...newItems] })
    }
  }

  const handleDeleteItem = () => {
    const newItems = [...formData.items]

    newItems.splice(indexForm, 1)

    onChangeField({ ...formData, items: [...newItems] })
  }

  return (
    <>
      <Row className="g-3 mb-4">
        <Col md={6}>
          <FloatingLabel controlId="nameLabelOrder" label="Наименование позиции">
            <FormControl
              onChange={handleChangeField}
              value={formData.items[indexForm].name}
              name="name"
              type="text"
              placeholder="name"
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="priceLabelOrder" label="Цена за единицу измерения">
            <FormControl
              onChange={handleChangeField}
              value={formData.items[indexForm].price}
              name="price"
              type="number"
              placeholder="price"
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="quantityLabelOrder" label="Количество">
            <FormControl
              onChange={handleChangeField}
              value={formData.items[indexForm].qty}
              name="qty"
              type="number"
              placeholder="quantity"
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="emailLabelOrder" label="Почта">
            <FormControl
              onChange={handleChangeField}
              value={formData.items[indexForm].email}
              name="email"
              type="email"
              placeholder="email"
              disabled={indexForm > 0}
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="publicIdLabelOrder" label="Юридическое лицо">
            <FormSelect
              onChange={handleChangeField}
              value={formData.processing_public_id}
              name="processing_public_id"
              aria-label="processing_public_id"
              disabled={indexForm > 0}
            >
              <option>выберите один из вариантов</option>
              {processingList?.map((item) => (
                <option
                  value={item.public_id}
                  // selected={item.public_id === defaultFormData?.processing?.public_id}
                  key={item.public_id}
                >
                  {item.entity}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="taxmodeLabelOrder" label="Код системы налогообложения">
            <FormSelect
              onChange={handleChangeField}
              value={formData.items[indexForm].taxmode}
              name="taxmode"
              aria-label="Код системы налогообложения"
            >
              <option>выберите один из вариантов</option>
              <OrderOptions data={taxmode} />
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="vatLabelOrder" label="Код ставки налогообложения">
            <FormSelect
              onChange={handleChangeField}
              value={formData.items[indexForm].vat}
              name="vat"
              aria-label="Код ставки налогообложения"
            >
              <option>выберите один из вариантов</option>
              <OrderOptions data={vat} />
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="payattrLabelOrder" label="Признак способа расчета">
            <FormSelect
              onChange={handleChangeField}
              value={formData.items[indexForm].payattr}
              name="payattr"
              aria-label="Признак способа расчета"
            >
              <option>выберите один из вариантов</option>
              <OrderOptions data={payattr} />
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="lineattrLabelOrder" label="Признак предмета расчета">
            <FormSelect
              onChange={handleChangeField}
              value={formData.items[indexForm].lineattr}
              name="lineattr"
              aria-label="Признак предмета расчета"
            >
              <option>выберите один из вариантов</option>
              <OrderOptions data={lineattr} />
            </FormSelect>
          </FloatingLabel>
        </Col>
      </Row>

      <Row>
        <Col md={9}>
          <p>Сумма в позиции: {formData.items[indexForm].price * formData.items[indexForm].qty}</p>
          <p>Сумма всех позиций: {formData.items.map((item) => item.price * item.qty).reduce((a, b) => a + b, 0)}</p>
        </Col>
        <Col className="d-grid align-content-start" md={3}>
          <Button onClick={handleDeleteItem} variant="outline-danger">
            Удалить позицию
          </Button>
        </Col>
      </Row>
    </>
  )
}

export default OrdersForm
