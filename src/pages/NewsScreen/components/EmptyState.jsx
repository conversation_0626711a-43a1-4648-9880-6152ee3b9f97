import { Card, Button } from 'react-bootstrap'

import styles from '../NewsScreen.module.scss'

const EmptyState = ({ searchQuery, statusFilter, onClearFilters, onCreateNews }) => {
  const hasFilters = searchQuery || statusFilter !== 'all'

  return (
    <Card className={`text-center py-5 ${styles.emptyState}`}>
      <Card.Body>
        <div className="mb-4">
          <i
            className={`bi ${hasFilters ? 'bi-search' : 'bi-newspaper'} text-muted`}
            style={{ fontSize: '4rem', opacity: 0.6 }}
          />
        </div>
        <h4 className="text-muted mb-3">{hasFilters ? 'Новости не найдены' : 'Нет новостей'}</h4>
        <p className="text-muted mb-4" style={{ maxWidth: '400px', margin: '0 auto' }}>
          {hasFilters
            ? 'Попробуйте изменить критерии поиска или сбросить фильтры для просмотра всех новостей'
            : 'Создайте первую новость, чтобы начать публикацию контента для ваших пользователей'}
        </p>
        <div className="d-flex gap-3 justify-content-center flex-wrap">
          {hasFilters && (
            <Button variant="outline-secondary" onClick={onClearFilters} className="d-flex align-items-center gap-2">
              <i className="bi bi-arrow-clockwise" />
              Сбросить фильтры
            </Button>
          )}
          <Button variant="primary" onClick={onCreateNews} className="d-flex align-items-center gap-2">
            <i className="bi bi-plus-circle" />
            Создать новость
          </Button>
        </div>
      </Card.Body>
    </Card>
  )
}

export default EmptyState
