import { useState, useCallback } from 'react'
import { Card, Col, Row, Badge, Button, Dropdown } from 'react-bootstrap'

import { times } from '@/utils/common'
import { getImageSrc } from '@/utils/images'

import styles from '../NewsScreen.module.scss'

const NewsCard = ({ item, onEdit, onDelete, onView }) => {
  const [imageError, setImageError] = useState(false)

  const handleImageError = useCallback(() => {
    setImageError(true)
  }, [])

  const sliceText = useCallback((text, maxLength = 80) => {
    if (!text) return ''
    return text.length > maxLength ? text.slice(0, maxLength) + '...' : text
  }, [])

  const getStatusBadge = useCallback(() => {
    if (!item.active) {
      return (
        <Badge bg="secondary" className="me-2">
          <i className="bi bi-pause-circle me-1" />
          Черновик
        </Badge>
      )
    }
    if (item.mailing) {
      return (
        <Badge bg="success" className="me-2">
          <i className="bi bi-envelope me-1" />В рассылке
        </Badge>
      )
    }
    return (
      <Badge bg="primary" className="me-2">
        <i className="bi bi-check-circle me-1" />
        Опубликовано
      </Badge>
    )
  }, [item.active, item.mailing])

  const handleEdit = useCallback(
    (e) => {
      e.stopPropagation()
      onEdit(item.public_id)
    },
    [item.public_id, onEdit]
  )

  const handleDelete = useCallback(
    (e) => {
      e.stopPropagation()
      onDelete(item.public_id)
    },
    [item.public_id, onDelete]
  )

  const handleView = useCallback(
    (e) => {
      e.stopPropagation()
      onView(item.public_id)
    },
    [item.public_id, onView]
  )

  const handleReadClick = useCallback(
    (e) => {
      e.preventDefault()
      e.stopPropagation()
      window.open(`${import.meta.env.VITE_REACT_APP_API}/news/${item.public_id}`, '_blank', 'noopener,noreferrer')
    },
    [item.public_id]
  )

  return (
    <Card className={styles.card}>
      <Card.Header className={styles.cardHeader}>
        <Row className="align-items-center">
          <Col>
            <div className="d-flex align-items-center flex-wrap gap-1">{getStatusBadge()}</div>
          </Col>
          <Col xs="auto">
            <Dropdown>
              <Dropdown.Toggle
                className="border-0"
                variant="outline-secondary"
                bsPrefix=" "
                size="sm"
                id={`dropdown-${item.public_id}`}
              >
                <i className="bi bi-three-dots-vertical" />
              </Dropdown.Toggle>
              <Dropdown.Menu align="end">
                <Dropdown.Item onClick={handleView}>
                  <i className="bi bi-eye me-2" />
                  Просмотр
                </Dropdown.Item>
                <Dropdown.Item onClick={handleEdit}>
                  <i className="bi bi-pencil me-2" />
                  Редактировать
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item onClick={handleDelete} className="text-danger">
                  <i className="bi bi-trash me-2" />
                  Удалить
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </Col>
        </Row>
      </Card.Header>

      <Card.Body className={styles.cardBody}>
        <div className={styles.imgWrapper}>
          {item.banners?.small && !imageError ? (
            <Card.Img
              className={styles.img}
              onError={handleImageError}
              variant="top"
              src={getImageSrc(item.banners.small)}
              alt={item.headline}
              loading="lazy"
            />
          ) : (
            <div className={`${styles.img} d-flex align-items-center justify-content-center`}>
              <i className="bi bi-image text-muted" style={{ fontSize: '2.5rem', opacity: 0.5 }} />
            </div>
          )}
        </div>

        <div className={styles.content}>
          <Card.Title className={styles.cardTitle} title={item.headline}>
            {sliceText(item.headline, 60)}
          </Card.Title>
          {item.subtitle && (
            <Card.Subtitle className={styles.cardSubtitle} title={item.subtitle}>
              {sliceText(item.subtitle, 90)}
            </Card.Subtitle>
          )}
          {item.restriction && (
            <div className="text-muted mt-2" style={{ fontSize: '0.75rem', lineHeight: '1.2' }}>
              <i className="bi bi-exclamation-triangle me-1" />
              {item.restriction}
            </div>
          )}
        </div>
      </Card.Body>

      <Card.Footer className={styles.cardFooter}>
        <Row className="align-items-center">
          <Col>
            <div className={styles.date}>
              <i className="bi bi-calendar3" />
              <span>{times.getFullDate(item.publication_date)}</span>
            </div>
          </Col>
          <Col xs="auto">
            <Button variant="primary" size="sm" onClick={handleReadClick} className={styles.btnRead}>
              <i className="bi bi-arrow-up-right-square me-1" />
              Читать
            </Button>
          </Col>
        </Row>
      </Card.Footer>
    </Card>
  )
}

export default NewsCard
