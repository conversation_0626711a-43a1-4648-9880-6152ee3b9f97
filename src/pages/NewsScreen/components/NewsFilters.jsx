import { Card, Row, Col, Form, InputGroup, Button, Badge } from 'react-bootstrap'

const NewsFilters = ({
  searchQuery,
  statusFilter,
  sortBy,
  onSearchChange,
  onStatusChange,
  onSortChange,
  onClearFilters,
  statusOptions,
  sortOptions,
  filteredCount,
  totalCount,
}) => {
  const hasActiveFilters = searchQuery || statusFilter !== 'all'

  return (
    <Card className="mb-4">
      <Card.Body className="p-3">
        <Row className="g-3 align-items-center">
          {/* Search Input - Left Side */}
          <Col md={12} lg={6}>
            <div className="position-relative">
              <Form.Control
                type="search"
                placeholder="Поиск новостей..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                autoComplete="off"
                className="ps-5"
              />
              <i className="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
              {searchQuery && (
                <Button
                  variant="link"
                  onClick={() => onSearchChange('')}
                  title="Очистить"
                  className="position-absolute top-50 end-0 translate-middle-y me-2 p-1 text-muted"
                  style={{ fontSize: '1.2rem', lineHeight: 1 }}
                >
                  <i className="bi bi-x" />
                </Button>
              )}
            </div>
          </Col>

          {/* Right Side - Filters & Results */}
          <Col md={12} lg={6}>
            <div className="d-flex align-items-center gap-2 justify-content-end">
              {/* Status Filter */}
              <div style={{ minWidth: '160px' }}>
                <Form.Select
                  value={statusFilter}
                  onChange={(e) => onStatusChange(e.target.value)}
                  title="Фильтр по статусу"
                  size="sm"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label} ({option.count})
                    </option>
                  ))}
                </Form.Select>
              </div>

              {/* Sort */}
              <div style={{ minWidth: '140px' }}>
                <Form.Select value={sortBy} onChange={(e) => onSortChange(e.target.value)} title="Сортировка" size="sm">
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Form.Select>
              </div>

              {/* Results */}
              <Badge bg="info" className="fs-6 px-2 py-1">
                {filteredCount} из {totalCount}
              </Badge>

              {/* Clear Button */}
              {hasActiveFilters && (
                <Button variant="outline-secondary" size="sm" onClick={onClearFilters} title="Сбросить фильтры">
                  <i className="bi bi-arrow-clockwise" />
                </Button>
              )}
            </div>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  )
}

export default NewsFilters
