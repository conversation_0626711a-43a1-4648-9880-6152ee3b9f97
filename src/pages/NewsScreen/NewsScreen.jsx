import { useGetNews } from '@/features/news/api'

import { NewsCard, EmptyState, NewsFilters } from './components'
import { useNewsFilters, useNewsActions } from './hooks'
import styles from './NewsScreen.module.scss'
import Layout from '../../components/Layout/Layout'
import PagePreloader from '../../components/PagePreloader/PagePreloader'

export default function NewsScreen() {
  const { data: news, isLoading: isNewsLoading, refetch } = useGetNews()

  const {
    searchQuery,
    statusFilter,
    sortBy,
    filteredNews,
    statusOptions,
    sortOptions,
    setSearchQuery,
    setStatusFilter,
    setSortBy,
    clearFilters,
  } = useNewsFilters(news?.data)

  const { handleDeleteNews, handleEditNews, handleViewNews, handleCreateNews, isDeleting } = useNewsActions(refetch)

  return (
    <Layout title="Управление новостями" onClickAddButton={handleCreateNews} addButtonText="Создать новость">
      <PagePreloader isLoading={isNewsLoading || isDeleting}>
        <NewsFilters
          searchQuery={searchQuery}
          statusFilter={statusFilter}
          sortBy={sortBy}
          onSearchChange={setSearchQuery}
          onStatusChange={setStatusFilter}
          onSortChange={setSortBy}
          onClearFilters={clearFilters}
          statusOptions={statusOptions}
          sortOptions={sortOptions}
          filteredCount={filteredNews.length}
          totalCount={news?.data?.length || 0}
        />

        {filteredNews.length > 0 ? (
          <div className={`${styles.newsGrid} mb-5`}>
            {filteredNews.map((item) => (
              <NewsCard
                key={item.public_id}
                item={item}
                onEdit={handleEditNews}
                onDelete={handleDeleteNews}
                onView={handleViewNews}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            searchQuery={searchQuery}
            statusFilter={statusFilter}
            onClearFilters={clearFilters}
            onCreateNews={handleCreateNews}
          />
        )}
      </PagePreloader>
    </Layout>
  )
}
