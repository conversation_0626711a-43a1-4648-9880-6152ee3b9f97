import { useMemo, useState, useCallback } from 'react'

export const useNewsFilters = (newsData) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('date_desc')

  // Filtered and sorted news
  const filteredNews = useMemo(() => {
    if (!newsData?.length) return []

    let filtered = newsData.filter((item) => {
      // Search filter - поиск по заголовку, подзаголовку и ограничениям
      const searchTerm = searchQuery.toLowerCase().trim()
      const matchesSearch =
        !searchTerm ||
        [item.headline, item.subtitle, item.restriction].some((field) => field?.toLowerCase().includes(searchTerm))

      // Status filter
      const matchesStatus =
        statusFilter === 'all' ||
        (statusFilter === 'published' && item.active && !item.mailing) ||
        (statusFilter === 'draft' && !item.active) ||
        (statusFilter === 'mailing' && item.mailing)

      return matchesSearch && matchesStatus
    })

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date_desc':
          return new Date(b.publication_date) - new Date(a.publication_date)
        case 'date_asc':
          return new Date(a.publication_date) - new Date(b.publication_date)
        case 'title_asc':
          return (a.headline || '').localeCompare(b.headline || '', 'ru')
        case 'title_desc':
          return (b.headline || '').localeCompare(a.headline || '', 'ru')
        default:
          return 0
      }
    })

    return filtered
  }, [newsData, searchQuery, statusFilter, sortBy])

  const statusOptions = useMemo(() => {
    const total = newsData?.length || 0
    const published = newsData?.filter((item) => item.active && !item.mailing).length || 0
    const draft = newsData?.filter((item) => !item.active).length || 0
    const mailing = newsData?.filter((item) => item.mailing).length || 0

    return [
      { value: 'all', label: 'Все новости', count: total, icon: 'bi-list-ul' },
      { value: 'published', label: 'Опубликованные', count: published, icon: 'bi-check-circle' },
      { value: 'draft', label: 'Черновики', count: draft, icon: 'bi-pause-circle' },
      { value: 'mailing', label: 'В рассылке', count: mailing, icon: 'bi-envelope' },
    ]
  }, [newsData])

  const sortOptions = useMemo(
    () => [
      { value: 'date_desc', label: 'Сначала новые', icon: 'bi-sort-down' },
      { value: 'date_asc', label: 'Сначала старые', icon: 'bi-sort-up' },
      { value: 'title_asc', label: 'По названию (А-Я)', icon: 'bi-sort-alpha-down' },
      { value: 'title_desc', label: 'По названию (Я-А)', icon: 'bi-sort-alpha-up' },
    ],
    []
  )

  const clearFilters = useCallback(() => {
    setSearchQuery('')
    setStatusFilter('all')
  }, [])

  return {
    // State
    searchQuery,
    statusFilter,
    sortBy,

    // Computed
    filteredNews,
    statusOptions,
    sortOptions,

    // Actions
    setSearchQuery,
    setStatusFilter,
    setSortBy,
    clearFilters,
  }
}
