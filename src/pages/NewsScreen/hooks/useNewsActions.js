import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

import { AppRoute } from '@/const'
import { useDeleteNews } from '@/features/news/api'
import { useToast } from '@/hooks/useToast'

export const useNewsActions = (refetch) => {
  const deleteNewsMutation = useDeleteNews()
  const navigate = useNavigate()
  const openToast = useToast()

  const handleDeleteNews = useCallback(
    async (id) => {
      if (window.confirm('Вы уверены, что хотите удалить эту новость? Это действие нельзя отменить.')) {
        try {
          await deleteNewsMutation.mutateAsync(id)
          await refetch()
          openToast.success({
            message: 'Новость успешно удалена',
            duration: 3000,
          })
        } catch (error) {
          console.error('Error deleting news:', error)
          openToast.error({
            message: 'Ошибка при удалении новости. Попробуйте еще раз.',
            duration: 4000,
          })
        }
      }
    },
    [deleteNewsMutation, refetch, openToast]
  )

  const handleEditNews = useCallback(
    (id) => {
      navigate(`${AppRoute.NEWS_EDITOR}/${id}`)
    },
    [navigate]
  )

  const handleViewNews = useCallback((id) => {
    window.open(`${import.meta.env.VITE_REACT_APP_API}/news/${id}`, '_blank', 'noopener,noreferrer')
  }, [])

  const handleCreateNews = useCallback(() => {
    navigate(AppRoute.NEWS_EDITOR)
  }, [navigate])

  return {
    handleDeleteNews,
    handleEditNews,
    handleViewNews,
    handleCreateNews,
    isDeleting: deleteNewsMutation.isLoading,
  }
}
