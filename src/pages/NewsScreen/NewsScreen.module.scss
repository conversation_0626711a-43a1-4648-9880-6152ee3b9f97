.card {
  height: 100%;
  width: 100%;
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  transition: box-shadow 0.15s ease-in-out;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
}

// Контейнер для карточек с уменьшенными отступами
.newsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;

  @media (min-width: 576px) {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.25rem;
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 992px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: 1200px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (min-width: 1400px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }
}

.cardHeader {
  background-color: var(--bs-card-bg);
  border-bottom: 1px solid var(--bs-border-color);
  padding: 0.75rem 1rem;
  flex-shrink: 0;
}

.cardBody {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.imgWrapper {
  margin-bottom: 1rem;
  height: 200px;
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--bs-secondary-bg);
  flex-shrink: 0;
}

.img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.cardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--bs-body-color);
  line-height: 1.3;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-shrink: 0;
  min-height: 2.6rem; // Фиксированная минимальная высота для заголовков
}

.cardSubtitle {
  font-size: 0.9rem;
  color: var(--bs-secondary-color);
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
  min-height: 2.8rem; // Фиксированная минимальная высота для подзаголовков
}

.cardFooter {
  background-color: var(--bs-card-bg);
  border-top: 1px solid var(--bs-border-color);
  padding: 0.75rem 1rem;
  flex-shrink: 0;
}

.date {
  font-size: 0.875rem;
  color: var(--bs-secondary-color);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.btnRead {
  // Используем стандартные стили Bootstrap
}

// Стили для пустого состояния
.emptyState {
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
}

// Улучшенные стили для фильтров
.filtersCard {
  border: 1px solid var(--bs-border-color);
  border-radius: 8px;
  background-color: var(--bs-card-bg);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  .card-body {
    padding: 1.5rem;
  }
}

// Исправленные стили для поиска и фильтров
:global(.input-group) {
  .form-control {
    font-size: 0.95rem;
    padding: 0.75rem 1rem;
    border: 2px solid var(--bs-border-color);
    border-left: none;
    background-color: var(--bs-body-bg);

    &:focus {
      border-color: #0d6efd;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      background-color: #fff;
    }

    &::placeholder {
      color: #adb5bd;
      font-size: 0.9rem;
    }
  }

  .input-group-text {
    background-color: var(--bs-secondary-bg);
    border: 2px solid var(--bs-border-color);
    border-right: 2px solid var(--bs-border-color);
    color: var(--bs-secondary-color);
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }

  .btn {
    border: 2px solid var(--bs-border-color);
    border-left: none;
    padding: 0.75rem 1rem;

    &:hover {
      background-color: var(--bs-secondary-bg);
      border-color: var(--bs-border-color);
    }

    &:focus {
      border-color: #0d6efd;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
  }
}

:global(.form-select) {
  font-size: 0.95rem;
  padding: 0.75rem 1rem;
  border: 2px solid var(--bs-border-color);
  background-color: var(--bs-body-bg);
  font-weight: 500;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    background-color: #fff;
  }

  option {
    font-weight: 500;
    padding: 0.5rem;
  }
}

// Улучшенные стили для Badge
:global(.badge) {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.5rem 0.75rem;

  &.fs-6 {
    font-size: 0.9rem !important;
    font-weight: 600;
    padding: 0.5rem 1rem;
  }
}
