.titleWrapper {
  position: absolute;
}

.tabsWrapper {
  width: 100%;
}

.tabs {
  margin-left: auto;
  padding: 2px;
  padding-bottom: 4px;

  width: max-content;

  background-color: #ffffff;
  border-bottom: 0;
  border-radius: 4px;
}

.tabs :global .nav-link.active {
  background-color: #f5f4f9;
}

.tabs :global .nav-link {
  padding: 4px 16px;
  color: #646d8f;
  border: 0;

  &.active::before {
    display: none;
  }
}

@media (max-width: 767px) {
  .titleWrapper {
    position: unset;
  }

  .tabs {
    margin-left: 5px;
  }
}

@media (max-width: 567px) {
  .tabs {
    width: unset;
  }
}
