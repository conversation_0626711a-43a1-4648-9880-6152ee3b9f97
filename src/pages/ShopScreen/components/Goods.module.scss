.sortingWrapper {
  padding-left: 16px;
  padding-right: 36px;

  display: grid;
  grid-template-columns: 1fr 5.5fr 1fr 1fr 1fr 2fr;
  justify-items: center;
}

.sortBtn {
  position: relative;
  padding-right: 14px;

  font-size: 12px;
  color: #646d8f;
  font-weight: 500;
  text-transform: uppercase;

  background: none;
  border: none;

  &::after {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);

    width: 10px;
    height: 10px;

    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;

    content: '';
  }
}

.sortBtnDefault {
  &::after {
    right: 2px;

    width: 9px;
    height: 9px;

    background-image: url('../../../assets/img/icons/icon-sort.svg');
  }
}

.sortBtnAsc {
  &::after {
    background-image: url('../../../assets/img/icons/icon-ascending.svg');
  }
}

.sortBtnDesc {
  &::after {
    background-image: url('../../../assets/img/icons/icon-descending.svg');
  }
}
