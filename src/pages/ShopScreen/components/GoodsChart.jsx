import useAxios from 'axios-hooks'
import moment from 'moment'
import { useEffect, useState, useCallback, useRef } from 'react'
import { Alert, Button, Col, Form, FormControl, FormGroup, Row } from 'react-bootstrap'
import Select from 'react-select'

import DownloadButton from '../../../components/EventInfo/components/DownloadButton/DownloadButton'
import LineChart from '../../../components/EventInfo/components/LineChart'
import Loader from '../../../components/Loader/Loader'
import { APIRoute } from '../../../const'
import { useTheme } from '../../../contexts/ThemeContext'

const generateProductOptions = (goods) => {
  const defaultOption = { value: '', label: 'Все' }
  const options = goods.map((item) => ({
    value: item.public_id,
    label: `${item.title} — ${item.public_id}`,
  }))

  return [defaultOption, ...options]
}

function GoodsChart({ goods, publicId }) {
  const { effectiveTheme } = useTheme()
  const [{ loading }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  const [toggleState, setToggleState] = useState('count')
  const [datesProduct, setDatesProduct] = useState(null)
  const [formData, setFormData] = useState({
    product: {
      public_id: publicId || '',
    },
    start_date: moment().subtract(1, 'month'),
    end_date: moment(),
  })

  const [downloadButtonData, setDownloadButtonData] = useState({
    start_date: moment().subtract(1, 'month').toISOString(),
    end_date: moment().toISOString(),
    status: 'paid',
  })

  const formDataRef = useRef(formData)

  // Update ref when formData changes
  useEffect(() => {
    formDataRef.current = formData
  }, [formData])

  const getDatesProducts = useCallback(
    (data) => {
      const currentData = data || { ...formDataRef.current }

      // Convert moment objects to ISO strings to avoid circular reference issues
      const body = {
        ...currentData,
        start_date: moment(currentData.start_date).toISOString(),
        end_date: moment(currentData.end_date).toISOString(),
      }

      api({ url: APIRoute.DATES_PRODUCT, method: 'POST', data: body }).then((r) => {
        r.status === 200 && r.data?.values?.length > 0 && setDatesProduct(r.data.values)
      })
    },
    [api]
  )

  useEffect(() => {
    if (publicId) {
      getDatesProducts()
      setDownloadButtonData((prev) => ({ ...prev, product_public_id: publicId }))
    }
  }, [publicId, getDatesProducts])

  const handleChangeProduct = (evt) => {
    const value = evt.value

    if (value === '') {
      const newFormData = { ...formData }
      const newDownloadButtonData = { ...downloadButtonData }

      delete newFormData.product
      delete newDownloadButtonData.product_public_id

      setFormData({ ...newFormData })
      setDownloadButtonData({ ...newDownloadButtonData })
    } else {
      setFormData({ ...formData, product: { public_id: value } })
      setDownloadButtonData({ ...downloadButtonData, product_public_id: value })
    }
  }

  // Create dynamic styles that respond to theme changes
  const dynamicSelectStyles = {
    container: (provided) => ({
      ...provided,
      height: '100%',
    }),
    control: (provided, state) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        border: darkTheme ? '1px solid #495057' : '1px solid #ced4da',
        borderRadius: '0.375rem',
        minHeight: '100%',
        backgroundColor: darkTheme ? '#212529' : '#ffffff',
        boxShadow: state.isFocused
          ? darkTheme
            ? '0 0 0 0.25rem rgba(13, 110, 253, 0.25)'
            : '0 0 0 0.25rem rgba(13, 110, 253, 0.25)'
          : 'none',
        '&:hover': {
          borderColor: darkTheme ? '#6c757d' : '#86b7fe',
        },
      }
    },
    menu: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#212529' : '#ffffff',
        border: darkTheme ? '1px solid #495057' : '1px solid #ced4da',
        borderRadius: '0.375rem',
        boxShadow: darkTheme ? '0 0.5rem 1rem rgba(0, 0, 0, 0.15)' : '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
      }
    },
    option: (provided, state) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: state.isSelected
          ? '#0d6efd'
          : state.isFocused
            ? darkTheme
              ? '#495057'
              : '#e9ecef'
            : 'transparent',
        color: state.isSelected ? '#ffffff' : darkTheme ? '#ffffff' : '#212529',
        '&:hover': {
          backgroundColor: state.isSelected ? '#0d6efd' : darkTheme ? '#495057' : '#e9ecef',
        },
      }
    },
    placeholder: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#6c757d' : '#6c757d',
      }
    },
    singleValue: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#212529',
      }
    },
    input: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#212529',
      }
    },
    indicatorSeparator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#495057' : '#ced4da',
      }
    },
    dropdownIndicator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#6c757d' : '#6c757d',
        '&:hover': {
          color: darkTheme ? '#adb5bd' : '#495057',
        },
      }
    },
  }

  const handleChangeDate = (evt) => {
    const newDate = new Date(evt.target.value)
    setFormData({ ...formData, [evt.target.name]: newDate })
    setDownloadButtonData({ ...downloadButtonData, [evt.target.name]: newDate.toISOString() })
  }

  return (
    <>
      <Row className="g-3 mb-4">
        {!publicId && (
          <Col md={6}>
            <FormGroup className="mb-0">
              <Row className="d-block">
                <Col>
                  <Form.Label>Выбор товара:</Form.Label>
                </Col>
                <Col>
                  <Select
                    onChange={handleChangeProduct}
                    options={goods?.length > 0 && generateProductOptions(goods)}
                    styles={dynamicSelectStyles}
                    placeholder="Поиск по названию или артикулу"
                  />
                </Col>
              </Row>
            </FormGroup>
          </Col>
        )}

        <Col md={3}>
          <Col>
            <Form.Label>От</Form.Label>
          </Col>
          <Col>
            <FormControl
              type="datetime-local"
              name="start_date"
              onChange={handleChangeDate}
              defaultValue={moment(formData.start_date).format('YYYY-MM-DDTHH:mm:ss')}
            />
          </Col>
        </Col>

        <Col md={3}>
          <Col>
            <Form.Label>До</Form.Label>
          </Col>
          <Col>
            <FormControl
              type="datetime-local"
              name="end_date"
              onChange={handleChangeDate}
              defaultValue={moment(formData.end_date).format('YYYY-MM-DDTHH:mm:ss')}
            />
          </Col>
        </Col>

        <Col className="align-self-end" md="auto">
          <Button onClick={() => getDatesProducts()} type="button" disabled={loading}>
            Посмотреть
          </Button>
        </Col>
        <Col className="align-self-end">
          <DownloadButton
            url={APIRoute.BOUGHT_PRODUCTS}
            fileName="bought-products.xlsx"
            label="Выгрузить .xlsx"
            data={downloadButtonData}
          />
        </Col>
      </Row>

      <Loader isLoading={loading}>
        {datesProduct?.length > 0 && (
          <>
            <Row className="mb-3">
              <Col md={12}>
                <Button
                  variant={toggleState === 'count' ? 'primary' : 'light'}
                  type="button"
                  onClick={() => setToggleState('count')}
                >
                  Количество
                </Button>{' '}
                <Button
                  variant={toggleState === 'cost' ? 'primary' : 'light'}
                  type="button"
                  onClick={() => setToggleState('cost')}
                >
                  Стоимость
                </Button>
              </Col>
              <Col style={{ overflow: 'auto' }}>
                <LineChart eventData={datesProduct} toggle={toggleState} />
              </Col>
            </Row>
            <Row>
              <Col>
                <p>
                  Всего: {datesProduct?.length > 0 && datesProduct.reduce((a, b) => a + b.inday_count, 0)} шт.{' '}
                  {datesProduct?.length > 0 && datesProduct.reduce((a, b) => a + b.inday_cost, 0)} &#8381;
                </p>
              </Col>
            </Row>
          </>
        )}
      </Loader>

      {datesProduct?.length === 0 && <Alert variant="warning">За выбранный период нет данных!</Alert>}
    </>
  )
}

export default GoodsChart
