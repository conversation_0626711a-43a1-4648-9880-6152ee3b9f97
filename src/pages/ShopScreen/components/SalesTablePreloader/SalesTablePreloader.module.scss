.container {
  height: 400px;

  background:
    linear-gradient(0.25turn, transparent, #fff, transparent), linear-gradient(#f5f7f9, #f5f7f9),
    linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9),
    linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9),
    linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9),
    linear-gradient(#f5f7f9, #f5f7f9), linear-gradient(#f5f7f9, #f5f7f9), #ffffff;
  background-repeat: no-repeat;
  background-size:
    100px 100%,
    80px 20px,
    80px 20px,
    80px 20px,
    80px 20px,
    100% 40px,
    100% 40px,
    100% 40px,
    100% 40px,
    100% 40px,
    100% 40px,
    100% 40px,
    100% 40px;
  background-position:
    -10% 0,
    20px 16px,
    315px 16px,
    545px 16px,
    785px 16px,
    0 43px,
    0 88px,
    0 133px,
    0 178px,
    0 223px,
    0 268px,
    0 313px,
    0 358px;

  animation: loading 2s infinite;

  cursor: progress;
}

@keyframes loading {
  to {
    background-position:
      110% 0,
      20px 16px,
      315px 16px,
      545px 16px,
      785px 16px,
      0 43px,
      0 88px,
      0 133px,
      0 178px,
      0 223px,
      0 268px,
      0 313px,
      0 358px;
  }
}
