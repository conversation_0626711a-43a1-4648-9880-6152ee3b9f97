import useAxios from 'axios-hooks'
import moment from 'moment'
import { useCallback, useEffect, useState } from 'react'
import { <PERSON><PERSON>, Card, Col, FloatingLabel, Form, FormControl, FormSelect, Row, Spinner } from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { Link } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import { ItemLabelValue } from '@/components/ItemLabelValue/ItemLabelValue'
import PageSearch from '@/components/PageSearch/PageSearch'

import { APIRoute } from '@/const'
import { formatPhoneNumber, getTicketStatus } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

import styles from './ProductOrders.module.scss'

const PAGE_LIMIT_PAGINATION = 10

export const ProductOrders = () => {
  const [{ loading: isLoading }, api] = useAxios(
    {
      url: APIRoute.SHOP_ORDERS,
      method: 'POST',
    },
    { manual: true }
  )

  const {
    watch,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      status: 'paid',
      start_date: moment().utc().subtract(1, 'months').format('YYYY-MM-DDTHH:mm'),
      end_date: moment().utc().format('YYYY-MM-DDTHH:mm'),
    },
  })

  const [values, setValues] = useState([])
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [valuesRender, setValuesRender] = useState([]) // для отрисовки на странице

  // Используется useCallback, чтобы ссылка на функцию оставалась стабильной между рендерами
  const handleClickSubmit = useCallback(
    (data) => {
      api({ data }).then((r) => {
        setValues(r.data)
      })
    },
    [api]
  )

  // Запрашивает данные при монтировании компонента.
  useEffect(() => {
    handleSubmit(handleClickSubmit)()
  }, [handleSubmit, handleClickSubmit])

  const setValueAsRelated = (v) => {
    if (v === 'true') {
      return true
    } else if (v === 'false') {
      return false
    } else if (v === 'all') {
      return undefined
    }
  }

  return (
    <>
      <Form className="mb-5" onSubmit={handleSubmit(handleClickSubmit)}>
        <Row className="mb-4">
          <Col>
            <Form.Group>
              <FloatingLabel controlId="statusLabel" label="Фильтр по статусу">
                <FormSelect
                  {...register('status', {
                    setValueAs: (v) => checkSetValue(v, {}, 'text'),
                  })}
                  isInvalid={errors?.status}
                  placeholder="Дата начала"
                >
                  <option value="all">все статусы</option>
                  <option value="paid">оплачен</option>
                  <option value="canceled">возврат</option>
                  <option value="created">не оплачен</option>
                </FormSelect>
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col>
            <Form.Group>
              <FloatingLabel controlId="isRelatedLabel" label="Товары">
                <FormSelect
                  {...register('is_related', {
                    setValueAs: (v) => setValueAsRelated(v),
                  })}
                  isInvalid={errors?.is_related}
                  placeholder=""
                >
                  <option value="all">все</option>
                  <option value="true">сопутствующие</option>
                  <option value="false">магазин</option>
                </FormSelect>
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col>
            <Form.Group>
              <FloatingLabel controlId="startDateLabel" label="Дата начала">
                <FormControl
                  {...register('start_date', {
                    setValueAs: (v) => checkSetValue(v, {}, 'datetime-local'),
                  })}
                  type="datetime-local"
                  isInvalid={errors?.start_date}
                  placeholder="Дата начала"
                />
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col>
            <Form.Group>
              <FloatingLabel controlId="endDateLabel" label="Дата окончания">
                <FormControl
                  {...register('end_date', {
                    setValueAs: (v) => checkSetValue(v, {}, 'datetime-local'),
                  })}
                  type="datetime-local"
                  isInvalid={errors?.end_date}
                  placeholder="Дата окончания"
                />
              </FloatingLabel>
            </Form.Group>
          </Col>

          <Col className="align-content-center" md="auto">
            <Button type="submit" size="lg" disabled={isLoading} style={{ minWidth: '121px' }}>
              {isLoading ? <Spinner animation="grow" size="sm" /> : 'Получить'}
            </Button>
          </Col>
        </Row>

        <Row>
          <Col style={{ marginLeft: 'auto' }} md="auto">
            <DownloadButton url={APIRoute.DATES_SOLDGOODS_XLSX} fileName="dates-soldgoods.xlsx" data={watch()} />
          </Col>
        </Row>
      </Form>

      <Row className="mb-3">
        <Col>
          <PageSearch values={values} setValues={setFilteredValues} />
        </Col>
      </Row>

      {valuesRender.map((item) => (
        <Card className="mb-1" key={item.public_id}>
          <Card.Body>
            <Row className="mb-2">
              <Col>
                <Link className={styles.link} to={`/shop/form/${item.info.product.public_id}`}>
                  {item.info.product.title}
                </Link>
              </Col>
              <Col md="auto">{getTicketStatus(item.status)}</Col>
            </Row>

            <Row className="mb-3">
              <Col>
                <ItemLabelValue value={unixToMoment(item.created).format('D MMMM YYYY, HH:mm')} label="дата заказа" />
              </Col>
              <Col>
                <ItemLabelValue value={item.info.proportion.name} label="размер" />
              </Col>
              <Col>
                <ItemLabelValue value={`${item.info.cost} ₽`} label="цена" />
              </Col>
              <Col>
                <ItemLabelValue
                  value={
                    <Link className={styles.link} to={`/user/${item.user_public_id}`}>
                      {item.user_public_id}
                    </Link>
                  }
                  label="пользователь"
                />
              </Col>
              <Col>
                <ItemLabelValue
                  value={
                    <Link className={styles.link} to={`/orders/${item.order_public_id}`}>
                      {item.order_public_id}
                    </Link>
                  }
                  label="ID заказа"
                />
              </Col>
            </Row>

            <p>Доставка:</p>
            <Row className="mb-3">
              <Col>
                <ItemLabelValue value={item.delivery.name} label="метод" />
              </Col>
              <Col>
                <ItemLabelValue
                  value={`${item.order_delivery.first_name || '-'} ${item.order_delivery.last_name || '-'}`}
                  label="имя фамилия"
                />
              </Col>
              <Col>
                <ItemLabelValue value={item.order_delivery.address} label="адрес" />
              </Col>
              <Col>
                <ItemLabelValue value={formatPhoneNumber(item.order_delivery.phone)} label="телефон" />
              </Col>
              <Col>
                <ItemLabelValue value={item.order_delivery.comment} label="примечание" />
              </Col>
            </Row>
          </Card.Body>
        </Card>
      ))}

      <div className="mt-3">
        <AdvancedPagination values={filteredValues} setValues={setValuesRender} limitValues={PAGE_LIMIT_PAGINATION} />
      </div>
    </>
  )
}
