.card {
  padding: 40px;
  min-width: 500px;
}

.buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.tag {
  cursor: pointer;
  background-color: darken(#5debab, 15);
  border: 1px solid #5debab;
  color: white;
  margin-left: 10px;
  padding-left: 10px;
  padding-right: 5px;
  border-radius: 4px;
  margin-top: 5px;
  display: inline-flex;
}

.close {
  width: 10px;
  height: 10px;
  background-color: rgb(225, 9, 9);
  clip-path: polygon(
    20% 0%,
    0% 20%,
    30% 50%,
    0% 80%,
    20% 100%,
    50% 70%,
    80% 100%,
    100% 80%,
    70% 50%,
    100% 20%,
    80% 0%,
    50% 30%
  );
  margin-left: 5px;
  margin-top: 5px;
}

@media (max-width: 767px) {
  .card {
    min-width: 320px;
  }
}
