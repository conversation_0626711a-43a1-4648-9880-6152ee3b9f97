import { useState, useEffect, useMemo } from 'react'
import { Button, Card, Col, Form, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'

import { useGetCountryRegions } from '@/features/cities/api/getCountryRegions'
import { useGetRegionCities } from '@/features/cities/api/getRegionCities'
import { useCreateDelivery } from '@/features/shop/api/createDelivery'
import { useDeleteDelivery } from '@/features/shop/api/deleteDelivery'
import { useDeliveryList } from '@/features/shop/api/getDeliveryList'
import { useUpdateDelivery } from '@/features/shop/api/updateDelivery'

import styles from './Delivery.module.scss'

const getIdLocations = (locations) => {
  const idLocations = []
  locations.forEach((el) => idLocations.push({ id: el.id }))
  return idLocations
}

const deliveryTypes = {
  product: 'Товар',
  ticket: 'Билет',
  book: 'Книга',
  troyka: 'Карта Тройка',
}

const deliveryType = [
  {
    value: 'product',
    label: 'Товар',
  },
  {
    value: 'ticket',
    label: 'Билет',
  },
  {
    value: 'book',
    label: 'Книга',
  },
  {
    value: 'troyka',
    label: 'Карта Тройка',
  },
]

export const Tag = ({ children, ...props }) => {
  if (!children) return null
  return (
    <span className={styles.tag} {...props}>
      {children}
      <span className={styles.close} />
    </span>
  )
}

export const Delivery = () => {
  const { data: deliveryResponse } = useDeliveryList()
  const delivery = deliveryResponse?.data?.values || []

  const { mutate: createDeliveryMutation } = useCreateDelivery()
  const { mutate: updateDeliveryMutation } = useUpdateDelivery()
  const { mutate: deleteDeliveryMutation } = useDeleteDelivery()

  const { data: regionsData } = useGetCountryRegions('RU')
  const regions = regionsData?.data?.values || []

  const [isModal, setIsModal] = useState(false)
  const [nameDelivery, setNameDelivery] = useState('')
  const [costDelivery, setCostDelivery] = useState('')
  const [region, setRegion] = useState(null)
  const [types, setTypes] = useState([])
  const [isAllCities, setIsAllCities] = useState(false)
  const [isAddRegion, setAddRegion] = useState(false)
  const [isEditModal, setIsEditModal] = useState(false)
  const [currentEditDelivery, setCurrentEditDelivery] = useState({})
  const [currentPublicId, setCurrentPublicId] = useState('')
  const [isConfirmModal, setIsConfirmModal] = useState(false)
  const [currentLocations, setCurrentLocations] = useState([])
  const [oldLocations, setOldLocations] = useState([])
  const [allInfoLocation, setAllInfoLocation] = useState([])
  const [isPickup, setIsPickup] = useState(false)
  const [isPublic, setIsPublic] = useState(false)

  const { data: citiesData } = useGetRegionCities(region)
  const city = useMemo(() => citiesData?.data?.values || [], [citiesData?.data?.values])

  const handleClickAddDelivery = () => {
    setIsEditModal(false)
    setIsModal(true)
    setIsPublic(true)
  }

  const handleCreateDelivery = () => {
    const idLocations = getIdLocations(allInfoLocation)

    createDeliveryMutation({
      cost: costDelivery,
      location: isAllCities ? [] : idLocations,
      all_cities: !idLocations.length ? true : isAllCities,
      types: types,
      name: nameDelivery,
      pickup: isPickup,
      public: isPublic,
    })

    handleCloseModal()
  }

  const handleEditDelivery = () => {
    const indexMap = new Map()

    currentLocations.forEach((el) => indexMap.set(el.id, el.name_ru))

    const filteredLocations = allInfoLocation.filter((el) => {
      const key = indexMap.get(el.id)
      if (!key) return el
    })

    const filteredCurrentLocation = currentLocations.filter((el) => el.id)

    const location = [...filteredLocations, ...filteredCurrentLocation]

    const idLocations = getIdLocations(location)

    updateDeliveryMutation({
      public_id: currentEditDelivery.public_id,
      cost: costDelivery || currentEditDelivery?.cost,
      location: isAllCities ? [] : idLocations,
      all_cities: !idLocations.length ? true : isAllCities,
      types: types,
      name: nameDelivery || currentEditDelivery?.name,
      pickup: isPickup,
      public: isPublic,
    })

    handleCloseModal()
  }

  const handleChangeCities = (evt) => {
    const selectedOptions = evt.target.selectedOptions
    const items = []

    for (let i = 0; i < selectedOptions.length; i++) {
      items.push(JSON.parse(selectedOptions[i].value))
    }

    if (items.length) {
      setAllInfoLocation(items)
    }
  }

  const handleChangeTypes = (evt) => {
    const selectedOptions = evt.target.selectedOptions
    const formatsValue = []

    for (let i = 0; i < selectedOptions.length; i++) {
      formatsValue.push(selectedOptions[i].value)
    }
    setTypes(formatsValue)
  }

  const handleCloseModal = () => {
    setIsModal(false)
    setAllInfoLocation([])
    setOldLocations([])
    setCurrentEditDelivery([])
    setIsPickup(false)
    setIsPublic(false)
  }

  useEffect(() => {
    const items = []

    const allCities = allInfoLocation.find((el) => el.id === 'all')

    if (allCities) {
      city.forEach((el) => {
        items.push(el)
      })

      setAllInfoLocation(items)
    }
  }, [city, allInfoLocation])

  const handleChangeLocations = () => {
    const newLocations = []
    allInfoLocation.forEach((item) => {
      if (!oldLocations.filter((el) => el.id === item.id).length) {
        newLocations.push({ name_ru: item.name_ru, id: item.id })
      }
    })

    setOldLocations([...oldLocations, ...newLocations])
    setAddRegion(false)
  }
  const handleClickEdit = (el) => {
    const currentLocations = []
    el?.location?.forEach((location) => {
      currentLocations.push(location)
    })

    if (currentLocations && currentLocations[0]) {
      setCurrentLocations(currentLocations)
    }

    setCurrentEditDelivery(el)
    setOldLocations(el.location)
    setCurrentPublicId(el.public_id)
    setIsAllCities(el.all_cities)
    setTypes(el.types)
    setIsEditModal(true)
    setIsModal(true)
    setIsPickup(el.pickup)
    setIsPublic(el.public)
  }

  const handleClickDelete = (el) => {
    setCurrentPublicId(el.public_id)
    setIsConfirmModal(true)
  }

  const handleDeleteDelivery = () => {
    deleteDeliveryMutation({ public_id: currentPublicId })
    setIsConfirmModal(false)
  }

  const handleDeleteCurrentLocation = (item) => {
    setOldLocations(oldLocations.filter((el) => el.id !== item?.id))
    setCurrentLocations(currentLocations.filter((el) => el.id !== item?.id))
    setAllInfoLocation(allInfoLocation.filter((el) => el.id !== item?.id))
  }

  return (
    <Row style={{ display: 'flex', flexDirection: 'column' }}>
      <Col md="auto" style={{ marginLeft: 'auto', marginBottom: '30px' }}>
        <Button onClick={handleClickAddDelivery} variant="success">
          <i className="bi bi-plus-circle me-2" />
          Добавить
        </Button>
      </Col>
      <Col sm="auto" style={{ display: 'grid', gap: '20px' }}>
        {delivery.map((el) => (
          <Card className={styles.card} key={el.public_id}>
            <div style={{ display: 'flex', gap: '20px' }}>
              <div>
                <Card.Text>Способ доставки: {el?.name}</Card.Text>
                <Card.Text className={styles.label}>Цена доставки: {el?.cost}</Card.Text>
                <Card.Text className={styles.label}>Самовывоз: {el?.pickup ? 'Да' : 'Нет'}</Card.Text>
              </div>
              <div>
                <Card.Text className={styles.label}>
                  Типы обьектов доставки:{' '}
                  {el?.types?.map((type) => (
                    <span key={type}>{type && deliveryTypes[type] && deliveryTypes[type]} </span>
                  ))}
                </Card.Text>
                <Card.Text className={styles.label}>
                  Города доставки:{' '}
                  {el?.location?.length > 0 &&
                    el?.location?.map((item, i) => (
                      <span key={item?.id || `location-${i}`}>
                        {item?.name_ru ? `${item?.name_ru}${i + 1 === el?.location.length ? ' ' : ', '}` : 'Все города'}
                      </span>
                    ))}
                </Card.Text>
                <Card.Text className={styles.label}>Отображать: {el?.public ? 'Да' : 'Нет'}</Card.Text>
              </div>
            </div>
            <div className={styles.buttons}>
              <Button onClick={() => handleClickEdit(el)}>Редактировать</Button>
              <Button onClick={() => handleClickDelete(el)}>Удалить</Button>
            </div>
          </Card>
        ))}
      </Col>

      <Modal show={isModal} onHide={handleCloseModal}>
        <Modal.Header closeButton>{isEditModal ? 'Редактировать доставку' : 'Добавить доставку'}</Modal.Header>
        <Modal.Body>
          <Form>
            <FormGroup controlId="nameForm">
              <Row>
                <Col xs={5} key="name-label">
                  <Form.Label>Название доставки</Form.Label>
                </Col>
                <Col key="name-input">
                  <FormControl
                    type="text"
                    name="name"
                    style={{ border: '1px solid black' }}
                    placeholder="delivery name"
                    onChange={(e) => setNameDelivery(e.target.value)}
                    defaultValue={isEditModal ? currentEditDelivery?.name : ''}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>
            <FormGroup controlId="costForm">
              <Row>
                <Col xs={5} key="cost-label">
                  <Form.Label>Цена доставки</Form.Label>
                </Col>
                <Col key="cost-input">
                  <FormControl
                    type="text"
                    name="cost"
                    style={{ border: '1px solid black' }}
                    placeholder="delivery cost"
                    onChange={(e) => setCostDelivery(e.target.value)}
                    defaultValue={isEditModal ? currentEditDelivery?.cost : ''}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>
            <FormGroup controlId="typeForm">
              <Row>
                <Col xs={5} key="type-label">
                  <Form.Label>Тип</Form.Label>
                </Col>
                <Col key="type-input">
                  <FormControl as="select" name="type" onChange={handleChangeTypes} className="mb-3" multiple>
                    <option disabled>Выберите тип</option>
                    {deliveryType.map((el, i) => (
                      <option key={`${el.value}${i}`} value={el.value}>
                        {el.label}
                      </option>
                    ))}
                  </FormControl>
                </Col>
              </Row>
            </FormGroup>
            {isEditModal && (
              <span>
                Текущий тип:{' '}
                {currentEditDelivery?.types?.map((el) => (
                  <span key={el}>{deliveryTypes[el]} </span>
                ))}
              </span>
            )}
            {!isAllCities && (
              <FormGroup style={{ marginBottom: '20px', marginTop: '20px', display: 'flex' }}>
                <Col xs={6}>
                  <Form.Label>Регион</Form.Label>
                </Col>
                <Row>
                  <Button onClick={() => setAddRegion(true)} variant="success">
                    Добавить регион
                  </Button>
                </Row>
              </FormGroup>
            )}

            <FormGroup style={{ marginBottom: '20px', marginTop: '20px', display: 'flex' }}>
              <Row>
                <Col>
                  <Form.Check
                    type="checkbox"
                    id="check_pickup"
                    label="Самовывоз"
                    name="pickup"
                    value={isPickup}
                    onChange={() => setIsPickup(!isPickup)}
                    defaultChecked={isPickup || currentEditDelivery?.pickup}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup style={{ marginBottom: '20px', marginTop: '20px', display: 'flex' }}>
              <Row>
                <Col>
                  <Form.Check
                    type="checkbox"
                    id="check_public"
                    label="Отображать"
                    name="public"
                    value={isPublic}
                    onChange={() => setIsPublic(!isPublic)}
                    defaultChecked={isPublic || currentEditDelivery?.public}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup style={{ marginBottom: '20px', marginTop: '20px', display: 'flex' }}>
              <Row>
                <Col>
                  <Form.Check
                    type="checkbox"
                    id="check_delivery"
                    label="Все города России"
                    name="delivery"
                    value={isAllCities}
                    onChange={() => setIsAllCities(!isAllCities)}
                    checked={isAllCities}
                  />
                </Col>
              </Row>
            </FormGroup>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {!isAllCities && (
                <span>
                  Выбранные города:{' '}
                  {oldLocations.length > 0 &&
                    oldLocations.map((item) => (
                      <Tag onClick={() => handleDeleteCurrentLocation(item)} key={item?.id}>
                        {item?.name_ru ? `${item?.name_ru}` : null}
                      </Tag>
                    ))}
                </span>
              )}
            </div>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="link" onClick={handleCloseModal}>
            Отменить
          </Button>
          <Button onClick={isEditModal ? handleEditDelivery : handleCreateDelivery}>Сохранить</Button>
        </Modal.Footer>
      </Modal>
      <Modal show={isAddRegion} onHide={() => setAddRegion(false)}>
        <Modal.Header closeButton>Добавить регион</Modal.Header>
        <Modal.Body>
          {!isAllCities && (
            <FormGroup controlId="regionForm">
              <Row style={{ marginBottom: '40px' }}>
                <Col xs={6} key="region-label">
                  <Form.Label>Регион</Form.Label>
                </Col>
                <Col key="region-input">
                  <FormControl
                    as="select"
                    aria-label="region"
                    name="region"
                    onChange={(e) => setRegion(e.target.value)}
                    required
                    value={region || ''}
                  >
                    <option>Выберите регион</option>
                    {regions.map((el) => (
                      <option key={el.id} value={el.id}>
                        {el.name_ru}
                      </option>
                    ))}
                  </FormControl>
                </Col>
              </Row>
            </FormGroup>
          )}
          {!isAllCities && city && city.length > 0 ? (
            <FormGroup controlId="regionForm">
              <Row>
                <Col xs={5} key="cities-label">
                  <Form.Label>Города</Form.Label>
                </Col>
                <Col key="cities-input">
                  <FormControl as="select" name="cities" onChange={handleChangeCities} multiple>
                    <option style={{ marginBottom: '10px' }} disabled>
                      Выберите города
                    </option>
                    <option style={{ marginBottom: '10px' }} value={JSON.stringify({ id: 'all', name_ru: 'all' })}>
                      Все города
                    </option>
                    {city.map((el) => (
                      <option key={el.id} value={JSON.stringify(el)}>
                        {el.name_ru}
                      </option>
                    ))}
                  </FormControl>
                </Col>
              </Row>
            </FormGroup>
          ) : null}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="link" onClick={() => setAddRegion(false)}>
            Отменить
          </Button>
          <Button onClick={handleChangeLocations}>Сохранить</Button>
        </Modal.Footer>
      </Modal>
      <Modal show={isConfirmModal} onHide={() => setIsConfirmModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Удалить доставку {currentEditDelivery?.name}?</Modal.Title>
        </Modal.Header>
        <Modal.Footer>
          <Button variant="link" onClick={() => setIsConfirmModal(false)}>
            Отменить
          </Button>
          <Button variant="danger" onClick={handleDeleteDelivery}>
            Удалить
          </Button>
        </Modal.Footer>
      </Modal>
    </Row>
  )
}
