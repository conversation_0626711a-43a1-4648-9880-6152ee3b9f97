.card {
  position: relative;
  margin-bottom: 8px;

  &:hover .buttons {
    opacity: 1;
  }
}

.cardInner {
  margin-bottom: 16px;

  display: grid;
  grid-template-columns: 30px 65px 1fr;
  gap: 12px;

  cursor: pointer;
}

.indexWrapper {
  height: 85px;
}

.pictureContainer {
  background-color: var(--bs-secondary-bg);
}

.imageLink {
  position: relative;

  display: block;
  width: 65px;
  height: 85px;

  &:not(:first-child) {
    display: none;
  }

  &:hover::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    display: block;

    background-color: rgba(var(--bs-body-bg-rgb), 0.5);
    background-image: url('../../../../assets/img/icons/icon-zoom.svg');
    background-repeat: no-repeat;
    background-position: center;

    content: '';
    z-index: 10;
  }
}

.picture {
  width: 100%;
  height: 100%;

  object-fit: cover;

  border-radius: 4px;
}

.content {
  display: grid;
  grid-template-columns: 5fr 1fr 1fr 1fr 2fr;
  grid-template-rows: auto auto auto;
  align-items: start;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: var(--bs-body-color);
}

.publicIdWrap,
.priceWrap,
.badge,
.proportionsWrapper {
  justify-self: center;
}

.publicIdWrap,
.priceWrap {
  color: var(--bs-body-color);
}

.badge {
  margin: 0 auto;
  padding: 8px 10px;

  width: max-content;

  font-size: 14px;
  font-weight: 500;
  line-height: 0.7;

  border-radius: 700px;
}

.badgeSuccess {
  background-color: var(--bs-success-bg-subtle);
  color: var(--bs-success-text-emphasis);
}

.badgeWarning {
  background-color: var(--bs-warning-bg-subtle);
  color: var(--bs-warning-text-emphasis);
}

.badgeDanger {
  background-color: var(--bs-danger-bg-subtle);
  color: var(--bs-danger-text-emphasis);
}

.subgroupWrap {
  margin-bottom: 4px;
  grid-column: 1 / -2;
}

.subgroup {
  padding: 3px 6px;
  padding-bottom: 6px;

  display: inline-block;

  line-height: 1;
  text-transform: lowercase;
  color: var(--bs-body-color);

  background-color: var(--bs-secondary-bg);
  border-radius: 4px;
}

.descriptionWrap {
  max-width: 58vw;
  grid-column: 1 / -2;
}

.description {
  margin: 0;
  color: var(--bs-body-color);
}

.descHidden {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.proportionsWrapper {
  padding-right: 36px;
  grid-column: 5 / 6;
  grid-row: 1 / 4;
  color: var(--bs-body-color);
}

.proportionsWrapperHidden {
  position: relative;
  height: 80px;

  overflow-y: hidden;

  &::before {
    position: absolute;
    left: 0;
    bottom: 0;

    display: block;
    height: 60px;
    width: 88%;
    background: var(--bs-body-bg);
    background: linear-gradient(0deg, var(--bs-body-bg) 0%, rgba(var(--bs-body-bg-rgb), 0) 100%);

    content: '';
  }
}

.tabsWrapper {
  margin-right: 25px;
  margin-left: 25px;
}

.buttons {
  position: absolute;
  top: 10px;
  right: 28px;

  display: block;
  width: max-content;

  opacity: 0;
  transition: opacity 0.3s;
}

.btnWrapper {
  padding: 0;
  width: 24px;
}

.btnEdit {
  width: 16px;
  height: 16px;

  background-color: transparent;
  background-image: url('../../../../assets/img/icons/icon_edit.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  border: 0;

  // Адаптация иконок к темам
  // По умолчанию иконки тёмные, инвертируем для тёмной темы
  :global([data-bs-theme='dark']) & {
    filter: invert(1);
  }

  // Для автоматической темы используем медиа-запрос
  @media (prefers-color-scheme: dark) {
    :global([data-bs-theme='auto']) & {
      filter: invert(1);
    }
  }

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    box-shadow: none;
  }
}

.btnDelete {
  background-image: url('../../../../assets/img/icons/icon-delete.svg');
}

.btnMore {
  background-image: url('../../../../assets/img/icons/icon-more2.svg');

  transition: transform 0.3s;
}

.btnMoreActive {
  transform: rotate(180deg);
}

.btnStats {
  background-image: url('../../../../assets/img/icons/icon-stats.svg');
}
