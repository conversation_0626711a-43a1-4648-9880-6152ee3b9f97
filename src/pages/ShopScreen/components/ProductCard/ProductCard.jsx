import { createRef, useState } from 'react'
import { <PERSON><PERSON>, Card, Col, OverlayTrigger, Row, Tab, Tabs } from 'react-bootstrap'
import 'photoswipe/style.css'

import PhotoViewer from '@/components/PhotoViewer/PhotoViewer'

import { useGetProductSales } from '@/features/shop/api/getProductSales'
import { getImageSrc } from '@/utils/images'
import { renderBtnTooltip } from '@/utils/tooltips.jsx'

import styles from './ProductCard.module.scss'
import GoodsChart from '../GoodsChart'
import SalesTable from '../SalesTable/SalesTable'

const getBadgeClass = (qty) => {
  if (qty === 0) {
    return styles.badgeDanger
  } else if (qty >= 5) {
    return styles.badgeSuccess
  } else if (qty < 5) {
    return styles.badgeWarning
  }
}

const countSizes = (item) => {
  return item?.proportions?.reduce((a, b) => a + b.count, 0) || 0
}

function ProductCard({ card, onConfirmDelete }) {
  const [isShowMore, setIsShowMore] = useState(false)
  const [activeTab, setActiveTab] = useState('sales')

  const { data: productSales, refetch: refetchProductSales } = useGetProductSales(card?.public_id, isShowMore)

  const cardRef = createRef()

  const proportions = countSizes(card)

  const handleClickOpenCard = () => {
    const MARGIN_TOP_CARD = 24
    const topPos = cardRef.current.getBoundingClientRect().top + window.pageYOffset - MARGIN_TOP_CARD

    setIsShowMore(!isShowMore)

    if (!isShowMore) {
      window.scrollTo({
        top: topPos,
        behavior: 'smooth',
      })

      refetchProductSales()
    }
  }

  return (
    <Card className={`${styles.card} p-3`}>
      <div className={`${styles.cardInner}`} ref={cardRef}>
        <div className={`${styles.indexWrapper} d-grid align-items-center justify-content-center`}>
          {card.priority_number}
        </div>
        <PhotoViewer className={styles.pictureContainer}>
          <a
            href={getImageSrc(card.picture)}
            data-pswp-width={800}
            data-pswp-height={300}
            target="_blank"
            rel="noreferrer"
            className={styles.imageLink}
          >
            <img className={styles.picture} src={getImageSrc(card.picture)} alt="" />
          </a>
          {card?.pictures?.map((image, index) => (
            <a
              href={getImageSrc(image)}
              data-pswp-width={800}
              data-pswp-height={300}
              key={image + '-' + index}
              target="_blank"
              rel="noreferrer"
              className={styles.imageLink}
            >
              <img className={styles.picture} src={getImageSrc(image)} alt="" />
            </a>
          ))}
        </PhotoViewer>

        <div className={styles.content} onClick={handleClickOpenCard}>
          <div>
            <h2 className={styles.title}>{card.title}</h2>
          </div>
          <div className={styles.publicIdWrap}>{card.vendor_code}</div>
          <div className={styles.priceWrap}>{card?.price?.toLocaleString()} &#8381;</div>
          <div className={`${styles.badge} ${getBadgeClass(proportions)}`}>{proportions}</div>
          <div className={`${styles.proportionsWrapper} ${!isShowMore ? styles.proportionsWrapperHidden : ''}`}>
            {card?.proportions?.map((proportion, index) => (
              <span key={proportion.public_id + index}>
                <span> {proportion.name}: </span>
                <span className="fw-bold">{proportion.count}</span>,
              </span>
            ))}
          </div>
          <div className={styles.subgroupWrap}>
            {card?.subgroup && <span className={styles.subgroup}>{card.subgroup}</span>}
          </div>
          <div className={styles.descriptionWrap}>
            <p className={`${styles.description} ${!isShowMore ? styles.descHidden : ''}`}>{card.description}</p>
          </div>
        </div>
      </div>

      <Row className={`${styles.tabsWrapper} ${!isShowMore ? 'visually-hidden' : ''}`}>
        <Col>
          <Tabs onSelect={(evt) => setActiveTab(evt)} defaultActiveKey="sales">
            <Tab className="pt-2" eventKey="sales" title="Продажи">
              <SalesTable product={productSales?.data} />
            </Tab>
            <Tab className="pt-2" eventKey="chart" title="График">
              {activeTab === 'chart' && <GoodsChart publicId={card.public_id} />}
            </Tab>
          </Tabs>
        </Col>
      </Row>

      <Row className={styles.buttons}>
        <Col className={styles.btnWrapper} md={12}>
          <OverlayTrigger
            placement="left"
            delay={{ show: 250, hide: 400 }}
            overlay={(evt) => renderBtnTooltip(evt, 'редактировать')}
          >
            <Button href={`shop/form/${card.public_id}`} className={styles.btnEdit} type="button" />
          </OverlayTrigger>
        </Col>
        <Col className={styles.btnWrapper} md={12}>
          <OverlayTrigger
            placement="left"
            delay={{ show: 250, hide: 400 }}
            overlay={(evt) => renderBtnTooltip(evt, 'удалить')}
          >
            <Button
              onClick={() => onConfirmDelete(card.public_id)}
              className={`${styles.btnEdit} ${styles.btnDelete}`}
              type="button"
            />
          </OverlayTrigger>
        </Col>
        <Col className={styles.btnWrapper} md={12}>
          <OverlayTrigger
            placement="left"
            delay={{ show: 250, hide: 400 }}
            overlay={(evt) => renderBtnTooltip(evt, isShowMore ? 'свернуть' : 'подробнее')}
          >
            <Button
              onClick={handleClickOpenCard}
              className={`${styles.btnEdit} ${styles.btnMore} ${isShowMore ? styles.btnMoreActive : ''}`}
              type="button"
            />
          </OverlayTrigger>
        </Col>
      </Row>
    </Card>
  )
}

export default ProductCard
