import React from 'react'
import { Col, Row, Table } from 'react-bootstrap'

function SalesTable({ product }) {
  if (!product) return <p>Нет статистики по продажам</p>

  return (
    <Row>
      <Col md={{ offset: 1, span: 10 }}>
        <Table>
          <thead>
            <tr>
              <th>Размер</th>
              <th>Продано</th>
              <th>Осталось</th>
              <th>Сумма</th>
            </tr>
          </thead>
          <tbody>
            {product?.proportions?.map((item, index) => (
              <tr key={item.name + index}>
                <td>{item.name}</td>
                <td>{item.count}</td>
                <td>
                  {item.limit - item.count} / {item.limit}
                </td>
                <td>{item.common_sum} &#8381;</td>
              </tr>
            ))}
            <tr>
              <td style={{ textAlign: 'right' }}>
                <b>Итого</b>
              </td>
              <td>{product?.proportions?.length > 0 && product?.proportions?.reduce((a, b) => a + b.count, 0)}</td>
              <td />
              <td>
                {product?.proportions?.length > 0 && product?.proportions?.reduce((a, b) => a + b.common_sum, 0)}{' '}
                &#8381;
              </td>
            </tr>
          </tbody>
        </Table>
      </Col>
    </Row>
  )
}

export default SalesTable
