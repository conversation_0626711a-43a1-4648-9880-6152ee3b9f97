.card {
  position: relative;

  &:hover .buttons {
    opacity: 1;
  }
}

.indexWrapper {
  min-width: 50px;
}

.pictureContainer {
  width: 65px;
  height: 65px;

  background-image: url('../../../../assets/img/no-image-small.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
}

.imageLink {
  position: relative;

  display: block;
  width: 65px;
  height: 65px;

  &:not(:first-child) {
    display: none;
  }

  &:hover::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    display: block;

    background-color: rgba(255, 255, 255, 0.5);
    background-image: url('../../../../assets/img/icons/icon-zoom.svg');
    background-repeat: no-repeat;
    background-position: center;

    content: '';
    z-index: 10;
  }
}

.picture {
  width: 100%;
  height: 100%;

  object-fit: cover;
  border-radius: 4px;
}

.title {
  font-size: 18px;
  font-weight: 500;
}

.btnWrapper {
  padding: 0;
  width: 24px;
}

.buttons {
  position: absolute;
  top: 10px;
  right: 28px;

  display: block;
  width: max-content;

  opacity: 0;
  transition: opacity 0.3s;
}

.btnEdit {
  width: 16px;
  height: 16px;

  background-color: transparent;
  background-image: url('../../../../assets/img/icons/icon_edit.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  border: 0;

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    box-shadow: none;
  }
}

.btnDelete {
  background-image: url('../../../../assets/img/icons/icon-delete.svg');
}

.fileButton {
  margin: 0;
  padding: 10px;

  display: block;
  width: 100%;
  min-height: 100%;

  text-align: center;

  background-color: #42c2ff;
  border-radius: 4px;

  cursor: pointer;

  &:hover {
    background-color: darken(#42c2ff, 10);
  }
}

.inputFile:disabled ~ .fileButton {
  background-color: #0000001a;

  &:hover {
    background-color: #0000001a;
    cursor: auto;
  }
}
