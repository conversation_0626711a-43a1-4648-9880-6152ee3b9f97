import useAxios from 'axios-hooks'
import { useState } from 'react'
import { Button, Col, Modal, Row } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import PagePreloader from '@/components/PagePreloader/PagePreloader'
import PageSearch from '@/components/PageSearch/PageSearch'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'

import styles from './Goods.module.scss'
import GoodsPreloader from './GoodsPreloader/GoodsPreloader'
import MainStatistics from './MainStatistics/MainStatistics'
import ProductCard from './ProductCard/ProductCard'

const countSum = (values, field) => {
  return values.reduce((a, b) => a - b[field], 0)
}

function Goods({ goods, isLoading, onUpdateGoods }) {
  const [, api] = useAxios(
    {
      url: APIRoute.SHOP_PRODUCT,
      method: 'DELETE',
    },
    { manual: true }
  )

  const openToast = useToast()

  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedProductId, setSelectedProductId] = useState('')

  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [renderValues, setRenderValues] = useState([]) // для отрисовки на странице

  const [sorting, setSorting] = useState({
    field: '',
    isASC: false, // ascending order - по возрастанию
  })

  const handleClickSort = (field) => {
    let sortedValues

    if (sorting.field === '' || sorting.field !== field) {
      if (field === 'title') {
        sortedValues = filteredValues.sort((a, b) => (a[field] > b[field] ? 1 : b[field] > a[field] ? -1 : 0))
        setSorting({ ...sorting, field: field, isASC: false })
      } else if (field === 'price') {
        sortedValues = filteredValues.sort((a, b) => b[field] - a[field])
        setSorting({ ...sorting, field: field, isASC: false })
      } else if (field === 'count') {
        sortedValues = filteredValues.sort(
          (a, b) => countSum(a.proportions.values, 'count') - countSum(b.proportions.values, 'count')
        )
        setSorting({ ...sorting, field: field, isASC: false })
      } else if (field === 'priority') {
        sortedValues = filteredValues.reverse()
        setSorting({ ...sorting, field: field, isASC: !sorting.isASC })
      }
    } else if (sorting.field === field) {
      sortedValues = filteredValues.reverse()
      setSorting({ ...sorting, isASC: !sorting.isASC })
    }

    setFilteredValues([...sortedValues])
  }

  const handleConfirmDelete = (public_id) => {
    setConfirmModal(true)
    setSelectedProductId(public_id)
  }

  const handleCloseConfirm = () => {
    setConfirmModal(false)
    setSelectedProductId('')
  }

  const handleDeleteOrder = () => {
    setConfirmModal(false)

    api({ url: `${APIRoute.SHOP_PRODUCT}/${selectedProductId}`, method: 'DELETE' }).then((r) => {
      if (r.status === 200 && r.data.message) {
        openToast.success({ message: 'Товар был удалён' })
        onUpdateGoods()
      }
    })
  }

  return (
    <>
      <MainStatistics goods={goods} />

      <Row className="mb-5">
        <Col>
          <PageSearch values={goods} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <Button href="shop/form" variant="success">
            <i className="bi bi-plus-circle me-2" />
            Добавить
          </Button>
        </Col>
      </Row>

      <div className={`${styles.sortingWrapper} mb-1`}>
        <div>
          <button
            className={`${styles.sortBtn} ${
              sorting.field === 'priority'
                ? sorting.isASC
                  ? styles.sortBtnAsc
                  : styles.sortBtnDesc
                : styles.sortBtnDefault
            }`}
            onClick={() => handleClickSort('priority')}
            type="button"
          >
            Приоритет
          </button>
        </div>
        <div>
          <button
            className={`${styles.sortBtn} ${
              sorting.field === 'title'
                ? sorting.isASC
                  ? styles.sortBtnAsc
                  : styles.sortBtnDesc
                : styles.sortBtnDefault
            }`}
            onClick={() => handleClickSort('title')}
            type="button"
          >
            Наименование
          </button>
        </div>
        <div>
          <span className={styles.sortBtn}>Артикул</span>
        </div>
        <div>
          <button
            className={`${styles.sortBtn} ${
              sorting.field === 'price'
                ? sorting.isASC
                  ? styles.sortBtnAsc
                  : styles.sortBtnDesc
                : styles.sortBtnDefault
            }`}
            onClick={() => handleClickSort('price')}
            type="button"
          >
            Цена
          </button>
        </div>
        <div>
          <button
            className={`${styles.sortBtn} ${
              sorting.field === 'count'
                ? sorting.isASC
                  ? styles.sortBtnAsc
                  : styles.sortBtnDesc
                : styles.sortBtnDefault
            }`}
            onClick={() => handleClickSort('count')}
            type="button"
          >
            Количество
          </button>
        </div>
        <div>
          <span className={styles.sortBtn}>Размеры</span>
        </div>
      </div>

      <PagePreloader isLoading={isLoading && !goods} preloader={<GoodsPreloader />}>
        <Row>
          <Col>
            {renderValues.length > 0 &&
              renderValues.map((item) => (
                <ProductCard card={item} onConfirmDelete={handleConfirmDelete} key={item.public_id} />
              ))}
          </Col>
        </Row>

        <AdvancedPagination values={filteredValues} setValues={setRenderValues} />

        <Modal show={confirmModal} onHide={handleCloseConfirm}>
          <Modal.Header closeButton>
            <Modal.Title>Удалить товар?</Modal.Title>
          </Modal.Header>
          <Modal.Footer>
            <Button variant="link" onClick={() => setConfirmModal(false)}>
              Отменить
            </Button>
            <Button variant="danger" onClick={handleDeleteOrder}>
              Удалить
            </Button>
          </Modal.Footer>
        </Modal>
      </PagePreloader>
    </>
  )
}

export default Goods
