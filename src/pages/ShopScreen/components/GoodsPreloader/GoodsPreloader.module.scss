.card {
  margin-bottom: 8px;

  height: 117px;

  background:
    linear-gradient(0.25turn, transparent, #fff, transparent), linear-gradient(#f5f4f9, #f5f4f9),
    linear-gradient(#f5f4f9, #f5f4f9), linear-gradient(#f5f4f9, #f5f4f9), linear-gradient(#f5f4f9, #f5f4f9),
    radial-gradient(25px circle at 19px 19px, #f5f4f9 50%, transparent 51%), linear-gradient(#f5f4f9, #f5f4f9),
    linear-gradient(#f5f4f9, #f5f4f9), linear-gradient(#f5f4f9, #f5f4f9), #ffffff;
  background-repeat: no-repeat;
  background-size:
    100px 100%,
    65px 85px,
    33% 20px,
    7% 20px,
    5% 20px,
    32px 32px,
    12% 20px,
    18% 20px,
    70% 20px;
  background-position:
    -10% 0,
    50px 16px,
    125px 16px,
    52% 16px,
    65% 16px,
    77% 8px,
    95% 16px,
    125px 47px,
    125px 80px;

  animation: loading 2s infinite;

  cursor: progress;
}

@keyframes loading {
  to {
    background-position:
      110% 0,
      50px 16px,
      125px 16px,
      52% 16px,
      65% 16px,
      77% 8px,
      95% 16px,
      125px 47px,
      125px 80px;
  }
}
