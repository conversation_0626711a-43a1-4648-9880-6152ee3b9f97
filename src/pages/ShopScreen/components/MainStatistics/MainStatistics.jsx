import { useEffect, useState } from 'react'
import { Card, Col, Row } from 'react-bootstrap'

import styles from './MainStatistics.module.scss'

const countSizes = (item) => {
  return item?.proportions?.reduce((a, b) => a + b.count, 0) || 0
}

function MainStatistics({ goods }) {
  const [statistics, setStatistics] = useState({
    end: 0,
    many: 0,
    littleLeft: 0,
    total: 0,
  })

  useEffect(() => {
    let end = 0
    let many = 0
    let littleLeft = 0

    if (goods?.length > 0) {
      goods.map((item) => {
        const sum = countSizes(item)

        if (sum === 0) {
          end += 1
        } else if (sum >= 5) {
          many += 1
        } else if (sum < 5) {
          littleLeft += 1
        }
      })

      setStatistics({ end: end, many: many, littleLeft: littleLeft, total: goods.length })
    }
  }, [goods])

  // if (!goods) return null;

  return (
    <Card className="mb-4 p-4">
      <Row>
        <Col className={`${styles.col} d-flex justify-content-center`}>
          <p className={styles.colWrap}>
            <span className={styles.value}>{statistics.end}</span>
            <span className={styles.label}>Закончились</span>
          </p>
        </Col>
        <Col className={`${styles.col} d-flex justify-content-center`}>
          <p className={styles.colWrap}>
            <span className={styles.value}>{statistics.littleLeft}</span>
            <span className={styles.label}>Заканчиваются</span>
          </p>
        </Col>
        <Col className={`${styles.col} d-flex justify-content-center`}>
          <p className={styles.colWrap}>
            <span className={styles.value}>{statistics.many}</span>
            <span className={styles.label}>Ещё много</span>
          </p>
        </Col>
        <Col className={`${styles.col} d-flex justify-content-center`}>
          <p className={styles.colWrap}>
            <span className={styles.value}>{statistics.total}</span>
            <span className={styles.label}>Всего товаров</span>
          </p>
        </Col>
      </Row>
    </Card>
  )
}

export default MainStatistics
