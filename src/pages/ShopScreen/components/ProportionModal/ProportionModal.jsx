import useAxios from 'axios-hooks'
import { useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Modal, Row } from 'react-bootstrap'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { updateFormData } from '@/utils/forms'

function ProportionModal({ productId, isShow, onHide, onUpdateOptions }) {
  const openToast = useToast()
  const [formData, setFormData] = useState({
    product: {
      public_id: productId,
    },
  })

  const [, apiProportion] = useAxios(
    {
      url: APIRoute.PRODUCT_PROPORTION,
      method: 'POST',
    },
    { manual: true }
  )

  const handleSubmitForm = (evt) => {
    evt.preventDefault()

    apiProportion({ data: formData }).then((r) => {
      if (r.status === 200) {
        openToast.success({ message: 'Опция создана' })
        onUpdateOptions()
        onHide(false)
      }
    })
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, {})
  }

  return (
    <Modal show={isShow} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Добавление опции</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmitForm} id="formProportion">
          <Row>
            <Col className="p-2">
              <FloatingLabel className="mb-2" controlId="proportionsProductLabel" label="Тип опции">
                <FormControl
                  // onChange={handleChangeProportionsName}
                  onChange={handleChangeField}
                  value={formData.type}
                  name="type"
                  type="text"
                  placeholder="Тип опции"
                  required
                  // disabled={isUpdateShopProduct}
                  style={{ backgroundColor: '#f5f7f9' }}
                />
              </FloatingLabel>
              <FloatingLabel className="mb-2" controlId="proportionsProductLabel" label="Название элемента опции">
                <FormControl
                  onChange={handleChangeField}
                  value={formData.name}
                  name="name"
                  type="text"
                  placeholder="Название элемента опции"
                  required
                  style={{ backgroundColor: '#f5f7f9' }}
                />
              </FloatingLabel>
              <FloatingLabel className="mb-2" controlId="proportionsProductLabel" label="Артикул опции">
                <FormControl
                  onChange={handleChangeField}
                  value={formData.vendor_code}
                  name="vendor_code"
                  type="text"
                  placeholder="Артикул опции"
                  required
                  style={{ backgroundColor: '#f5f7f9' }}
                />
              </FloatingLabel>
              <Row className="align-items-center mb-2">
                <Col>
                  <FloatingLabel controlId="proportionsProductLabel" label="Остаток">
                    <FormControl
                      // value={proportionCount}
                      onChange={handleChangeField}
                      name="count"
                      type="number"
                      placeholder="Остаток"
                      required
                      style={{ backgroundColor: '#f5f7f9' }}
                    />
                  </FloatingLabel>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button type="submit" form="formProportion">
          Создать
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default ProportionModal
