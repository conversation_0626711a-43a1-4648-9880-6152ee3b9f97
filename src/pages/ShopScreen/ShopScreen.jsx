import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { Tab, Tabs } from 'react-bootstrap'

import { ProductOrders } from '@/pages/ShopScreen/components/ProductOrders/ProductOrders'

import Collections from './components/Collections/Collections'
import { Delivery } from './components/Delivery/Delivery'
import Goods from './components/Goods'
import GoodsChart from './components/GoodsChart'
import ShopCategories from './components/ShopCategories/ShopCategories'
import Layout from '../../components/Layout/Layout'
import { APIRoute } from '../../const'

function ShopScreen() {
  const [{ data, loading: isLoading }, apiShop] = useAxios(
    {
      url: APIRoute.SHOP_ITEMS,
      method: 'GET',
    },
    { manual: true }
  )

  const [activeTab, setActiveTab] = useState('')

  useEffect(() => {
    apiShop()
  }, [apiShop])

  const handleSelectTab = (evt) => {
    setActiveTab(evt)
  }

  return (
    <Layout title="Магазин">
      <Tabs className={`mb-4`} defaultActiveKey="goods" onSelect={handleSelectTab}>
        <Tab eventKey="goods" title="Товары">
          <Goods goods={data?.values} isLoading={isLoading} onUpdateGoods={apiShop} />
        </Tab>
        <Tab eventKey="orders" title="Заказы">
          {activeTab === 'orders' && <ProductOrders />}
        </Tab>
        <Tab eventKey="categories" title="Категории">
          {activeTab === 'categories' && <ShopCategories />}
        </Tab>
        <Tab eventKey="collections" title="Коллекции">
          {activeTab === 'collections' && <Collections />}
        </Tab>
        <Tab eventKey="delivery" title="Доставка">
          {activeTab === 'delivery' && <Delivery />}
        </Tab>
        <Tab eventKey="statistics" title="Статистика">
          <GoodsChart goods={data?.values} />
        </Tab>
      </Tabs>
    </Layout>
  )
}

export default ShopScreen
