import useAxios from 'axios-hooks'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { Button, Col, Floating<PERSON>abel, FormCheck, FormControl, FormSelect, Row } from 'react-bootstrap'
import { useLocation } from 'react-router-dom'

import { APIRoute, Kind } from '@/const'
import { useGetEvents } from '@/features/events/api/getEvents'
import { toLowerCaseEmail } from '@/utils/common'
import { getFormatTime } from '@/utils/date'

// Constants
const ORDER_STATUS_OPTIONS = [
  { value: '', label: 'выберите один из вариантов' },
  { value: 'paid', label: 'Оплачен' },
  { value: 'created', label: 'Создан' },
]

const KIND_OPTIONS = [
  { value: Kind.CORPORATE, label: 'Корпоративный', name: 'kind' },
  { value: Kind.PARTNER, label: 'Партнер', name: 'kind' },
  { value: Kind.BRAND, label: 'Бренд', name: 'kind' },
  { value: Kind.SPORT, label: 'Спорт', name: 'kind' },
  { value: Kind.PRODUCTS, label: 'Продукты', name: 'kind' },
  { value: Kind.OTHER, label: 'Другие', name: 'kind' },
]

// Sub-components
const OrderSection = memo(
  ({ formData, defaultFormData, onChangeMainField, onChangeKind, isInvalidKind, indexForm, isDisabled }) => (
    <Row className={`${indexForm > 0 ? 'visually-hidden' : ''} g-3 mb-4`}>
      <h4>Заказ</h4>
      <Col md={6}>
        <FloatingLabel controlId="quantityLabelOrder" label="Email">
          <FormControl
            onChange={onChangeMainField}
            value={formData.email || defaultFormData.email || ''}
            name="email"
            type="email"
            placeholder="email"
            disabled={indexForm > 0}
          />
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="statusLabelTicket" label="Статус">
          <FormSelect
            onChange={onChangeMainField}
            value={formData.status || defaultFormData.status || ''}
            name="status"
            aria-label="Статус"
            disabled={indexForm > 0 || isDisabled}
          >
            {ORDER_STATUS_OPTIONS.map(({ value, label }) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="descriptionLabelOrder" label="Описание">
          <FormControl
            onChange={onChangeMainField}
            value={formData.description || defaultFormData.description || ''}
            name="description"
            as="textarea"
            placeholder="Описание"
            disabled={indexForm > 0}
          />
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <span>Тип:</span>{' '}
        {KIND_OPTIONS.map(({ value, label, name }, idx) => (
          <FormCheck
            key={value}
            inline
            label={label}
            name={name}
            type="radio"
            id={`inline-kind-${idx + 1}${indexForm}`}
            value={value}
            onChange={onChangeKind}
            isInvalid={isInvalidKind}
            checked={(indexForm === 0 && formData?.kind === value) || defaultFormData?.kind === value}
            disabled={isDisabled}
            required
          />
        ))}
      </Col>
    </Row>
  )
)

OrderSection.displayName = 'OrderSection'

const TicketSection = memo(
  ({
    formData,
    indexForm,
    events,
    selectedEvent,
    formats,
    onChangeEvent,
    onChangeCity,
    onChangeFormat,
    onChangeField,
    isDisabled,
    onDeleteTicket,
  }) => (
    <>
      <Row className="g-3 mb-4">
        <h4>Билет</h4>
        <Col md={6}>
          <FloatingLabel controlId="eventLabelTicket" label="Событие">
            <FormSelect
              onChange={onChangeEvent}
              value={formData.tickets[indexForm].event_public_id || ''}
              name="event_public_id"
              aria-label="Событие"
              disabled={isDisabled}
            >
              <option value="">выберите один из вариантов</option>
              {events.map((event) => (
                <option value={event.public_id} key={event.public_id}>
                  {event.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="cityLabelTicket" label="Город">
            <FormSelect
              onChange={onChangeCity}
              value={formData.tickets[indexForm].event_city_public_id || ''}
              name="event_city_public_id"
              aria-label="Город"
              disabled={isDisabled}
            >
              <option value="">выберите один из вариантов</option>
              {selectedEvent?.event_city.map((city) => (
                <option value={city.public_id} key={city.public_id}>
                  {`${city.city.name_ru} — ${getFormatTime(city.start_time, city.timezone)}`}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="formatLabelTicket" label="Формат">
            <FormSelect
              onChange={onChangeFormat}
              value={formData.tickets[indexForm].format_public_id || ''}
              name="format_public_id"
              aria-label="Формат"
              disabled={isDisabled}
            >
              <option value="">выберите один из вариантов</option>
              {formats?.map((format) => (
                <option value={format.public_id} key={format.public_id}>
                  {format.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="costLabelOrder" label="Стоимость">
            <FormControl
              onChange={onChangeField}
              value={'cost' in formData.tickets[indexForm] ? formData.tickets[indexForm].cost : ''}
              name="cost"
              type="number"
              placeholder="cost"
              disabled={isDisabled}
            />
          </FloatingLabel>
        </Col>

        <Col md={6}>
          <FloatingLabel controlId="ticketCountLabelOrder" label="Количество">
            <FormControl
              onChange={onChangeField}
              value={formData.tickets[indexForm].ticket_count || ''}
              name="ticket_count"
              type="number"
              placeholder="Количество"
              disabled={isDisabled}
            />
          </FloatingLabel>
        </Col>
      </Row>

      <Row>
        <Col md={10} />
        <Col className="d-grid align-content-start" md={2}>
          <Button onClick={onDeleteTicket} variant="outline-danger" disabled={isDisabled}>
            Удалить билет
          </Button>
        </Col>
      </Row>
    </>
  )
)

TicketSection.displayName = 'TicketSection'

// Custom hook for form logic
const useTicketForm = ({ formData, onChangeField, indexForm }) => {
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [formats, setFormats] = useState(null)
  const [, api] = useAxios({ method: 'GET' }, { manual: true })

  const updateDataTickets = useCallback(
    (name, value) => {
      const newTickets = formData.tickets.map((item, ticketIndex) => {
        if (ticketIndex === indexForm) {
          if (value === '') {
            const newItem = { ...item }
            delete newItem[name]
            return newItem
          }
          return { ...item, [name]: value }
        }
        return item
      })

      onChangeField({ ...formData, tickets: newTickets })
    },
    [formData, indexForm, onChangeField]
  )

  const handleChangeEvent = useCallback(
    (evt, events) => {
      const { name, value: eventId } = evt.target
      const event = events.find((event) => event.public_id === eventId)

      const newTickets = [...formData.tickets]
      delete newTickets[indexForm]['event_city_public_id']
      delete newTickets[indexForm]['format_public_id']

      setFormats(null)

      if (event) {
        setSelectedEvent(event)
        onChangeField({ ...formData, tickets: newTickets })
        updateDataTickets(name, eventId)
      }
    },
    [formData, indexForm, onChangeField, updateDataTickets]
  )

  const handleChangeCity = useCallback(
    (evt) => {
      const { name, value: cityId } = evt.target

      updateDataTickets(name, cityId)
      setFormats(null)

      if (cityId !== 0 && cityId !== '') {
        api({ url: `${APIRoute.EVENT_FORMATS}/${cityId}?hidden=true` })
          .then((r) => {
            setFormats(r.data.values.length > 0 ? r.data.values : [])
          })
          .catch((error) => {
            console.error('Failed to load formats:', error)
            setFormats([])
          })
      }
    },
    [api, updateDataTickets]
  )

  const handleDeleteTicket = useCallback(() => {
    const newTickets = [...formData.tickets]
    newTickets.splice(indexForm, 1)
    onChangeField({ ...formData, tickets: newTickets })
  }, [formData, indexForm, onChangeField])

  const handleChangeFormat = useCallback(
    (evt) => {
      const { name, value } = evt.target
      updateDataTickets(name, value)
    },
    [updateDataTickets]
  )

  const handleChangeField = useCallback(
    (evt) => {
      const { name, value } = evt.target
      const numericValue = value === '' ? value : +value
      updateDataTickets(name, numericValue)
    },
    [updateDataTickets]
  )

  return {
    selectedEvent,
    formats,
    handleChangeEvent,
    handleChangeCity,
    handleChangeFormat,
    handleChangeField,
    handleDeleteTicket,
    updateDataTickets,
  }
}

const TicketsForm = memo(
  ({ formData, defaultFormData, onChangeField, indexForm, isInvalidKind, onChangeInvalidKind }) => {
    const { data: eventsData } = useGetEvents()
    const events = useMemo(() => eventsData?.data?.values || [], [eventsData])
    const search = useLocation().search
    const searchParams = useMemo(() => new URLSearchParams(search), [search])
    const companyCreateId = searchParams.get('company-create')

    const isDisabled = useMemo(() => Object.keys(defaultFormData).length > 0, [defaultFormData])

    const {
      selectedEvent,
      formats,
      handleChangeEvent,
      handleChangeCity,
      handleChangeFormat,
      handleChangeField,
      handleDeleteTicket,
    } = useTicketForm({ formData, onChangeField, indexForm })

    const handleEventChange = useCallback(
      (evt) => {
        handleChangeEvent(evt, events)
      },
      [handleChangeEvent, events]
    )

    // Handle company ID from URL params
    useEffect(() => {
      if (companyCreateId && formData.company?.public_id !== companyCreateId) {
        onChangeField({ ...formData, company: { public_id: companyCreateId } })
      }
    }, [companyCreateId, formData, onChangeField])

    const handleChangeMainField = useCallback(
      (evt) => {
        const { name, value, type } = evt.target

        if (type === 'email') {
          const email = toLowerCaseEmail(value)
          onChangeField({ ...formData, [name]: email })
        } else {
          onChangeField({ ...formData, [name]: value })
        }
      },
      [formData, onChangeField]
    )

    const handleChangeKind = useCallback(
      (evt) => {
        const { value } = evt.target
        onChangeField({ ...formData, kind: value })
        onChangeInvalidKind(false)
      },
      [formData, onChangeField, onChangeInvalidKind]
    )

    return (
      <>
        <OrderSection
          formData={formData}
          defaultFormData={defaultFormData}
          onChangeMainField={handleChangeMainField}
          onChangeKind={handleChangeKind}
          isInvalidKind={isInvalidKind}
          indexForm={indexForm}
          isDisabled={isDisabled}
        />

        <TicketSection
          formData={formData}
          indexForm={indexForm}
          events={events}
          selectedEvent={selectedEvent}
          formats={formats}
          onChangeEvent={handleEventChange}
          onChangeCity={handleChangeCity}
          onChangeFormat={handleChangeFormat}
          onChangeField={handleChangeField}
          isDisabled={isDisabled}
          onDeleteTicket={handleDeleteTicket}
        />
      </>
    )
  }
)

TicketsForm.displayName = 'TicketsForm'

export default TicketsForm
