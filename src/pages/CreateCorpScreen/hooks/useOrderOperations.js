import useAxios from 'axios-hooks'
import { useCallback, useState } from 'react'

import { APIRoute } from '../../../const'

export const useOrderOperations = () => {
  const [isLoading, setIsLoading] = useState(false)

  const [, executeApi] = useAxios(
    {
      method: 'GET',
    },
    {
      manual: true,
    }
  )

  const loadOrder = useCallback(
    async (publicId) => {
      setIsLoading(true)
      try {
        const response = await executeApi({
          url: `${APIRoute.ORDER_INFO}/${publicId}`,
          method: 'GET',
        })
        return response.data
      } catch (error) {
        console.error('Failed to load order:', error)
        throw error
      } finally {
        setIsLoading(false)
      }
    },
    [executeApi]
  )

  const createOrder = useCallback(
    async (formData) => {
      setIsLoading(true)
      try {
        const response = await executeApi({
          url: APIRoute.CREATE_PAYMENT,
          method: 'POST',
          data: formData,
        })
        return response
      } catch (error) {
        console.error('Failed to create order:', error)
        throw error
      } finally {
        setIsLoading(false)
      }
    },
    [executeApi]
  )

  const updateOrder = useCallback(
    async (publicId, formData) => {
      setIsLoading(true)
      try {
        const response = await executeApi({
          url: APIRoute.CREATE_PAYMENT,
          method: 'PUT',
          data: formData,
        })
        return response
      } catch (error) {
        console.error('Failed to update order:', error)
        throw error
      } finally {
        setIsLoading(false)
      }
    },
    [executeApi]
  )

  return {
    loadOrder,
    createOrder,
    updateOrder,
    isLoading,
  }
}
