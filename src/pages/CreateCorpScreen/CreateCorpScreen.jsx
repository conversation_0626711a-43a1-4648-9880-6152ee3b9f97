import { use<PERSON>allback, useEffect, useMemo, useReducer, useRef } from 'react'
import { Button, Col, Nav, Row, Tab } from 'react-bootstrap'
import { Link, useNavigate, useParams, useSearchParams } from 'react-router-dom'

import TicketsForm from './components/TicketsForm'
import Layout from '../../components/Layout/Layout'
import SuccessModal from '../../components/Modal/SuccessModal/SuccessModal'
import { APIRoute, AppRoute } from '../../const'
import { useOrderOperations } from './hooks/useOrderOperations'

// Action types for reducer
const ACTION_TYPES = {
  SET_ACTIVE_TAB: 'SET_ACTIVE_TAB',
  SET_INVALID_KIND: 'SET_INVALID_KIND',
  SET_SHOW_SUCCESS_MODAL: 'SET_SHOW_SUCCESS_MODAL',
  SET_FORM_DATA: 'SET_FORM_DATA',
  SET_DEFAULT_FORM_DATA: 'SET_DEFAULT_FORM_DATA',
  SET_ORDER_LOADED: 'SET_ORDER_LOADED',
  ADD_TICKET: 'ADD_TICKET',
  RESET_FORM: 'RESET_FORM',
}

const initialState = {
  activeTab: 0,
  isInvalidKind: undefined,
  showSuccessModal: false,
  defaultFormData: {},
  isOrderLoaded: false,
  formData: {
    tickets: [
      {
        promocode: null,
        insurance: false,
      },
    ],
  },
}

function createCorpReducer(state, action) {
  switch (action.type) {
    case ACTION_TYPES.SET_ACTIVE_TAB:
      return { ...state, activeTab: action.payload }
    case ACTION_TYPES.SET_INVALID_KIND:
      return { ...state, isInvalidKind: action.payload }
    case ACTION_TYPES.SET_SHOW_SUCCESS_MODAL:
      return { ...state, showSuccessModal: action.payload }
    case ACTION_TYPES.SET_FORM_DATA:
      return { ...state, formData: action.payload }
    case ACTION_TYPES.SET_DEFAULT_FORM_DATA:
      return { ...state, defaultFormData: action.payload }
    case ACTION_TYPES.SET_ORDER_LOADED:
      return { ...state, isOrderLoaded: action.payload }
    case ACTION_TYPES.ADD_TICKET:
      return {
        ...state,
        formData: {
          ...state.formData,
          tickets: [...state.formData.tickets, { promocode: null, insurance: false }],
        },
      }
    case ACTION_TYPES.RESET_FORM:
      return {
        ...state,
        formData: {
          tickets: [{ promocode: null, insurance: false }],
        },
      }
    default:
      return state
  }
}

function CreateCorpScreen() {
  const [state, dispatch] = useReducer(createCorpReducer, initialState)
  const loadingRef = useRef(false)
  const { public_id } = useParams()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  const isUpdateItem = !!public_id
  const companyId = searchParams.get('company-create') ?? state.defaultFormData?.company?.public_id

  const { loadOrder, createOrder, updateOrder, isLoading } = useOrderOperations()

  // Set document title
  useEffect(() => {
    window.document.title = 'Создание корпоративного заказа'
  }, [])

  // Reset order loaded state when public_id changes
  useEffect(() => {
    dispatch({ type: ACTION_TYPES.SET_ORDER_LOADED, payload: false })
    loadingRef.current = false
  }, [public_id])

  // Load order data for editing
  useEffect(() => {
    if (public_id && !loadingRef.current) {
      loadingRef.current = true
      loadOrder(public_id)
        .then((order) => {
          if (order) {
            dispatch({ type: ACTION_TYPES.SET_DEFAULT_FORM_DATA, payload: { ...order } })
            const newDefaultTickets = order.items.tickets.map((ticket) => ticket)
            dispatch({
              type: ACTION_TYPES.SET_FORM_DATA,
              payload: { order_public_id: order.public_id, tickets: [...newDefaultTickets] },
            })
            dispatch({ type: ACTION_TYPES.SET_ORDER_LOADED, payload: true })
          }
        })
        .catch((error) => {
          console.error('Failed to load order:', error)
          loadingRef.current = false
        })
    }
  }, [public_id, loadOrder])

  const handleAddItem = useCallback(() => {
    dispatch({ type: ACTION_TYPES.ADD_TICKET })
  }, [])

  const handleCreateOrder = useCallback(async () => {
    if (isUpdateItem) {
      try {
        await updateOrder(public_id, state.formData)
        // Simply show success modal without reloading the order data
        dispatch({ type: ACTION_TYPES.SET_SHOW_SUCCESS_MODAL, payload: true })
      } catch (error) {
        console.error('Failed to update order:', error)
      }
    } else {
      if (state.isInvalidKind === undefined || state.isInvalidKind === true) {
        dispatch({ type: ACTION_TYPES.SET_INVALID_KIND, payload: true })
        dispatch({ type: ACTION_TYPES.SET_ACTIVE_TAB, payload: 0 })
      } else {
        try {
          const response = await createOrder(state.formData)
          if (response?.data?.order?.public_id) {
            navigate(`${AppRoute.ORDERS}/${response.data.order.public_id}`)
            dispatch({ type: ACTION_TYPES.RESET_FORM })
          }
        } catch (error) {
          console.error('Failed to create order:', error)
        }
      }
    }
  }, [isUpdateItem, state.formData, state.isInvalidKind, public_id, updateOrder, createOrder, navigate])

  const handleFormDataChange = useCallback((newFormData) => {
    dispatch({ type: ACTION_TYPES.SET_FORM_DATA, payload: newFormData })
  }, [])

  const handleTabSelect = useCallback((index) => {
    dispatch({ type: ACTION_TYPES.SET_ACTIVE_TAB, payload: index })
  }, [])

  const handleCloseSuccessModal = useCallback(() => {
    dispatch({ type: ACTION_TYPES.SET_SHOW_SUCCESS_MODAL, payload: false })
  }, [])

  const handleInvalidKindChange = useCallback((value) => {
    dispatch({ type: ACTION_TYPES.SET_INVALID_KIND, payload: value })
  }, [])

  const ticketTabs = useMemo(
    () =>
      state.formData.tickets.map((_, index) => (
        <Nav.Item key={index}>
          <Nav.Link onClick={() => handleTabSelect(index)} eventKey={`${index}`}>
            Билет {index + 1}
          </Nav.Link>
        </Nav.Item>
      )),
    [state.formData.tickets, handleTabSelect]
  )

  const ticketPanes = useMemo(
    () =>
      state.formData.tickets.map((_, index) => (
        <Tab.Pane eventKey={`${index}`} key={index}>
          <TicketsForm
            formData={state.formData}
            defaultFormData={state.defaultFormData}
            onChangeField={handleFormDataChange}
            indexForm={index}
            isInvalidKind={state.isInvalidKind}
            onChangeInvalidKind={handleInvalidKindChange}
          />
        </Tab.Pane>
      )),
    [state.formData, state.defaultFormData, state.isInvalidKind, handleFormDataChange, handleInvalidKindChange]
  )

  return (
    <Layout>
      <Row className="mb-3">
        <Col>
          <Button as={Link} to={`/corp/company/${companyId}`} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Назад
          </Button>
        </Col>
      </Row>

      <Row>
        <h3 className="mb-3">
          {isUpdateItem ? 'Редактирование корпоративного заказа' : 'Создание корпоративного заказа'}
        </h3>
      </Row>

      <Row>
        <Col className="d-grid" md={2}>
          <Button
            className="mb-3"
            onClick={handleAddItem}
            variant="outline-success"
            disabled={isUpdateItem || isLoading}
          >
            Добавить билет
          </Button>
        </Col>
        <Col md={10} />
      </Row>

      <Tab.Container id="left-tabs-example" activeKey={state.activeTab} defaultActiveKey="0">
        <Row className="mb-5">
          <Col sm={2}>
            <Nav variant="pills" className="flex-column">
              {ticketTabs}
            </Nav>
          </Col>
          <Col sm={10}>
            <Tab.Content>{ticketPanes}</Tab.Content>
          </Col>
        </Row>
      </Tab.Container>

      <Row className="justify-content-md-center mb-5">
        <Col className="d-grid" md={{ span: 3, offset: 4 }}>
          <Button onClick={handleCreateOrder} variant="primary" type="button" size="lg" disabled={isLoading}>
            {isLoading ? 'Загрузка...' : isUpdateItem ? 'Сохранить' : 'Создать заказ'}
          </Button>
        </Col>
      </Row>

      <SuccessModal
        show={state.showSuccessModal}
        handleCloseModal={handleCloseSuccessModal}
        description={isUpdateItem ? 'Заказ успешно был изменён.' : 'Заказ успешно был создан.'}
      />
    </Layout>
  )
}

export default CreateCorpScreen
