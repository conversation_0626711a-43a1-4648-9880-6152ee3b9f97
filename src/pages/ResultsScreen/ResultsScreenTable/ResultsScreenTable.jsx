import useAxios from 'axios-hooks'
import { useState, useCallback } from 'react'
import { Button, ButtonGroup, Col, Form, FormControl, FormGroup, Modal, Row, Spinner } from 'react-bootstrap'
import { useNavigate, useParams } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import Layout from '@/components/Layout/Layout'
import Loader from '@/components/Loader/Loader'
import SuccessModal from '@/components/Modal/SuccessModal/SuccessModal'
import PageSearch from '@/components/PageSearch/PageSearch'

import { APIRoute, AppRoute } from '@/const'
import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'
import { useGetEventResults } from '@/features/results/api/getEventResults'
import { useGetResultsList } from '@/features/results/api/getResultsList'
import { useToast } from '@/hooks/useToast'
import { unixToMoment } from '@/utils/date'

import { ALL_FIELDS } from './resultsScreenData'
import styles from './resultsTable.module.scss'
import TableTemplate from './TableTemplate/TableTemplate'

export const ResultsScreenTable = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const openToast = useToast()

  const [searchValues, setSearchValues] = useState([])
  const [filteredValues, setFilteredValues] = useState([])
  const [isEdditPopup, setIsEdditPopup] = useState(false)
  const [resultRowData, setResultRowData] = useState(null)
  const [isSuccessUpdateModal, setIsSuccessUpdateModal] = useState(false)
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedFormatIndex, setSelectedFormatIndex] = useState(0)

  const getResultsListQuery = useGetResultsList(id)
  const settings = getResultsListQuery?.data?.data
  const filterFormat = settings?.formats?.[selectedFormatIndex] || null
  const isLoadingSettings = getResultsListQuery.isLoading

  const resultList = filterFormat?.fields_list

  const getEventTypeListQuery = useGetEventTypeList()
  const events = getEventTypeListQuery?.data?.data?.values
  const isLoadingEvents = getEventTypeListQuery?.isLoading
  const event = events?.filter((item) => item.public_id === settings?.event_city?.event_type?.public_id)[0]

  const getEventResultsQuery = useGetEventResults(filterFormat?.external_format, id)
  const results = getEventResultsQuery?.data?.data?.values || []
  const isLoadingResults = getEventResultsQuery.isLoading

  const [, apiAdd] = useAxios(
    {
      method: 'POST',
    },
    { manual: true }
  )

  const [{ loading: isLoadingUpdate }, apiUpdate] = useAxios(
    {},
    {
      manual: true,
    }
  )

  const [{ loading: isLoadingSync }, apiSync] = useAxios(
    {},
    {
      manual: true,
    }
  )

  const [, apiDownload] = useAxios(
    {
      method: 'GET',
      responseType: 'blob',
    },
    {
      manual: true,
    }
  )

  const handleChangeFormat = (format) => {
    if (filterFormat?.external_format !== format?.external_format) {
      // Поиск индекса выбранного формата в массиве форматов
      const formatIndex = settings?.formats?.findIndex((f) => f.external_format === format.external_format)

      if (formatIndex !== -1) {
        setSelectedFormatIndex(formatIndex)
      }
    }
  }

  const handleClickAdd = () => {
    setIsEdditPopup(true)
  }

  const handleClosePopup = () => {
    setIsEdditPopup(false)
  }

  const sendData = (e) => {
    e.preventDefault()

    if (resultRowData) {
      apiAdd({ url: APIRoute.RESULT_EDIT_PUBLIC_ID, data: resultRowData }).then((response) => {
        if (response.status === 200) {
          setIsEdditPopup(false)
        }
      })
    }
  }

  const handleChangeForm = (e, name) => {
    const newValue = {}
    newValue[`${name}`] = e.target.value
    setResultRowData({
      ...resultRowData,
      ...newValue,
      format_public_id: filterFormat?.public_id,
      event_city: { public_id: id },
      city: { id: event?.city?.id },
    })
  }

  const getSearchValues = useCallback((searchValue) => {
    setFilteredValues(searchValue)
  }, [])

  const handleSetSearchValues = useCallback((values) => {
    setSearchValues(values)
  }, [])

  const handleClickUpdate = () => {
    apiUpdate({ url: `/api/admin/event/results/${settings.public_id}`, method: 'PUT' }).then((r) => {
      if (r.status === 200) {
        setIsSuccessUpdateModal(true)
        getEventResultsQuery.refetch()
        getResultsListQuery.refetch()
      } else if (r.code === 'ECONNABORTED') {
        openToast.error({ message: 'Запрос превысил допустимое время ожидания' })
      }
    })
  }

  const handleClickSync = () => {
    apiSync({ url: `/api/results/tickets/${settings.event_city.public_id}`, method: 'PUT' }).then((r) => {
      if (r.status === 200) {
        setIsSuccessUpdateModal(true)
        getEventResultsQuery.refetch()
        getResultsListQuery.refetch()
      }
    })
  }

  const handleDownloadOriginal = async () => {
    try {
      const response = await apiDownload({
        url: `/api/admin/original/results/${settings.event_city.public_id}`,
      })

      const blob = new Blob([response.data], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `results_${settings.event_city.public_id}.csv`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
    } catch {
      openToast.error({ message: 'Ошибка при скачивании файла' })
    }
  }

  const handleClickEdit = () => {
    if (!id) {
      console.error('ID отсутствует, невозможно перейти к редактированию')
      return
    }

    const path = `${AppRoute.RESULTS_TABLE_EDIT.replace(':id', id)}`
    navigate(path)
  }

  const handleConfirmDelete = () => {
    setConfirmModal(true)
  }

  const handleCloseConfirm = () => {
    setConfirmModal(false)
  }

  const handleDeleteOrder = () => {
    const body = {
      event_city: {
        public_id: settings.event_city.public_id,
      },
    }

    setConfirmModal(false)

    apiUpdate({ url: '/api/admin/event/results', method: 'DELETE', data: body }).then((r) => {
      if (r.status === 200) {
        navigate(AppRoute.RESULTS)
      }
    })
  }

  return (
    <Layout>
      {!id ? (
        <div className="alert alert-danger">Ошибка: Идентификатор не найден в URL</div>
      ) : (
        <>
          <Row>
            <Col className="mb-3">
              <Button onClick={() => navigate(AppRoute.RESULTS)} variant="outline-secondary" size="sm">
                Назад к результатам
              </Button>
            </Col>
          </Row>

          <Loader isLoading={isLoadingEvents || isLoadingSettings} text="загрузка события">
            {settings && (
              <section className={styles.wrapper}>
                <Row className="mb-3">
                  <Row>
                    <Col>
                      <h2 className={styles.subtitle}>
                        {event?.title}, {settings?.event_city?.address},{' '}
                        {unixToMoment(settings?.event_city?.start_time).format('DD.MM.YYYY')}
                      </h2>
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col />
                    <Col md="auto">
                      <Button
                        className={styles.addBtn}
                        onClick={handleClickAdd}
                        disabled={!filterFormat?.format_public_id}
                      >
                        Добавить
                      </Button>
                    </Col>
                    <Col md="auto">
                      <ButtonGroup>
                        <Button onClick={handleDownloadOriginal}>Скачать оригинал</Button>
                        <Button onClick={handleClickUpdate} disabled={isLoadingUpdate || isLoadingSync}>
                          {isLoadingUpdate && <Spinner animation="grow" size="sm" />} Обновить
                        </Button>
                        <Button onClick={handleClickSync} disabled={isLoadingSync || isLoadingUpdate}>
                          {isLoadingSync && <Spinner animation="grow" size="sm" />} Синхронизировать
                        </Button>
                        <Button onClick={handleClickEdit} disabled={isLoadingUpdate}>
                          Изменить
                        </Button>
                        <Button onClick={handleConfirmDelete} variant="danger" disabled={isLoadingUpdate}>
                          Удалить
                        </Button>
                      </ButtonGroup>
                    </Col>
                  </Row>

                  <div className="mb-3">
                    <PageSearch values={results || []} setValues={getSearchValues} />
                  </div>

                  <ul className={styles.list}>
                    {settings?.formats?.length > 0 ? (
                      settings.formats.map((format, index) => (
                        <li key={format.external_format}>
                          <input
                            className={`${styles.radio} visually-hidden`}
                            id={`${format.external_format}_filter`}
                            type="radio"
                            name="format"
                            checked={index === selectedFormatIndex}
                            onChange={() => handleChangeFormat(format)}
                          />
                          <label className={styles.label} htmlFor={`${format.external_format}_filter`}>
                            {format.external_format}
                          </label>
                        </li>
                      ))
                    ) : (
                      <li>Нет доступных форматов</li>
                    )}
                  </ul>
                  <Col>
                    <Loader isLoading={isLoadingResults} text="загрузка результатов">
                      <TableTemplate
                        keys={resultList}
                        values={searchValues}
                        onUpdateResults={getEventResultsQuery.refetch}
                      />

                      <Row>
                        <Col>
                          <AdvancedPagination
                            limitValues={100}
                            values={filteredValues}
                            setValues={handleSetSearchValues}
                          />
                        </Col>
                      </Row>
                    </Loader>
                  </Col>
                </Row>

                <Modal show={isEdditPopup} onHide={handleClosePopup}>
                  <Form onSubmit={sendData} style={{ backgroundColor: '#eef0f8', borderRadius: '4px' }}>
                    <Modal.Header closeButton>
                      <Modal.Title>Добавление результата</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                      <FormGroup controlId="nameForm">
                        {resultList?.map((el) => (
                          <Row key={el}>
                            <Col xs={5}>
                              <Form.Label>{ALL_FIELDS[el]?.label}</Form.Label>
                            </Col>
                            <Col>
                              <FormControl
                                type={ALL_FIELDS[el]?.type}
                                name={ALL_FIELDS[el]?.value}
                                placeholder={ALL_FIELDS[el]?.label}
                                onChange={(e) => handleChangeForm(e, el)}
                                className="mb-3"
                              />
                            </Col>
                          </Row>
                        ))}
                      </FormGroup>
                    </Modal.Body>
                    <Modal.Footer>
                      <Button variant="secondary" onClick={handleClosePopup}>
                        Закрыть
                      </Button>
                      <Button type="submit" variant="primary">
                        Сохранить
                      </Button>
                    </Modal.Footer>
                  </Form>
                </Modal>
              </section>
            )}
          </Loader>

          <Modal show={confirmModal} onHide={handleCloseConfirm}>
            <Modal.Header closeButton>
              <Modal.Title>Удаление результатов</Modal.Title>
            </Modal.Header>
            <Modal.Body>Все результаты данного события будут удалены, вы действительно хотите продолжить?</Modal.Body>
            <Modal.Footer>
              <Button variant="link" onClick={() => setConfirmModal(false)}>
                Отменить
              </Button>
              <Button variant="danger" onClick={handleDeleteOrder}>
                Удалить
              </Button>
            </Modal.Footer>
          </Modal>

          <SuccessModal
            show={isSuccessUpdateModal}
            handleCloseModal={setIsSuccessUpdateModal}
            description="Данные обновлены."
          />
        </>
      )}
    </Layout>
  )
}
