@import '../../../../assets/styles/mixins.scss';

.cardWrapper {
  overflow: auto;
}

.title {
  color: #646d8f;
}

.cardHeader {
  padding-bottom: 0;
}

.cardBody {
  padding-top: 0;
}

.sort {
  position: relative;
  padding-right: 0.625rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  &::before {
    @include pseudo;
    top: 50%;
    left: -15px;
    transform: translateY(-50%);
    width: 0.68rem;
    height: 1rem;
    background-image: url('../../../../assets/img/icons/icon-sort.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }
}

.sortButton {
  visibility: hidden;
}

.tablee {
  border-collapse: separate;
  border-spacing: 0 5px;

  & thead {
    position: sticky;
    //display: flex;
    //flex-wrap: wrap;
    color: #b4bac7;
    text-transform: uppercase;
    z-index: 10;
  }

  & tbody {
    //display: flex;
    //flex-wrap: wrap;
  }
  & td {
    font-weight: 500;
    vertical-align: middle;
    border: none;
  }

  & thead td {
    font-weight: 400;
  }

  & tr {
    margin-bottom: 5px;
    //display: inline-block;
    //display: grid;
    //grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    //align-items: center;
    //width: 100%;
    background-color: rgba(255, 255, 255, 1);
    border-radius: 5px;
    //border-bottom: 5px solid #eef0f8;
  }

  & tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

.td {
  display: table-cell;
  vertical-align: middle;
}

.pictureContainer {
  width: max-content;

  background-color: rgba(100, 109, 143, 0.05);
}

.imageLink {
  position: relative;

  display: block;
  width: 48px;
  height: 48px;

  &:not(:first-child) {
    display: none;
  }

  &:hover::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    display: block;

    background-color: rgba(255, 255, 255, 0.5);
    background-image: url('../../../../assets/img/icons/icon-zoom.svg');
    background-repeat: no-repeat;
    background-position: center;

    content: '';
    z-index: 10;
  }
}

.picture {
  width: 100%;
  height: 100%;

  object-fit: cover;

  border-radius: 4px;
}

.imageWrapper {
  width: 48px;
  height: 48px;
}

.image {
  width: 100%;
  height: 100%;

  object-fit: cover;

  border-radius: 4px;
}

.editButtonWrap {
  display: flex;
  align-items: center;
  gap: 4px;
}

.editButton {
  display: grid;
  place-items: center;
  width: 24px;
  height: 24px;

  background-color: transparent;
  border: 0;
  border-radius: 4px;

  transition: background-color 0.2s;

  &:hover {
    background-color: #ffffff;
  }
}

.editButtonIcon {
  width: 14px;
  height: 14px;
}
