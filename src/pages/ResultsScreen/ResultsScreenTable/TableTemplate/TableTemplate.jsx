import { useMask } from '@react-input/mask'
import useAxios from 'axios-hooks'
import React, { useState, useEffect, useMemo } from 'react'
import {
  Button,
  Card,
  FloatingLabel,
  Form,
  FormControl,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  ModalTitle,
  Table,
} from 'react-bootstrap'
import { Link } from 'react-router-dom'

import IconEdit from '@/assets/img/icons/icon_edit.svg?react'

import { APIRoute, AppRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { removeEmptyString } from '@/utils/common'

import styles from './TableTemplate.module.scss'
import { ALL_FIELDS } from '../resultsScreenData'

const TimeInput = React.memo(function TimeInput({ name, placeholder, value, onChange, onBlur }) {
  // Check if the value is in extended format (more than 99 hours)
  const isExtendedFormat = useMemo(() => {
    return value && /^\d+h \d+m \d+s$/.test(String(value))
  }, [value])

  // Always call useMask hook to follow rules of hooks
  const inputRef = useMask({
    mask: '99:99:99',
    replacement: { 9: /\d/ },
  })

  // Validate and sanitize the value before applying mask
  const sanitizedValue = useMemo(() => {
    if (!value) return ''
    const stringValue = String(value)

    // If it looks like a valid time format, use it
    if (/^\d{1,2}:\d{2}:\d{2}$/.test(stringValue)) {
      return stringValue
    }

    // If it's in extended format (e.g., "1590h 1m 20s"), allow it as is
    if (/^\d+h \d+m \d+s$/.test(stringValue)) {
      return stringValue
    }

    // If it's too long or invalid, return empty to let user input fresh
    if (stringValue.length > 15 || !/^[\d:hms ]*$/.test(stringValue)) {
      return ''
    }

    return stringValue
  }, [value])

  return (
    <input
      ref={isExtendedFormat ? null : inputRef}
      type="text"
      name={name}
      placeholder={placeholder}
      value={sanitizedValue}
      onChange={onChange}
      onBlur={onBlur}
      readOnly={isExtendedFormat}
      style={{
        width: isExtendedFormat ? '150px' : '120px',
        fontSize: '16px',
        backgroundColor: isExtendedFormat ? '#f8f9fa' : 'unset',
        border: 'none',
        outline: 'none',
        color: isExtendedFormat ? '#6c757d' : 'inherit',
      }}
    />
  )
})

const InputRow = React.memo(function InputRow({ type, name, item, value, onBlur, inputId }) {
  const isTimeField = item === 'individual_time_result' || item === 'absolute_time_result'

  // Extract the current value to avoid complex dependency expression
  const currentValue = value[item] ?? (isTimeField ? 0 : '')

  // Initialize state with current value
  const [inputValue, setInputValue] = useState(currentValue)

  // Update inputValue when currentValue changes, but avoid infinite loops
  useEffect(() => {
    setInputValue(currentValue)
  }, [currentValue])

  /**
   * Преобразует миллисекунды в строку формата HH:MM:SS, округляя вверх до ближайшей секунды.
   * Например: 262470 мс => 00:04:23
   * @param {number} ms - миллисекунды
   * @returns {string} строка времени
   */
  const msToTimeString = (ms) => {
    if (!ms && ms !== 0) return ''

    // Convert milliseconds to seconds
    let totalSeconds = Math.ceil(ms / 1000)

    // If the original value was very small (less than 1000) and we're getting a large number,
    // it might have been in seconds already
    if (ms < 1000 && totalSeconds === 0) {
      totalSeconds = Math.ceil(ms)
    }

    // If we get a ridiculously large value (more than 100 years),
    // assume the input was already in seconds
    if (totalSeconds > 3155760000) {
      // 100 years in seconds
      totalSeconds = Math.ceil(ms)
    }

    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    // For very large times (more than 99 hours), show in a different format
    if (hours > 99) {
      return `${hours}h ${minutes}m ${seconds}s`
    }

    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
  }

  const timeStringToMs = (str) => {
    if (!str) return null

    // Handle pure numbers - but be smart about it
    if (/^\d+$/.test(str)) {
      const num = parseInt(str, 10)
      // If it's a very large number (>999999), it's likely already in milliseconds
      if (num > 999999) {
        return num
      }
      // Otherwise treat as seconds
      return Math.round(num * 1000)
    }

    // More flexible regex to handle partial input and various formats
    const match = str.match(/^(\d{0,2}):?(\d{0,2}):?(\d{0,2})$/)
    if (match) {
      const hh = parseInt(match[1] || '0', 10)
      const mm = parseInt(match[2] || '0', 10)
      const ss = parseInt(match[3] || '0', 10)

      // Validate ranges
      if (mm >= 60 || ss >= 60) {
        return null
      }

      return (hh * 3600 + mm * 60 + ss) * 1000
    }

    // Try to extract numbers from masked input like "02:27:0_"
    const cleanStr = str.replace(/[_\s]/g, '')
    const digitMatch = cleanStr.match(/^(\d{0,2}):?(\d{0,2}):?(\d{0,2})$/)
    if (digitMatch) {
      const hh = parseInt(digitMatch[1] || '0', 10)
      const mm = parseInt(digitMatch[2] || '0', 10)
      const ss = parseInt(digitMatch[3] || '0', 10)

      // Validate ranges
      if (mm >= 60 || ss >= 60) {
        return null
      }

      return (hh * 3600 + mm * 60 + ss) * 1000
    }

    return null
  }

  const handleChange = (e) => {
    setInputValue(e.target.value)
  }

  const handleBlur = () => {
    let result = inputValue
    if (isTimeField) {
      // If inputValue is already a number (original value), don't convert it
      if (typeof inputValue === 'number') {
        result = inputValue
      } else {
        // Only convert string values
        const ms = timeStringToMs(inputValue)
        // Only update if conversion succeeded, otherwise keep original value
        if (ms !== null) {
          result = ms
        } else {
          // If conversion failed, keep the original numeric value
          result = value[item]
          // Reset input to show the original value formatted correctly
          setInputValue(value[item])
        }
      }
    }
    if (result === '') result = null
    onBlur(result, item, value, inputId)
  }

  const displayValue = useMemo(() => {
    if (!isTimeField) return inputValue

    if (typeof inputValue === 'number') {
      return msToTimeString(inputValue)
    }

    // If it's already a string in proper format, use it
    if (typeof inputValue === 'string' && /^\d{1,2}:\d{2}:\d{2}$/.test(inputValue)) {
      return inputValue
    }

    // If it's an extended format string, use it as-is
    if (typeof inputValue === 'string' && /^\d+h \d+m \d+s$/.test(inputValue)) {
      return inputValue
    }

    // Try to convert from string to ms and back
    const ms = timeStringToMs(inputValue)
    if (ms !== null) {
      return msToTimeString(ms)
    }

    // If all else fails, return as string
    return String(inputValue)
  }, [inputValue, isTimeField])

  const timePlaceholder = 'чч:мм:сс'

  return isTimeField ? (
    <TimeInput
      name={name}
      placeholder={timePlaceholder}
      value={displayValue}
      onChange={handleChange}
      onBlur={handleBlur}
    />
  ) : (
    <input
      type={type}
      name={name}
      onBlur={handleBlur}
      onChange={handleChange}
      style={{
        width: '120px',
        fontSize: '16px',
        backgroundColor: 'unset',
        border: 'none',
        outline: 'none',
      }}
      value={inputValue}
    />
  )
})

const TableTemplate = React.memo(function TableTemplate({
  keys = [],
  values,
  minWidth = '1200',
  scrolled,
  maxWidth = '',
  minHeight = 'unset',
  onUpdateResults,
}) {
  const [, apiEdit] = useAxios(
    {
      method: 'PUT',
    },
    { manual: true }
  )
  const [, apiAssignTicket] = useAxios(
    {
      method: 'PUT',
    },
    { manual: true }
  )

  const [newValues, setNewValues] = useState(null)
  const [timeoutId, setTimeoutId] = useState({})
  const [isCheckedSort, setIsCheckedSort] = useState({})
  const [isShowAssignTicketModal, setIsShowAssignTicketModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [inputChangeStatus, setInputChangeStatus] = useState({})

  const openToast = useToast()

  useEffect(() => {
    if (values) {
      setNewValues(values)
    }
  }, [values])

  const handleBlurEvent = (inputValue, item, value, inputId) => {
    // JSDoc: Обработка потери фокуса для поля. Преобразует пустые строки в null и отправляет только изменённое поле.
    const newValue = {}
    let newRow = {}

    // Если пустая строка, отправлять null
    const sendValue = inputValue === '' ? null : inputValue
    const originalValue = value[item]

    // Check if the value actually changed
    const hasChanged = sendValue !== originalValue

    // If no change, don't do anything
    if (!hasChanged) {
      return
    }

    // Обновляем только одну строку через spread
    const values = newValues.map((el) => {
      if (el?.public_id === value.public_id) {
        const updated = { ...el, [item]: sendValue }
        newValue[`${item}`] = sendValue
        newRow = newValue
        return updated
      }
      return el
    })
    setNewValues(values)

    if (newRow) {
      // Проверка на null и undefined перед отправкой
      // Если sendValue === null, просто отправляем объект как есть
      const dataToSend = sendValue !== null ? removeEmptyString(newRow) : newRow

      apiEdit({
        url: `${APIRoute.RESULT_EDIT_PUBLIC_ID}/${value.public_id}`,
        data: dataToSend,
      }).then((response) => {
        if (response?.status === 200) {
          setInputStatus('success', inputId)
        } else {
          setInputStatus('error', inputId)
        }
      })
    }
  }

  const setInputStatus = (type, id) => {
    if (timeoutId[id]) {
      clearTimeout(timeoutId[id])
    }

    setInputChangeStatus((prev) => ({
      ...prev,
      [id]: type,
    }))

    const newTimeout = setTimeout(() => {
      setInputChangeStatus((prev) => ({
        ...prev,
        [id]: '',
      }))
    }, 4000)

    setTimeoutId((prev) => ({
      ...prev,
      [id]: newTimeout,
    }))
  }

  const handleSortClick = (e, el) => {
    setIsCheckedSort({ ...isCheckedSort, [`${el}`]: e.target.checked })

    const sortedValues = [...newValues].sort((a, b) => {
      if (String(a?.[el])?.split(':').length === 3) {
        const timeA = a?.[el]?.split(':')
        const timeB = b?.[el]?.split(':')

        if (timeA && timeB) {
          const [hhA, mmA, ssA] = timeA
          const [hhB, mmB, ssB] = timeB
          const secondsA = hhA * 3600 + mmA * 60 + ssA
          const secondsB = hhB * 3600 + mmB * 60 + ssB

          if (isCheckedSort[`${el}`]) {
            return secondsA - secondsB
          } else {
            return secondsB - secondsA
          }
        }
      }

      if (typeof a?.[el] === 'number') {
        if (isCheckedSort[`${el}`]) {
          return a?.[el] - b?.[el]
        } else {
          return b?.[el] - a?.[el]
        }
      } else if (typeof a?.[el] === 'string') {
        if (isCheckedSort[`${el}`]) {
          if (!a?.[el]) return 1
          else return a?.[el] > b?.[el] ? -1 : 1
        } else {
          if (!b?.[el]) return -1
          else return b?.[el] < a?.[el] ? 1 : -1
        }
      } else {
        if (isCheckedSort[`${el}`]) {
          if (!a?.[el]) return 1
        } else {
          if (!b?.[el]) return -1
        }
      }
    })

    setNewValues(sortedValues)
  }

  const returnBgColor = (status, id) => {
    if (status[id] === 'success') {
      return 'rgba(93, 235, 171, 0.3)'
    } else if (status[id] === 'error') {
      return 'rgba(220, 53, 69, 0.2)'
    }

    return '#f5f7f9'
  }

  const handleClickAssignTicket = (item) => {
    setSelectedItem(item)
    setIsShowAssignTicketModal(true)
  }

  const handleCloseAssignTicketModal = () => {
    setIsShowAssignTicketModal(false)
    setSelectedItem(null)
  }

  const handleSubmitAssignForm = (evt) => {
    evt.preventDefault()
    const formData = new FormData(evt.target)
    const startNumber = formData.get('start_number')

    apiAssignTicket({
      url: `${APIRoute.CHANGE_RESULT_TICKET_NUMBER}/${selectedItem?.public_id}/${startNumber}`,
    }).then((r) => {
      if (r.status === 200) {
        onUpdateResults()
        handleCloseAssignTicketModal()
        openToast.success({
          message: 'Билет успешно присвоен',
        })
      }
    })
  }

  if (!newValues) return null

  return (
    <>
      {newValues.length ? (
        <Card style={{ maxWidth: maxWidth, minHeight: minHeight }} className={`${styles.cardWrapper} mb-3`}>
          <Card.Body className={styles.cardBody} style={{ minWidth: `${minWidth}px` }}>
            <Table>
              <thead style={{ top: scrolled ? '0' : '68px' }}>
                <tr>
                  {keys.map((item, index) => (
                    <th key={item || `header-${index}`}>
                      <label className={styles.sort}>
                        {ALL_FIELDS[item]?.label}
                        <input
                          type="checkbox"
                          className={styles.sortButton}
                          onChange={(e) => handleSortClick(e, item)}
                        />
                      </label>
                    </th>
                  ))}
                  <th key="ticket-header">Билет</th>
                </tr>
              </thead>
              <tbody>
                {newValues.map((value, indexValue) => (
                  <tr key={value.public_id || `row-${indexValue}`}>
                    {keys.map((item, indexItem) => {
                      const cellId = `${indexValue}_${indexItem}`
                      const uniqueKey = `${value.public_id || indexValue}_${item}_${indexItem}`
                      return (
                        <td
                          className={styles.td}
                          key={uniqueKey}
                          style={{
                            backgroundColor: returnBgColor(inputChangeStatus, cellId),
                          }}
                        >
                          <InputRow
                            type={ALL_FIELDS[item]?.type}
                            name={ALL_FIELDS[item]?.value}
                            onBlur={handleBlurEvent}
                            item={item}
                            value={value}
                            inputId={cellId}
                          />
                        </td>
                      )
                    })}
                    <td>
                      <div className={styles.editButtonWrap}>
                        {value?.ticket?.public_id ? (
                          <Link to={`${AppRoute.ORDERS}/${value.format_public_id}/${value.start_number}`}>Да</Link>
                        ) : (
                          'Нет'
                        )}{' '}
                        <button className={styles.editButton} onClick={() => handleClickAssignTicket(value)}>
                          <IconEdit className={styles.editButtonIcon} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card.Body>
        </Card>
      ) : (
        <h4>Нет результатов</h4>
      )}

      <Modal show={isShowAssignTicketModal} onHide={handleCloseAssignTicketModal}>
        <ModalHeader closeButton>
          <ModalTitle>Присвоить билет</ModalTitle>
        </ModalHeader>
        <ModalBody>
          <Form onSubmit={handleSubmitAssignForm} id="assignTicketForm">
            <FloatingLabel controlId="titleLabel" label="Стартовый номер">
              <FormControl name="start_number" type="number" placeholder="" required />
            </FloatingLabel>
          </Form>
        </ModalBody>
        <ModalFooter>
          <Button type="submit" form="assignTicketForm">
            Присвоить
          </Button>
          <Button onClick={handleCloseAssignTicketModal} variant="secondary">
            Закрыть
          </Button>
        </ModalFooter>
      </Modal>
    </>
  )
})

export default TableTemplate
