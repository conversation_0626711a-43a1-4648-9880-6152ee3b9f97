.td {
  display: table-cell;
  vertical-align: middle;
}

.buttons {
  margin: 0 auto;
  //position: absolute;
  //top: 10px;
  //right: 28px;

  //display: block;
  width: max-content;
}

.btnWrapper {
  padding: 0;

  width: 24px;

  border-radius: 4px;

  &:hover {
    background-color: #ffffff;
  }
}

.btnEdit {
  width: 16px;
  height: 16px;

  background-color: transparent;
  background-image: url('../../../../../assets/img/icons/icon_edit.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  border: 0;

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    box-shadow: none;
  }
}

.btnDelete {
  background-image: url('../../../../../assets/img/icons/icon-delete.svg');
}
