import { But<PERSON>, Col, OverlayTrigger, Row } from 'react-bootstrap'

import styles from './ActionsRow.module.scss'
import { renderBtnTooltip } from '../../../pages/ShopScreen/components/ProductCard/ProductCard'

function ActionsRow({ item, onClickEdit, onClickDelete }) {
  return (
    <td className={styles.td}>
      <Row onClick={(evt) => evt.stopPropagation()} className={styles.buttons}>
        {onClickEdit && (
          <Col className={styles.btnWrapper} md={12}>
            <OverlayTrigger
              placement="left"
              delay={{ show: 250, hide: 400 }}
              overlay={(evt) => renderBtnTooltip(evt, 'редактировать')}
            >
              <Button onClick={() => onClickEdit(item)} className={styles.btnEdit} type="button" />
            </OverlayTrigger>
          </Col>
        )}
        {onClickDelete && (
          <Col className={styles.btnWrapper} md={12}>
            <OverlayTrigger
              placement="left"
              delay={{ show: 250, hide: 400 }}
              overlay={(evt) => renderBtnTooltip(evt, 'удалить')}
            >
              <Button
                onClick={() => onClickDelete(item)}
                className={`${styles.btnEdit} ${styles.btnDelete}`}
                type="button"
              />
            </OverlayTrigger>
          </Col>
        )}
      </Row>
    </td>
  )
}

export default ActionsRow
