export const ALL_FIELDS = {
  distance: { label: 'Дистанция', value: 'distance', type: 'text' },
  comments: { label: 'Примечание', value: 'comments', type: 'text' },
  status: { label: 'Статус', value: 'status', type: 'text' },
  time_start: {
    label: 'Время старта',
    value: 'time_start',
    type: 'text',
  },
  time_finish: {
    label: 'Время финиша',
    value: 'time_finish',
    type: 'text',
  },
  position_gen_absolute: {
    label: 'Место по полу чистое',
    value: 'position_gen_absolute',
    type: 'number',
  },
  position_cat_absolute: {
    label: 'Место в категории чистое',
    value: 'position_cat_absolute',
    type: 'number',
  },
  last_name: { label: 'Фамилия', value: 'last_name', type: 'text' },
  category: { label: 'Категория', value: 'category', type: 'text' },
  gender: { label: 'Пол', value: 'gender', type: 'text' },
  first_name: { label: 'Имя', value: 'first_name', type: 'text' },
  start_number: { label: 'Номер', value: 'start_number', type: 'number' },
  absolute_time_result: {
    label: 'Время чистое',
    value: 'absolute_time_result',
    type: 'number',
  },
  individual_time_result: {
    label: 'Время официальное',
    value: 'individual_time_result',
    type: 'number',
  },
  position: { label: 'Место', value: 'position', type: 'number' },
  position_gen: {
    label: 'Место по полу',
    value: 'position_gen',
    type: 'number',
  },
  position_absolute: {
    label: 'Место чистое',
    value: 'position_absolute',
    type: 'number',
  },
  position_age: {
    label: 'Место по возрасту',
    value: 'position_age',
    type: 'number',
  },
  position_cat: {
    label: 'Место по категории',
    value: 'position_cat',
    type: 'number',
  },
  position_gen_age: {
    label: 'Место по полу и возрасту',
    value: 'position_gen_age',
    type: 'number',
  },
  fine_time_result: {
    label: 'Штрафное время',
    value: 'fine_time_result',
    type: 'text',
  },
  time_result: {
    label: 'Время',
    value: 'time_result',
    type: 'text',
  },
}
