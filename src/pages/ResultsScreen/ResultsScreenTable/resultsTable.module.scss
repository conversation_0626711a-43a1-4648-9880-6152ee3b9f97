@import '../../../assets/styles/mixins.scss';

.wrapper {
  margin-top: 20px;
}
.addBtn {
  @include addButton;
}
.top {
  display: flex;
  justify-content: space-between;
}
.title {
}

.subtitle {
  margin-bottom: 20px;
}
.formatsLabel {
  margin-bottom: 1rem;
  font-size: 0.75rem;
  font-weight: 400;
  color: #000000;
}

.list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  list-style-type: none;
}

.label {
  padding: 0.9375rem 2rem;
  display: block;
  border: 1px solid #ffffff;
  border-radius: 0.25rem;
  cursor: pointer;

  &:hover {
    opacity: 0.75;
  }
}
.radio:checked ~ label {
  color: #1a191f;
  background-color: #ffffff;
}
