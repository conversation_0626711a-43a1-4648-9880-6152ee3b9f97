import useAxios from 'axios-hooks'
import moment from 'moment'
import { useState, useEffect } from 'react'
import { Badge, Button, Card, Col, Floating<PERSON>abel, Row, Stack, Tab, Tabs, FormSelect, Spinner } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

import { AppRoute } from '@/const'
import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { getFormatTime } from '@/utils/date'

import { sources, types } from './constants'
import Layout from '../../components/Layout/Layout'

const urls = {
  orgeo: APIRoute.GET_SOLD_GOODS_ORGEO,
  json: APIRoute.GET_SOLD_GOODS_JSON,
  excel: APIRoute.GET_SOLD_GOODS,
}

function ButtonDownload({ name, label, eventCityPublicId }) {
  const [{ loading }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  const downloadFile = () => {
    if (urls?.[name] && eventCityPublicId) {
      const urlApi = `${urls[name]}/${eventCityPublicId}`

      const config = {
        url: urlApi,
        method: 'GET',
        responseType: 'blob',
      }

      api({ ...config }).then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const fileName = response?.headers?.['content-disposition']?.split('filename=')[1]?.split(';')?.[0]

        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
      })
    }
  }

  return (
    <Button onClick={() => downloadFile()} type="button" disabled={loading}>
      {loading ? <Spinner className="me-1" animation="grow" size="sm" /> : <i className="bi bi-download me-1" />}{' '}
      {label}
    </Button>
  )
}

function ResultsScreen() {
  const [confirmDeleteModal, setConfirmDeleteModal] = useState(false)
  const [deleteItem, setDeleteItem] = useState(null)
  const openToast = useToast()

  const [{ data }, api] = useAxios(
    {
      url: APIRoute.RESULTS_LIST,
    },
    {
      manual: true,
    }
  )
  const [{ data: eventCityFiltered }, apiEventCityFiltered] = useAxios(
    {
      url: APIRoute.EVENT_CITY_FILTERED,
      method: 'POST',
    },
    { manual: true }
  )

  const [{ data: eventTypeList }] = useAxios(
    {
      url: APIRoute.EVENT_TYPE_LIST,
      method: 'GET',
    },
    { manual: false }
  )

  const [, apiDelete] = useAxios(
    {
      url: APIRoute.RESULTS,
      method: 'DELETE',
    },
    { manual: true }
  )

  const [{ loading: updateLoading }, apiUpdate] = useAxios(
    {
      url: APIRoute.RESULTS_UPDATE,
      method: 'PUT',
    },
    { manual: true }
  )

  const [selectedTab, setSelectedTab] = useState('events')

  const [eventId, setEventId] = useState('')
  const [eventCityPublicId, setEventCityPublicId] = useState(null)
  const [type, setType] = useState(null)
  const [eventPublicId, setEventPublicId] = useState('')
  const [sourceFilter, setSourceFilter] = useState('')
  const [filteredResults, setFilteredResults] = useState([])
  const [availableSources, setAvailableSources] = useState([])

  const navigate = useNavigate()

  useEffect(() => {
    if (data?.values) {
      let filtered = [...data.values]

      if (eventPublicId) {
        filtered = filtered.filter((item) => item.event_city.event_public_id === eventPublicId)
      }

      if (sourceFilter) {
        filtered = filtered.filter((item) => item.source === sourceFilter)
      }

      setFilteredResults(filtered)

      // Получаем уникальные источники из текущих результатов
      const uniqueSources = [...new Set(data.values.map((item) => item.source))]
        .filter((source) => source) // Фильтруем пустые значения
        .map((sourceValue) => {
          const sourceObj = sources.find((s) => s.value === sourceValue)
          return sourceObj || { value: sourceValue, name: sourceValue }
        })

      setAvailableSources(uniqueSources)
    } else {
      setFilteredResults([])
      setAvailableSources([])
    }
  }, [data, eventPublicId, sourceFilter])

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = eventTypeList?.values.find((event) => event.public_id === eventId)

    if (event) {
      const body = {
        city: {
          id: 0,
        },
        date_from: moment().subtract(5, 'days'),
        date_to: moment().add(1, 'year'),
        event_type: {
          public_id: event.public_id,
        },
      }

      apiEventCityFiltered({ data: body })
    }
  }

  const handleGetResults = (evt) => {
    const eventId = evt.target.value

    setEventId(eventId)
    // Сбрасываем фильтры при смене события
    setEventPublicId('')
    setSourceFilter('')
    api({ url: `${APIRoute.RESULTS_LIST_EVENT_TYPE}/${eventId}` })
  }

  const handleEventPublicIdChange = (evt) => {
    setEventPublicId(evt.target.value)
  }

  const handleSourceFilterChange = (evt) => {
    setSourceFilter(evt.target.value)
  }

  const handleUpdateResults = () => {
    const eventCityList = filteredResults.map((item) => item.event_city.public_id)

    if (eventCityList.length > 0) {
      apiUpdate({
        data: {
          event_city_list: eventCityList,
        },
      }).then((response) => {
        if (response?.status === 200) {
          openToast.success({ message: 'Результаты успешно обновлены' })
        }
      })
    }
  }

  const handleClickOpen = (item) => {
    if (item?.external_url) {
      window.open(item?.external_url, '_blank')
    } else {
      navigate(`table/${item?.event_city?.public_id}`)
    }
  }

  const handleSelectTab = (tab) => {
    setSelectedTab(tab)
  }

  const handleChangeCity = (evt) => {
    const values = evt.target.value.split('#')
    const cityPublicId = values[0]

    if (cityPublicId !== 0) {
      setEventCityPublicId(cityPublicId)
    }
  }

  const handleChangeField = (e) => {
    const type = e.target.value
    setType(type)
  }

  const handleOpenConfirmDeleteModal = (item) => {
    setDeleteItem(item)
    setConfirmDeleteModal(true)
  }

  const handleDeleteItem = () => {
    const body = {
      event_city: {
        public_id: deleteItem.event_city.public_id,
      },
    }

    apiDelete({ data: body }).then((response) => {
      if (response?.status === 200) {
        api({ url: `${APIRoute.RESULTS_LIST_EVENT_TYPE}/${eventId}` })
        setConfirmDeleteModal(false)
        setDeleteItem(null)
      }
    })
  }

  const handleClickEdit = (item) => {
    const id = item?.event_city?.public_id

    if (!id) {
      console.error('ID отсутствует, невозможно перейти к редактированию')
      return
    }

    const path = `${AppRoute.RESULTS_TABLE_EDIT.replace(':id', id)}`
    navigate(path)
  }

  return (
    <Layout title="Результаты" href={selectedTab === 'events' && 'results/create'}>
      <Row className="mb-3">
        <Col>
          <Tabs defaultActiveKey="events" className="mb-3" onSelect={handleSelectTab}>
            <Tab title="События" eventKey="events">
              <Row className="mb-4">
                <Col md={3}>
                  <FloatingLabel controlId="eventLabelTicket" label="Тип события">
                    <FormSelect onChange={handleGetResults} name="event_type" aria-label="Тип события">
                      <option value="">выберите один из вариантов</option>
                      {eventTypeList?.values?.map((event) => (
                        <option value={event.public_id} key={event.public_id}>
                          {event.title}
                        </option>
                      ))}
                    </FormSelect>
                  </FloatingLabel>
                </Col>
                <Col md={3}>
                  <FloatingLabel controlId="eventPublicIdLabel" label="Событие">
                    <FormSelect
                      onChange={handleEventPublicIdChange}
                      value={eventPublicId}
                      name="event_public_id"
                      aria-label="Событие"
                    >
                      <option value="">выберите один из вариантов</option>
                      {data?.values
                        ?.map((item) => item.event_city.event_public_id)
                        .filter((id, index, self) => id && self.indexOf(id) === index)
                        .map((id) => (
                          <option value={id} key={id}>
                            {id}
                          </option>
                        ))}
                    </FormSelect>
                  </FloatingLabel>
                </Col>
                <Col md={3}>
                  <FloatingLabel controlId="sourceFilterLabel" label="Источник">
                    <FormSelect
                      onChange={handleSourceFilterChange}
                      value={sourceFilter}
                      name="source"
                      aria-label="Источник"
                    >
                      <option value="">выберите один из вариантов</option>
                      {availableSources.map((source) => (
                        <option value={source.value} key={source.value}>
                          {source.name}
                        </option>
                      ))}
                    </FormSelect>
                  </FloatingLabel>
                </Col>
                <Col md={3}>
                  <Button
                    onClick={handleUpdateResults}
                    className="w-100 h-100"
                    disabled={filteredResults.length === 0 || updateLoading}
                  >
                    {updateLoading ? <Spinner animation="border" size="sm" /> : null} Обновить для выбранных
                  </Button>
                </Col>
              </Row>

              <Row className="g-3">
                {filteredResults.length > 0 ? (
                  filteredResults.map((result) => (
                    <Col className="col-md-4" key={result.public_id}>
                      <Card className="h-100 shadow-sm">
                        <Card.Header className="d-flex justify-content-between align-items-center">
                          <h6 className="mb-0 text-truncate">
                            {getFormatTime(result?.event_city?.start_time, result?.event_city?.timezone)}
                          </h6>
                          <Badge bg="primary" pill>
                            {result.source || 'Нет источника'}
                          </Badge>
                        </Card.Header>
                        <Card.Body className="d-flex flex-column">
                          <Card.Title className="fs-6 mb-3">{result.event_city.address || 'Нет адреса'}</Card.Title>
                          <div className="text-muted small mb-3">ID: {result.event_city.public_id}</div>
                          {result.event_city.event_public_id && (
                            <div className="mb-3">
                              <Badge bg="secondary">{result.event_city.event_public_id}</Badge>
                            </div>
                          )}
                          <div className="mt-auto pt-3 border-top">
                            <Stack direction="horizontal" gap={2}>
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => handleClickOpen(result)}
                                className="flex-fill"
                              >
                                Открыть
                              </Button>
                              <Button
                                variant="outline-secondary"
                                size="sm"
                                onClick={() => handleClickEdit(result)}
                                className="flex-fill"
                              >
                                Изменить
                              </Button>
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleOpenConfirmDeleteModal(result)}
                                className="flex-fill"
                              >
                                Удалить
                              </Button>
                            </Stack>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  ))
                ) : data?.values?.length > 0 ? (
                  <Col>
                    <div className="text-center p-5 rounded">
                      <p className="mb-0">Нет результатов с выбранными фильтрами</p>
                    </div>
                  </Col>
                ) : (
                  <Col>
                    <div className="text-center p-5 rounded">
                      <p className="mb-0">Выберите событие для отображения результатов</p>
                    </div>
                  </Col>
                )}
              </Row>
            </Tab>
            <Tab title="Участники" eventKey="members">
              <Row className="g-3 mb-5">
                <Col md={3}>
                  <FloatingLabel controlId="eventLabelTicket" label="Событие">
                    <FormSelect onChange={handleChangeEvent} name="event_type" aria-label="Событие">
                      <option value="">выберите один из вариантов</option>
                      {eventTypeList?.values?.map((event) => (
                        <option value={event.public_id} key={event.public_id}>
                          {event.title}
                        </option>
                      ))}
                    </FormSelect>
                  </FloatingLabel>
                </Col>

                <Col md={3}>
                  <FloatingLabel controlId="cityLabelTicket" label="Город">
                    <FormSelect onChange={handleChangeCity} name="event_city" aria-label="Город">
                      <option value="">выберите один из вариантов</option>
                      {eventCityFiltered?.values?.map((city) => (
                        <option value={`${city.public_id}#${city.city.id}`} key={city.public_id}>
                          {city.address} — {getFormatTime(city.start_time, city.timezone)}
                        </option>
                      ))}
                    </FormSelect>
                  </FloatingLabel>
                </Col>

                <Col md={3}>
                  <FloatingLabel controlId="sourceLabelTicket" label="Тип">
                    <FormSelect onChange={handleChangeField} name="type" aria-label="Тип">
                      <option value="">выберите один из вариантов</option>
                      {types?.map((el) => (
                        <option value={el?.value} key={el?.value}>
                          {el?.name}
                        </option>
                      ))}
                    </FormSelect>
                  </FloatingLabel>
                </Col>
                <Col md={3}>
                  <ButtonDownload label="Скачать" name={type} eventCityPublicId={eventCityPublicId} />
                </Col>
              </Row>
            </Tab>
          </Tabs>
        </Col>
      </Row>

      <ConfirmDeleteModal isShow={confirmDeleteModal} onClose={setConfirmDeleteModal} onDeleteItem={handleDeleteItem} />
    </Layout>
  )
}

export default ResultsScreen
