import { useEffect, useCallback, useMemo } from 'react'
import { <PERSON><PERSON>, Col, Container, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { AppRoute, MAX_IMG_SIZE } from '@/const'
import { useCreateNews, useGetNews, useUpdateNews } from '@/features/news/api'
import { getChangedValues } from '@/helpers/getChangedValues'
import { useToast } from '@/hooks/useToast'
import { convertBase64, removeEmptyString } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { checkSetValue } from '@/utils/forms'

import NewsFormBanners from './components/NewsFormBanners'
import NewsFormEditor from './components/NewsFormEditor'

// Constants
const DEFAULT_NEWS_VALUES = {
  headline: '',
  subtitle: '',
  restriction: '',
  active: true,
  mailing: true,
  publication_date: null,
  text: '',
  banners: {},
}

const NewsEditorScreen = () => {
  const navigate = useNavigate()
  const { public_id } = useParams()
  const openToast = useToast()

  const isEdit = !!public_id
  const { data: newsData, isLoading } = useGetNews()
  const createNewsMutation = useCreateNews()
  const updateNewsMutation = useUpdateNews()

  // Find the news item being edited
  const newsItem = useMemo(() => {
    if (!isEdit || !newsData?.data) return null
    return newsData.data.find((item) => item.public_id === public_id)
  }, [isEdit, newsData?.data, public_id])

  // Form default values
  const defaultValues = useMemo(() => {
    if (!isEdit || !newsItem) return DEFAULT_NEWS_VALUES

    return {
      headline: newsItem.headline || '',
      subtitle: newsItem.subtitle || '',
      restriction: newsItem.restriction || '',
      active: newsItem.active ?? true,
      mailing: newsItem.mailing ?? true,
      publication_date: newsItem.publication_date
        ? unixToMoment(newsItem.publication_date).format('YYYY-MM-DDTHH:mm')
        : null,
      text: newsItem.text || '',
      banners: newsItem.banners || {},
    }
  }, [isEdit, newsItem])

  // Form setup
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting, dirtyFields },
    reset,
  } = useForm({
    defaultValues,
  })

  // Initialize form with default values
  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues)
    }
  }, [defaultValues, reset])

  // Set document title
  useEffect(() => {
    document.title = isEdit ? 'Редактирование новости' : 'Создание новости'
  }, [isEdit])

  // Editor change handler
  const handleEditorChange = useCallback(
    (html) => {
      setValue('text', html, { shouldDirty: true, shouldValidate: true })
    },
    [setValue]
  )

  // Banner upload handler
  const handleBannerUpload = useCallback(
    async (bannerType, file) => {
      if (!file) return

      try {
        const fileSizeInB = file.size
        if (fileSizeInB > MAX_IMG_SIZE) {
          openToast.error({
            message: `Файл слишком большой: ${file.name}. Максимальный размер: 5MB`,
            duration: 6000,
          })
          return
        }

        const base64 = await convertBase64(file)
        if (base64) {
          const currentBanners = watch('banners') || {}
          setValue('banners', { ...currentBanners, [bannerType]: base64 }, { shouldDirty: true })
        }
      } catch (error) {
        console.error('Error uploading banner:', error)
        openToast.error({
          message: 'Ошибка при загрузке изображения',
          duration: 4000,
        })
      }
    },
    [setValue, watch, openToast]
  )

  // Banner delete handler
  const handleBannerDelete = useCallback(
    (bannerType) => {
      const currentBanners = watch('banners') || {}
      const updatedBanners = { ...currentBanners }
      delete updatedBanners[bannerType]

      setValue('banners', updatedBanners, { shouldDirty: true })
    },
    [setValue, watch]
  )

  // Form submission
  const onSubmit = useCallback(
    async (data) => {
      try {
        if (isEdit) {
          const changedData = getChangedValues(dirtyFields, data, defaultValues)
          if (Object.keys(changedData).length > 0) {
            await updateNewsMutation.mutateAsync({ ...changedData, public_id })
          } else {
            openToast.info({ message: 'Нет изменений для сохранения' })
          }
        } else {
          const processedData = {
            ...removeEmptyString(data),
            publication_date: data.publication_date ? new Date(data.publication_date) : null,
          }
          await createNewsMutation.mutateAsync(processedData)
        }
      } catch (error) {
        console.error('Error submitting form:', error)
        openToast.error({
          message: 'Произошла ошибка при сохранении новости',
          duration: 4000,
        })
      }
    },
    [isEdit, public_id, createNewsMutation, updateNewsMutation, openToast, dirtyFields, defaultValues]
  )

  // Watch banners for validation
  const watchedBanners = watch('banners')
  const isFormValid = !isEdit || (watchedBanners?.main && watchedBanners?.small && watchedBanners?.preview)

  if (isLoading) {
    return (
      <Layout title={isEdit ? 'Редактирование новости' : 'Создание новости'}>
        <Container>
          <div className="d-flex justify-content-center p-5">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Загрузка...</span>
            </div>
          </div>
        </Container>
      </Layout>
    )
  }

  if (isEdit && !newsItem) {
    return (
      <Layout title="Новость не найдена">
        <Container>
          <div className="alert alert-warning">
            <h4>Новость не найдена</h4>
            <p>Запрашиваемая новость не существует или была удалена.</p>
            <Button variant="primary" onClick={() => navigate(AppRoute.NEWS)}>
              Вернуться к списку новостей
            </Button>
          </div>
        </Container>
      </Layout>
    )
  }

  return (
    <Layout title={isEdit ? 'Редактирование новости' : 'Создание новости'}>
      <Container>
        {/* Header with breadcrumb and actions */}
        <div className="d-flex justify-content-between align-items-center mb-4">
          <div>
            <nav aria-label="breadcrumb">
              <ol className="breadcrumb mb-1">
                <li className="breadcrumb-item">
                  <Button variant="link" className="p-0 text-decoration-none" onClick={() => navigate(AppRoute.NEWS)}>
                    <i className="bi bi-newspaper me-1"></i>
                    Новости
                  </Button>
                </li>
                <li className="breadcrumb-item active" aria-current="page">
                  {isEdit ? 'Редактирование' : 'Создание'}
                </li>
              </ol>
            </nav>
            <h2 className="mb-0">
              <i className={`bi ${isEdit ? 'bi-pencil-square' : 'bi-plus-circle'} me-2 text-primary`}></i>
              {isEdit ? 'Редактирование новости' : 'Создание новости'}
            </h2>
          </div>
          <div className="d-flex gap-2">
            <Button variant="outline-secondary" onClick={() => navigate(AppRoute.NEWS)} disabled={isSubmitting}>
              <i className="bi bi-arrow-left me-1"></i>
              Назад
            </Button>
          </div>
        </div>

        <Form onSubmit={handleSubmit(onSubmit)} className="mb-5">
          {/* Progress indicator */}
          {!isEdit && (
            <div className="card mb-4 border-info">
              <div className="card-body py-3">
                <div className="d-flex align-items-center">
                  <div className="me-3">
                    <i className="bi bi-info-circle-fill text-info fs-4"></i>
                  </div>
                  <div className="flex-grow-1">
                    <h6 className="mb-1">Создание новости</h6>
                    <div className="progress" style={{ height: '4px' }}>
                      <div
                        className={`progress-bar ${isFormValid ? 'bg-success' : 'bg-warning'}`}
                        role="progressbar"
                        style={{ width: isFormValid ? '100%' : '70%' }}
                      ></div>
                    </div>
                    <small className="text-muted mt-1">
                      {isFormValid
                        ? 'Все обязательные поля заполнены'
                        : 'Заполните основную информацию и загрузите баннеры'}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="card mb-4 shadow-sm">
            <div className="card-header bg-body-tertiary">
              <div className="d-flex align-items-center">
                <i className="bi bi-info-circle me-2 text-primary"></i>
                <h5 className="mb-0">Основная информация</h5>
                <small className="ms-auto text-muted">Обязательные поля отмечены *</small>
              </div>
            </div>
            <div className="card-body">
              <Row className="g-4">
                <Col md={12}>
                  <Form.Group>
                    <div className="d-flex align-items-center mb-2">
                      <i className="bi bi-type me-2 text-muted"></i>
                      <Form.Label className="mb-0 fw-medium">Заголовок *</Form.Label>
                    </div>
                    <FormControl
                      {...register('headline', {
                        required: 'Заголовок обязателен',
                        minLength: { value: 3, message: 'Минимум 3 символа' },
                        maxLength: { value: 200, message: 'Максимум 200 символов' },
                        setValueAs: (v) => checkSetValue(v, defaultValues.headline, 'text'),
                      })}
                      type="text"
                      placeholder="Введите привлекательный заголовок новости..."
                      isInvalid={!!errors.headline}
                      className="form-control-lg"
                    />
                    {errors.headline ? (
                      <Form.Control.Feedback type="invalid">
                        <i className="bi bi-exclamation-circle me-1"></i>
                        {errors.headline?.message}
                      </Form.Control.Feedback>
                    ) : (
                      <Form.Text className="text-muted">
                        <i className="bi bi-lightbulb me-1"></i>
                        Хороший заголовок привлекает внимание и точно передает суть новости
                      </Form.Text>
                    )}
                  </Form.Group>
                </Col>

                <Col md={12}>
                  <Form.Group>
                    <div className="d-flex align-items-center mb-2">
                      <i className="bi bi-text-paragraph me-2 text-muted"></i>
                      <Form.Label className="mb-0 fw-medium">Подзаголовок</Form.Label>
                      <span className="badge bg-secondary ms-2">необязательно</span>
                    </div>
                    <FormControl
                      {...register('subtitle', {
                        maxLength: { value: 300, message: 'Максимум 300 символов' },
                        setValueAs: (v) => checkSetValue(v, defaultValues.subtitle, 'text'),
                      })}
                      type="text"
                      placeholder="Краткое описание или дополнительная информация..."
                      isInvalid={!!errors.subtitle}
                    />
                    {errors.subtitle ? (
                      <Form.Control.Feedback type="invalid">
                        <i className="bi bi-exclamation-circle me-1"></i>
                        {errors.subtitle?.message}
                      </Form.Control.Feedback>
                    ) : (
                      <Form.Text className="text-muted">
                        Подзаголовок поможет читателям лучше понять содержание новости
                      </Form.Text>
                    )}
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group>
                    <div className="d-flex align-items-center mb-2">
                      <i className="bi bi-calendar-event me-2 text-muted"></i>
                      <Form.Label className="mb-0 fw-medium">Дата публикации</Form.Label>
                    </div>
                    <FormControl
                      {...register('publication_date', {
                        setValueAs: (v) => checkSetValue(v, defaultValues.publication_date, 'datetime'),
                      })}
                      type="datetime-local"
                      isInvalid={!!errors.publication_date}
                    />
                    {errors.publication_date ? (
                      <Form.Control.Feedback type="invalid">
                        <i className="bi bi-exclamation-circle me-1"></i>
                        {errors.publication_date?.message}
                      </Form.Control.Feedback>
                    ) : (
                      <Form.Text className="text-muted">Оставьте пустым для немедленной публикации</Form.Text>
                    )}
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group>
                    <div className="d-flex align-items-center mb-2">
                      <i className="bi bi-shield-exclamation me-2 text-muted"></i>
                      <Form.Label className="mb-0 fw-medium">Ограничения</Form.Label>
                    </div>
                    <FormControl
                      {...register('restriction', {
                        maxLength: { value: 100, message: 'Максимум 100 символов' },
                        setValueAs: (v) => checkSetValue(v, defaultValues.restriction, 'text'),
                      })}
                      type="text"
                      placeholder="Например: 18+, только для участников..."
                      isInvalid={!!errors.restriction}
                    />
                    {errors.restriction ? (
                      <Form.Control.Feedback type="invalid">
                        <i className="bi bi-exclamation-circle me-1"></i>
                        {errors.restriction?.message}
                      </Form.Control.Feedback>
                    ) : (
                      <Form.Text className="text-muted">
                        Укажите возрастные или другие ограничения для просмотра
                      </Form.Text>
                    )}
                  </Form.Group>
                </Col>
              </Row>
            </div>
          </div>

          {/* Settings */}
          <div className="card mb-4 shadow-sm">
            <div className="card-header bg-body-tertiary">
              <div className="d-flex align-items-center">
                <i className="bi bi-gear me-2 text-primary"></i>
                <h5 className="mb-0">Настройки публикации</h5>
              </div>
            </div>
            <div className="card-body">
              <Row>
                <Col md={6}>
                  <div className="border rounded p-3 h-100">
                    <Form.Group>
                      <Form.Check
                        {...register('active', {
                          setValueAs: (v) => checkSetValue(v, defaultValues.active, 'checkbox'),
                        })}
                        type="switch"
                        id="activeCheck"
                        label=""
                        className="mb-2"
                      />
                      <div className="d-flex align-items-start">
                        <i className="bi bi-eye me-2 text-success mt-1"></i>
                        <div>
                          <h6 className="mb-1">Опубликовать новость</h6>
                          <small className="text-muted">Новость будет видна всем пользователям сайта</small>
                        </div>
                      </div>
                    </Form.Group>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="border rounded p-3 h-100">
                    <Form.Group>
                      <Form.Check
                        {...register('mailing', {
                          setValueAs: (v) => checkSetValue(v, defaultValues.mailing, 'checkbox'),
                        })}
                        type="switch"
                        id="mailingCheck"
                        label=""
                        className="mb-2"
                      />
                      <div className="d-flex align-items-start">
                        <i className="bi bi-envelope me-2 text-info mt-1"></i>
                        <div>
                          <h6 className="mb-1">Отправить в рассылку</h6>
                          <small className="text-muted">Уведомление будет отправлено подписчикам</small>
                        </div>
                      </div>
                    </Form.Group>
                  </div>
                </Col>
              </Row>
            </div>
          </div>

          {/* Banners */}
          <NewsFormBanners
            banners={watchedBanners}
            onUpload={handleBannerUpload}
            onDelete={handleBannerDelete}
            isEdit={isEdit}
            register={register}
            errors={errors}
          />

          {/* Content Editor */}
          <NewsFormEditor value={watch('text') || ''} onChange={handleEditorChange} error={errors.text?.message} />

          {/* Action Buttons */}
          <div className="card shadow-sm">
            <div className="card-body">
              <Row>
                <Col>
                  <div className="d-flex gap-3 justify-content-end">
                    <Button
                      type="button"
                      variant="outline-secondary"
                      size="lg"
                      onClick={() => navigate(AppRoute.NEWS)}
                      disabled={isSubmitting}
                      className="px-4"
                    >
                      <i className="bi bi-x-circle me-2"></i>
                      Отменить
                    </Button>
                    <Button
                      type="submit"
                      variant="success"
                      size="lg"
                      disabled={
                        isSubmitting ||
                        (!isEdit && !isFormValid) ||
                        createNewsMutation.isLoading ||
                        updateNewsMutation.isLoading
                      }
                      className="px-4"
                    >
                      {isSubmitting || createNewsMutation.isLoading || updateNewsMutation.isLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true" />
                          Сохранение...
                        </>
                      ) : (
                        <>
                          <i className={`bi ${isEdit ? 'bi-check-circle' : 'bi-plus-circle'} me-2`}></i>
                          {isEdit ? 'Сохранить изменения' : 'Создать новость'}
                        </>
                      )}
                    </Button>
                  </div>
                </Col>
              </Row>
              {!isEdit && !isFormValid && (
                <Row className="mt-3">
                  <Col>
                    <div className="alert alert-warning d-flex align-items-center mb-0">
                      <i className="bi bi-exclamation-triangle-fill me-3 fs-4"></i>
                      <div>
                        <strong>Внимание!</strong> Для создания новости необходимо:
                        <ul className="mb-0 mt-1">
                          <li>Заполнить заголовок</li>
                          <li>Загрузить все три баннера (главный, маленький и превью)</li>
                          <li>Добавить текст новости</li>
                        </ul>
                      </div>
                    </div>
                  </Col>
                </Row>
              )}
            </div>
          </div>
        </Form>
      </Container>
    </Layout>
  )
}

export default NewsEditorScreen
