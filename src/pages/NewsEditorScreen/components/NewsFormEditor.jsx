import { useMemo } from 'react'
import { Card } from 'react-bootstrap'

import QuillEditor from '@/components/QuillEditor/QuillEditor'

const NewsFormEditor = ({ value, onChange, error }) => {
  const getPlainText = (html) => {
    if (typeof html === 'string') {
      const doc = new DOMParser().parseFromString(html, 'text/html')
      return doc.body.textContent || ''
    }
    return ''
  }

  const charCount = useMemo(() => getPlainText(value).length, [value])

  return (
    <Card className="mb-4 shadow-sm">
      <Card.Header className="bg-body-tertiary">
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <i className="bi bi-pencil-square me-2 text-primary"></i>
            <h5 className="mb-0">Содержание новости</h5>
            <span className="badge bg-danger ms-2">обязательно</span>
          </div>
          <div className="d-flex align-items-center text-muted">
            <i className="bi bi-info-circle me-1"></i>
            <small>Используйте панель инструментов для форматирования</small>
          </div>
        </div>
      </Card.Header>
      <Card.Body className="p-0">
        <div className="position-relative">
          <style>
            {`
              .ql-toolbar {
                border-radius: 0 !important;
                border: none !important;
                border-bottom: 1px solid var(--bs-border-color) !important;
                background: var(--bs-secondary-bg) !important;
                padding: 12px 16px !important;
              }
              .ql-container {
                border: none !important;
              }
              .ql-editor {
                min-height: 300px !important;
                padding: 20px !important;
                font-size: 16px !important;
                line-height: 1.6 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
              }
              .ql-editor.ql-blank::before {
                color: var(--bs-secondary-color) !important;
                font-style: italic !important;
                left: 20px !important;
              }
              .is-invalid .ql-editor {
                border: 1px solid var(--bs-danger) !important;
                border-radius: 0.375rem !important;
              }
              /* Цвет иконок / текста в Quill Toolbar для поддержки тёмной темы */
              .ql-toolbar button {
                color: var(--bs-body-color) !important;
              }
              .ql-toolbar .ql-picker {
                color: var(--bs-body-color) !important;
              }
              .ql-toolbar .ql-stroke {
                stroke: var(--bs-body-color) !important;
              }
              .ql-toolbar .ql-fill {
                fill: var(--bs-body-color) !important;
              }
              /* Ховер / активное состояние */
              .ql-toolbar button:hover .ql-stroke,
              .ql-toolbar button.ql-active .ql-stroke,
              .ql-toolbar button:hover .ql-fill,
              .ql-toolbar button.ql-active .ql-fill {
                stroke: var(--bs-primary) !important;
                fill: var(--bs-primary) !important;
              }
            `}
          </style>
          <QuillEditor value={value} onChange={onChange} placeholder="Начните писать содержание новости..." />
          <div className="position-absolute bottom-0 end-0 p-3">
            <small className="text-muted bg-body px-2 py-1 rounded shadow-sm">
              <i className="bi bi-type me-1"></i>
              {charCount} символов
            </small>
          </div>
        </div>
        {error && (
          <div className="px-3 py-2 bg-danger bg-opacity-10 border-top border-danger">
            <div className="d-flex align-items-center text-danger">
              <i className="bi bi-exclamation-circle me-2"></i>
              <small>{error}</small>
            </div>
          </div>
        )}
      </Card.Body>
      <Card.Footer className="bg-body-tertiary">
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center gap-3">
            <small className="text-muted">
              <i className="bi bi-lightbulb me-1"></i>
              <strong>Совет:</strong> Добавляйте изображения и ссылки для лучшего восприятия
            </small>
          </div>
          <div className="d-flex align-items-center gap-2">
            <small className="text-muted">
              <i className="bi bi-check-circle text-success me-1"></i>
              Автосохранение включено
            </small>
          </div>
        </div>
      </Card.Footer>
    </Card>
  )
}

export default NewsFormEditor
