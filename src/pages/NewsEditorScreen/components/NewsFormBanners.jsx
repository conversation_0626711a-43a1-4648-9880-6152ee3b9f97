import { useCallback, useRef, useState } from 'react'
import { But<PERSON>, Card, Col, Figure, Row } from 'react-bootstrap'

import { getImageSrc } from '@/utils/images'

const BANNER_TYPES = [
  {
    key: 'main',
    label: 'Главный баннер',
    description: 'Отображается на главной странице и в карточке новости',
    icon: 'bi-image',
    color: 'primary',
    required: true,
    size: '1200×600 px',
  },
  {
    key: 'small',
    label: 'Маленький баннер',
    description: 'Используется в списках и боковых блоках',
    icon: 'bi-images',
    color: 'success',
    required: true,
    size: '400×200 px',
  },
  {
    key: 'preview',
    label: 'Превью баннер',
    description: 'Показывается в превью и социальных сетях',
    icon: 'bi-eye',
    color: 'info',
    required: true,
    size: '600×315 px',
  },
]

const NewsFormBanners = ({ banners = {}, onUpload, onDelete, isEdit }) => {
  const [draggedOver, setDraggedOver] = useState(null)
  const fileInputRefs = useRef({})

  const handleFileSelect = useCallback(
    (bannerType, file) => {
      if (file && onUpload) {
        onUpload(bannerType, file)
      }
    },
    [onUpload]
  )

  const handleFileChange = useCallback(
    (bannerType) => (event) => {
      const file = event.target.files?.[0]
      if (file) {
        handleFileSelect(bannerType, file)
      }
      // Clear the input value to allow re-uploading the same file
      event.target.value = ''
    },
    [handleFileSelect]
  )

  const handleDragOver = useCallback((event) => {
    event.preventDefault()
    event.stopPropagation()
  }, [])

  const handleDragEnter = useCallback(
    (bannerType) => (event) => {
      event.preventDefault()
      event.stopPropagation()
      setDraggedOver(bannerType)
    },
    []
  )

  const handleDragLeave = useCallback((event) => {
    event.preventDefault()
    event.stopPropagation()
    // Only clear if we're leaving the drop zone entirely
    if (!event.currentTarget.contains(event.relatedTarget)) {
      setDraggedOver(null)
    }
  }, [])

  const handleDrop = useCallback(
    (bannerType) => (event) => {
      event.preventDefault()
      event.stopPropagation()
      setDraggedOver(null)

      const files = event.dataTransfer.files
      if (files.length > 0) {
        handleFileSelect(bannerType, files[0])
      }
    },
    [handleFileSelect]
  )

  const handleDeleteBanner = useCallback(
    (bannerType) => () => {
      if (onDelete) {
        onDelete(bannerType)
      }
    },
    [onDelete]
  )

  const triggerFileInput = useCallback((bannerType) => {
    fileInputRefs.current[bannerType]?.click()
  }, [])

  return (
    <Card className="mb-4 shadow-sm">
      <Card.Header className="bg-body-tertiary">
        <div className="d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <i className="bi bi-images me-2 text-primary"></i>
            <h5 className="mb-0">Баннеры новости</h5>
            {!isEdit && <span className="badge bg-danger ms-2">обязательно</span>}
          </div>
          <div className="d-flex align-items-center">
            {isEdit ? (
              <small className="text-muted">
                <i className="bi bi-info-circle me-1"></i>
                Загрузите новые изображения для замены
              </small>
            ) : (
              <div className="d-flex align-items-center">
                <div className="me-3">
                  <small className="text-muted">Прогресс:</small>
                  <div className="progress ms-2" style={{ width: '80px', height: '6px' }}>
                    <div
                      className="progress-bar bg-success"
                      style={{
                        width: `${(Object.keys(banners).length / BANNER_TYPES.length) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>
                <small className="text-muted">
                  {Object.keys(banners).length}/{BANNER_TYPES.length}
                </small>
              </div>
            )}
          </div>
        </div>
      </Card.Header>
      <Card.Body>
        {/* Instructions */}
        <div className="alert alert-info d-flex align-items-center mb-4">
          <i className="bi bi-lightbulb-fill me-3 fs-4"></i>
          <div>
            <strong>Рекомендации по изображениям:</strong>
            <ul className="mb-0 mt-1">
              <li>Формат: JPG, PNG или JPEG</li>
              <li>Максимальный размер: 5 МБ</li>
              <li>Высокое качество для лучшего отображения</li>
            </ul>
          </div>
        </div>

        <Row className="g-4">
          {BANNER_TYPES.map((bannerType) => {
            const hasImage = banners[bannerType.key]
            const isDraggedOver = draggedOver === bannerType.key

            return (
              <Col lg={4} md={6} key={bannerType.key}>
                <div className="h-100 d-flex flex-column">
                  {/* Banner type header */}
                  <div className="d-flex align-items-center mb-3">
                    <div className={`me-2 p-2 rounded-circle bg-${bannerType.color} bg-opacity-10`}>
                      <i className={`${bannerType.icon} text-${bannerType.color}`}></i>
                    </div>
                    <div className="flex-grow-1">
                      <h6 className="mb-0 d-flex align-items-center">
                        {bannerType.label}
                        {bannerType.required && <span className="text-danger ms-1">*</span>}
                        {hasImage && <i className="bi bi-check-circle-fill text-success ms-2"></i>}
                      </h6>
                      <small className="text-muted">{bannerType.description}</small>
                    </div>
                  </div>

                  {/* Upload Zone */}
                  <div
                    className={`border rounded-3 p-4 position-relative transition-all flex-grow-1 d-flex ${
                      isDraggedOver
                        ? `border-${bannerType.color} bg-${bannerType.color} bg-opacity-10 border-2`
                        : hasImage
                          ? 'border-success bg-success bg-opacity-5'
                          : 'border-2 border-dashed border-secondary bg-body-tertiary'
                    }`}
                    style={{
                      minHeight: '220px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                    }}
                    onDragOver={handleDragOver}
                    onDragEnter={handleDragEnter(bannerType.key)}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop(bannerType.key)}
                    onClick={() => triggerFileInput(bannerType.key)}
                  >
                    {hasImage ? (
                      // Preview existing image
                      <div className="position-relative h-100 d-flex flex-column align-items-center justify-content-center">
                        <div
                          className="d-flex align-items-center justify-content-center mb-3"
                          style={{ minHeight: '120px' }}
                        >
                          <Figure className="mb-0">
                            <Figure.Image
                              src={getImageSrc(banners[bannerType.key])}
                              alt={bannerType.label}
                              style={{
                                maxWidth: '100%',
                                maxHeight: '120px',
                                width: 'auto',
                                height: 'auto',
                                objectFit: 'contain',
                                borderRadius: '8px',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                              }}
                            />
                          </Figure>
                        </div>
                        <div className="position-absolute top-0 end-0 p-2">
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteBanner(bannerType.key)()
                            }}
                            className="rounded-circle p-1"
                            style={{ width: '32px', height: '32px' }}
                          >
                            <i className="bi bi-trash"></i>
                          </Button>
                        </div>
                        <div className="text-center">
                          <div className="d-flex align-items-center justify-content-center mb-1">
                            <i className="bi bi-check-circle-fill text-success me-2"></i>
                            <small className="text-success fw-medium">Загружено</small>
                          </div>
                          <small className="text-muted">Нажмите для замены</small>
                        </div>
                      </div>
                    ) : (
                      // Upload prompt
                      <div className="d-flex flex-column align-items-center justify-content-center h-100 text-center">
                        <div className="mb-3">
                          <i
                            className={`${bannerType.icon} ${
                              isDraggedOver ? `text-${bannerType.color}` : 'text-muted'
                            } transition-all`}
                            style={{ fontSize: '2.5rem' }}
                          />
                        </div>
                        <div className="mb-3">
                          <h6 className={isDraggedOver ? `text-${bannerType.color}` : 'text-muted'}>
                            {isDraggedOver ? 'Отпустите для загрузки' : 'Перетащите изображение'}
                          </h6>
                          <p className="text-muted small mb-2">или</p>
                          <Button variant={`outline-${bannerType.color}`} size="sm" className="px-3">
                            <i className="bi bi-cloud-upload me-2"></i>
                            Выбрать файл
                          </Button>
                        </div>
                        <div className="mt-auto pt-2">
                          <small className="text-muted d-block mb-1">
                            <i className="bi bi-info-circle me-1"></i>
                            Рекомендуемый размер: {bannerType.size}
                          </small>
                          <small className="text-muted">Максимум 5 МБ</small>
                        </div>
                      </div>
                    )}

                    {/* Hidden file input */}
                    <input
                      type="file"
                      ref={(el) => (fileInputRefs.current[bannerType.key] = el)}
                      onChange={handleFileChange(bannerType.key)}
                      accept="image/jpeg,image/jpg,image/png"
                      style={{ display: 'none' }}
                    />
                  </div>
                </div>
              </Col>
            )
          })}
        </Row>
      </Card.Body>
      <Card.Footer className="bg-body-tertiary">
        <div className="d-flex align-items-center justify-content-between">
          <small className="text-muted">
            <i className="bi bi-shield-check me-1"></i>
            Все изображения автоматически оптимизируются для веб-отображения
          </small>
          <small className="text-muted">
            <i className="bi bi-question-circle me-1"></i>
            Нужна помощь? Обратитесь к администратору
          </small>
        </div>
      </Card.Footer>
    </Card>
  )
}

export default NewsFormBanners
