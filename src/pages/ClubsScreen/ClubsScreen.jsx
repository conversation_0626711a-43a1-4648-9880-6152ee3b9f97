import { useState } from 'react'
import { Col, Row, Tab, Tabs } from 'react-bootstrap'

import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import Layout from '@/components/Layout/Layout'

import { APIRoute } from '@/const'
import { useGetClubs } from '@/features/clubs/api/getClubs'
import ClubStatistics from '@/pages/ClubsScreen/components/ClubStatistics/ClubStatistics'

import ClubsTable from './components/ClubsTable/ClubsTable'
import CompetitionsType from './components/CompetitionsType/CompetitionsType'

const STEP = 20

function ClubsScreen() {
  const [currentPage, setCurrentPage] = useState(1)
  const {
    data: clubsData,
    refetch: updateClubs,
    isLoading,
  } = useGetClubs({
    skip: (currentPage - 1) * STEP,
    limit: STEP,
  })
  const clubs = clubsData?.data?.values || []
  const stat = clubsData?.data?.stat || {}

  const handleUpdateClubs = () => {
    updateClubs()
  }

  const pages = Math.ceil(stat.total_clubs / STEP)

  return (
    <Layout title="Клубы" isLoading={isLoading}>
      <Row className="align-items-center mb-3">
        <Col md="auto">Участники: {stat?.total_users}</Col>
        <Col md="auto">Результаты: {stat?.total_results}</Col>
        <Col md="auto">Клубы: {stat?.total_clubs}</Col>
        <Col md="auto">
          <DownloadButton url={`${APIRoute.CLUB_LIST_XLSX}`} fileName="Список клубов.xlsx" label="Скачать" />
        </Col>
      </Row>

      <Tabs defaultActiveKey="clubs" className="mb-3">
        <Tab title="Клубы" eventKey="clubs">
          <ClubsTable
            clubs={clubs}
            onUpdateClubs={handleUpdateClubs}
            currentPage={currentPage}
            totalPages={pages}
            onPageChange={setCurrentPage}
          />
        </Tab>
        <Tab title="Типы соревнований" eventKey="competitionsType">
          <CompetitionsType />
        </Tab>
        <Tab title="Статистика" eventKey="statistics">
          <ClubStatistics />
        </Tab>
      </Tabs>
    </Layout>
  )
}

export default ClubsScreen
