import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState } from 'react'
import { Button, Col, Modal, Row } from 'react-bootstrap'

import { competitionsTypeData } from './competitionsTypeData'
import AdvancedPagination from '../../../../components/AdvancedPagination/AdvancedPagination'
import ConfirmDeleteModal from '../../../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import PageSearch from '../../../../components/PageSearch/PageSearch'
import ActionsRow from '../../../../components/TableTemplate/ActionsRow/ActionsRow'
import TableTemplate from '../../../../components/TableTemplate/TableTemplate'
import AddButton from '../../../../components/ui/AddButton/AddButton'
import { APIRoute } from '../../../../const'
import { useToast } from '../../../../hooks/useToast'
import CompetitionsForm from '../CompetitionsForm/CompetitionsForm'

function CompetitionsType() {
  const [values, setValues] = useState([])
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [valuesRender, setValuesRender] = useState([]) // для отрисовки на странице
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState('')
  const [selectedItem, setSelectedItem] = useState({})
  const [isOpenModalForm, setIsModalForm] = useState(false)

  const openToast = useToast()

  const [, api] = useAxios(
    {
      url: APIRoute.GET_COMPETITIONS_TYPE,
      method: 'GET',
    },
    { manual: true }
  )

  const [, apiDelete] = useAxios(
    {
      url: `${APIRoute.CUD_COMPETITIONS_TYPE}`,
      method: 'DELETE',
    },
    { manual: true }
  )

  const getValues = useCallback(() => {
    api().then((r) => {
      if (r?.data?.values?.length > 0) {
        setValues(r.data.values)
      }
    })
  }, [api])

  useEffect(() => {
    getValues()
  }, [getValues])

  const handleUpdateValues = (newValue, type) => {
    if (type === 'add') {
      setValues([...values, { ...newValue }])
    } else if (type === 'update') {
      const newValues = [...values]
      const indexValue = values.findIndex((item) => item.public_id === newValue.public_id)

      newValues[indexValue] = newValue
      setValues([...newValues])
    }
  }

  const handleClickCreate = () => {
    setIsModalForm(true)
  }

  const handleCloseFormModal = () => {
    setIsModalForm(false)
    setSelectedItem({})
  }

  const handleEdit = (item) => {
    setSelectedItem(item)
    setIsModalForm(true)
  }

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedDeleteItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ url: `${APIRoute.CUD_COMPETITIONS_TYPE}/${selectedDeleteItem.public_id}` }).then((r) => {
      if (r.status === 200 && r.data.message) {
        const newValues = [...values]
        const deletedValueIndex = values.findIndex((el) => el.public_id === selectedDeleteItem.public_id, 1)

        newValues.splice(deletedValueIndex, 1)
        setValues([...newValues])
        openToast.success({ message: 'Тип был удалён' })
        setSelectedDeleteItem('')
      }
    })
  }

  const returnActionsRow = (item) => {
    return <ActionsRow item={item} onClickDelete={handleConfirmDelete} onClickEdit={handleEdit} />
  }

  return (
    <>
      <Row className="mb-3">
        <Col>
          <PageSearch values={values} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <AddButton onClickAddButton={handleClickCreate} label="Добавить тип" />
        </Col>
      </Row>

      <TableTemplate data={competitionsTypeData} values={valuesRender} actions={returnActionsRow} />

      <AdvancedPagination values={filteredValues} setValues={setValuesRender} />

      <Modal show={isOpenModalForm} onHide={handleCloseFormModal}>
        <Modal.Header closeButton />
        <Modal.Body>
          <CompetitionsForm
            selectedItem={selectedItem}
            onUpdateValues={handleUpdateValues}
            onCloseModal={handleCloseFormModal}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button type="submit" variant="success" form="form">
            Сохранить
          </Button>
          <Button onClick={handleCloseFormModal} variant="outline-secondary">
            Отмена
          </Button>
        </Modal.Footer>
      </Modal>

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItem}
        onDeleteItem={handleDeleteItem}
        text="Удалить тип соревнования?"
      />
    </>
  )
}

export default CompetitionsType
