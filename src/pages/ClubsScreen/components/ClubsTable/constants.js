const clubEventTypes = (types) => {
  if (!types || !Array.isArray(types)) return '-'
  if (types.length === 0) return '-'

  return types
    .map((item, index) => {
      if (!item) return 'Unknown'

      let title = ''
      if (typeof item === 'string') {
        title = item
      } else if (typeof item?.title === 'string') {
        title = item.title
      } else if (item?.title) {
        title = String(item.title)
      } else if (item?.public_id) {
        title = String(item.public_id)
      } else {
        title = 'Unknown'
      }

      return index === types.length - 1 ? title : `${title}, `
    })
    .join('')
}

const clubTitle = (title, item) => {
  return item?.priority ? `${title} ⭐` : title
}

export const clubsTableData = {
  title: '',
  clickUrl: '/clubs/club',
  clickUrlParam: 'public_id',
  list: [
    { label: 'Картинка', value: 'picture', param: 'image' },
    { label: 'Название', value: 'title', param: 'customCell', customCell: clubTitle },
    { label: 'Описание', value: 'description' },
    { label: 'Типы событий', value: 'event_type', param: 'customCell', customCell: clubEventTypes },
    { label: 'Город', value: 'location.name_ru' },
    { label: 'Участников', value: 'users_count' },
    { label: 'Дата создания', value: 'created_date', param: 'date' },
  ],
}

export const userClubsTableData = {
  title: '',
  clickUrl: '/clubs/club',
  clickUrlParam: 'public_id',
  list: [
    { label: 'Идентификатор', value: 'public_id' },
    { label: 'Вступил', value: 'join_date', param: 'date' },
  ],
}
