import { useState } from 'react'
import { Col, Row } from 'react-bootstrap'

import { clubsTableData } from './constants'
import ClubsFormModal from '../../../../components/Modal/ClubsFormModal/ClubsFormModal'
import Paginator from '../../../../components/Paginator/Paginator'
import TableTemplate from '../../../../components/TableTemplate/TableTemplate'
import AddButton from '../../../../components/ui/AddButton/AddButton'

function ClubsTable({ clubs, onUpdateClubs, currentPage, totalPages, onPageChange }) {
  const [selectedItem, setSelectedItem] = useState({})
  const [isOpenFormModal, setIsOpenFormModal] = useState(false)
  const [isView, setIsView] = useState(false)

  const handleClickCreateClub = () => {
    setIsOpenFormModal(true)
  }

  const handleCloseModal = (state) => {
    setIsOpenFormModal(state)
    setSelectedItem({})
    setIsView(false)
  }

  return (
    <>
      <Row className="mb-3 justify-content-end">
        <Col md="auto">
          <AddButton onClickAddButton={handleClickCreateClub} label="Добавить клуб" />
        </Col>
      </Row>

      <TableTemplate data={clubsTableData} values={clubs} />

      {totalPages > 1 && (
        <Paginator totalPages={totalPages} currentPage={currentPage} changePageHandler={onPageChange} />
      )}

      <ClubsFormModal
        isShowModal={isOpenFormModal}
        onCloseModal={handleCloseModal}
        selectedItem={selectedItem}
        isViewForm={isView}
        onUpdateClubs={onUpdateClubs}
      />
    </>
  )
}

export default ClubsTable
