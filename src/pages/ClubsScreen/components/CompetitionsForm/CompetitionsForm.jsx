import useAxios from 'axios-hooks'
import React from 'react'
import { Col, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import styles from './CompetitionsForm.module.scss'
import { APIRoute } from '../../../../const'
import { useToast } from '../../../../hooks/useToast'
import { removeEmptyString } from '../../../../utils/common'
import { checkSetValue } from '../../../../utils/forms'

function CompetitionsForm({ selectedItem, onUpdateValues, onCloseModal }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: selectedItem?.title,
    },
  })

  const [, api] = useAxios(
    {
      url: APIRoute.CUD_COMPETITIONS_TYPE,
      method: 'POST',
    },
    { manual: true }
  )

  const openToast = useToast()

  const isEdit = Object.keys(selectedItem).length > 0

  const onSubmit = (data) => {
    const filteredData = removeEmptyString(data)

    if (Object.keys(filteredData).length > 0) {
      const method = isEdit ? 'PUT' : 'POST'
      const toastText = isEdit ? 'Тип изменён' : 'Тип добавлен'
      const url = isEdit
        ? `${APIRoute.CUD_COMPETITIONS_TYPE}/${selectedItem.public_id}`
        : APIRoute.CUD_COMPETITIONS_TYPE
      const type = isEdit ? 'update' : 'add'

      api({ url: url, method: method, data: filteredData }).then((r) => {
        if (r.status === 200) {
          onUpdateValues(r.data, type)
          onCloseModal()
          openToast.success({ message: toastText })
        }
      })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Row>
        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId={'titleInput'} label="Название">
              <FormControl
                className={styles.field}
                {...register('title', {
                  required: !isEdit,
                  setValueAs: (v) => checkSetValue(v, selectedItem?.title, 'text'),
                })}
                type="text"
                isInvalid={errors?.title}
                placeholder="Название"
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>
    </Form>
  )
}

export default CompetitionsForm
