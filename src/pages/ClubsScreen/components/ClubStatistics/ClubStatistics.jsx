import useAxios from 'axios-hooks'
import moment from 'moment/moment'
import { useState } from 'react'
import { Col, Floating<PERSON>abel, FormSelect, Row } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import PageSearch from '@/components/PageSearch/PageSearch'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { APIRoute } from '@/const'
import { getFormatTime } from '@/utils/date'

const EMPTY_ARRAY = []

const statisticsTableData = {
  title: '',
  clickUrl: '/clubs/club',
  clickUrlParam: 'public_id',
  list: [
    { label: 'Логотип', value: 'picture', param: 'image' },
    { label: 'Название', value: 'title' },
    { label: 'Участники', value: 'total_members' },
    { label: 'Билеты', value: 'tickets_count' },
  ],
}

export const ClubStatistics = () => {
  const [filteredValues, setFilteredValues] = useState(EMPTY_ARRAY) // для пагинации
  const [valuesRender, setValuesRender] = useState([]) // для отрисовки на странице
  const [eventCityId, setEventCityId] = useState('')

  const [{ data }, api] = useAxios(
    {
      url: APIRoute.GET_CLUB_SOLDGOODS_STATISTICS,
      method: 'GET',
    },
    { manual: true }
  )

  const [{ data: eventTypeList }] = useAxios(
    {
      url: APIRoute.EVENT_TYPE_LIST,
      method: 'GET',
    },
    { manual: false }
  )

  const [{ data: eventCityFiltered }, apiEventCityFiltered] = useAxios(
    {
      url: APIRoute.EVENT_CITY_FILTERED,
      method: 'POST',
    },
    { manual: true }
  )

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = eventTypeList?.values.find((event) => event.public_id === eventId)

    if (event) {
      const body = {
        city: {
          id: 0,
        },
        date_from: moment().subtract(2, 'years'),
        date_to: moment().add(1, 'years'),
        event_type: {
          public_id: event.public_id,
        },
      }

      setEventCityId('')

      apiEventCityFiltered({ data: body })
    }
  }

  const handleChangeCity = (evt) => {
    const cityPublicId = evt.target.value

    api({ url: `${APIRoute.GET_CLUB_SOLDGOODS_STATISTICS}/${cityPublicId}` })

    setEventCityId(cityPublicId)
  }

  return (
    <>
      <Row className="mb-5">
        <Col>
          <FloatingLabel controlId="eventLabelTicket" label="Событие">
            <FormSelect onChange={handleChangeEvent} name="event_type" aria-label="Событие">
              <option value="">выберите один из вариантов</option>
              {eventTypeList?.values?.map((event) => (
                <option value={event.public_id} key={event.public_id}>
                  {event.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>

        <Col>
          <FloatingLabel controlId="cityLabelTicket" label="Город">
            <FormSelect onChange={handleChangeCity} name="event_city" aria-label="Город">
              <option value="">выберите один из вариантов</option>
              {eventCityFiltered?.values?.map((city) => (
                <option value={city.public_id} key={city.public_id}>
                  {city.address} — {getFormatTime(city.start_time, city.timezone)}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>
          <PageSearch values={data?.values || EMPTY_ARRAY} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <DownloadButton
            url={`${APIRoute.CLUB_SOLDGOODS_XLSX}/${eventCityId}`}
            fileName="Статистика по продажам в клубе.xlsx"
            label="Скачать"
            disabled={!eventCityId}
          />
        </Col>
      </Row>

      <TableTemplate data={statisticsTableData} values={valuesRender} />

      <AdvancedPagination values={filteredValues} setValues={setValuesRender} />
    </>
  )
}

export default ClubStatistics
