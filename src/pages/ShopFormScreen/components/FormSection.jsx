import { Card } from 'react-bootstrap'

function FormSection({ title, icon, children, className = '' }) {
  return (
    <Card className={`mb-4 ${className}`}>
      <Card.Header className="bg-light">
        <h5 className="mb-0 text-primary d-flex align-items-center">
          {icon && <i className={`${icon} me-2`}></i>}
          {title}
        </h5>
      </Card.Header>
      <Card.Body>{children}</Card.Body>
    </Card>
  )
}

export default FormSection
