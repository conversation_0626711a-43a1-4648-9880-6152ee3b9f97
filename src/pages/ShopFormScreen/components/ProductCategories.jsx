import { Col, Floating<PERSON>abel, FormSelect, FormControl, Row } from 'react-bootstrap'
import Select from 'react-select'

import { getDefaultOptionsSelect, getOptionsSelect } from '@/utils/common'

const groupProducts = [
  { name: 'Одежда', value: 'clothes' },
  { name: 'Аксессуары', value: 'accessories' },
  { name: 'Головные уборы', value: 'hats' },
]

const genderProducts = [
  { name: 'Унисекс', value: 'unisex' },
  { name: 'Женский', value: 'female' },
  { name: 'Мужской', value: 'male' },
]

function ProductCategories({
  formData,
  defaultFormData,
  handleChangeField,
  handleChangeSelect,
  handleChangeCollect,
  categories,
  collections,
  processingList,
  dynamicSelectStyles,
  getFieldValue,
}) {
  return (
    <Row className="g-3">
      <Col md={6}>
        <FloatingLabel controlId="groupProductLabel" label="Раздел">
          <FormSelect onChange={handleChangeField} name="group" aria-label="Раздел" value={getFieldValue('group', '')}>
            <option value="">выберите один из вариантов</option>
            {groupProducts.map((item) => (
              <option value={item.value} key={item.value}>
                {item.name}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="subgroupProductLabel" label="Подраздел">
          <FormControl
            onChange={handleChangeField}
            value={getFieldValue('subgroup', '')}
            name="subgroup"
            type="text"
            placeholder="Подраздел, напр.: Футболки"
          />
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="genderProductLabel" label="Пол">
          <FormSelect onChange={handleChangeField} name="gender" aria-label="Пол" value={getFieldValue('gender', '')}>
            <option value="">выберите один из вариантов</option>
            {genderProducts.map((item) => (
              <option value={item.value} key={item.value}>
                {item.name}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="categoryLabelProduct" label="Категория товара">
          <FormSelect
            onChange={handleChangeSelect}
            value={getFieldValue('category')?.public_id || ''}
            name="category"
            aria-label="Категория товара"
          >
            <option value="">выберите один из вариантов</option>
            {categories?.values?.map((item, index) => (
              <option value={item.public_id} key={`${item.public_id}-${index}`}>
                {item.title}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="publicIdLabelOrder" label="Юридическое лицо">
          <FormSelect
            onChange={handleChangeSelect}
            name="processing"
            aria-label="processing_public_id"
            value={getFieldValue('processing')?.public_id || ''}
          >
            <option value="">выберите один из вариантов</option>
            {processingList?.map((item, index) => (
              <option value={item.public_id} key={`${item.public_id}-${index}`}>
                {item.entity}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={6}>
        {collections?.values?.length > 0 && (
          <div>
            <label className="form-label">Коллекции</label>
            <Select
              value={getDefaultOptionsSelect(getFieldValue('collections', []))}
              onChange={handleChangeCollect}
              styles={dynamicSelectStyles}
              isMulti
              name="collections"
              options={getOptionsSelect(collections.values, 'title', 'title')}
              className="basic-multi-select"
              classNamePrefix="select"
              placeholder="Коллекции"
              closeMenuOnSelect={false}
            />
          </div>
        )}
      </Col>
    </Row>
  )
}

export default ProductCategories
