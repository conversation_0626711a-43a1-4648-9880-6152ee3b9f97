import { useState, useMemo, useEffect } from 'react'
import { <PERSON><PERSON>, But<PERSON>, Card, Form, InputGroup, ListGroup, Spinner } from 'react-bootstrap'

import { useCreateHonestCode } from '@/features/shop/api/createHonestCode'
import { useDeleteHonestCode } from '@/features/shop/api/deleteHonestCode'
import { useGetHonestCodes } from '@/features/shop/api/getHonestCodes'

function HonestCodeManager({ proportionId }) {
  const [bulkCodes, setBulkCodes] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const { data: codesData, isLoading } = useGetHonestCodes(proportionId)
  const { mutate: createCode, isLoading: isCreating } = useCreateHonestCode()
  const { mutate: deleteCode, isLoading: isDeleting } = useDeleteHonestCode(proportionId)

  const codes = useMemo(() => codesData?.data?.codes || [], [codesData?.data?.codes])

  useEffect(() => {
    setSearchQuery('')
  }, [proportionId])

  const filteredCodes = useMemo(() => {
    if (!searchQuery.trim()) return codes
    return codes.filter((code) => code.toLowerCase().includes(searchQuery.toLowerCase()))
  }, [codes, searchQuery])

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!bulkCodes.trim()) return

    // Разделение ввода на отдельные коды
    const codesToAdd = bulkCodes
      .split(/[\s,]+/) // Разделение по пробелам, запятым и переносам строк
      .map((code) => code.trim())
      .filter((code) => code.length > 0)

    if (codesToAdd.length === 0) return

    // Отправка всех кодов за один запрос
    createCode({
      codes: codesToAdd,
      proportion_public_id: proportionId,
    })

    setBulkCodes('')
  }

  const handleDelete = (codeValue) => {
    deleteCode(codeValue)
  }

  const handleSearch = (e) => {
    setSearchQuery(e.target.value)
  }

  if (!proportionId) {
    return <Alert variant="info">Выберите опцию товара для управления</Alert>
  }

  return (
    <Card className="mt-3">
      <Card.Header as="h6">Управление кодами</Card.Header>
      <Card.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Control
              as="textarea"
              rows={3}
              value={bulkCodes}
              onChange={(e) => setBulkCodes(e.target.value)}
              placeholder=""
              disabled={isCreating}
            />
            <Form.Text className="text-muted">
              Вы можете добавить несколько кодов, разделяя их запятыми, пробелами или переносами строки
            </Form.Text>
          </Form.Group>
          <div className="d-grid gap-2 d-md-flex justify-content-md-end mb-3">
            <Button variant="primary" type="submit" disabled={!bulkCodes.trim() || isCreating}>
              {isCreating ? <Spinner animation="border" size="sm" className="me-2" /> : null}
              {isCreating ? 'Добавление...' : 'Добавить'}
            </Button>
          </div>
        </Form>

        {!isLoading && codes.length > 0 && (
          <InputGroup className="mb-3">
            <InputGroup.Text className="border-0">
              <i className="bi bi-search"></i>
            </InputGroup.Text>
            <Form.Control placeholder="Поиск по кодам" value={searchQuery} onChange={handleSearch} />
            {searchQuery && (
              <Button variant="outline-secondary" onClick={() => setSearchQuery('')}>
                Очистить
              </Button>
            )}
          </InputGroup>
        )}

        {isLoading ? (
          <div className="text-center my-3">
            <Spinner animation="border" />
          </div>
        ) : codes.length > 0 ? (
          <div className="honest-codes-container" style={{ maxHeight: '300px', overflowY: 'auto' }}>
            <ListGroup>
              {filteredCodes.length > 0 ? (
                filteredCodes.map((codeItem) => (
                  <ListGroup.Item key={codeItem} className="d-flex justify-content-between align-items-center">
                    <span className="text-monospace">{codeItem}</span>
                    <Button
                      variant="outline-danger"
                      size="sm"
                      onClick={() => handleDelete(codeItem)}
                      disabled={isDeleting}
                    >
                      Удалить
                    </Button>
                  </ListGroup.Item>
                ))
              ) : (
                <ListGroup.Item className="text-center">Ничего не найдено</ListGroup.Item>
              )}
            </ListGroup>
          </div>
        ) : (
          <Alert variant="light">Коды отсутствуют</Alert>
        )}

        {!isLoading && filteredCodes.length > 0 && (
          <div className="d-flex justify-content-between align-items-center mt-2">
            <small className="text-muted">
              Показано: {filteredCodes.length} из {codes.length}
            </small>
          </div>
        )}
      </Card.Body>
    </Card>
  )
}

export default HonestCodeManager
