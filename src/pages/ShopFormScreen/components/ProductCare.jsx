import { Col, FloatingLabel, Form, Row } from 'react-bootstrap'

function ProductCare({ formData, defaultFormData, handleChangeField, getFieldValue }) {
  return (
    <Row className="g-3">
      <Col md={12}>
        <FloatingLabel controlId="floatingTextareaCare" label="Рекомендации по уходу">
          <Form.Control
            onChange={handleChangeField}
            value={getFieldValue('care', '')}
            as="textarea"
            placeholder="Рекомендации по уходу"
            name="care"
            style={{ height: '140px', resize: 'vertical' }}
          />
        </FloatingLabel>
      </Col>
    </Row>
  )
}

export default ProductCare
