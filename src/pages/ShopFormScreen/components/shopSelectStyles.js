const isDarkTheme = () => {
  return (
    document.documentElement.getAttribute('data-bs-theme') === 'dark' ||
    document.documentElement.classList.contains('theme-dark')
  )
}

export const shopSelectStyles = {
  container: (provided) => ({
    ...provided,
    height: '100%',
    zIndex: 4,
  }),
  control: (provided, state) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      border: darkTheme ? '1px solid #495057' : '1px solid #ced4da',
      borderRadius: '0.375rem',
      minHeight: '100%',
      backgroundColor: darkTheme ? '#212529' : '#ffffff',
      boxShadow: state.isFocused
        ? darkTheme
          ? '0 0 0 0.25rem rgba(13, 110, 253, 0.25)'
          : '0 0 0 0.25rem rgba(13, 110, 253, 0.25)'
        : 'none',
      '&:hover': {
        borderColor: darkTheme ? '#6c757d' : '#86b7fe',
      },
    }
  },
  menu: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      backgroundColor: darkTheme ? '#212529' : '#ffffff',
      border: darkTheme ? '1px solid #495057' : '1px solid #ced4da',
      borderRadius: '0.375rem',
      boxShadow: darkTheme ? '0 0.5rem 1rem rgba(0, 0, 0, 0.15)' : '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
    }
  },
  option: (provided, state) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      backgroundColor: state.isSelected
        ? '#0d6efd'
        : state.isFocused
          ? darkTheme
            ? '#495057'
            : '#e9ecef'
          : 'transparent',
      color: state.isSelected ? '#ffffff' : darkTheme ? '#ffffff' : '#212529',
      '&:hover': {
        backgroundColor: state.isSelected ? '#0d6efd' : darkTheme ? '#495057' : '#e9ecef',
      },
    }
  },
  multiValue: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      backgroundColor: darkTheme ? '#495057' : '#e9ecef',
      borderRadius: '0.25rem',
    }
  },
  multiValueLabel: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#ffffff' : '#495057',
    }
  },
  multiValueRemove: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#ffffff' : '#495057',
      borderRadius: '0 0.25rem 0.25rem 0',
      '&:hover': {
        backgroundColor: '#dc3545',
        color: '#ffffff',
      },
    }
  },
  placeholder: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#6c757d' : '#6c757d',
    }
  },
  singleValue: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#ffffff' : '#212529',
    }
  },
  input: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#ffffff' : '#212529',
    }
  },
  indicatorSeparator: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      backgroundColor: darkTheme ? '#495057' : '#ced4da',
    }
  },
  dropdownIndicator: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#6c757d' : '#6c757d',
      '&:hover': {
        color: darkTheme ? '#adb5bd' : '#495057',
      },
    }
  },
  clearIndicator: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#6c757d' : '#6c757d',
      '&:hover': {
        color: darkTheme ? '#adb5bd' : '#495057',
      },
    }
  },
  loadingIndicator: (provided) => {
    const darkTheme = isDarkTheme()
    return {
      ...provided,
      color: darkTheme ? '#6c757d' : '#6c757d',
    }
  },
}
