import { Col, Form, Row } from 'react-bootstrap'

const projects = [
  { id: 1, value: 'heroes', label: 'Лига Героев' },
  { id: 2, value: 'triathlon', label: 'Триатлон' },
  { id: 3, value: 'ocr', label: 'OCR' },
]

function ProductSettings({ formData, defaultFormData, handleChangeToggle, handleChangeCheckbox, checkCheckbox }) {
  return (
    <Row className="g-3">
      {/* Main toggles */}
      <Col md={12}>
        <div className="border rounded p-3 bg-light">
          <h6 className="mb-3">Основные настройки</h6>
          <Row className="g-3">
            <Col md={6}>
              <Form.Check
                onChange={handleChangeToggle}
                checked={Boolean(formData.public ?? defaultFormData.public)}
                name="public"
                type="switch"
                id="public-switch"
                label="Показывать в каталоге"
              />
            </Col>
            <Col md={6}>
              <Form.Check
                onChange={handleChangeToggle}
                checked={Boolean(formData.delivery ?? defaultFormData.delivery)}
                name="delivery"
                type="switch"
                id="delivery-switch"
                label="Доставка"
              />
            </Col>
            <Col md={6}>
              <Form.Check
                onChange={handleChangeToggle}
                checked={Boolean(formData.new ?? defaultFormData.new)}
                name="new"
                type="switch"
                id="new-switch"
                label="Новый товар"
              />
            </Col>
            <Col md={6}>
              <Form.Check
                onChange={handleChangeToggle}
                checked={Boolean(formData.is_related ?? defaultFormData.is_related)}
                name="is_related"
                type="switch"
                id="is_related-switch"
                label="Сопутствующий товар"
              />
            </Col>
          </Row>
        </div>
      </Col>

      {/* Integration settings */}
      <Col md={12}>
        <div className="border rounded p-3 bg-light">
          <h6 className="mb-3">Интеграции</h6>
          <Row className="g-3">
            <Col md={6}>
              <Form.Check
                onChange={handleChangeToggle}
                checked={Boolean(formData.retailcrm ?? defaultFormData.retailcrm)}
                name="retailcrm"
                type="switch"
                id="retailcrm-switch"
                label="Синхронизация со складом"
              />
            </Col>
            <Col md={6}>
              <Form.Check
                onChange={handleChangeToggle}
                checked={Boolean(formData.text_field ?? defaultFormData.text_field)}
                name="text_field"
                type="switch"
                id="text_field-switch"
                label="Поле для текста"
              />
            </Col>
          </Row>
        </div>
      </Col>

      {/* Projects */}
      <Col md={12}>
        <div className="border rounded p-3 bg-light">
          <h6 className="mb-3">Проекты</h6>
          <Row>
            <Col md="auto">
              <strong>Магазин:</strong>
            </Col>
            {projects.map(({ value, label }) => (
              <Col md="auto" key={value}>
                <Form.Check
                  type="checkbox"
                  id={`project-${value}`}
                  label={label}
                  name="project"
                  value={value}
                  onChange={handleChangeCheckbox}
                  checked={checkCheckbox('project', value)}
                />
              </Col>
            ))}
          </Row>
        </div>
      </Col>
    </Row>
  )
}

export default ProductSettings
