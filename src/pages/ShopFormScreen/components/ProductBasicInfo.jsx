import { Col, FloatingLabel, FormControl, Row } from 'react-bootstrap'
import AsyncSelect from 'react-select/async'

function ProductBasicInfo({
  formData,
  defaultFormData,
  handleChangeField,
  loadCountryOptions,
  handleChangeCountry,
  countriesLoaded,
  dynamicSelectStyles,
  getFieldValue,
}) {
  return (
    <Row className="g-3">
      <Col md={6}>
        <FloatingLabel controlId="titleProductLabel" label="Название товара *">
          <FormControl
            onChange={handleChangeField}
            value={getFieldValue('title', '')}
            name="title"
            type="text"
            placeholder="Название товара"
            required
          />
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="vendorCodeProductLabel" label="Артикул">
          <FormControl
            onChange={handleChangeField}
            value={getFieldValue('vendor_code', '')}
            name="vendor_code"
            type="text"
            placeholder="Артикул"
          />
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <FloatingLabel controlId="materialProductLabel" label="Материал товара">
          <FormControl
            onChange={handleChangeField}
            value={getFieldValue('material', '')}
            name="material"
            type="text"
            placeholder="Материал товара"
          />
        </FloatingLabel>
      </Col>

      <Col md={6}>
        <AsyncSelect
          key={`country-select-${defaultFormData?.country?.id || 'empty'}-${countriesLoaded ? 'ready' : 'loading'}`}
          cacheOptions
          defaultOptions={countriesLoaded}
          loadOptions={loadCountryOptions}
          value={
            getFieldValue('country')
              ? { value: getFieldValue('country').id, label: getFieldValue('country').name_ru }
              : null
          }
          onChange={handleChangeCountry}
          styles={dynamicSelectStyles}
          name="country"
          className="basic-select"
          classNamePrefix="select"
          placeholder="Страна происхождения"
          isClearable
          noOptionsMessage={() => 'Нет доступных стран'}
          loadingMessage={() => 'Загрузка стран...'}
        />
      </Col>
    </Row>
  )
}

export default ProductBasicInfo
