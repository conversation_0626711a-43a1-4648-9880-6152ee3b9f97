import { useState } from 'react'
import { Tab, Tabs, Card } from 'react-bootstrap'

import HonestCodeManager from './HonestCodeManager'
import ProductProportions from './ProductProportions'
import ProportionSelect from './ProportionSelect'

function ProductTabs({ productId }) {
  const [key, setKey] = useState('proportions')
  const [selectedProportion, setSelectedProportion] = useState('')

  return (
    <Card className="mb-4">
      <Card.Body>
        <Tabs id="product-tabs" activeKey={key} onSelect={(k) => setKey(k)} className="mb-3">
          <Tab eventKey="proportions" title="Опции товара">
            <ProductProportions productId={productId} />
          </Tab>
          <Tab eventKey="honest-code" title="Коды честного знака">
            <ProportionSelect productId={productId} onChange={setSelectedProportion} />
            <HonestCodeManager proportionId={selectedProportion} />
          </Tab>
        </Tabs>
      </Card.Body>
    </Card>
  )
}

export default ProductTabs
