import { Col, FloatingLabel, <PERSON>, Row } from 'react-bootstrap'

function ProductDescription({ formData, defaultFormData, handleChangeField, getFieldValue }) {
  return (
    <Row className="g-3">
      <Col md={8}>
        <FloatingLabel controlId="floatingTextareaDescription" label="Описание товара">
          <Form.Control
            onChange={handleChangeField}
            value={getFieldValue('description', '')}
            as="textarea"
            placeholder="Описание товара"
            name="description"
            style={{ height: '140px', resize: 'vertical' }}
          />
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <Form.Group>
          <Form.Label>Цвет товара</Form.Label>
          <Form.Control
            type="color"
            value={getFieldValue('color', '#ffffff')}
            onChange={handleChangeField}
            name="color"
            style={{ height: '50px', cursor: 'pointer' }}
            title="Выберите цвет товара"
          />
          <Form.Text className="text-muted">Цвет для отображения товара</Form.Text>
        </Form.Group>
      </Col>
    </Row>
  )
}

export default ProductDescription
