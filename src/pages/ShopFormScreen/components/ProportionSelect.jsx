import useAxios from 'axios-hooks'
import { useState, useEffect } from 'react'
import { Form, Card } from 'react-bootstrap'

import { APIRoute } from '@/const'

function ProportionSelect({ productId, onChange }) {
  const [selectedProportion, setSelectedProportion] = useState('')

  const [{ data: productProportions, loading }] = useAxios(
    {
      url: productId ? `${APIRoute.PRODUCT_PROPORTION_LIST}/${productId}` : '',
      method: 'GET',
    },
    { manual: !productId }
  )

  useEffect(() => {
    if (productId) {
      setSelectedProportion('')
    }
  }, [productId])

  const handleChange = (e) => {
    const value = e.target.value
    setSelectedProportion(value)
    onChange(value)
  }

  const proportions = productProportions?.values || []

  if (!productId) {
    return null
  }

  return (
    <Card className="mb-3">
      <Card.Body>
        <Form.Select value={selectedProportion} onChange={handleChange} disabled={loading || proportions.length === 0}>
          <option value="">Выберите опцию</option>
          {proportions.map((proportion, index) => (
            <option key={`${proportion.public_id}-${index}`} value={proportion.public_id}>
              {proportion.name} ({proportion.type})
            </option>
          ))}
        </Form.Select>
        {proportions.length === 0 && !loading && <p className="text-muted mt-2 mb-0">У товара нет опций</p>}
      </Card.Body>
    </Card>
  )
}

export default ProportionSelect
