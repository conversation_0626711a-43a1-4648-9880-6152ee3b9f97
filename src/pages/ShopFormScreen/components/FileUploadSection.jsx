import { Col, Form, Row } from 'react-bootstrap'

import styles from '../ShopFormScreen.module.scss'

/**
 * Компонент для секции загрузки файлов
 */
function FileUploadSection({ onMainImageChange, onMultipleImagesChange, picturesInputRef, disabled = false }) {
  return (
    <Row className="g-3 mb-3">
      <Col md={6}>
        <Form.Group controlId="formFile">
          <Form.Label className={styles.fileButton}>
            <i className="bi bi-image me-2"></i>
            Выбрать главное изображение
          </Form.Label>
          <Form.Control
            className="visually-hidden"
            type="file"
            onChange={onMainImageChange}
            accept=".jpg, .png, .jpeg, .webp"
            disabled={disabled}
          />
        </Form.Group>
        <small className="text-muted">* максимальный размер изображений 5MB</small>
      </Col>

      <Col md={6}>
        <Form.Group controlId="formMoreFile">
          <Form.Label className={styles.fileButton}>
            <i className="bi bi-images me-2"></i>
            Выбрать дополнительные изображения
          </Form.Label>
          <Form.Control
            className="visually-hidden"
            type="file"
            onChange={onMultipleImagesChange}
            ref={picturesInputRef}
            accept=".jpg, .png, .jpeg, .webp"
            multiple
            disabled={disabled}
          />
        </Form.Group>
        <small className="text-muted">Можно выбрать несколько файлов</small>
      </Col>
    </Row>
  )
}

export default FileUploadSection
