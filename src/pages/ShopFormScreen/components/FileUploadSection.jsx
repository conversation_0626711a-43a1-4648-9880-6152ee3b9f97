import { Col, Form, Row, Card, Button } from 'react-bootstrap'

/**
 * Компонент для секции загрузки файлов
 */
function FileUploadSection({ onMainImageChange, onMultipleImagesChange, picturesInputRef, disabled = false }) {
  return (
    <Row className="g-3 mb-4">
      <Col md={6}>
        <Card className="h-100">
          <Card.Body className="d-flex flex-column justify-content-center text-center">
            <Form.Group controlId="formFile">
              <Button variant="outline-primary" className="w-100" disabled={disabled} as="label" htmlFor="formFile">
                <i className="bi bi-cloud-upload me-2"></i>
                Выбрать главное изображение
              </Button>
              <Form.Control
                id="formFile"
                className="visually-hidden"
                type="file"
                onChange={onMainImageChange}
                accept=".jpg, .png, .jpeg, .webp"
                disabled={disabled}
              />
            </Form.Group>
            <small className="text-muted mt-2">
              <i className="bi bi-info-circle me-1"></i>
              Максимальный размер: 5MB
            </small>
          </Card.Body>
        </Card>
      </Col>

      <Col md={6}>
        <Card className="h-100">
          <Card.Body className="d-flex flex-column justify-content-center text-center">
            <Form.Group controlId="formMoreFile">
              <Button variant="outline-success" className="w-100" disabled={disabled} as="label" htmlFor="formMoreFile">
                <i className="bi bi-cloud-upload me-2"></i>
                Выбрать дополнительные изображения
              </Button>
              <Form.Control
                id="formMoreFile"
                className="visually-hidden"
                type="file"
                onChange={onMultipleImagesChange}
                ref={picturesInputRef}
                accept=".jpg, .png, .jpeg, .webp"
                multiple
                disabled={disabled}
              />
            </Form.Group>
            <small className="text-muted mt-2">
              <i className="bi bi-info-circle me-1"></i>
              Можно выбрать несколько файлов
            </small>
          </Card.Body>
        </Card>
      </Col>
    </Row>
  )
}

export default FileUploadSection
