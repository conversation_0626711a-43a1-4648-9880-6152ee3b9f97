import { useRef, useCallback } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ge, Spinner } from 'react-bootstrap'

import { MAX_IMG_SIZE } from '@/const'
import { convertBase64 } from '@/utils/common'

import FileUploadSection from './FileUploadSection'
import ImageGallery from './ImageGallery'
import { useImageManager } from '../hooks/useImageManager'

function ProductImages({
  defaultFormData,
  getFieldValue,
  onChangePictures,
  onSavedPictures,
  onSavedMainPicture,
  onUpdateMainPicture,
  productPublicId,
}) {
  const picturesInputRef = useRef()

  const imageManager = useImageManager({
    defaultFormData,
    getFieldValue,
    onChangePictures,
    onSavedPictures,
    onSavedMainPicture,
    onUpdateMainPicture,
    productPublicId,
  })

  /**
   * Сохраняет изменения изображений
   */
  const handleSavePictures = useCallback(async () => {
    const {
      productPublicId: pid,
      hasChanges,
      isUploading,
      setIsUploading,
      hasBase64,
      orderChanged,
      mainChanged,
      pictures,
      savedNormalized,
      mainCurrent,
      uploadBase64Images,
      deleteUnusedFiles,
      updateProductItemMutateAsync,
      onSavedPictures,
      onSavedMainPicture,
      setLastSavedMainValue,
      setDisableOnce,
      openToast,
    } = imageManager

    if (!pid) {
      openToast.error({ message: 'Сначала сохраните товар' })
      return
    }

    if (!hasChanges || isUploading) return

    setIsUploading(true)
    try {
      let updatedPictures = []

      // Обработка изображений галереи
      if (hasBase64 || orderChanged) {
        updatedPictures = await uploadBase64Images(pictures)
        await deleteUnusedFiles(savedNormalized, updatedPictures)
      }

      // Подготовка данных для обновления
      const payload = {
        ...(hasBase64 || (orderChanged && { pictures: updatedPictures })),
        ...(mainChanged && { picture: mainCurrent }),
      }

      // Обновление товара
      const response = await updateProductItemMutateAsync({
        public_id: pid,
        data: payload,
      })

      if (response?.status === 200) {
        // Вызов коллбэков
        if (hasBase64 || orderChanged) {
          onSavedPictures?.(updatedPictures)
        }
        if (mainChanged) {
          setLastSavedMainValue(mainCurrent)
          onSavedMainPicture?.(mainCurrent)
        }

        setDisableOnce(true)
        openToast.success({ message: 'Изображения сохранены' })

        // Очистка input
        if (picturesInputRef.current) {
          picturesInputRef.current.value = ''
        }
      }
    } catch (error) {
      // Ошибки обрабатываются через хуки
      console.error('Error saving pictures:', error)
    } finally {
      setIsUploading(false)
    }
  }, [imageManager, picturesInputRef])

  /**
   * Обработчик загрузки главного изображения
   */
  const handleFileRead = useCallback(
    async (event) => {
      const file = event.target.files[0]
      if (!file) return

      const { validateFileSize, onUpdateMainPicture, setLastSavedMainValue, setDisableOnce, openToast } = imageManager

      if (!validateFileSize(file)) return

      try {
        const base64 = await convertBase64(file)

        if (base64 === '') {
          onUpdateMainPicture?.('')
        } else {
          onUpdateMainPicture?.(base64)
        }

        setLastSavedMainValue(null)
        setDisableOnce(false)
      } catch {
        openToast.error({ message: 'Ошибка при обработке файла' })
      }
    },
    [imageManager]
  )

  /**
   * Обработчик загрузки дополнительных изображений
   */
  const handleMorePicture = useCallback(
    async (event) => {
      const files = Array.from(event.target.files)
      if (files.length === 0) return

      const { getFieldValue, onChangePictures, setDisableOnce, openToast } = imageManager
      const current = getFieldValue('pictures', [])
      const oversizedFiles = []
      const validFiles = []

      // Разделяем файлы на валидные и слишком большие
      files.forEach((file) => {
        if (file.size <= MAX_IMG_SIZE) {
          validFiles.push(file)
        } else {
          oversizedFiles.push(file.name)
        }
      })

      try {
        // Конвертируем валидные файлы в base64
        const base64Images = await Promise.all(validFiles.map((file) => convertBase64(file)))

        // Обновляем список изображений
        onChangePictures?.([...current, ...base64Images])

        // Показываем ошибку для слишком больших файлов
        if (oversizedFiles.length > 0) {
          openToast.error({
            title: true,
            message: `Размер слишком большой: ${oversizedFiles.join(', ')}`,
            duration: 6000,
          })
        }

        setDisableOnce(false)
      } catch {
        openToast.error({ message: 'Ошибка при обработке файлов' })
      }
    },
    [imageManager]
  )

  /**
   * Удаляет изображение из галереи по индексу
   */
  const handleDeletePictures = useCallback(
    (index) => {
      const { getFieldValue, onChangePictures, setDisableOnce } = imageManager
      const currentPictures = getFieldValue('pictures', [])
      const newPictures = [...currentPictures]
      newPictures.splice(index, 1)

      onChangePictures?.(newPictures)

      // Очищаем input если больше нет изображений
      if (newPictures.length === 0 && picturesInputRef.current) {
        picturesInputRef.current.value = ''
      }

      setDisableOnce(false)
    },
    [imageManager, picturesInputRef]
  )

  /**
   * Перемещает изображение в списке
   */
  const movePictureInList = useCallback(
    (index, toIndex) => {
      const { getFieldValue, onChangePictures, setDisableOnce } = imageManager
      const currentPictures = getFieldValue('pictures', [])

      // Проверяем границы
      if (toIndex < 0 || toIndex >= currentPictures.length) return

      const newPictures = [...currentPictures]
      const [movedElement] = newPictures.splice(index, 1)
      newPictures.splice(toIndex, 0, movedElement)

      onChangePictures?.(newPictures)
      setDisableOnce(false)
    },
    [imageManager]
  )

  const { pictures, mainImage, hasChanges, disableOnce, isUploading } = imageManager

  return (
    <Card className="shadow-sm">
      <Card.Header className="bg-light">
        <Row className="align-items-center">
          <Col>
            <h5 className="mb-0 text-primary">
              <i className="bi bi-images me-2"></i>
              Изображения товара
              {hasChanges && (
                <Badge bg="warning" text="dark" className="ms-2">
                  <i className="bi bi-exclamation-circle me-1"></i>
                  Есть изменения
                </Badge>
              )}
              {!productPublicId && (
                <Badge bg="secondary" className="ms-2">
                  <i className="bi bi-info-circle me-1"></i>
                  Сначала сохраните товар
                </Badge>
              )}
            </h5>
          </Col>
          <Col xs="auto">
            <Button
              variant={hasChanges ? 'primary' : 'outline-secondary'}
              size="sm"
              onClick={handleSavePictures}
              disabled={!hasChanges || disableOnce || isUploading || !productPublicId}
            >
              {isUploading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Сохранение...
                </>
              ) : (
                <>
                  <i className="bi bi-check-circle me-2"></i>
                  Сохранить изображения
                </>
              )}
            </Button>
          </Col>
        </Row>
      </Card.Header>

      <Card.Body>
        {/* Статус индикатор */}
        {hasChanges && (
          <Alert variant="info" className="mb-3">
            <i className="bi bi-info-circle me-2"></i>У вас есть несохраненные изменения в изображениях. Не забудьте
            сохранить их.
          </Alert>
        )}

        {/* Секция загрузки файлов */}
        <FileUploadSection
          onMainImageChange={handleFileRead}
          onMultipleImagesChange={handleMorePicture}
          picturesInputRef={picturesInputRef}
          disabled={isUploading}
        />

        {/* Галерея изображений */}
        <ImageGallery
          mainImage={mainImage}
          pictures={pictures}
          onMoveImage={movePictureInList}
          onDeleteImage={handleDeletePictures}
          isEditable={true}
        />
      </Card.Body>
    </Card>
  )
}

export default ProductImages
