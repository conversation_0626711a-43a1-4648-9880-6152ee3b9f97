import { useCallback } from 'react'
import { Col, Figure, Row, Button } from 'react-bootstrap'

import { getImageSrc } from '@/utils/images'

/**
 * Компонент для отображения одного изображения с кнопками управления
 */
const ImageItem = ({ item, index, isEditable, onMoveLeft, onMoveRight, onDelete, isFirst, isLast }) => (
  <Col className="d-grid" md="auto">
    <Figure className="m-0">
      <Figure.Caption className="m-1">Доп картинка {index + 1}</Figure.Caption>
      <Figure.Image
        className="m-0"
        width={171}
        alt={`Дополнительное изображение ${index + 1}`}
        src={getImageSrc(item)}
      />
    </Figure>

    {isEditable && (
      <Row className="mt-2">
        <Col>
          <Button
            onClick={() => onMoveLeft(index)}
            variant="outline-secondary"
            size="sm"
            disabled={isFirst}
            title="Переместить влево"
          >
            {'<'}
          </Button>
        </Col>
        <Col>
          <Button onClick={() => onDelete(index)} variant="outline-danger" size="sm" title="Удалить изображение">
            Удалить
          </Button>
        </Col>
        <Col>
          <Button
            onClick={() => onMoveRight(index)}
            variant="outline-secondary"
            size="sm"
            disabled={isLast}
            title="Переместить вправо"
          >
            {'>'}
          </Button>
        </Col>
      </Row>
    )}
  </Col>
)

/**
 * Компонент галереи изображений
 */
function ImageGallery({ mainImage, pictures = [], onMoveImage, onDeleteImage, isEditable = true }) {
  const handleMoveLeft = useCallback(
    (index) => {
      onMoveImage(index, index - 1)
    },
    [onMoveImage]
  )

  const handleMoveRight = useCallback(
    (index) => {
      onMoveImage(index, index + 1)
    },
    [onMoveImage]
  )

  return (
    <Row className="mb-3">
      {/* Главное изображение */}
      {mainImage && (
        <Col md="auto">
          <Figure>
            <Figure.Caption className="m-1">
              <strong>Главное изображение</strong>
            </Figure.Caption>
            <Figure.Image
              width={200}
              alt="Главное изображение"
              src={getImageSrc(mainImage)}
              className="border rounded"
            />
          </Figure>
        </Col>
      )}

      {/* Дополнительные изображения */}
      {pictures.map((item, index) => (
        <ImageItem
          key={`picture-${index}`}
          item={item}
          index={index}
          isEditable={isEditable}
          onMoveLeft={handleMoveLeft}
          onMoveRight={handleMoveRight}
          onDelete={onDeleteImage}
          isFirst={index === 0}
          isLast={index === pictures.length - 1}
        />
      ))}
    </Row>
  )
}

export default ImageGallery
