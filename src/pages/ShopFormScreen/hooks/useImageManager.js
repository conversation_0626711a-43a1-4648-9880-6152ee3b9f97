import { useState, useCallback, useMemo } from 'react'

import { MAX_IMG_SIZE } from '@/const'
import { useDeleteUploadFile } from '@/features/files/api/deleteUploadFile'
import { useUploadFile } from '@/features/files/api/uploadFile'
import { useUpdateProductItem } from '@/features/shop/api/updateProductItem'
import { useToast } from '@/hooks/useToast'

/**
 * Хук для работы с URL изображений
 */
const useImageUrlUtils = () => {
  const baseApi = import.meta.env.VITE_REACT_APP_API?.replace(/\/$/, '') || ''

  const toRelative = useCallback(
    (url) => {
      if (!url) return url
      if (typeof url === 'string' && url.startsWith('http')) {
        try {
          const urlObj = new URL(url)
          return urlObj.pathname + urlObj.search + urlObj.hash
        } catch {
          return url.replace(baseApi, '')
        }
      }
      return url
    },
    [baseApi]
  )

  const normalizeList = useCallback(
    (arr) => (arr || []).filter((p) => typeof p === 'string' && !p.startsWith('data:')).map((p) => toRelative(p)),
    [toRelative]
  )

  return { toRelative, normalizeList }
}

/**
 * Хук для определения изменений в изображениях
 */
const useImageChanges = (pictures, defaultFormData, getFieldValue, lastSavedMainValue, normalizeList) => {
  return useMemo(() => {
    const hasBase64 = Array.isArray(pictures)
      ? pictures.some((p) => typeof p === 'string' && p.startsWith('data:'))
      : false

    const savedNormalized = normalizeList(defaultFormData?.pictures)
    const currentNormalized = normalizeList(pictures)
    const orderChanged = JSON.stringify(currentNormalized) !== JSON.stringify(savedNormalized)

    const mainDefault = defaultFormData?.picture ?? ''
    const mainCurrent = getFieldValue('picture', '')
    const mainChanged = mainCurrent !== mainDefault && mainCurrent !== lastSavedMainValue

    const hasChanges = hasBase64 || orderChanged || mainChanged

    return {
      hasBase64,
      orderChanged,
      mainChanged,
      hasChanges,
      savedNormalized,
      mainCurrent,
    }
  }, [pictures, defaultFormData, getFieldValue, lastSavedMainValue, normalizeList])
}

/**
 * Основной хук для управления изображениями продукта
 */
export const useImageManager = ({
  defaultFormData,
  getFieldValue,
  onChangePictures,
  onSavedPictures,
  onSavedMainPicture,
  onUpdateMainPicture,
  productPublicId,
}) => {
  const openToast = useToast()
  const { mutateAsync: uploadFileMutateAsync } = useUploadFile()
  const { mutateAsync: deleteUploadFileMutateAsync } = useDeleteUploadFile()
  const { mutateAsync: updateProductItemMutateAsync } = useUpdateProductItem()

  const [isUploading, setIsUploading] = useState(false)
  const [lastSavedMainValue, setLastSavedMainValue] = useState(null)
  const [disableOnce, setDisableOnce] = useState(false)

  const pictures = getFieldValue('pictures', [])
  const { toRelative, normalizeList } = useImageUrlUtils()
  const { hasBase64, orderChanged, mainChanged, hasChanges, savedNormalized, mainCurrent } = useImageChanges(
    pictures,
    defaultFormData,
    getFieldValue,
    lastSavedMainValue,
    normalizeList
  )

  /**
   * Проверяет размер файла и показывает ошибку если файл слишком большой
   */
  const validateFileSize = useCallback(
    (file) => {
      if (file.size > MAX_IMG_SIZE) {
        openToast.error({
          title: true,
          message: `Файл слишком большой: ${file.name}`,
          duration: 6000,
        })
        return false
      }
      return true
    },
    [openToast]
  )

  /**
   * Загружает base64 изображения на сервер и возвращает пути к файлам
   */
  const uploadBase64Images = useCallback(
    async (pictures) => {
      const uploadedPaths = []

      for (const item of pictures) {
        if (typeof item === 'string' && item.startsWith('data:')) {
          const resp = await uploadFileMutateAsync({ file: item })
          const path = resp?.data?.path
          if (path) {
            uploadedPaths.push(path)
          } else {
            openToast.error({ message: 'Ошибка: сервер не вернул путь файла' })
          }
        } else if (typeof item === 'string') {
          uploadedPaths.push(toRelative(item))
        }
      }

      return uploadedPaths
    },
    [uploadFileMutateAsync, toRelative, openToast]
  )

  /**
   * Удаляет файлы, которые больше не используются
   */
  const deleteUnusedFiles = useCallback(
    async (savedFiles, currentFiles) => {
      const filesToDelete = savedFiles.filter((file) => !currentFiles.includes(file))

      await Promise.allSettled(filesToDelete.map((file) => deleteUploadFileMutateAsync({ file })))
    },
    [deleteUploadFileMutateAsync]
  )

  return {
    // Состояние
    isUploading,
    disableOnce,
    hasChanges,
    pictures,
    mainImage: getFieldValue('picture', ''),

    // Утилиты
    validateFileSize,
    setDisableOnce,
    setLastSavedMainValue,

    // Внутренние функции для сохранения
    uploadBase64Images,
    deleteUnusedFiles,
    updateProductItemMutateAsync,
    setIsUploading,

    // Данные для сохранения
    hasBase64,
    orderChanged,
    mainChanged,
    savedNormalized,
    mainCurrent,

    // Коллбэки
    onSavedPictures,
    onSavedMainPicture,
    onUpdateMainPicture,
    onChangePictures,
    getFieldValue,
    productPublicId,
    openToast,
  }
}
