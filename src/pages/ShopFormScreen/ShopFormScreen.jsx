import useAxios from 'axios-hooks'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Button, Col, Row, Alert, ProgressBar, Form, Figure } from 'react-bootstrap'
import { Link, useNavigate, useParams } from 'react-router-dom'
import Select from 'react-select'
import AsyncSelect from 'react-select/async'

import { APIRoute } from '@/const'
import { useTheme } from '@/contexts/ThemeContext'
import { useGetCountryList } from '@/features/cities/api/getCountryList'
import { useGetEvents } from '@/features/events/api/getEvents'
import { useGetProcessingList } from '@/features/processing/api/getProcessingList'
import { useGetShopCategories } from '@/features/shop/api/getShopCategories'
import { useGetShopCollections } from '@/features/shop/api/getShopCollections'
import { useGetShopItems } from '@/features/shop/api/getShopItems'
import { useToast } from '@/hooks/useToast'
import { unixToMoment } from '@/utils/date'
import { updateFormData } from '@/utils/forms'

import FormSection from './components/FormSection'
import ProductBasicInfo from './components/ProductBasicInfo'
import ProductCare from './components/ProductCare'
import ProductCategories from './components/ProductCategories'
import ProductDescription from './components/ProductDescription'
import ProductImages from './components/ProductImages'
import ProductPricing from './components/ProductPricing'
import ProductSettings from './components/ProductSettings'
import ProductTabs from './components/ProductTabs'
import { shopSelectStyles } from './components/shopSelectStyles'
import Layout from '../../components/Layout/Layout'
import { Tag } from '../ShopScreen/components/Delivery/Delivery'

function ShopFormScreen() {
  const { effectiveTheme } = useTheme()
  const { data: shopItemsData } = useGetShopItems()
  const { data: categoriesData } = useGetShopCategories()
  const { data: collectionsData } = useGetShopCollections()

  const data = shopItemsData?.data
  const categories = categoriesData?.data
  const collections = collectionsData?.data

  const [, api] = useAxios(
    {
      url: APIRoute.SHOP_PRODUCT,
      method: 'POST',
    },
    { manual: true }
  )

  const getProcessingListQuery = useGetProcessingList()
  const processingList = getProcessingListQuery?.data?.data?.values

  const openToast = useToast()
  const { data: eventsData } = useGetEvents()
  const events = useMemo(() => eventsData?.data?.values || [], [eventsData?.data?.values])
  const { data: countriesData, isSuccess: countriesLoaded } = useGetCountryList()
  const countries = countriesData?.data?.values || []

  const [formData, setFormData] = useState({})
  const [defaultFormData, setDefaultFormData] = useState({})
  const [isOldPrice, setIsOldPrice] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [selectedCities, setSelectedCities] = useState([])
  const [allCities, setAllCities] = useState([])
  const [isSaving, setIsSaving] = useState(false)
  const [saveProgress, setSaveProgress] = useState(0)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const selectCityRef = useRef()
  const { public_id } = useParams()
  const navigate = useNavigate()

  const isUpdateShopProduct = !!public_id

  // Helper function to get the current value for form controls
  const getFieldValue = (fieldName, defaultValue = '') => {
    if (formData[fieldName] !== undefined) {
      return formData[fieldName]
    }
    if (defaultFormData[fieldName] !== undefined) {
      return defaultFormData[fieldName]
    }
    return defaultValue
  }

  const validateForm = () => {
    const errors = []

    // Get the effective values (formData takes precedence over defaultFormData)
    const effectiveData = { ...defaultFormData, ...formData }

    if (!effectiveData.title?.trim()) {
      errors.push('Название товара обязательно для заполнения')
    }

    if (!effectiveData.price || effectiveData.price <= 0) {
      errors.push('Цена товара должна быть больше 0')
    }

    if (!effectiveData.group) {
      errors.push('Необходимо выбрать раздел товара')
    }

    if (!effectiveData.category?.public_id) {
      errors.push('Необходимо выбрать категорию товара')
    }

    return errors
  }

  const handleSubmitForm = useCallback(
    async (evt) => {
      evt.preventDefault()

      const validationErrors = validateForm()
      if (validationErrors.length > 0) {
        openToast.error({
          message: `Пожалуйста, исправьте следующие ошибки:\n${validationErrors.join('\n')}`,
          duration: 8000,
        })
        return
      }

      setIsSaving(true)
      setSaveProgress(10)

      try {
        setSaveProgress(30)
        const method = isUpdateShopProduct ? 'PUT' : 'POST'
        const toastText = isUpdateShopProduct ? 'Товар успешно обновлен' : 'Товар успешно создан'
        const url = isUpdateShopProduct
          ? `${APIRoute.SHOP_PRODUCT}/${defaultFormData.public_id}`
          : APIRoute.SHOP_PRODUCT

        setSaveProgress(60)

        // Prepare data for backend - remove name_ru from country field
        const submitData = { ...formData }
        if (submitData.country && submitData.country.name_ru) {
          submitData.country = { id: submitData.country.id }
        }

        const response = await api({ url: url, data: submitData, method: method })

        setSaveProgress(90)

        if (response.status === 200) {
          setSaveProgress(100)
          openToast.success({
            message: toastText,
            duration: 4000,
          })

          // Small delay to show completion
          setTimeout(() => {
            navigate('/shop')
          }, 500)
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
      } catch (error) {
        console.error('Save error:', error)
        const errorMessage = error.response?.data?.message || error.message || 'Неизвестная ошибка'
        openToast.error({
          title: 'Ошибка сохранения',
          message: `Не удалось сохранить товар: ${errorMessage}`,
          duration: 8000,
        })
        setSaveProgress(0)
      } finally {
        setIsSaving(false)
      }
    },
    [isSaving, formData, defaultFormData, isUpdateShopProduct, openToast, navigate, api, validateForm]
  )

  // Track unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = 'У вас есть несохраненные изменения. Вы действительно хотите покинуть страницу?'
        return e.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Update unsaved changes flag
  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(defaultFormData)
    setHasUnsavedChanges(hasChanges && !isSaving)
  }, [formData, defaultFormData, isSaving])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event) => {
      // Ctrl/Cmd + S to save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault()
        if (!isSaving) {
          handleSubmitForm(event)
        }
      }
      // Escape to go back
      if (event.key === 'Escape') {
        event.preventDefault()
        if (!isSaving) {
          if (hasUnsavedChanges) {
            const confirmLeave = window.confirm(
              'У вас есть несохраненные изменения. Вы действительно хотите покинуть страницу?'
            )
            if (confirmLeave) {
              window.history.back()
            }
          } else {
            window.history.back()
          }
        }
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [isSaving, hasUnsavedChanges, handleSubmitForm])

  useEffect(() => {
    if (events.length) {
      const newCities = []

      events.forEach((el) => {
        el?.event_city?.forEach((city) => {
          defaultFormData?.event_city?.forEach((item) => {
            if (item === city?.public_id) {
              newCities.push(city)
              return true
            }
          })
        })
      })

      if (defaultFormData?.event_city?.length > 0) {
        const eventPublicId = defaultFormData.event_city[0].split('_')[0]

        const findEvent = events.find((item) => item.public_id === eventPublicId)

        setSelectedEvent(findEvent)
      }

      if (newCities.length) setSelectedCities(newCities)
    }
  }, [events, defaultFormData.event_city])

  useEffect(() => {
    if (public_id && data) {
      const findProduct = data.values.find((item) => item.public_id === public_id)

      if (findProduct) {
        setDefaultFormData({ ...findProduct })

        if (findProduct?.old_price) {
          setIsOldPrice(true)
        }
      }
    } else if (data?.values?.length > 0) {
      setFormData((prev) => ({
        ...prev,
        priority_number: data.values[0].priority_number + 1,
        delivery: true,
        public: true,
        retailcrm: true,
      }))
    }
  }, [public_id, data, defaultFormData.event_city])

  const loadCountryOptions = (inputValue) => {
    return new Promise((resolve) => {
      // Небольшая задержка для лучшего UX
      setTimeout(() => {
        if (!countries.length) {
          resolve([])
          return
        }

        const filteredCountries = !inputValue
          ? countries
          : countries.filter(
              (country) =>
                country.name_ru.toLowerCase().includes(inputValue.toLowerCase()) ||
                country.name_en.toLowerCase().includes(inputValue.toLowerCase())
            )

        const options = filteredCountries.map((country) => ({
          value: country.id,
          label: country.name_ru,
        }))

        resolve(options)
      }, 100)
    })
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleChangeSelect = (evt) => {
    const value = evt.target.value
    const name = evt.target.name

    setFormData({ ...formData, [name]: { public_id: value } })
  }

  const handleEvent = (e) => {
    const event = events.find((event) => event.public_id === e.target.value)

    setSelectedEvent(event)
  }

  const handleSelectCity = () => {
    const selectedOptions = selectCityRef.current.selectedOptions
    const findCities = []

    setSelectedCities(allCities)

    for (let i = 0; i < selectedOptions.length; i++) {
      const option = selectedOptions[i].value

      if (option === 'none' || option === 'all') return
      else {
        const findCity = selectedEvent?.event_city?.find((item) => item.public_id === option)

        if (!selectedCities.map((item) => item.public_id).includes(option)) {
          findCities.push(findCity)
        }
      }

      const newCities = [...selectedCities, ...findCities]
      const idCities = newCities.map((item) => item.public_id)

      setFormData({ ...formData, event_city: idCities })
      setSelectedCities(newCities)
    }
  }

  const handleChangeCity = (evt) => {
    if (evt.target.value === 'all') {
      setAllCities(selectedEvent?.event_city)
      const newCities = selectedEvent?.event_city
      const idCities = newCities?.map((item) => item.public_id)

      setFormData({ ...formData, event_city: idCities })
    }
  }

  const handleDelSelectedCity = (city) => {
    const filteredCities = selectedCities.filter((item) => item.public_id !== city.public_id)
    const idCities = filteredCities.map((city) => city.public_id)

    setFormData({ ...formData, event_city: idCities })
    setSelectedCities(filteredCities)
  }

  const handleChangeToggle = (evt) => {
    const name = evt.target.name
    const value = evt.target.checked

    setFormData({ ...formData, [name]: value })
  }

  const handleChangeCollect = (evt) => {
    const newFormData = { ...formData }

    if (evt.length > 0) {
      const collections = evt.map((item) => item.value)
      setFormData({ ...formData, collections: collections })
    } else {
      delete newFormData.collections
      setFormData({ ...newFormData })
    }
  }

  const handleChangeCountry = (selectedOption) => {
    if (selectedOption) {
      // Store both id and name_ru to preserve label for display
      setFormData({ ...formData, country: { id: parseInt(selectedOption.value), name_ru: selectedOption.label } })
    } else {
      const newFormData = { ...formData }
      delete newFormData.country
      setFormData(newFormData)
    }
  }

  const handleChangeCheckbox = (evt) => {
    const isChecked = evt.target.checked
    const name = evt.target.name
    const value = evt.target.value
    const newCalendarStory = { ...formData }
    let newArr = []

    if (name in formData) {
      newArr = [...formData[name]]
    } else if (name in defaultFormData) {
      newArr = [...defaultFormData[name]]
    }

    if (isChecked) {
      newArr.push(value)
      setFormData({ ...formData, [name]: [...newArr] })
    } else {
      const filteredArr = newArr.filter((item) => item !== value)

      if (filteredArr.length === 0) {
        delete newCalendarStory[name]
        setFormData({ ...newCalendarStory })
      } else {
        setFormData({ ...formData, [name]: [...filteredArr] })
      }
    }
  }

  const checkCheckbox = (name, value) => {
    if (formData[name]) {
      return formData[name] && formData[name].includes(value)
    } else {
      return defaultFormData[name] && defaultFormData[name].includes(value)
    }
  }

  const handleChangeOldPrice = (evt) => {
    const value = +evt.target.value
    const newFormData = { ...formData }

    if (value > 0) {
      setFormData({ ...formData, old_price: value })
    } else if (value === defaultFormData?.old_price) {
      delete newFormData.old_price
      setFormData({ ...newFormData })
    } else if (value === 0 && defaultFormData?.old_price > 0) {
      setFormData({ ...formData, old_price: 0 })
    }
  }

  const handleToggleOldPrice = (evt) => {
    const isChecked = evt.target.checked
    const newFormData = { ...formData }

    setIsOldPrice(isChecked)

    if (!isChecked && defaultFormData?.old_price) {
      setFormData({ ...formData, old_price: 0 })
    } else if (!isChecked && !defaultFormData?.old_price) {
      delete newFormData.old_price
      setFormData({ ...newFormData })
    } else if (isChecked && defaultFormData?.old_price) {
      delete newFormData.old_price
      setFormData({ ...newFormData })
    }
  }

  // Create dynamic styles that respond to theme changes
  const dynamicSelectStyles = {
    ...shopSelectStyles,
    control: (provided, state) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        border: darkTheme ? '1px solid #495057' : '1px solid #ced4da',
        borderRadius: '0.375rem',
        minHeight: '100%',
        backgroundColor: darkTheme ? '#212529' : '#ffffff',
        boxShadow: state.isFocused
          ? darkTheme
            ? '0 0 0 0.25rem rgba(13, 110, 253, 0.25)'
            : '0 0 0 0.25rem rgba(13, 110, 253, 0.25)'
          : 'none',
        '&:hover': {
          borderColor: darkTheme ? '#6c757d' : '#86b7fe',
        },
      }
    },
    menu: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#212529' : '#ffffff',
        border: darkTheme ? '1px solid #495057' : '1px solid #ced4da',
        borderRadius: '0.375rem',
        boxShadow: darkTheme ? '0 0.5rem 1rem rgba(0, 0, 0, 0.15)' : '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
      }
    },
    option: (provided, state) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: state.isSelected
          ? '#0d6efd'
          : state.isFocused
            ? darkTheme
              ? '#495057'
              : '#e9ecef'
            : 'transparent',
        color: state.isSelected ? '#ffffff' : darkTheme ? '#ffffff' : '#212529',
        '&:hover': {
          backgroundColor: state.isSelected ? '#0d6efd' : darkTheme ? '#495057' : '#e9ecef',
        },
      }
    },
    multiValue: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#495057' : '#e9ecef',
        borderRadius: '0.25rem',
      }
    },
    multiValueLabel: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#495057',
      }
    },
    multiValueRemove: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#495057',
        borderRadius: '0 0.25rem 0.25rem 0',
        '&:hover': {
          backgroundColor: '#dc3545',
          color: '#ffffff',
        },
      }
    },
    placeholder: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#6c757d' : '#6c757d',
      }
    },
    singleValue: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#212529',
      }
    },
    input: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#212529',
      }
    },
    indicatorSeparator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#495057' : '#ced4da',
      }
    },
    dropdownIndicator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#6c757d' : '#6c757d',
        '&:hover': {
          color: darkTheme ? '#adb5bd' : '#495057',
        },
      }
    },
    clearIndicator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#6c757d' : '#6c757d',
        '&:hover': {
          color: darkTheme ? '#adb5bd' : '#495057',
        },
      }
    },
    loadingIndicator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#6c757d' : '#6c757d',
      }
    },
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col>
          <Button as={Link} to={'/shop'} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Вернуться к списку товаров
          </Button>
        </Col>
      </Row>
      <Row className="mb-3">
        <Col>
          <div className="d-flex align-items-center">
            <h3 className="mb-0 me-2">{isUpdateShopProduct ? 'Редактирование товара' : 'Добавление товара'}</h3>
            {hasUnsavedChanges && (
              <span className="badge bg-warning text-dark">
                <i className="bi bi-exclamation-triangle me-1"></i>
                Несохраненные изменения
              </span>
            )}
          </div>
          <small className="text-muted">
            {isUpdateShopProduct
              ? 'Измените данные товара и сохраните изменения'
              : 'Заполните форму для создания нового товара'}
          </small>
        </Col>
        <Col xs="auto">
          <div className="d-flex gap-2">
            <Button
              variant="outline-secondary"
              onClick={() => {
                if (hasUnsavedChanges) {
                  const confirmLeave = window.confirm(
                    'У вас есть несохраненные изменения. Вы действительно хотите покинуть страницу?'
                  )
                  if (confirmLeave) {
                    window.history.back()
                  }
                } else {
                  window.history.back()
                }
              }}
              aria-label="Вернуться назад"
              disabled={isSaving}
            >
              <i className="bi bi-arrow-left me-1"></i>
              Назад
            </Button>
            <Button as={Link} to={'/shop'} variant="outline-primary" aria-label="Вернуться к списку товаров">
              <i className="bi bi-list me-1"></i>
              Список товаров
            </Button>
          </div>
        </Col>
      </Row>

      {/* Keyboard shortcuts help */}
      <Row className="mb-3">
        <Col>
          <Alert variant="light" className="border">
            <div className="d-flex align-items-center">
              <i className="bi bi-info-circle me-2 text-info"></i>
              <small className="mb-0">
                <strong>Горячие клавиши:</strong> Ctrl+S (сохранить), Escape (назад) •<strong> Подсказка:</strong> Все
                поля с * обязательны для заполнения
              </small>
            </div>
          </Alert>
        </Col>
      </Row>

      {isSaving && (
        <div className="mb-3">
          <Alert variant="info">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status">
                <span className="visually-hidden">Сохранение...</span>
              </div>
              <span>Сохранение товара...</span>
            </div>
            <ProgressBar now={saveProgress} className="mt-2" />
          </Alert>
        </div>
      )}

      <Form className="mb-5" onSubmit={handleSubmitForm} id="productForm">
        {/* Basic Information Section */}
        <FormSection title="Основная информация" icon="bi bi-info-circle">
          <ProductBasicInfo
            formData={formData}
            defaultFormData={defaultFormData}
            handleChangeField={handleChangeField}
            loadCountryOptions={loadCountryOptions}
            handleChangeCountry={handleChangeCountry}
            countriesLoaded={countriesLoaded}
            dynamicSelectStyles={dynamicSelectStyles}
            getFieldValue={getFieldValue}
          />
        </FormSection>

        {/* Pricing Section */}
        <FormSection title="Ценообразование" icon="bi bi-cash">
          <ProductPricing
            formData={formData}
            defaultFormData={defaultFormData}
            handleChangeField={handleChangeField}
            handleChangeOldPrice={handleChangeOldPrice}
            handleToggleOldPrice={handleToggleOldPrice}
            isOldPrice={isOldPrice}
            getFieldValue={getFieldValue}
          />
        </FormSection>

        {/* Categories Section */}
        <FormSection title="Категории и классификация" icon="bi bi-tags">
          <ProductCategories
            formData={formData}
            defaultFormData={defaultFormData}
            handleChangeField={handleChangeField}
            handleChangeSelect={handleChangeSelect}
            handleChangeCollect={handleChangeCollect}
            categories={categories}
            collections={collections}
            processingList={processingList}
            dynamicSelectStyles={dynamicSelectStyles}
            getFieldValue={getFieldValue}
          />
        </FormSection>

        {/* Settings Section */}
        <FormSection title="Настройки и параметры" icon="bi bi-gear">
          <ProductSettings
            formData={formData}
            defaultFormData={defaultFormData}
            handleChangeToggle={handleChangeToggle}
            handleChangeCheckbox={handleChangeCheckbox}
            checkCheckbox={checkCheckbox}
          />
        </FormSection>

        {/* Description Section */}
        <FormSection title="Описание и характеристики" icon="bi bi-file-text">
          <ProductDescription
            formData={formData}
            defaultFormData={defaultFormData}
            handleChangeField={handleChangeField}
            getFieldValue={getFieldValue}
          />
        </FormSection>

        {/* Care Recommendations Section */}
        <FormSection title="Рекомендации по уходу" icon="bi bi-heart">
          <ProductCare
            formData={formData}
            defaultFormData={defaultFormData}
            handleChangeField={handleChangeField}
            getFieldValue={getFieldValue}
          />
        </FormSection>
      </Form>

      <h4 className="mb-2">Сопутствующие товары</h4>

      <div style={{ maxWidth: '700px', marginTop: '40px', marginBottom: '40px' }}>
        <Row>
          <Col xs={4}>
            <Form.Label>Выберите событие</Form.Label>
          </Col>
          <Col>
            <Form.Control
              as="select"
              aria-label="event"
              name="event"
              onChange={handleEvent}
              className="mb-3"
              value={selectedEvent?.public_id}
              required
            >
              <option>Выберите событие</option>
              {events.map((event) => (
                <option value={event?.public_id} key={event?.public_id}>
                  {event?.title}
                </option>
              ))}
            </Form.Control>
          </Col>
        </Row>

        {selectedEvent && (
          <Row>
            <Col xs={4}>
              <Form.Label>Выберите город</Form.Label>
            </Col>
            <Col>
              <Form.Control
                as="select"
                aria-label="event"
                name="city"
                multiple
                ref={selectCityRef}
                onChange={handleChangeCity}
                className="mb-3"
                required
              >
                <option value="none">Выберите город</option>
                <option value="all">Все города</option>
                {selectedEvent?.event_city?.map((city, index) => (
                  <option value={city.public_id} key={`${city.public_id}-${index}`}>
                    {city.city.name_ru} — {unixToMoment(city.start_time).format('DD.MM.YYYY')}
                  </option>
                ))}
              </Form.Control>
            </Col>
          </Row>
        )}

        {selectedEvent && (
          <Row className="mb-3">
            <Col />
            <Col md="auto">
              <Button onClick={handleSelectCity}>Добавить</Button>
            </Col>
          </Row>
        )}

        {selectedCities?.length > 0 && (
          <p style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            <span>
              Выбранные города:{' '}
              {selectedCities.map((city) => (
                <Tag onClick={() => handleDelSelectedCity(city)} key={city?.public_id}>
                  {city.address}
                </Tag>
              ))}
            </span>
          </p>
        )}
      </div>

      <Row className="mt-4 mb-5">
        <Col className="d-grid" md={{ offset: 3, span: 6 }}>
          <div className="d-flex gap-3 justify-content-center">
            <Button type="submit" form="productForm" variant="success" size="lg" disabled={isSaving} className="px-5">
              {isSaving ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Сохранение...
                </>
              ) : (
                <>
                  <i className="bi bi-check-circle me-2"></i>
                  {isUpdateShopProduct ? 'Обновить товар' : 'Создать товар'}
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline-secondary"
              size="lg"
              onClick={() => {
                if (hasUnsavedChanges) {
                  const confirmLeave = window.confirm(
                    'У вас есть несохраненные изменения. Вы действительно хотите покинуть страницу?'
                  )
                  if (confirmLeave) {
                    window.history.back()
                  }
                } else {
                  window.history.back()
                }
              }}
              disabled={isSaving}
            >
              <i className="bi bi-x-circle me-2"></i>
              Отмена
            </Button>
          </div>
        </Col>
      </Row>

      {/* Images Section moved below action buttons */}
      <ProductImages
        formData={formData}
        defaultFormData={defaultFormData}
        getFieldValue={getFieldValue}
        onChangePictures={(pictures) => setFormData((prev) => ({ ...prev, pictures }))}
        onSavedPictures={(updated) => {
          setFormData((prev) => ({ ...prev, pictures: updated }))
          setDefaultFormData((prev) => ({ ...prev, pictures: updated }))
        }}
        onSavedMainPicture={(picture) => {
          setFormData((prev) => ({ ...prev, picture }))
          setDefaultFormData((prev) => ({ ...prev, picture }))
        }}
        onUpdateMainPicture={(picture) => setFormData((prev) => ({ ...prev, picture }))}
        productPublicId={defaultFormData?.public_id}
      />

      <h3 className="mb-3">Редактирование опций</h3>

      <ProductTabs productId={public_id} />
    </Layout>
  )
}

export default ShopFormScreen
