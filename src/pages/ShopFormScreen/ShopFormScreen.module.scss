.fileButton {
  margin: 0;
  padding: 10px;

  display: block;
  width: 100%;
  min-height: 100%;

  text-align: center;

  background-color: #42c2ff;
  border-radius: 4px;

  cursor: pointer;

  &:hover {
    background-color: darken(#42c2ff, 10);
  }
}

.error {
  color: #dc3545;
}

.descriptionWrap :global .form-floating > .form-control:focus ~ label,
.descriptionWrap :global .form-floating > .form-control:not(:placeholder-shown) ~ label,
.descriptionWrap :global .form-floating > .form-select ~ label {
  opacity: 1;
  color: rgba(0, 0, 0, 0.5);
  background-color: #fff;
  height: auto;
  padding: 0 0 0 10px;
  width: 95%;
  transform: scale(0.982) translateX(0.1rem);
  border-radius: 5px 0 0 0;
}
