import { Button, ListGroup, Modal } from 'react-bootstrap'

function ModalResultPromocodes({ show, handleClosePopup, newPromocodes }) {
  return (
    <Modal show={show} onHide={() => handleClosePopup(false)}>
      <Modal.Header closeButton>
        <Modal.Title>Созданные промокоды</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <ListGroup>
          {newPromocodes.map((promocode) => (
            <ListGroup.Item key={promocode}>{promocode}</ListGroup.Item>
          ))}
        </ListGroup>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={() => handleClosePopup(false)}>
          Закрыть
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default ModalResultPromocodes
