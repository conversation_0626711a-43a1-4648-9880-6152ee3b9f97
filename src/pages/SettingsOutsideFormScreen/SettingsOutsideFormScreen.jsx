import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { Link, useNavigate, useParams } from 'react-router-dom'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { updateFormData } from '@/utils/forms'

import Layout from '../../components/Layout/Layout'

function SettingsOutsideFormScreen() {
  const [formData, setFormData] = useState({
    active: true,
  })
  const [defaultFormData, setDefaultFormData] = useState({})

  const { public_id } = useParams()
  const navigate = useNavigate()

  const isUpdateSettings = !!public_id

  const openToast = useToast()

  const [{ data }] = useAxios({
    url: APIRoute.ACTIONS_SETTINGS,
    method: 'GET',
  })
  const [, api] = useAxios(
    {
      url: APIRoute.ACTIONS_SETTINGS,
      method: 'POST',
    },
    { manual: true }
  )

  useEffect(() => {
    if (public_id && data) {
      const findSettingsItem = data.values.find((item) => item._id === public_id)

      if (findSettingsItem) {
        setDefaultFormData({ ...findSettingsItem })
      }
    }
  }, [public_id, data])

  const handleSubmitForm = () => {
    const method = isUpdateSettings ? 'PUT' : 'POST'
    const toastText = isUpdateSettings ? 'Настройки изменены' : 'Настройки созданы'
    const url = isUpdateSettings
      ? `${APIRoute.ACTIONS_SETTINGS}/${defaultFormData.name}/${defaultFormData.type}`
      : APIRoute.ACTIONS_SETTINGS

    api({ url, data: formData, method: method }).then((r) => {
      if (r.status === 200) {
        openToast.success({ message: toastText })
        navigate('/settings')
      }
    })
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, defaultFormData)
  }

  const handleChangeEmail = (evt) => {
    const value = evt.target.value
    const newFormData = { ...formData }

    if (value === '' || value === 'none' || value === defaultFormData?.info?.email) {
      delete newFormData.info.email
      setFormData({ ...newFormData })
    } else {
      setFormData({ ...formData, info: { email: value } })
    }
  }

  const handleChangeToggle = (evt) => {
    const name = evt.target.name
    const value = evt.target.checked

    setFormData({ ...formData, [name]: value })
  }

  return (
    <Layout>
      <Row className="mb-4">
        <Col>
          <Button as={Link} to={'/settings'} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Вернуться к списку настроек
          </Button>
        </Col>
      </Row>

      <Row className="g-3 mb-3">
        <Col md={4}>
          <FloatingLabel controlId="emailLabel" label="Email">
            <FormControl
              onChange={handleChangeEmail}
              defaultValue={defaultFormData?.info?.email}
              name="email"
              type="text"
              placeholder="Email"
            />
          </FloatingLabel>
        </Col>

        <Col md={4}>
          <FloatingLabel controlId="nameLabel" label="Название">
            <FormControl
              onChange={handleChangeField}
              defaultValue={defaultFormData?.name}
              name="name"
              type="text"
              placeholder="Название"
              disabled={isUpdateSettings}
            />
          </FloatingLabel>
        </Col>

        <Col md={4}>
          <FloatingLabel controlId="typeLabel" label="Тип">
            <FormControl
              onChange={handleChangeField}
              defaultValue={defaultFormData.type}
              name="type"
              type="text"
              placeholder="Тип"
              disabled={isUpdateSettings}
            />
          </FloatingLabel>
        </Col>

        <Col md={4}>
          <Form.Check
            onChange={handleChangeToggle}
            defaultChecked={defaultFormData.active || formData.active}
            name="active"
            type="switch"
            id="active-switch"
            label="Активно"
          />
        </Col>
      </Row>

      <Row>
        <Col className="d-grid" md={{ offset: 4, span: 4 }}>
          <Button onClick={handleSubmitForm} variant="success" size="lg">
            Сохранить
          </Button>
        </Col>
      </Row>
    </Layout>
  )
}

export default SettingsOutsideFormScreen
