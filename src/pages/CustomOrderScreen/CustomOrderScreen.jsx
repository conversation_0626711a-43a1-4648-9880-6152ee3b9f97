import useAxios from 'axios-hooks'
import { useContext, useState } from 'react'
import {
  Accordion,
  AccordionContext,
  Button,
  Card,
  Col,
  OverlayTrigger,
  Row,
  Table,
  Tooltip,
  useAccordionButton,
} from 'react-bootstrap'
import { Link } from 'react-router-dom'

import { APIRoute } from '@/const'
import { API_URL } from '@/lib/axios'
import { getDescCode, getTicketStatus, times } from '@/utils/common'

import copyIcon from '../../assets/img/icons/icon-copy.svg?react'
import AdvancedPagination from '../../components/AdvancedPagination/AdvancedPagination'
import Layout from '../../components/Layout/Layout'
import PageSearch from '../../components/PageSearch/PageSearch'

function ContextAwareToggle({ children, eventKey, callback }) {
  const { activeEventKey } = useContext(AccordionContext)

  const decoratedOnClick = useAccordionButton(eventKey, () => callback && callback(eventKey))

  const isCurrentEventKey = activeEventKey === eventKey

  return (
    <Button variant={isCurrentEventKey ? 'info' : 'primary'} onClick={decoratedOnClick}>
      {children}
    </Button>
  )
}

function CustomOrderScreen() {
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [valuesRender, setValuesRender] = useState([]) // для отрисовки на странице
  const [clipboardText, setClipboardText] = useState('Скопировать ссылку')

  const [{ data }] = useAxios({
    url: APIRoute.CUSTOM_ORDERS,
    method: 'GET',
  })

  // придумать откуда брать нормальный полный адрес для ссылки
  const handleCopyToClipboard = (id) => {
    navigator.clipboard.writeText(`${window.location.origin.toString()}/api/random/payment/${id}`).then(
      () => {
        setClipboardText('Скопирована')
      },
      () => {
        setClipboardText('Не удалось скопировать')
      }
    )

    setTimeout(() => setClipboardText('Скопировать ссылку'), 800)
  }

  return (
    <Layout title="Произвольные заказы" href={'custom-order/create'}>
      <Row className="mb-3">
        <Col>
          <PageSearch values={data?.values || []} setValues={setFilteredValues} />
        </Col>
      </Row>

      <Row className="mb-3">
        <Col>Статус</Col>
        <Col>Сумма</Col>
        <Col>Дата создания</Col>
        <Col>Имейл</Col>
        <Col>Айди пользователя</Col>
        <Col>Айди заказа</Col>
        <Col className="d-grid justify-content-center">Заказы</Col>
      </Row>

      <Accordion className="mb-3" defaultActiveKey="0">
        {valuesRender?.map((order, index) => (
          <Card className="mb-2" key={order.public_id}>
            <Card.Header>
              <Row>
                <Col>{getTicketStatus(order.status)}</Col>
                <Col>{order.amount}</Col>
                <Col>{times.getFullDate(order.created)}</Col>
                <Col>{order.email}</Col>
                <Col>
                  {order.user_public_id ? (
                    <Link to={`/user/${order.user_public_id}`}>{order.user_public_id}</Link>
                  ) : (
                    order.user_public_id
                  )}
                </Col>
                <Col>
                  <a href={`${API_URL}/api/random/payment/${order.public_id}`} target="_blank" rel="noreferrer">
                    {order.public_id}
                  </a>
                </Col>
                <Col style={{ marginLeft: '50px' }} md="auto">
                  <ContextAwareToggle eventKey={`${index + 1}`}>Открыть</ContextAwareToggle>
                  <OverlayTrigger
                    placement="left"
                    delay={{ show: 250, hide: 400 }}
                    overlay={<Tooltip id="button-tooltip-2">{clipboardText}</Tooltip>}
                  >
                    <Button
                      onClick={() => {
                        handleCopyToClipboard(order.public_id)
                      }}
                      style={{
                        width: '16px',
                        height: '16px',
                        backgroundImage: `url(${copyIcon})`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                      }}
                      variant="link"
                    />
                  </OverlayTrigger>
                </Col>
              </Row>
            </Card.Header>
            <Accordion.Collapse eventKey={`${index + 1}`}>
              <Card.Body>
                <Table striped bordered hover responsive size="sm">
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>Наименование</th>
                      <th>Цена за единицу</th>
                      <th>Количество</th>
                      <th>Сумма</th>
                      <th>Код системы налогообложения</th>
                      <th>Код ставки налогообложения</th>
                      <th>Признак способа расчета</th>
                      <th>Признак предмета расчета</th>
                    </tr>
                  </thead>
                  <tbody style={{ textAlign: 'center' }}>
                    {order.items.other_orders.map((item, index) => (
                      <tr key={item.name}>
                        <td>{index + 1}</td>
                        <td>{item.name}</td>
                        <td>{item.price}</td>
                        <td>{item.qty}</td>
                        <td>{item.sum}</td>
                        <td>{getDescCode('taxmode', item.taxmode)}</td>
                        <td>{getDescCode('vat', item.vat)}</td>
                        <td>{getDescCode('payattr', item.payattr)}</td>
                        <td>{getDescCode('lineattr', item.lineattr)}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        ))}
      </Accordion>

      <AdvancedPagination values={filteredValues} setValues={setValuesRender} />
    </Layout>
  )
}

export default CustomOrderScreen
