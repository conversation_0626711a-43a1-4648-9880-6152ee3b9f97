import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState } from 'react'
import { Container } from 'react-bootstrap'

import PagePreloader from '@/components/PagePreloader/PagePreloader'

import { APIRoute } from '@/const'

import EventCard from '../EventCard/EventCard'
import EventsFilter from '../EventsFilter/EventsFilter'

/**
 * Custom hook to manage archived events data
 */
const useArchiveEventsData = () => {
  const [{ loading: eventsLoading, error }, apiEvents] = useAxios(
    {
      url: APIRoute.EVENT_ARCHIVE,
      method: 'GET',
    },
    { manual: true }
  )

  const [events, setEvents] = useState([])
  const [filteredEvents, setFilteredEvents] = useState([])

  const getEvents = useCallback(() => {
    apiEvents()
      .then((r) => {
        if (r?.data?.values && r.data.values.length > 0) {
          setEvents(r.data.values)
          setFilteredEvents(r.data.values)
        } else {
          // Handle empty response
          setEvents([])
          setFilteredEvents([])
        }
      })
      .catch((error) => {
        console.error('Error fetching events archive:', error)
        setEvents([])
        setFilteredEvents([])
      })
  }, [apiEvents])

  useEffect(() => {
    getEvents()
  }, [getEvents])

  const handleFilterEvents = useCallback((filtered) => {
    setFilteredEvents(filtered || [])
  }, [])

  return {
    events,
    filteredEvents,
    isLoading: eventsLoading,
    error,
    handleFilterEvents,
    refetchEvents: getEvents,
  }
}

/**
 * Empty state component for archived events
 */
const EmptyArchiveState = () => (
  <div className="text-center p-5 mb-4">
    <h4>Не найдено архивных событий по указанным критериям</h4>
    <p className="text-muted">Попробуйте изменить параметры фильтрации</p>
  </div>
)

/**
 * Archived events list component
 */
const ArchiveEventsList = ({ events }) => (
  <div className="mb-5 pt-4">
    {events.map((event) => (
      <EventCard key={event.public_id} event={event} isArchive={true} />
    ))}
  </div>
)

/**
 * Events Archive Tab component
 * Displays archived events with filtering capabilities
 */
function EventsArchive() {
  const { events, filteredEvents, isLoading, error, handleFilterEvents } = useArchiveEventsData()

  if (error) {
    return (
      <Container fluid>
        <div className="alert alert-danger">Ошибка при загрузке архива событий: {error.message}</div>
      </Container>
    )
  }

  return (
    <PagePreloader isLoading={isLoading}>
      <Container fluid>
        <EventsFilter events={events} onFilteredEvents={handleFilterEvents} />

        {filteredEvents.length === 0 && events.length > 0 && <EmptyArchiveState />}

        {filteredEvents.length > 0 && <ArchiveEventsList events={filteredEvents} />}
      </Container>
    </PagePreloader>
  )
}

export default EventsArchive
