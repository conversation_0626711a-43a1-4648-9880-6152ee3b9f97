import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { Button, Col, Row } from 'react-bootstrap'

import ConfirmDeleteModal from '../../../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import { APIRoute } from '../../../../const'
import { useToast } from '../../../../hooks/useToast'
import EventTypeCard from '../EventTypeCard/EventTypeCard'
import EventTypeForm from '../EventTypeForm/EventTypeForm'

function EventsTypeTab() {
  const [editEvent, setEditEvent] = useState({})
  const [isActiveForm, setIsActiveForm] = useState(false)
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState('')

  const openToast = useToast()

  const [{ data }, api] = useAxios(
    {
      url: APIRoute.ACTIONS_EVENT_TYPE,
      method: 'GET',
    },
    { manual: true }
  )
  const [, apiDelete] = useAxios(
    {
      url: APIRoute.ACTIONS_EVENT_TYPE,
      method: 'DELETE',
    },
    { manual: true }
  )

  useEffect(() => {
    api()
  }, [api])

  const handleCloseForm = () => {
    setIsActiveForm(false)
    setEditEvent({})
    api()
  }

  const handleEditEvent = (event) => {
    setEditEvent(event)
    setIsActiveForm(true)
  }

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedDeleteItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ data: { public_id: selectedDeleteItem.public_id } }).then((r) => {
      if (r.status === 200 && r.data.message) {
        openToast.success({ message: 'Тип события был удалён' })
        setSelectedDeleteItem('')
        api()
      }
    })
  }

  return isActiveForm ? (
    <Row className="pt-4">
      <Col>
        <EventTypeForm editEvent={editEvent} onCloseForm={handleCloseForm} />
      </Col>
    </Row>
  ) : (
    <>
      <Row className="mb-4 pt-4">
        <Col />
        <Col md="auto">
          <Button onClick={() => setIsActiveForm(true)}>Добавить</Button>
        </Col>
      </Row>
      <div className="mb-5">
        {data?.values.map((event) => (
          <EventTypeCard event={event} onEdit={handleEditEvent} onDelete={handleConfirmDelete} key={event.public_id} />
        ))}
      </div>

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItem}
        onDeleteItem={handleDeleteItem}
        text="Удалить тип события?"
      />
    </>
  )
}

export default EventsTypeTab
