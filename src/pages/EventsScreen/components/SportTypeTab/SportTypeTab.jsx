import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON> } from 'react-bootstrap'

import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { useDeleteSportType, useGetSportTypeList } from '@/features/sport/api'

import SportTypeForm from '../SportTypeForm/SportTypeForm'

function SportTypeTab() {
  const [editSportType, setEditSportType] = useState(null)
  const [isActiveForm, setIsActiveForm] = useState(false)
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState('')

  const { data, isLoading } = useGetSportTypeList()
  const deleteSportType = useDeleteSportType()

  const handleCloseForm = () => {
    setIsActiveForm(false)
    setEditSportType(null)
  }

  const handleEditSportType = (sportType) => {
    setEditSportType(sportType)
    setIsActiveForm(true)
  }

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedDeleteItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    if (selectedDeleteItem?.public_id) {
      deleteSportType.mutate(selectedDeleteItem.public_id, {
        onSuccess: () => {
          setSelectedDeleteItem('')
        },
      })
    }
  }

  const handleOpenForm = () => setIsActiveForm(true)

  const tableData = {
    title: '',
    list: [
      { label: 'Иконка', value: 'image', param: 'image' },
      { label: 'Название', value: 'name', param: 'text' },
      { label: 'Дата создания', value: 'created_date', param: 'dateTime' },
      { label: 'ID', value: 'public_id', param: 'text' },
    ],
  }

  const tableActions = (item) => (
    <td>
      <Button variant="link" onClick={() => handleEditSportType(item)}>
        Редактировать
      </Button>
      <Button variant="link" className="text-danger" onClick={() => handleConfirmDelete(item)}>
        Удалить
      </Button>
    </td>
  )

  if (isActiveForm) {
    return (
      <Row className="pt-4">
        <Col>
          <SportTypeForm sportType={editSportType} onCloseForm={handleCloseForm} />
        </Col>
      </Row>
    )
  }

  return (
    <>
      <Row className="mb-4 pt-4">
        <Col />
        <Col md="auto">
          <Button onClick={handleOpenForm}>Добавить</Button>
        </Col>
      </Row>

      {isLoading ? (
        <div>Загрузка...</div>
      ) : (
        <TableTemplate data={tableData} values={data?.data?.values || []} actions={tableActions} />
      )}

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItem}
        onDeleteItem={handleDeleteItem}
        text="Удалить вид спорта?"
      />
    </>
  )
}

export default SportTypeTab
