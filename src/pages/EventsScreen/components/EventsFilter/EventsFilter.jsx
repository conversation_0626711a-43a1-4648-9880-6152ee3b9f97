import useAxios from 'axios-hooks'
import { useEffect } from 'react'
import Select from 'react-select'

import { eventFilterSelectStyles } from './shopSelectStyles'
import { APIRoute } from '../../../../const'
import { useTheme } from '../../../../contexts/ThemeContext'
import { getOptionsSelect } from '../../../../utils/common'

function EventsFilter({ events, onFilteredEvents }) {
  const { effectiveTheme } = useTheme()
  const [{ data }, api] = useAxios(
    {
      url: APIRoute.ACTIONS_EVENT_TYPE,
      method: 'GET',
    },
    { manual: false }
  )

  useEffect(() => {
    api()
  }, [api])

  const handleChangeSelect = (items) => {
    if (items.length > 0) {
      const filteredItems = items.map((item) => item.value)
      const filteredEvents = events.filter((event) => filteredItems.includes(event.event_type.public_id))
      onFilteredEvents(filteredEvents)
    } else {
      onFilteredEvents(events)
    }
  }

  const dynamicSelectStyles = {
    ...eventFilterSelectStyles,
    control: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        border: 'none',
        minHeight: '100%',
        backgroundColor: darkTheme ? '#2b3035' : '#ffffff',
        boxShadow: darkTheme
          ? '0 1px 0 0 rgb(73, 80, 87), 0 0 0 1px rgb(108, 117, 125)'
          : '0 1px 0 0 rgb(221, 227, 238), 0 0 0 1px rgb(232, 234, 244)',
        '&:hover': {
          boxShadow: darkTheme
            ? '0 1px 0 0 rgb(108, 117, 125), 0 0 0 1px rgb(134, 142, 150)'
            : '0 1px 0 0 rgb(200, 210, 220), 0 0 0 1px rgb(210, 220, 230)',
        },
      }
    },
    menu: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#2b3035' : '#ffffff',
        border: darkTheme ? '1px solid #495057' : '1px solid #dee2e6',
      }
    },
    option: (provided, state) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: state.isSelected
          ? darkTheme
            ? '#0d6efd'
            : '#0d6efd'
          : state.isFocused
            ? darkTheme
              ? '#495057'
              : '#f8f9fa'
            : darkTheme
              ? '#2b3035'
              : '#ffffff',
        color: state.isSelected ? '#ffffff' : darkTheme ? '#ffffff' : '#212529',
        '&:hover': {
          backgroundColor: state.isSelected ? (darkTheme ? '#0d6efd' : '#0d6efd') : darkTheme ? '#495057' : '#f8f9fa',
        },
      }
    },
    multiValue: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#495057' : '#e9ecef',
      }
    },
    multiValueLabel: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#495057',
      }
    },
    multiValueRemove: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#495057',
        '&:hover': {
          backgroundColor: darkTheme ? '#dc3545' : '#dc3545',
          color: '#ffffff',
        },
      }
    },
    placeholder: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#adb5bd' : '#6c757d',
      }
    },
    singleValue: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#212529',
      }
    },
    input: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#ffffff' : '#212529',
      }
    },
    indicatorSeparator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        backgroundColor: darkTheme ? '#6c757d' : '#dee2e6',
      }
    },
    dropdownIndicator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#adb5bd' : '#6c757d',
        '&:hover': {
          color: darkTheme ? '#ffffff' : '#495057',
        },
      }
    },
    clearIndicator: (provided) => {
      const darkTheme = effectiveTheme === 'dark'
      return {
        ...provided,
        color: darkTheme ? '#adb5bd' : '#6c757d',
        '&:hover': {
          color: darkTheme ? '#ffffff' : '#495057',
        },
      }
    },
  }

  return (
    <div className="mb-2">
      Фильтр по типу:
      <Select
        isMulti
        name="colors"
        onChange={handleChangeSelect}
        options={getOptionsSelect(data?.values, 'public_id', 'title')}
        styles={dynamicSelectStyles}
        className="basic-multi-select"
        classNamePrefix="select"
        placeholder="Выберите тип события"
      />
    </div>
  )
}

export default EventsFilter
