import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, Badge, ListGroup, Modal, Form } from 'react-bootstrap'

const EventSelectionModal = ({ show, onHide, events, title, onSelectEvents, selectedEventIds }) => {
  const [tempSelectedIds, setTempSelectedIds] = useState([])

  const handleCheckboxChange = (eventId, checked) => {
    if (checked) {
      setTempSelectedIds((prev) => [...prev, eventId])
    } else {
      setTempSelectedIds((prev) => prev.filter((id) => id !== eventId))
    }
  }

  const handleSelectAll = () => {
    const availableIds = events
      .filter((event) => !selectedEventIds.includes(event.public_id))
      .map((event) => event.public_id)
    setTempSelectedIds(availableIds)
  }

  const handleClearAll = () => {
    setTempSelectedIds([])
  }

  const handleConfirm = () => {
    const selectedEvents = events.filter((event) => tempSelectedIds.includes(event.public_id))
    onSelectEvents(selectedEvents)
    setTempSelectedIds([])
    onHide()
  }

  const handleClose = () => {
    setTempSelectedIds([])
    onHide()
  }

  return (
    <Modal show={show} onHide={handleClose} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {events.length === 0 ? (
          <p className="text-muted">Нет доступных событий</p>
        ) : (
          <>
            <div className="mb-3">
              <Button variant="outline-primary" size="sm" onClick={handleSelectAll} className="me-2">
                Выбрать все
              </Button>
              <Button variant="outline-secondary" size="sm" onClick={handleClearAll}>
                Очистить
              </Button>
            </div>
            <ListGroup>
              {events.map((event) => {
                const isAlreadySelected = selectedEventIds.includes(event.public_id)
                const isTempSelected = tempSelectedIds.includes(event.public_id)

                return (
                  <ListGroup.Item
                    key={event.public_id}
                    className="d-flex align-items-center"
                    action={!isAlreadySelected}
                    onClick={() => !isAlreadySelected && handleCheckboxChange(event.public_id, !isTempSelected)}
                    style={{ cursor: isAlreadySelected ? 'default' : 'pointer' }}
                  >
                    <Form.Check
                      type="checkbox"
                      id={`event-${event.public_id}`}
                      checked={isTempSelected}
                      disabled={isAlreadySelected}
                      onChange={() => {}} // Обработка клика происходит на уровне ListGroup.Item
                      className="me-3"
                      style={{ pointerEvents: 'none' }} // Отключаем прямые клики по чекбоксу
                    />
                    <div className="flex-grow-1">
                      <strong className={isAlreadySelected ? 'text-muted' : ''}>{event.title}</strong>
                      <br />
                      <small className="text-muted">ID: {event.public_id}</small>
                    </div>
                    {isAlreadySelected && <Badge bg="success">Выбрано</Badge>}
                  </ListGroup.Item>
                )
              })}
            </ListGroup>
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Отмена
        </Button>
        <Button variant="primary" onClick={handleConfirm} disabled={tempSelectedIds.length === 0}>
          Добавить выбранные ({tempSelectedIds.length})
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

const EventSelector = ({ activeEvents, archiveEvents, selectedEvents, onAddEvent, onRemoveEvent }) => {
  const [showActiveModal, setShowActiveModal] = useState(false)
  const [showArchiveModal, setShowArchiveModal] = useState(false)

  const selectedEventIds = selectedEvents.map((event) => event.public_id)

  const handleSelectActiveEvents = (events) => {
    events.forEach((event) => onAddEvent(event, false))
    setShowActiveModal(false)
  }

  const handleSelectArchiveEvents = (events) => {
    events.forEach((event) => onAddEvent(event, true))
    setShowArchiveModal(false)
  }

  return (
    <div>
      <Row className="mb-3">
        <Col md={6}>
          <Button variant="outline-primary" onClick={() => setShowActiveModal(true)} className="w-100">
            Добавить активные события
          </Button>
        </Col>
        <Col md={6}>
          <Button variant="outline-secondary" onClick={() => setShowArchiveModal(true)} className="w-100">
            Добавить архивные события
          </Button>
        </Col>
      </Row>

      {selectedEvents.length > 0 && (
        <div>
          <h6>Выбранные события ({selectedEvents.length}):</h6>
          <ListGroup className="mb-3">
            {selectedEvents.map((event) => (
              <ListGroup.Item key={event.public_id} className="d-flex justify-content-between align-items-center">
                <div>
                  <strong>{event.title}</strong>
                  <span className="text-muted"> ({event.public_id})</span>
                  {event.isArchive && (
                    <Badge bg="secondary" className="ms-2">
                      Архив
                    </Badge>
                  )}
                </div>
                <Button variant="outline-danger" size="sm" onClick={() => onRemoveEvent(event.public_id)}>
                  <i className="bi bi-trash3-fill" />
                  <span className="visually-hidden">Удалить</span>
                </Button>
              </ListGroup.Item>
            ))}
          </ListGroup>
        </div>
      )}

      {selectedEvents.length === 0 && (
        <div className="text-center text-muted p-3 border rounded">
          <p className="mb-0">Выберите события для получения статистики</p>
        </div>
      )}

      <EventSelectionModal
        show={showActiveModal}
        onHide={() => setShowActiveModal(false)}
        events={activeEvents}
        title="Выбрать актуальные события"
        onSelectEvents={handleSelectActiveEvents}
        selectedEventIds={selectedEventIds}
      />

      <EventSelectionModal
        show={showArchiveModal}
        onHide={() => setShowArchiveModal(false)}
        events={archiveEvents}
        title="Выбрать архивные события"
        onSelectEvents={handleSelectArchiveEvents}
        selectedEventIds={selectedEventIds}
      />
    </div>
  )
}

export default EventSelector
