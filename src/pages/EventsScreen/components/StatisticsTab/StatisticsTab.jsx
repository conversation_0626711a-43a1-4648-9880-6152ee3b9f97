import { useState, useCallback } from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Container, <PERSON><PERSON>, <PERSON>, ButtonGroup, ToggleButton } from 'react-bootstrap'

import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import Loader from '@/components/Loader/Loader'

import { APIRoute } from '@/const'
import { useGetEventArchive } from '@/features/events/api/getEventArchive'
import { useGetEvents } from '@/features/events/api/getEvents'
import { useGetEventTicketStatistics } from '@/features/events/api/getEventTicketStatistics'
import { useGetEventTicketStatisticsByCity } from '@/features/events/api/getEventTicketStatisticsByCity'
import { useGetEventTicketStatisticsByFormat } from '@/features/events/api/getEventTicketStatisticsByFormat'
import { useToast } from '@/hooks/useToast'

import EventSelector from './components/EventSelector/EventSelector'
import StatisticsTable from './components/StatisticsTable/StatisticsTable'

const STATISTICS_TYPES = {
  GENERAL: 'general',
  BY_CITY: 'by_city',
  BY_FORMAT: 'by_format',
}

const STATISTICS_TYPE_LABELS = {
  [STATISTICS_TYPES.GENERAL]: 'Общий',
  [STATISTICS_TYPES.BY_CITY]: 'По городам',
  [STATISTICS_TYPES.BY_FORMAT]: 'По форматам',
}

const useStatisticsData = () => {
  const [selectedEvents, setSelectedEvents] = useState([])
  const [statisticsData, setStatisticsData] = useState(null)
  const [statisticsType, setStatisticsType] = useState(STATISTICS_TYPES.GENERAL)

  const openToast = useToast()
  const { data: activeEventsData, isLoading: activeEventsLoading } = useGetEvents()
  const { data: archiveEventsData, isLoading: archiveEventsLoading } = useGetEventArchive({})

  const { mutate: getStatistics, isLoading: statisticsLoading, error: statisticsError } = useGetEventTicketStatistics()
  const {
    mutate: getStatisticsByCity,
    isLoading: statisticsByCityLoading,
    error: statisticsByCityError,
  } = useGetEventTicketStatisticsByCity()
  const {
    mutate: getStatisticsByFormat,
    isLoading: statisticsByFormatLoading,
    error: statisticsByFormatError,
  } = useGetEventTicketStatisticsByFormat()

  const activeEvents = activeEventsData?.data?.values || []
  const archiveEvents = archiveEventsData?.data?.values || []

  const handleAddEvent = useCallback((event, isArchive = false) => {
    const eventWithType = { ...event, isArchive }
    setSelectedEvents((prev) => {
      // Проверяем, не добавлен ли уже этот event
      const exists = prev.some((e) => e.public_id === event.public_id)
      if (exists) return prev
      return [...prev, eventWithType]
    })
  }, [])

  const handleRemoveEvent = useCallback((publicId) => {
    setSelectedEvents((prev) => prev.filter((e) => e.public_id !== publicId))
  }, [])

  const handleGetStatistics = useCallback(() => {
    if (selectedEvents.length === 0) return

    const eventPublicIdList = selectedEvents.map((event) => event.public_id)
    const requestData = { event_public_id_list: eventPublicIdList }
    const successCallback = {
      onSuccess: (response) => {
        if (response?.data?.values) {
          setStatisticsData(response.data.values)
          openToast.success({ message: 'Статистика билетов успешно загружена' })
        }
      },
    }

    switch (statisticsType) {
      case STATISTICS_TYPES.BY_CITY:
        getStatisticsByCity(requestData, successCallback)
        break
      case STATISTICS_TYPES.BY_FORMAT:
        getStatisticsByFormat(requestData, successCallback)
        break
      case STATISTICS_TYPES.GENERAL:
      default:
        getStatistics(requestData, successCallback)
        break
    }
  }, [selectedEvents, statisticsType, getStatistics, getStatisticsByCity, getStatisticsByFormat, openToast])

  const handleClearSelection = useCallback(() => {
    setSelectedEvents([])
    setStatisticsData(null)
  }, [])

  const handleChangeStatisticsType = useCallback((type) => {
    setStatisticsType(type)
    setStatisticsData(null) // Очищаем данные при смене типа
  }, [])

  // Объединяем состояния загрузки и ошибок для всех типов статистики
  const isStatisticsLoading = statisticsLoading || statisticsByCityLoading || statisticsByFormatLoading
  const statisticsErrorCombined = statisticsError || statisticsByCityError || statisticsByFormatError

  return {
    activeEvents,
    archiveEvents,
    selectedEvents,
    statisticsData,
    statisticsType,
    activeEventsLoading,
    archiveEventsLoading,
    statisticsLoading: isStatisticsLoading,
    statisticsError: statisticsErrorCombined,
    handleAddEvent,
    handleRemoveEvent,
    handleGetStatistics,
    handleClearSelection,
    handleChangeStatisticsType,
  }
}

const StatisticsTab = () => {
  const {
    activeEvents,
    archiveEvents,
    selectedEvents,
    statisticsData,
    statisticsType,
    activeEventsLoading,
    archiveEventsLoading,
    statisticsLoading,
    statisticsError,
    handleAddEvent,
    handleRemoveEvent,
    handleGetStatistics,
    handleClearSelection,
    handleChangeStatisticsType,
  } = useStatisticsData()

  const isLoading = activeEventsLoading || archiveEventsLoading

  // Функция для получения правильного URL для скачивания в зависимости от типа статистики
  const getDownloadUrl = () => {
    switch (statisticsType) {
      case STATISTICS_TYPES.BY_CITY:
        return APIRoute.EVENT_TICKET_STATISTICS_CITY_XLSX
      case STATISTICS_TYPES.BY_FORMAT:
        return APIRoute.EVENT_TICKET_STATISTICS_FORMAT_XLSX
      case STATISTICS_TYPES.GENERAL:
      default:
        return APIRoute.EVENT_TICKET_STATISTICS_XLSX
    }
  }

  if (isLoading) {
    return <Loader isLoading={true} text="Загрузка событий..." />
  }

  return (
    <Container fluid>
      <Row>
        <Col>
          <div className="mt-3 mb-4">
            <ButtonGroup>
              {Object.entries(STATISTICS_TYPE_LABELS).map(([type, label]) => (
                <ToggleButton
                  key={type}
                  id={`statistics-type-${type}`}
                  type="radio"
                  variant={statisticsType === type ? 'primary' : 'outline-primary'}
                  name="statistics-type"
                  value={type}
                  checked={statisticsType === type}
                  onChange={() => handleChangeStatisticsType(type)}
                >
                  {label}
                </ToggleButton>
              ))}
            </ButtonGroup>
          </div>

          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Выбор событий</h5>
            </Card.Header>
            <Card.Body>
              <EventSelector
                activeEvents={activeEvents}
                archiveEvents={archiveEvents}
                selectedEvents={selectedEvents}
                onAddEvent={handleAddEvent}
                onRemoveEvent={handleRemoveEvent}
              />

              <div className="mt-3">
                <Button
                  variant="primary"
                  onClick={handleGetStatistics}
                  disabled={selectedEvents.length === 0 || statisticsLoading}
                  className="me-2"
                >
                  {statisticsLoading ? 'Загрузка...' : 'Получить статистику'}
                </Button>

                <DownloadButton
                  url={getDownloadUrl()}
                  fileName="event_ticket_statistics.xlsx"
                  label="Скачать"
                  variant="success"
                  data={{ event_public_id_list: selectedEvents.map((event) => event.public_id) }}
                  disabled={selectedEvents.length === 0}
                  className="me-2"
                />

                <Button
                  variant="outline-secondary"
                  onClick={handleClearSelection}
                  disabled={selectedEvents.length === 0}
                >
                  Очистить выбор
                </Button>
              </div>
            </Card.Body>
          </Card>

          {statisticsError && (
            <Alert variant="danger" className="mb-4">
              <Alert.Heading>Ошибка при получении статистики</Alert.Heading>
              <p>{statisticsError?.response?.data?.message || 'Произошла ошибка при загрузке данных'}</p>
            </Alert>
          )}

          {statisticsData && (
            <Card>
              <Card.Header>
                <h5 className="mb-0">Результаты статистики</h5>
              </Card.Header>
              <Card.Body>
                <StatisticsTable data={statisticsData} />
              </Card.Body>
            </Card>
          )}

          {!statisticsData && !statisticsLoading && selectedEvents.length > 0 && (
            <Alert variant="info">
              Нажмите &quot;Получить статистику&quot; для загрузки данных по выбранным событиям.
            </Alert>
          )}
        </Col>
      </Row>
    </Container>
  )
}

export default StatisticsTab
