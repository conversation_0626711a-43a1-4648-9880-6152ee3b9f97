import useAxios from 'axios-hooks'
import { useState } from 'react'
import { Button, Col, Figure, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'

import EventTypeFormImage from '@/components/EventTypeFormImage/EventTypeFormImage'

import { APIRoute, MAX_IMG_SIZE } from '@/const'
import { useToast } from '@/hooks/useToast'
import { convertBase64 } from '@/utils/common'
import { updateFormData } from '@/utils/forms'
import { getImageSrc } from '@/utils/images'

import styles from '../../../BannersFormScreen/BannersFormScreen.module.scss'

function EventTypeForm({ editEvent, onCloseForm }) {
  const [formData, setFormData] = useState({})

  const openToast = useToast()

  const isUpdate = Object.keys(editEvent)?.length > 0

  const [, api] = useAxios(
    {
      url: APIRoute.ACTIONS_EVENT_TYPE,
      method: 'POST',
    },
    { manual: true }
  )

  const handleSubmitForm = () => {
    let config = {}

    if (isUpdate) {
      config = {
        url: `${APIRoute.ACTIONS_EVENT_TYPE}/${editEvent.public_id}`,
        method: 'PUT',
        data: formData,
      }
    } else {
      config = { data: formData }
    }

    api({ ...config }).then((r) => {
      if (r.status === 200) {
        openToast.success({ message: 'Событие сохранено' })
        onCloseForm()
      }
    })
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, editEvent)
  }

  const handleFileRead = async (evt) => {
    const name = evt.target.name
    const file = evt.target.files[0]
    const fileSizeInKB = file.size
    const base64 = await convertBase64(file)

    const newFormData = { ...formData }

    if (base64 === '') {
      delete newFormData[name]
      setFormData({ ...newFormData })
    } else if (fileSizeInKB <= MAX_IMG_SIZE) {
      setFormData({ ...formData, [name]: base64 })
    } else if (fileSizeInKB > MAX_IMG_SIZE) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
    }
  }

  const handleRemovePoster = () => {
    const newFormData = { ...formData }
    delete newFormData.poster
    setFormData({ ...newFormData })
  }

  return (
    <>
      <Row className="g-3 mb-3 flex-column">
        <Col md={{ offset: 3, span: 6 }}>
          <FloatingLabel controlId="titleLabel" label="Заголовок">
            <FormControl
              onChange={handleChangeField}
              defaultValue={editEvent?.title}
              name="title"
              type="text"
              placeholder="Заголовок"
            />
          </FloatingLabel>
        </Col>
        <Col md={{ offset: 3, span: 6 }}>
          <FloatingLabel controlId="descriptionLabel" label="Описание">
            <FormControl
              onChange={handleChangeField}
              defaultValue={editEvent?.description}
              as="textarea"
              name="description"
              style={{ height: '100px' }}
              placeholder="Описание"
            />
          </FloatingLabel>
        </Col>
        <Col md={{ offset: 3, span: 6 }}>
          <FloatingLabel controlId="videoPromoLabel" label="Ссылка на видеоролик">
            <FormControl
              onChange={handleChangeField}
              defaultValue={editEvent?.video_promo}
              name="video_promo"
              type="text"
              placeholder="Ссылка на видеоролик"
            />
          </FloatingLabel>
        </Col>
        <Col md={{ offset: 3, span: 3 }}>
          <FloatingLabel controlId="publicIdLabel" label="public_id, например trail">
            <FormControl
              onChange={handleChangeField}
              defaultValue={editEvent?.public_id}
              name="public_id"
              type="text"
              disabled={isUpdate}
              placeholder="public_id, например trail"
            />
          </FloatingLabel>
        </Col>
        <Col md={{ offset: 3, span: 4 }}>
          <Row>
            <Col>
              <Form.Group controlId="logoFile">
                <Form.Label className={styles.fileButton}>Загрузить логотип</Form.Label>
                <Form.Control
                  className="visually-hidden"
                  type="file"
                  onChange={handleFileRead}
                  name="logo"
                  accept=".svg, .png, .webp,"
                />
              </Form.Group>
            </Col>
            <Col className="align-self-center" md="auto">
              <em>Только SVG, WEBP или PNG</em>
            </Col>
          </Row>
        </Col>
        <Col className="mb-4" md={{ offset: 3, span: 3 }}>
          {(formData?.logo || editEvent?.logo) && (
            <Figure className={`${styles.previewLogo} mb-0`}>
              <Figure.Image
                className="mb-0"
                width={140}
                alt="Логотип"
                src={getImageSrc(formData?.logo || editEvent?.logo)}
              />
            </Figure>
          )}
        </Col>

        <Col md={{ offset: 3, span: 4 }}>
          <Row>
            <Col>
              <Form.Group controlId="posterFile">
                <Form.Label className={styles.fileButton}>Загрузить постер</Form.Label>
                <Form.Control
                  className="visually-hidden"
                  type="file"
                  onChange={handleFileRead}
                  name="poster"
                  accept="jpeg, jpg, png, webp, svg"
                />
              </Form.Group>
            </Col>
            <Col className="align-self-center" md="auto">
              <em>JPEG, JPG, PNG, WEBP или SVG</em>
            </Col>
          </Row>
        </Col>
        <Col className="mb-4" md={{ offset: 3, span: 3 }}>
          {(formData?.poster || editEvent?.poster) && (
            <>
              <Figure className={`${styles.previewLogo} mb-0`}>
                <Figure.Image
                  className="mb-0"
                  width={140}
                  alt="Постер"
                  src={getImageSrc(formData?.poster || editEvent?.poster)}
                />
              </Figure>
              {formData?.poster && (
                <Row>
                  <Col>
                    <Button onClick={handleRemovePoster} variant="link" size="sm">
                      удалить
                    </Button>
                  </Col>
                </Row>
              )}
            </>
          )}
        </Col>
      </Row>

      <EventTypeFormImage
        field="project"
        outsideFormData={formData}
        defaultFormData={editEvent.project}
        handleOutsideFormData={setFormData}
      />

      <Row>
        <Col md={{ offset: 3, span: 6 }}>
          <Row className="mb-5">
            <Col />
            <Col md="auto">
              <Button onClick={onCloseForm} variant="secondary">
                Отменить
              </Button>
            </Col>
            <Col md="auto">
              <Button variant="success" onClick={handleSubmitForm} disabled={Object.keys(formData).length === 0}>
                Сохранить
              </Button>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  )
}

export default EventTypeForm
