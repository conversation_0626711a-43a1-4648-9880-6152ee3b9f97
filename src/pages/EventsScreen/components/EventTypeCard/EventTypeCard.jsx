import { But<PERSON>, <PERSON>, <PERSON>, <PERSON> } from 'react-bootstrap'

import { getImageSrc } from '@/utils/images'

import styles from './EventTypeCard.module.scss'

function EventTypeCard({ event, onEdit, onDelete }) {
  const handleEdit = () => onEdit(event)
  const handleDelete = () => onDelete(event)

  return (
    <Card>
      <Card.Body>
        <Row>
          <Col md="auto">
            <div className={styles.logoWrap}>
              <img className={styles.logo} src={getImageSrc(event.logo)} alt={`${event.title} logo`} />
            </div>
          </Col>
          <Col>
            <h4>{event.title}</h4>
            {event.description}
          </Col>
          <Col className="d-grid align-content-start" md="auto">
            <Button className={styles.btn} onClick={handleEdit} variant="link">
              Редактировать
            </Button>
            <Button className={styles.btn} onClick={handleDelete} variant="link">
              Удалить
            </Button>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  )
}

export default EventTypeCard
