import moment from 'moment-timezone'
import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, But<PERSON>, Card, Col, Row, OverlayTrigger, Tooltip } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import { AppRoute } from '@/const'
import { useTheme } from '@/contexts/ThemeContext'
import { getFormatTime } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

import styles from './EventCard.module.scss'

// Ключ для хранения состояний в localStorage
const CITIES_EXPANDED_STATE_KEY = 'eventCardCitiesExpanded'

/**
 * Компонент карточки события
 * @param {Object} props
 * @param {Object} props.event - данные события
 * @param {boolean} props.isArchive - флаг архивного режима (без счетчика и статистики)
 */
const EventCard = ({ event, isArchive = false }) => {
  const { effectiveTheme, themes } = useTheme()
  const [isExpanded, setIsExpanded] = useState(false)
  const [isExpandedStateLoaded, setIsExpandedStateLoaded] = useState(false)

  // Загрузка состояния развернутости из localStorage при первом рендере
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(CITIES_EXPANDED_STATE_KEY)
      if (savedState) {
        const expandedStates = JSON.parse(savedState)
        setIsExpanded(expandedStates[event.public_id] === true)
      }
      setIsExpandedStateLoaded(true)
    } catch (error) {
      console.error('Ошибка при загрузке состояния городов:', error)
      setIsExpandedStateLoaded(true)
    }
  }, [event.public_id])

  // Сохранение состояния в localStorage при изменении состояния
  useEffect(() => {
    if (!isExpandedStateLoaded) return

    try {
      const savedState = localStorage.getItem(CITIES_EXPANDED_STATE_KEY)
      const expandedStates = savedState ? JSON.parse(savedState) : {}

      expandedStates[event.public_id] = isExpanded
      localStorage.setItem(CITIES_EXPANDED_STATE_KEY, JSON.stringify(expandedStates))
    } catch (error) {
      console.error('Ошибка при сохранении состояния городов:', error)
    }
  }, [isExpanded, isExpandedStateLoaded, event.public_id])

  // Функция для переключения состояния развернутости
  const toggleCitiesExpand = () => {
    setIsExpanded(!isExpanded)
  }

  // Функция для определения прошедших событий
  const isPastEvent = (dateTime, timezone) => {
    return moment.tz(dateTime, timezone).isBefore(moment())
  }

  // Функция для сортировки городов
  const getSortedCities = (cities) => {
    return [...cities].sort((a, b) => {
      const timeA = moment.tz(a.start_time, a.timezone)
      const timeB = moment.tz(b.start_time, b.timezone)
      const isPastA = isPastEvent(a.start_time, a.timezone)
      const isPastB = isPastEvent(b.start_time, b.timezone)

      // Если одно событие прошло, а другое предстоит - предстоящее всегда в начале
      if (isPastA && !isPastB) return 1
      if (!isPastA && isPastB) return -1

      // Если оба события либо прошли, либо предстоят - сортировка по времени
      return timeA - timeB
    })
  }

  // Получение статистики для городов
  const getCityStats = (cities) => {
    if (!cities || cities.length === 0) return { upcoming: 0, past: 0, total: 0 }

    const past = cities.filter((city) => isPastEvent(city.start_time, city.timezone)).length
    const total = cities.length
    const upcoming = total - past

    return { upcoming, past, total }
  }

  // Функция для расчета времени до ближайшего события с учетом таймзоны
  const getCountdownText = (cities) => {
    if (!cities || cities.length === 0) return { text: 'Нет запланированных событий', date: null }

    const sortedCities = getSortedCities(cities)
    const nextEvent = sortedCities.find((city) => !isPastEvent(city.start_time, city.timezone))

    if (!nextEvent) return { text: 'Все события завершены', date: null }

    const now = moment()
    const eventTime = moment.tz(nextEvent.start_time, nextEvent.timezone)
    const diff = eventTime.diff(now)

    const duration = moment.duration(diff)
    const days = Math.floor(duration.asDays())
    const hours = duration.hours()
    const minutes = duration.minutes()

    let text
    if (days > 0) {
      text = `До ближайшего события: ${days} д. ${hours} ч.`
    } else if (hours > 0) {
      text = `До ближайшего события: ${hours} ч. ${minutes} мин.`
    } else if (minutes > 0) {
      text = `До ближайшего события: ${minutes} мин.`
    } else {
      text = 'Событие начинается прямо сейчас!'
    }

    return {
      text,
      date: eventTime,
      address: nextEvent.address,
      timezone: nextEvent.timezone,
    }
  }

  const sortedCities = getSortedCities(event.event_city)
  const cityStats = !isArchive ? getCityStats(event.event_city) : null

  // Если состояние еще не загружено из localStorage, не рендерим содержимое
  if (!isExpandedStateLoaded) {
    return (
      <Card className="mb-3 shadow-sm border-0">
        <Card.Body>
          <div className="text-center">
            <div className="spinner-border spinner-border-sm text-primary" role="status">
              <span className="visually-hidden">Загрузка...</span>
            </div>
          </div>
        </Card.Body>
      </Card>
    )
  }

  return (
    <Card className="mb-3 shadow-sm border-0" key={event.public_id}>
      <Card.Body>
        <Row className={`mb-3 ${styles.cardHeader} align-items-sm-start`}>
          <Col xs={12} sm="auto" className="d-flex align-items-center mb-3 mb-sm-0">
            <div
              className={styles.logoWrap}
              style={{
                backgroundColor: effectiveTheme === themes.DARK ? '#2d3748' : '#160e4d',
              }}
            >
              <img className={styles.logo} src={getImageSrc(event.event_type.logo)} alt={event.event_type.title} />
            </div>
          </Col>
          <Col xs={12} sm={true} className="mb-3 mb-sm-0">
            <h5 className={`mb-2 d-flex align-items-center flex-wrap ${styles.titleWrap}`}>
              <Badge bg="light" text="dark" className="me-2 py-2 px-3 mb-1">
                {event.event_type.title}
              </Badge>
              <span className="fw-bold">{event.title}</span>
            </h5>

            <p className="mb-2 text-secondary small">
              ID события:{' '}
              <Link to={event.public_id} className="text-decoration-none">
                {event.public_id}
              </Link>
            </p>

            {!isArchive &&
              (() => {
                const countdownInfo = getCountdownText(event.event_city)
                return <div className={`${styles.countdownBadge} bg-body-tertiary`}>{countdownInfo.text}</div>
              })()}
          </Col>
          <Col xs={12} sm="auto" className="d-flex align-items-center">
            <Button to={`${AppRoute.EVENTS}/${event.public_id}`} as={Link} variant="light" className="w-100">
              <i className="bi bi-eye me-2"></i> Подробнее
            </Button>
          </Col>
        </Row>

        <Row>
          <Col>
            {event?.title_above && (
              <p className="mb-3">
                <span className="text-muted">Подзаголовок:</span> {event.title_above}
              </p>
            )}

            <div className="mb-3">
              {/* Статистика городов - только не для архива */}
              {!isArchive && (
                <p className="mb-2 d-flex align-items-center flex-wrap">
                  <span className="text-muted me-1">Города:</span>
                  <span className={styles.cityStats}>
                    <OverlayTrigger placement="top" overlay={<Tooltip>Предстоящие события</Tooltip>}>
                      <span className={styles.upcomingCount}>{cityStats.upcoming}</span>
                    </OverlayTrigger>

                    <OverlayTrigger placement="top" overlay={<Tooltip>Прошедшие события</Tooltip>}>
                      <span className={styles.pastCount}>{cityStats.past}</span>
                    </OverlayTrigger>

                    <OverlayTrigger placement="top" overlay={<Tooltip>Всего событий</Tooltip>}>
                      <span className={styles.totalCount}>{cityStats.total}</span>
                    </OverlayTrigger>
                  </span>
                </p>
              )}

              {!isArchive ? (
                // Отображение городов для обычного режима с возможностью сворачивания/разворачивания
                <div className={styles.citiesRow}>
                  {event.event_city.length > 0 && (
                    <div className={styles.horizontalCities}>
                      {(isExpanded ? sortedCities : sortedCities.slice(0, 3)).map((city, index) => {
                        const isPast = isPastEvent(city.start_time, city.timezone)
                        return (
                          <div key={index} className={`${styles.cityChip} ${isPast ? styles.pastCity : ''}`}>
                            <div className={styles.cityChipContent}>
                              <span className={styles.cityName}>{city.address}</span>
                              <span className={styles.cityDateTime}>
                                {getFormatTime(city.start_time, city.timezone)}
                              </span>
                            </div>
                          </div>
                        )
                      })}

                      {event.event_city.length > 3 && (
                        <Button variant="outline-secondary" onClick={toggleCitiesExpand} size="sm">
                          {isExpanded ? 'Свернуть' : `Все города (${event.event_city.length})`}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                // Упрощенное отображение городов для архива
                <div className={styles.archiveCities}>
                  <p className="text-muted mb-1">Города:</p>
                  <div className={styles.horizontalCities}>
                    {sortedCities.map((city, index) => (
                      <div key={index} className={styles.cityChip}>
                        <div className={styles.cityChipContent}>
                          <span className={styles.cityName}>{city.address}</span>
                          <span className={styles.cityDateTime}>{getFormatTime(city.start_time, city.timezone)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {event?.description && (
              <div className="mt-2">
                <p className="text-muted mb-1">Описание:</p>
                <p className={`${styles.description} bg-body-tertiary w-100`}>{event.description}</p>
              </div>
            )}
          </Col>
        </Row>
      </Card.Body>
    </Card>
  )
}

export default EventCard
