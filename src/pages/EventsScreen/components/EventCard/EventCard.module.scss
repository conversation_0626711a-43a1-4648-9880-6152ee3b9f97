.logoWrap {
  padding: 5px;
  display: grid;
  place-items: center;
  width: 100px;
  min-height: 105px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  @media (max-width: 576px) {
    width: 90px;
    min-height: 95px;
    margin: 0 auto;
  }
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.description {
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid var(--bs-border-color);
}

.cardHeader {
  @media (max-width: 576px) {
    text-align: center;
  }
}

.titleWrap {
  @media (max-width: 576px) {
    justify-content: center;
  }
}

/* Счетчик до ближайшего события */
.countdownBadge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  border-radius: 6px;

  @media (max-width: 576px) {
    width: 100%;
    justify-content: center;
    margin-top: 8px;
  }
}

/* Статистика городов */
.cityStats {
  font-size: 0.75rem;
  margin-left: 8px;
  display: flex;
  align-items: center;
  gap: 6px;

  @media (max-width: 576px) {
    margin-top: 4px;
  }
}

.upcomingCount {
  color: var(--bs-success);
  background-color: var(--bs-success-bg-subtle);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--bs-success-border-subtle);
}

.pastCount {
  color: var(--bs-secondary);
  background-color: var(--bs-secondary-bg-subtle);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--bs-secondary-border-subtle);
}

.totalCount {
  color: var(--bs-primary);
  background-color: var(--bs-primary-bg-subtle);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--bs-primary-border-subtle);
}

/* Стили для отображения городов */
.citiesRow {
  width: 100%;
}

.horizontalCities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;

  @media (max-width: 576px) {
    justify-content: center;
  }
}

.cityChip {
  position: relative;
  padding: 6px 12px;
  background-color: var(--bs-body-bg);
  border-radius: 4px;
  border: 1px solid var(--bs-border-color);

  @media (max-width: 420px) {
    width: 100%;
  }
}

/* Стиль для прошедших городов/мероприятий */
.pastCity {
  opacity: 0.85;
  border-color: var(--bs-border-color-translucent);

  .cityName,
  .cityDateTime {
    color: var(--bs-secondary-color);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bs-secondary-bg-subtle);
    opacity: 0.1;
    border-radius: 4px;
    pointer-events: none;
  }
}

.cityChipContent {
  display: flex;
  gap: 10px;
  align-items: center;

  @media (max-width: 420px) {
    justify-content: space-between;
  }
}

.cityName {
  font-size: 0.85rem;
  color: var(--bs-body-color);

  @media (max-width: 576px) {
    flex: 1;
  }
}

.cityDateTime {
  font-size: 0.75rem;
  color: var(--bs-secondary-color);
}

/* Архивные стили */
.archiveCities {
  width: 100%;
}
