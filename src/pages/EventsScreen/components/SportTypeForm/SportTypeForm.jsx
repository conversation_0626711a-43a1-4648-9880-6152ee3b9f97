import React from 'react'
import { <PERSON><PERSON>, Col, Form, Floating<PERSON><PERSON>l, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import ImageField from '@/components/Forms/ImageField/ImageField'

import { useCreateSportType, useUpdateSportType } from '@/features/sport/api'
import { removeEmptyString2 } from '@/utils/common'

function SportTypeForm({ sportType, onCloseForm }) {
  const isUpdate = !!sportType?.public_id

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
  } = useForm({
    defaultValues: {
      name: sportType?.name || '',
    },
    mode: 'onChange',
  })

  const createSportType = useCreateSportType()
  const updateSportType = useUpdateSportType()

  const onSubmit = (data) => {
    const filteredData = removeEmptyString2(data)

    if (isUpdate) {
      updateSportType.mutate(
        {
          id: sportType.public_id,
          data: filteredData,
        },
        {
          onSuccess: () => {
            onCloseForm()
          },
        }
      )
    } else {
      createSportType.mutate(filteredData, {
        onSuccess: () => {
          onCloseForm()
        },
      })
    }
  }

  const isSubmitting = createSportType.isLoading || updateSportType.isLoading

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Row className="g-3 mb-3">
        <Col md={{ offset: 4, span: 4 }}>
          <FloatingLabel controlId="nameLabel" label="Название">
            <Form.Control
              {...register('name', { required: 'Название обязательно' })}
              type="text"
              placeholder="Название"
              isInvalid={!!errors.name}
            />
            {errors.name && <Form.Control.Feedback type="invalid">{errors.name.message}</Form.Control.Feedback>}
          </FloatingLabel>
        </Col>

        <Col md={{ offset: 3, span: 6 }}>
          <ImageField
            imagePath={sportType?.image}
            setValue={setValue}
            fieldName="image"
            accept=".svg, .png"
            title="Иконка вида спорта"
          />
        </Col>
      </Row>

      <Row>
        <Col className="d-flex justify-content-center">
          <Row className="mb-5">
            <Col md="auto">
              <Button onClick={onCloseForm} variant="secondary">
                Отменить
              </Button>
            </Col>
            <Col md="auto">
              <Button variant="success" type="submit" disabled={!isValid || isSubmitting}>
                {isUpdate ? 'Обновить' : 'Сохранить'}
              </Button>
            </Col>
          </Row>
        </Col>
      </Row>
    </form>
  )
}

export default SportTypeForm
