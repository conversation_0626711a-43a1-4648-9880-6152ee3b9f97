import { useEffect, useState, useCallback } from 'react'
import { Button, Col, Row, Container } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import Loader from '@/components/Loader/Loader'

import { AppRoute } from '@/const'
import { useGetEvents } from '@/features/events/api/getEvents'

import EventCard from '../EventCard/EventCard'
import EventsFilter from '../EventsFilter/EventsFilter'

/**
 * Custom hook to manage events data and filtering
 */
const useEventsData = () => {
  const { data: eventsData, isLoading, error } = useGetEvents()
  const [events, setEvents] = useState([])
  const [filteredEvents, setFilteredEvents] = useState([])

  useEffect(() => {
    if (eventsData?.data?.values?.length > 0) {
      const eventsList = eventsData.data.values
      setEvents(eventsList)
      setFilteredEvents(eventsList)
    } else {
      setEvents([])
      setFilteredEvents([])
    }
  }, [eventsData])

  const handleFilterEvents = useCallback((filtered) => {
    setFilteredEvents(filtered || [])
  }, [])

  return {
    events,
    filteredEvents,
    isLoading,
    error,
    handleFilterEvents,
  }
}

/**
 * Empty state component for when no events are found
 */
const EmptyEventsState = () => (
  <div className="text-center p-5 mb-4">
    <h4>Не найдено событий по указанным критериям</h4>
    <p className="text-muted">Попробуйте изменить параметры фильтрации</p>
  </div>
)

/**
 * Events list component
 */
const EventsList = ({ events }) => (
  <div className="mb-5 pt-2">
    {events.map((event) => (
      <EventCard key={event.public_id} event={event} isArchive={false} />
    ))}
  </div>
)

/**
 * Header with filter and add button
 */
const EventsHeader = ({ events, onFilterEvents }) => (
  <Row className="align-items-end mb-4">
    <Col>
      <EventsFilter events={events} onFilteredEvents={onFilterEvents} />
    </Col>
    <Col md="auto">
      <Button className="mb-2 d-flex align-items-center" to={AppRoute.EVENTS_CREATE} variant="success" as={Link}>
        <i className="bi bi-plus-circle me-2" />
        Добавить
      </Button>
    </Col>
  </Row>
)

/**
 * Main Events Tab component
 * Displays active events with filtering capabilities
 */
function EventsTab() {
  const { events, filteredEvents, isLoading, error, handleFilterEvents } = useEventsData()

  if (error) {
    return (
      <Container fluid>
        <div className="alert alert-danger">Ошибка при загрузке событий: {error.message}</div>
      </Container>
    )
  }

  return (
    <Loader isLoading={isLoading} text="Загрузка событий">
      <Container fluid>
        <EventsHeader events={events} onFilterEvents={handleFilterEvents} />

        {filteredEvents.length === 0 && events.length > 0 && <EmptyEventsState />}

        {filteredEvents.length > 0 && <EventsList events={filteredEvents} />}
      </Container>
    </Loader>
  )
}

export default EventsTab
