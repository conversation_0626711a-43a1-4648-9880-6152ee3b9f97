.logoWrap {
  padding: 5px;
  display: grid;
  place-items: center;
  width: 100px;
  min-height: 105px;
  background-color: rgb(22, 14, 77);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.description {
  max-height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #dee2e6;
}

/* Счетчик до ближайшего события */
.countdownBadge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  background-color: #f8f9fa;
  border-radius: 6px;
}

/* Статистика городов */
.cityStats {
  font-size: 0.75rem;
  margin-left: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.upcomingCount {
  color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(25, 135, 84, 0.2);
}

.pastCount {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.totalCount {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(13, 110, 253, 0.2);
}

/* Новые стили для элегантного отображения городов */
.citiesRow {
  width: 100%;
}

.horizontalCities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
}

.expandedCities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.cityChip {
  position: relative;
  padding: 6px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

/* Стиль для прошедших городов/мероприятий */
.pastCity {
  opacity: 0.85;
  border-color: #f0f0f0;

  .cityName,
  .cityDateTime {
    color: #888;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(240, 240, 240, 0.1);
    border-radius: 4px;
    pointer-events: none;
  }
}

.cityChipContent {
  display: flex;
  gap: 10px;
  align-items: center;
}

.cityName {
  font-size: 0.85rem;
  color: #212529;
}

.cityDateTime {
  font-size: 0.75rem;
  color: #6c757d;
}
