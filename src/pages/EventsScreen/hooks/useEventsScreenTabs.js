import { useCallback, useState } from 'react'

import { DEFAULT_TAB } from '../eventsScreenData'

/**
 * Custom hook to manage Events Screen tab state and navigation
 * @returns {Object} Tab state and handlers
 */
export const useEventsScreenTabs = () => {
  const [activeTab, setActiveTab] = useState(DEFAULT_TAB)

  const handleSelectTab = useCallback((tab) => {
    setActiveTab(tab)
  }, [])

  const isTabActive = useCallback(
    (tabKey) => {
      return activeTab === tabKey
    },
    [activeTab]
  )

  return {
    activeTab,
    handleSelectTab,
    isTabActive,
  }
}
