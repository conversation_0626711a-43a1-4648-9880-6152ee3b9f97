export const eventCityData = {
  title: 'Города',
  list: [
    { label: 'Адрес', value: 'address' },
    { label: 'Id города', value: 'public_id' },
    { label: 'Начало продаж', value: 'registration_open', param: 'dateTs' },
    { label: 'Закрытие продаж', value: 'registration_close', param: 'dateTs' },
    { label: 'Старт', value: 'start_time', param: 'dateTs' },
  ],
}

export const EVENTS_TABS = {
  EVENTS: 'events',
  EVENTS_ARCHIVE: 'eventsArchive',
  EVENTS_TYPE: 'eventsType',
  SPORT_TYPE: 'sportType',
  STATISTICS: 'statistics',
}

export const EVENTS_TAB_CONFIG = [
  {
    key: EVENTS_TABS.EVENTS,
    title: 'События',
    component: 'EventsTab',
  },
  {
    key: EVENTS_TABS.EVENTS_ARCHIVE,
    title: 'Архив событий',
    component: 'EventsArchive',
  },
  {
    key: EVENTS_TABS.EVENTS_TYPE,
    title: 'Типы событий',
    component: 'EventsTypeTab',
  },
  {
    key: EVENTS_TABS.SPORT_TYPE,
    title: 'Виды спорта',
    component: 'SportTypeTab',
  },
  {
    key: EVENTS_TABS.STATISTICS,
    title: 'Статистика',
    component: 'StatisticsTab',
  },
]

export const DEFAULT_TAB = EVENTS_TABS.EVENTS
