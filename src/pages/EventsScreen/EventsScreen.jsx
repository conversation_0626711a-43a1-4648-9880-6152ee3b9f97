import { useMemo } from 'react'
import { Tab, Tabs } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'

import TabContent from './components/TabContent/TabContent'
import { EVENTS_TAB_CONFIG, DEFAULT_TAB } from './eventsScreenData'
import { useEventsScreenTabs } from './hooks/useEventsScreenTabs'

/**
 * Main Events Screen component with tabbed interface
 * Manages different types of events: active events, archived events, event types, and sport types
 */
function EventsScreen() {
  const { activeTab, handleSelectTab, isTabActive } = useEventsScreenTabs()

  // Memoize tab configuration to prevent unnecessary re-renders
  const tabElements = useMemo(() => {
    return EVENTS_TAB_CONFIG.map((tabConfig) => (
      <Tab key={tabConfig.key} eventKey={tabConfig.key} title={tabConfig.title}>
        <TabContent componentName={tabConfig.component} isActive={isTabActive(tabConfig.key)} />
      </Tab>
    ))
  }, [isTabActive])

  return (
    <Layout title="События">
      <Tabs className="mb-3" onSelect={handleSelectTab} defaultActiveKey={DEFAULT_TAB} activeKey={activeTab}>
        {tabElements}
      </Tabs>
    </Layout>
  )
}

export default EventsScreen
