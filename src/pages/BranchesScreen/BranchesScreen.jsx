import useAxios from 'axios-hooks'
import { useEffect, useState, useCallback } from 'react'
import { Button, Col, Modal, Row, Form, FloatingLabel } from 'react-bootstrap'

import { branchesTableData } from './branchesScreenData'
import BranchesForm from './components/BranchesForm/BranchesForm'
import AdvancedPagination from '../../components/AdvancedPagination/AdvancedPagination'
import Layout from '../../components/Layout/Layout'
import ConfirmDeleteModal from '../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import ActionsRow from '../../components/TableTemplate/ActionsRow/ActionsRow'
import TableTemplate from '../../components/TableTemplate/TableTemplate'
import { APIRoute } from '../../const'
import { useToast } from '../../hooks/useToast'

const PAGE_LIMIT_PAGINATION = 10

function BranchesScreen() {
  const [values, setValues] = useState([])
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [valuesRender, setValuesRender] = useState([]) // для отрисовки на странице
  const [selectedItem, setSelectedItem] = useState({})
  const [confirmModal, setConfirmModal] = useState(false)
  const [isShowFormModal, setIsShowFormModal] = useState(false)
  const [typeFilter, setTypeFilter] = useState('')
  const [searchValue, setSearchValue] = useState('')

  const openToast = useToast()

  const [, api] = useAxios(
    {
      url: APIRoute.BRANCHES_LIST,
      method: 'GET',
    },
    { manual: true }
  )
  const [, apiDelete] = useAxios(
    {
      url: `${APIRoute.CUD_BRANCHES}`,
      method: 'DELETE',
    },
    { manual: true }
  )

  const getValues = useCallback(() => {
    api().then((r) => {
      setValues(Array.isArray(r.data) ? r.data : [])
    })
  }, [api])

  useEffect(() => {
    getValues()
  }, [getValues])

  const applyFilters = useCallback(() => {
    let filtered = Array.isArray(values) ? values : []

    // Фильтр по типу отделения
    if (typeFilter) {
      if (typeFilter === 'not_specified') {
        filtered = filtered.filter((item) => !item.kind || item.kind === '')
      } else {
        filtered = filtered.filter((item) => item.kind === typeFilter)
      }
    }

    // Поиск по тексту
    if (searchValue) {
      filtered = filtered.filter((item) => {
        const itemName = JSON.stringify(item).toLowerCase()
        return itemName.includes(searchValue.toLowerCase())
      })
    }

    setFilteredValues(filtered)
  }, [values, typeFilter, searchValue])

  useEffect(() => {
    applyFilters()
  }, [values, typeFilter, searchValue, applyFilters])

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ url: `${APIRoute.CUD_BRANCHES}/${selectedItem?.public_id}` }).then((r) => {
      if (r.status === 200) {
        setValues(Array.isArray(r.data) ? r.data : [])
        openToast.success({ message: 'Отделение было удалено' })
        setSelectedItem({})
      }
    })
  }

  const handleClickAddButton = () => {
    setIsShowFormModal(true)
  }

  const handleEdit = (item) => {
    setSelectedItem(item)
    setIsShowFormModal(true)
  }

  const handleCloseFormModal = () => {
    setIsShowFormModal(false)
    setSelectedItem({})
  }

  const returnActionsRow = (item) => {
    return <ActionsRow item={item} onClickDelete={handleConfirmDelete} onClickEdit={handleEdit} />
  }

  const renderCustomCell = (param, _, item) => {
    if (param === 'kindTranslate') {
      switch (item.kind) {
        case 'regions':
          return 'Регионы'
        case 'child':
          return 'Дети'
        default:
          return item.kind || '-'
      }
    }
    return null
  }

  return (
    <Layout title="Отделения" onClickAddButton={handleClickAddButton}>
      <Row className="mb-3">
        <Col md={9}>
          <FloatingLabel controlId="searchInput" label="Поиск">
            <Form.Control placeholder="Поиск" value={searchValue} onChange={(e) => setSearchValue(e.target.value)} />
          </FloatingLabel>
        </Col>
        <Col md={3}>
          <FloatingLabel controlId="typeFilter" label="Фильтр по типу">
            <Form.Control as="select" value={typeFilter} onChange={(e) => setTypeFilter(e.target.value)}>
              <option value="">Все</option>
              <option value="regions">Регионы</option>
              <option value="child">Дети</option>
              <option value="not_specified">Не указан</option>
            </Form.Control>
          </FloatingLabel>
        </Col>
      </Row>

      <TableTemplate
        data={branchesTableData}
        values={valuesRender}
        actions={returnActionsRow}
        renderCustomCell={renderCustomCell}
      />

      <AdvancedPagination values={filteredValues} setValues={setValuesRender} limitValues={PAGE_LIMIT_PAGINATION} />

      <Modal show={isShowFormModal} onHide={handleCloseFormModal}>
        <Modal.Header closeButton>
          <Modal.Title>
            {Object.keys(selectedItem).length > 0 ? 'Редактирование отделения' : 'Добавление отделения'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <BranchesForm
            selectedItem={selectedItem}
            priorityNumber={values?.length > 0 ? values[0]?.priority_number + 1 : 1}
            onUpdateBranches={getValues}
            onCloseModal={handleCloseFormModal}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button variant="success" type="submit" form="form">
            {Object.keys(selectedItem).length > 0 ? 'Сохранить' : 'Добавить'}
          </Button>
          <Button onClick={handleCloseFormModal} variant="outline-secondary" type="button">
            Отмена
          </Button>
        </Modal.Footer>
      </Modal>

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedItem}
        onDeleteItem={handleDeleteItem}
        text="Удалить отделение?"
      />
    </Layout>
  )
}

export default BranchesScreen
