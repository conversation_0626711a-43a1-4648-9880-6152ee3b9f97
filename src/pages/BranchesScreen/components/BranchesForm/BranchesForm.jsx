import { nanoid } from '@reduxjs/toolkit'
import useAxios from 'axios-hooks'
import { useEffect, useMemo } from 'react'
import { Col, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { APIRoute } from '@/const'
import { useGetCountryRegions } from '@/features/cities/api/getCountryRegions'
import { useToast } from '@/hooks/useToast'
import { removeEmptyStringBranches } from '@/utils/common'
import { checkSetValueBranches } from '@/utils/forms'

const fields = [
  {
    id: nanoid(),
    name: 'name',
    label: 'Название',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'kind',
    label: 'Тип отделения',
    type: 'select',
    required: false,
    options: [
      { value: 'regions', label: 'Регионы' },
      { value: 'child', label: 'Дети' },
    ],
  },
  {
    id: nanoid(),
    name: 'title',
    label: 'Должность руководителя',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'person',
    label: 'ФИО руководителя',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'phone',
    label: 'Контактный телефон (формат: 79998887766)',
    type: 'tel',
    pattern: /[7][0-9]{10}$/,
    required: false,
  },
  {
    id: nanoid(),
    name: 'email',
    label: 'Контактный имейл',
    type: 'email',
    required: false,
  },
  {
    id: nanoid(),
    name: 'link',
    label: 'Ссылки',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'priority_number',
    label: 'Приоритет',
    type: 'number',
    required: true,
    isNumber: true,
  },
]

function BranchesForm({ priorityNumber, selectedItem, onUpdateBranches, onCloseModal }) {
  const {
    register,
    resetField,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: selectedItem?.name,
      kind: selectedItem?.kind,
      title: selectedItem?.title,
      person: selectedItem?.person,
      phone: selectedItem?.phone,
      email: selectedItem?.email,
      link: selectedItem?.link,
      priority_number: selectedItem?.priority_number || priorityNumber,
      region_id: selectedItem?.region?.id,
    },
  })
  const { data: regionsData } = useGetCountryRegions('RU')
  const regions = useMemo(() => regionsData?.data?.values || [], [regionsData?.data?.values])

  const openToast = useToast()

  const isEdit = Object.keys(selectedItem).length > 0

  const [, api] = useAxios(
    {
      url: APIRoute.GET_REGION_RU,
      method: 'GET',
    },
    { manual: true }
  )

  useEffect(() => {
    if (isEdit && regions?.length > 0) {
      resetField('region_id')
    }
  }, [regions, isEdit, resetField])

  const onSubmit = (data) => {
    const filteredData = removeEmptyStringBranches(data)

    if (Object.keys(filteredData).length > 0) {
      const method = isEdit ? 'PUT' : 'POST'
      const toastText = isEdit ? 'Отделение изменено' : 'Отделение добавлено'
      const url = isEdit ? `${APIRoute.CUD_BRANCHES}/${selectedItem.public_id}` : APIRoute.CUD_BRANCHES

      api({ url: url, method: method, data: filteredData }).then((r) => {
        if (r.status === 200) {
          onUpdateBranches()
          onCloseModal()
          openToast.success({ message: toastText })
        } else if (r.status === 422) {
          openToast.error({ message: 'Не удалось добавить отделение' })
        }
      })
    }
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)} id="form">
      <Row className="g-3 mb-3">
        {fields.map((field) => (
          <Col md={12} key={field.id}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                {field.type === 'select' ? (
                  <FormControl
                    as="select"
                    {...register(field.name, {
                      required: field.required && !isEdit,
                      setValueAs: (v) => checkSetValueBranches(v, selectedItem?.[field.name], field.type, field.name),
                    })}
                    isInvalid={errors[field.name]}
                    placeholder={field.label}
                  >
                    <option value="">Выберите тип отделения</option>
                    {field.options?.map((option) => (
                      <option value={option.value} key={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </FormControl>
                ) : (
                  <FormControl
                    {...register(field.name, {
                      required: field.required && !isEdit,
                      pattern: field.pattern,
                      setValueAs: (v) => checkSetValueBranches(v, selectedItem?.[field.name], field.type, field.name),
                    })}
                    type={field.type}
                    isInvalid={errors[field.name]}
                    placeholder={field.label}
                  />
                )}
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}

        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="regionsLabel" label="Регион">
              <FormControl
                as="select"
                {...register('region_id', {
                  setValueAs: (v) => checkSetValueBranches(v, selectedItem?.region?.id, 'number', 'region'),
                })}
                type="number"
                placeholder="Регион"
              >
                <option value="">Выберите регион</option>
                {regions?.map((item) => (
                  <option value={item.id} key={item.id}>
                    {item.name_ru}
                  </option>
                ))}
              </FormControl>
            </FloatingLabel>
          </Form.Group>
        </Col>
      </Row>
    </Form>
  )
}

export default BranchesForm
