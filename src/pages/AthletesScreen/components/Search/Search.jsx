import { Button, Col, FormControl, InputGroup, Row } from 'react-bootstrap'

function Search({ title, onChangeSearch, onClickAdd }) {
  return (
    <>
      <Row className="mb-3">
        <Col>
          <InputGroup>
            <InputGroup.Text>
              <i className="bi bi-search" />
            </InputGroup.Text>
            <FormControl
              placeholder={title}
              aria-label={title}
              aria-describedby="basic-addon2"
              onChange={(e) => onChangeSearch(e)}
            />
          </InputGroup>
        </Col>
        <Col md="auto">
          <Button onClick={onClickAdd} variant="success">
            <i className="bi bi-plus-circle me-2" />
            Добавить
          </Button>
        </Col>
      </Row>
    </>
  )
}

export default Search
