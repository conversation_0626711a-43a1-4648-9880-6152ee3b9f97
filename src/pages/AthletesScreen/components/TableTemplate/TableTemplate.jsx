import { useState } from 'react'
import { <PERSON>, Col, Modal, ModalBody, ModalHeader, Row, Table } from 'react-bootstrap'

import { checkAndSetImgUrl, getImageSrc } from '@/utils/images'

function recompose(obj, string) {
  const parts = string.split('.')
  const newObj = obj[parts[0]]
  if (parts[1]) {
    parts.splice(0, 1)
    const newString = parts.join('.')
    return recompose(newObj, newString)
  }
  return newObj
}

const TableTemplate = ({ data, values, actions = null, handleClickRow }) => {
  const [imageModal, setImageModal] = useState(null)
  const [imageModalShow, setImageModalShow] = useState(false)

  const isTitle = data.title.length > 0

  const switchTd = (value, item) => {
    if (item === 'picture') {
      return (
        <img
          src={getImageSrc(value)}
          onError={checkAndSetImgUrl}
          width="63px"
          height="63px"
          style={{ borderRadius: 100 }}
        />
      )
    }
    return value ? value : '-'
  }

  if (!values) return null

  return (
    <Card>
      {isTitle && (
        <Card.Header>
          <Card.Title>{data.title}</Card.Title>
        </Card.Header>
      )}

      <Card.Body style={{ minWidth: '1200px', position: 'relative' }}>
        <Table>
          <thead>
            <tr>
              <th
                colSpan={data.list.length + (actions ? 1 : 0)}
                style={{
                  position: 'absolute',
                  top: '18px',
                  left: '80px',
                  background: 'transparent',
                  border: 'none',
                  padding: 0,
                }}
              >
                <strong>{values.length}</strong>
              </th>
            </tr>
            <tr>
              {data.list.map((item) => (
                <th key={item.label}>{item.label}</th>
              ))}
              {actions && <th style={{ textAlign: 'center' }}>Действие</th>}
            </tr>
          </thead>
          <tbody>
            {values.map((value, index) => (
              <tr onClick={() => handleClickRow(value)} style={{ cursor: `${actions ? 'pointer' : ''}` }} key={index}>
                {data.list.map((item) => (
                  <td key={item.value}>{switchTd(recompose(value, item.value), item.value)}</td>
                ))}
                {actions && actions(value)}
              </tr>
            ))}
          </tbody>
        </Table>

        <Modal
          show={imageModalShow}
          onHide={() => {
            setImageModalShow(false)
            setImageModal(null)
          }}
          fullscreen
        >
          <ModalHeader closeButton>Результаты</ModalHeader>
          <ModalBody>
            <Row className="d-flex justify-content-center">
              <Col md="auto">{imageModal && <img src={imageModal} alt="результат" />}</Col>
            </Row>
          </ModalBody>
        </Modal>
      </Card.Body>
    </Card>
  )
}

export default TableTemplate
