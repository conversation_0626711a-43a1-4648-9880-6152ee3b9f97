import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Container, Mo<PERSON>, <PERSON> } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import PageSearch from '@/components/PageSearch/PageSearch'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { useGetCoaches, useCreateCoach, useUpdateCoach, useDeleteCoach } from '@/features/coaches/api'
import { useGetGroups } from '@/features/groups/api'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

import { trainersTableData } from './trainersData'
import AthletesForm from '../AthletesForm/AthletesForm'

const PAGE_LIMIT_PAGINATION = 10

function Trainers() {
  const [formData, setFormData] = useState({})
  const [selectedItem, setSelectedItem] = useState({})
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [coaches, setCoaches] = useState([]) // для отрисовки на странице

  const [editUserPopup, setEditUserPopup] = useState(false)
  const [isDeleteUserPopup, setIsDeleteUserPopup] = useState(false)
  const [isChangedData, setChangedData] = useState(false)
  const [isNewUser, setIsNewUser] = useState(false)
  const [isClickRow, setIsClickRow] = useState(false)

  const { data: allCoaches = [] } = useGetCoaches()
  const { mutate: createCoach } = useCreateCoach()
  const { mutate: updateCoach } = useUpdateCoach()
  const { mutate: deleteCoach } = useDeleteCoach()
  const { data: group = [] } = useGetGroups()

  useEffect(() => {
    if (Object.keys(formData).length !== 0) {
      setChangedData(true)
    } else {
      setChangedData(false)
    }
  }, [formData])

  const sendData = (e) => {
    e.preventDefault()

    if (isNewUser) {
      createCoach({ ...formData })
    } else {
      updateCoach({ ...formData, public_id: selectedItem?.public_id })
    }
    handleClosePopup()
  }

  const handleClosePopup = () => {
    setEditUserPopup(false)
    setFormData({})
    setIsClickRow(false)
    setIsNewUser(false)
  }

  const handleEditUser = (user) => {
    setSelectedItem({
      ...user,
      birthday: unixToMoment(user.birthday).format('DD.MM.YYYY'),
    })

    setEditUserPopup(true)
  }

  const handleClickAdd = () => {
    setIsNewUser(true)
    handleEditUser({})
  }

  const handleClickRow = (data) => {
    handleEditUser(data)
    setIsClickRow(true)
  }

  const handleDelete = () => {
    setEditUserPopup(false)
    setIsDeleteUserPopup(true)
    setIsClickRow(false)
  }

  const handleCloseDeletePopup = () => {
    setIsDeleteUserPopup(false)
  }

  const handleDeleteMember = () => {
    deleteCoach(selectedItem?.public_id)
    setIsDeleteUserPopup(false)
  }

  const returnActionsTable = (user) => {
    return (
      <td onClick={(evt) => evt.stopPropagation()} className="align-middle">
        <Button className="border-0" onClick={() => handleEditUser(user)} variant="outline-secondary" size="sm">
          <i className="bi bi-pencil" />
        </Button>
      </td>
    )
  }

  return (
    <Container fluid>
      <Row className="mb-3">
        <Col>
          <PageSearch values={allCoaches} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <Button onClick={handleClickAdd} variant="success">
            <i className="bi bi-plus-circle me-2" />
            Добавить
          </Button>
        </Col>
      </Row>

      <TableTemplate
        data={trainersTableData}
        values={coaches}
        actions={returnActionsTable}
        actionRow={handleClickRow}
      />

      <AthletesForm
        isClickRow={isClickRow}
        sendData={sendData}
        formData={formData}
        defaultFormData={selectedItem}
        isNewUser={isNewUser}
        handleChangeForm={setFormData}
        group={group}
        editUserPopup={editUserPopup}
        handleClosePopup={handleClosePopup}
        handleDelete={handleDelete}
        isChangedData={isChangedData}
      />

      <Modal show={isDeleteUserPopup} onHide={handleCloseDeletePopup}>
        <Modal.Header closeButton>
          {selectedItem?.picture && (
            <img
              style={{ borderRadius: 100, marginRight: '30px' }}
              width="70"
              height="70"
              src={getImageSrc(selectedItem.picture)}
              alt="Аватар"
            />
          )}
          <Modal.Title>Удалить тренера</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <span>Вы точно хотите удалить тренера?</span>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseDeletePopup}>
            Отмена
          </Button>
          <Button onClick={handleDeleteMember} variant="warning">
            Удалить
          </Button>
        </Modal.Footer>
      </Modal>

      <AdvancedPagination values={filteredValues} setValues={setCoaches} limitValues={PAGE_LIMIT_PAGINATION} />
    </Container>
  )
}

export default Trainers
