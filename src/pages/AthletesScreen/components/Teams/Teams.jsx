import { useEffect, useState } from 'react'
import {
  Button,
  ButtonGroup,
  Col,
  Container,
  Dropdown,
  Form,
  FormControl,
  FormGroup,
  Modal,
  Row,
} from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'

import { useGetGroups, useCreateGroup, useUpdateGroup, useDeleteGroup } from '@/features/groups/api'

import { groupTableData } from './teamsData'
import Search from '../Search/Search'
import TableTemplate from '../TableTemplate/TableTemplate'

function Teams() {
  const { data: allGroup = [] } = useGetGroups()
  const { mutate: createGroup } = useCreateGroup()
  const { mutate: updateGroup } = useUpdateGroup()
  const { mutate: deleteGroup } = useDeleteGroup()

  const [valuesRender, setValuesRender] = useState([])
  const [filteredValues, setFilteredValues] = useState([])
  const navigate = useNavigate()

  useEffect(() => {
    if (allGroup && allGroup.length > 0) {
      setFilteredValues(allGroup)
    }
  }, [allGroup])

  const handleChangeSearch = (evt) => {
    const filteredValues = allGroup.filter((item) => {
      const itemName = JSON.stringify(item).toLowerCase()
      return itemName.includes(evt.target.value.toLowerCase())
    })

    setFilteredValues(filteredValues)
  }

  const [editGroupPopup, setEditGroupPopup] = useState(false)
  const [isDeleteGroupPopup, setIsDeleteGroupPopup] = useState(false)
  const [editDataGroup, setEditDataGroup] = useState({
    name: '',
    group_public_id: '',
    public_id: '',
  })

  const [changedDataGroup, setChangedDataGroup] = useState({})
  const [isChangedData, setChangedData] = useState(false)
  const [isNewGroup, setIsNewGroup] = useState(false)

  useEffect(() => {
    if (Object.keys(changedDataGroup).length !== 0) {
      setChangedData(true)
    } else {
      setChangedData(false)
    }
  }, [changedDataGroup])

  const sendData = (e) => {
    e.preventDefault()
    handleClosePopup()
    const addDataGroup = { ...editDataGroup, ...changedDataGroup }
    if (isNewGroup) {
      createGroup(addDataGroup)
    } else {
      updateGroup(addDataGroup)
    }
  }

  const handleClosePopup = () => {
    setEditGroupPopup(false)
    setChangedDataGroup({})
    setIsNewGroup(false)
  }

  const handleEditGroup = ({ group_public_id, name, public_id }) => {
    const isExistingGroup = !!public_id

    setEditDataGroup({
      ...editDataGroup,
      name,
      group_public_id,
      public_id,
    })

    if (isExistingGroup) {
      setIsNewGroup(false)
    }

    setEditGroupPopup(true)
  }

  const handleClickRow = (data) => {
    navigate(`${groupTableData.clickUrl}/${data.public_id}/${data.name}`)
  }

  const handleDelete = ({ public_id }) => {
    setEditDataGroup({
      ...editDataGroup,
      public_id,
    })
    setIsDeleteGroupPopup(true)
  }

  const handleCloseDeletePopup = () => {
    setIsDeleteGroupPopup(false)
  }

  const handleDeleteGroup = () => {
    deleteGroup(editDataGroup.public_id)
    setIsDeleteGroupPopup(false)
  }

  const title = isNewGroup ? 'Добавить сборную' : 'Изменение данных'

  const handleClickAdd = () => {
    setIsNewGroup(true)
    handleEditGroup({})
  }

  const returnActionsTable = (group) => {
    return (
      <td className="text-center" onClick={(evt) => evt.stopPropagation()}>
        <Dropdown>
          <Dropdown.Toggle as={ButtonGroup} bsPrefix=" " id="dropdown-basic">
            <i className="bi bi-three-dots" />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item as="button" onClick={() => handleEditGroup(group)}>
              <i className="bi bi-pencil me-2" />
              Изменить
            </Dropdown.Item>
            <Dropdown.Item className="text-danger" as="button" onClick={() => handleDelete(group)}>
              <i className="bi bi-trash me-2" />
              Удалить
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  return (
    <Container fluid>
      <Search title="Поиск сборной" onChangeSearch={handleChangeSearch} onClickAdd={handleClickAdd} />

      <Row className="mb-3">
        <Col>
          <TableTemplate
            data={groupTableData}
            values={valuesRender}
            actions={returnActionsTable}
            handleClickRow={handleClickRow}
          />
        </Col>
      </Row>

      <AdvancedPagination values={filteredValues} setValues={setValuesRender} />

      <Modal show={editGroupPopup} onHide={handleClosePopup}>
        <Form onSubmit={sendData} style={{ backgroundColor: '#eef0f8', borderRadius: '4px' }}>
          <Modal.Header closeButton>
            <Modal.Title>{title}</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <FormGroup controlId="kindContactForm">
              <Row>
                <Col xs={5}>
                  <Form.Label>Название сборной</Form.Label>
                </Col>
                <Col>
                  <FormControl
                    type="text"
                    name="name"
                    placeholder="name"
                    defaultValue={editDataGroup.name}
                    onChange={(e) => setChangedDataGroup({ name: e.target.value })}
                    className="mb-3"
                    required
                  />
                </Col>
              </Row>
            </FormGroup>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleClosePopup}>
              Закрыть
            </Button>
            <Button type="submit" variant="primary" disabled={!isChangedData}>
              Сохранить
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
      <Modal show={isDeleteGroupPopup} onHide={handleCloseDeletePopup}>
        <Modal.Header closeButton>
          <Modal.Title>Удалить сборную</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <span>Вы точно хотите удалить сборную?</span>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseDeletePopup}>
            Отмена
          </Button>
          <Button onClick={handleDeleteGroup} variant="warning">
            Удалить
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  )
}

export default Teams
