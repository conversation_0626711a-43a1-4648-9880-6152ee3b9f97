@import '../../../../assets/styles/mixins';

.hideArrow {
  display: grid;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: #646d8f;
  cursor: pointer;
}

.image {
  margin: 0;

  border-radius: 4px;
}

.fileButton {
  margin: 0;
  padding: 12px;

  display: grid;
  justify-content: center;
  align-content: end;
  width: 100%;
  min-height: 100px;

  background-color: #ffffff;
  background-image: url('../../../../assets/img/icons/icon-photo-camera.svg');
  background-repeat: no-repeat;
  background-position: center 10px;
  background-size: 50px;
  border-radius: 4px;

  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: darken(#ffffff, 4);
  }
}

:global(.was-validated .form-control:invalid) ~ .fileButton {
  background-color: #dc354560;
}

.addedImage {
  padding: 6px;

  min-height: 0;

  background-position: 16.5% center;
  background-size: auto;
}

.addBtn {
  @include addButton;
}

.editBtnWrap {
  position: relative;
}

.editBtn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  display: grid;
  place-items: center;
  width: 32px;
  height: 32px;

  background-color: transparent;
  border: 0;
  border-radius: 4px;

  transition: background-color 0.2s;

  &:hover {
    background-color: #ffffff;
  }
}

.descWrapper {
  margin: 0;

  min-height: 64px;

  background-color: #e9ecef;
}

.descLabel {
  font-size: 14px;
  line-height: 0.5;

  opacity: 0.65;
}

.editorWrapper :global .rdw-editor-toolbar {
  margin-bottom: 1px;

  border: 0;
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.editorWrapper :global .rdw-editor-main {
  padding: 0 15px;
  background-color: #ffffff;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
