import { useEffect, useState } from 'react'
import { <PERSON><PERSON>, Col, Mo<PERSON>, Row } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import PageSearch from '@/components/PageSearch/PageSearch'
import ActionsRow from '@/components/TableTemplate/ActionsRow/ActionsRow'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { useCreateAthlete, useUpdateAthlete, useDeleteAthlete } from '@/features/athletes/api'
import { useGetGroups } from '@/features/groups/api'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

import { participantsTableData } from './participantsData'
import AthletesForm from '../AthletesForm/AthletesForm'

const PAGE_LIMIT_PAGINATION = 10

function Participants({ allAthletes }) {
  const [formData, setFormData] = useState({})
  const [selectedItem, setSelectedItem] = useState({})
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [athletes, setAthletes] = useState([]) // для отрисовки на странице

  const { mutate: createAthlete } = useCreateAthlete()
  const { mutate: updateAthlete } = useUpdateAthlete()
  const { mutate: deleteAthlete } = useDeleteAthlete()
  const { data: group = [] } = useGetGroups()

  const [editUserPopup, setEditUserPopup] = useState(false)
  const [isDeleteUserPopup, setIsDeleteUserPopup] = useState(false)

  const [isChangedData, setChangedData] = useState(false)

  const [isNewUser, setIsNewUser] = useState(false)
  const [isClickRow, setIsClickRow] = useState(false)

  useEffect(() => {
    if (Object.keys(formData).length !== 0) {
      setChangedData(true)
    } else {
      setChangedData(false)
    }
  }, [formData])

  const sendData = (e) => {
    e.preventDefault()
    if (isNewUser) {
      createAthlete({ ...formData })
    } else {
      updateAthlete({ ...formData, public_id: selectedItem?.public_id })
    }

    handleClosePopup()
  }

  const handleClosePopup = () => {
    setEditUserPopup(false)
    setFormData({})
    setIsClickRow(false)
    setIsNewUser(false)
  }

  const handleEditUser = (user) => {
    setSelectedItem({
      ...user,
      birthday: unixToMoment(user.birthday),
    })

    setEditUserPopup(true)
  }

  const handleClickAdd = () => {
    setIsNewUser(true)
    handleEditUser({})
  }

  const handleClickRow = (data) => {
    handleEditUser(data)
    setIsClickRow(true)
  }

  const handleDelete = (user) => {
    setSelectedItem(user)
    setIsDeleteUserPopup(true)
  }

  const handleCloseDeletePopup = () => {
    setIsDeleteUserPopup(false)
  }

  const handleDeleteMember = () => {
    deleteAthlete(selectedItem?.public_id)
    setIsDeleteUserPopup(false)
  }

  const returnActionsRow = (item) => {
    return <ActionsRow item={item} onClickDelete={handleDelete} onClickEdit={handleEditUser} />
  }

  return (
    <>
      <Row className="mb-3 align-items-center">
        <Col>
          <PageSearch values={allAthletes} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <Button onClick={handleClickAdd} variant="success">
            <i className="bi bi-plus-circle me-2" />
            Добавить
          </Button>
        </Col>
      </Row>

      <TableTemplate
        data={participantsTableData}
        values={athletes}
        actions={returnActionsRow}
        actionRow={handleClickRow}
      />

      <Modal show={isDeleteUserPopup} onHide={handleCloseDeletePopup}>
        <Modal.Header closeButton>
          {selectedItem?.picture && (
            <img
              style={{ borderRadius: 100, marginRight: '30px' }}
              width="70"
              height="70"
              src={getImageSrc(selectedItem.picture)}
              alt="Аватар"
            />
          )}
          <Modal.Title>Удалить участника</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <span>Вы действительно хотите удалить участника?</span>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleCloseDeletePopup}>
            Нет
          </Button>
          <Button onClick={handleDeleteMember} variant="danger">
            Да
          </Button>
        </Modal.Footer>
      </Modal>

      <AthletesForm
        isClickRow={isClickRow}
        sendData={sendData}
        formData={formData}
        defaultFormData={selectedItem}
        isNewUser={isNewUser}
        handleChangeForm={setFormData}
        group={group}
        editUserPopup={editUserPopup}
        handleClosePopup={handleClosePopup}
        isChangedData={isChangedData}
      />

      <AdvancedPagination values={filteredValues} setValues={setAthletes} limitValues={PAGE_LIMIT_PAGINATION} />
    </>
  )
}

export default Participants
