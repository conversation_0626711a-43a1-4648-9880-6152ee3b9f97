import { useState } from 'react'
import { Container, Tab, Tabs } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'

import { useGetAthletes } from '@/features/athletes/api'

import Participants from './components/Participants/Participants'
import Teams from './components/Teams/Teams'
import Trainers from './components/Trainers/Trainers'

function AthletesScreen() {
  const [activeTab, setActiveTab] = useState('')
  const { data: athletes = [] } = useGetAthletes()

  const handleSelectTab = (evt) => {
    setActiveTab(evt)
  }

  return (
    <Layout>
      <Container className="pt-4 pb-4" fluid>
        <Tabs className="mb-4" defaultActiveKey="participants" onSelect={handleSelectTab}>
          <Tab eventKey="participants" title="Участники">
            <Participants allAthletes={athletes} />
          </Tab>

          <Tab eventKey="teams" title="Сборные">
            <Teams />
          </Tab>
          <Tab eventKey="trainers" title="Тренеры">
            <Trainers isOpenTabs={activeTab} />
          </Tab>
        </Tabs>
      </Container>
    </Layout>
  )
}

export default AthletesScreen
