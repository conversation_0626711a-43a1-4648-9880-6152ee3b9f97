import React, { useEffect, useState } from 'react'
import { Badge, Col, ListGroup, ListGroupItem, Row } from 'react-bootstrap'

import { ALL_FIELDS } from '@/pages/ResultsScreen/ResultsScreenTable/resultsScreenData'

const SortingFields = React.memo(function SortingFields({ fields, fieldsData, defaultFieldsData, onChangeFields }) {
  const [innerFieldData, setInnerFieldsData] = useState([])

  useEffect(() => {
    if (fieldsData && Array.isArray(fieldsData) && fieldsData.length > 0) {
      setInnerFieldsData([...fieldsData])
    } else if (defaultFieldsData && Object.keys(defaultFieldsData).length > 0) {
      setInnerFieldsData([...defaultFieldsData])
    } else {
      setInnerFieldsData([]) // Устанавливаем пустой массив по умолчанию
    }
  }, [fieldsData, defaultFieldsData])

  const handleClickItem = (evt) => {
    const value = evt.target.dataset.value
    if (!value) return // Защищаемся от клика по области без значения

    const newFields = [...innerFieldData]

    if (!innerFieldData.includes(value)) {
      newFields.push(value)
      setInnerFieldsData(newFields)
      onChangeFields([...newFields])
    } else {
      const index = innerFieldData.findIndex((item) => item === value)
      newFields.splice(index, 1)
      setInnerFieldsData(newFields)
      onChangeFields([...newFields])
    }
  }

  const returnBadge = (item) => {
    const index = innerFieldData.findIndex((el) => el === item)
    if (index < 0) return null

    return (
      <Badge bg="light" text="dark">
        {index + 1}
      </Badge>
    )
  }

  if (!fields?.length) return null

  return (
    <Row className="mb-3">
      <Col>
        <ListGroup>
          {fields.map((item, index) => (
            <React.Fragment key={`left-${item}-${index}`}>
              {ALL_FIELDS[item]?.label && (
                <ListGroupItem
                  className="d-flex justify-content-between"
                  onClick={handleClickItem}
                  active={innerFieldData.includes(item)}
                  data-value={item}
                  action
                >
                  {ALL_FIELDS[item]?.label}
                  {returnBadge(item)}
                </ListGroupItem>
              )}
            </React.Fragment>
          ))}
        </ListGroup>
      </Col>
      <Col>
        <ListGroup as="ol" numbered>
          {innerFieldData.map((field, index) => (
            <React.Fragment key={`right-${field}-${index}`}>
              {ALL_FIELDS[field]?.label && (
                <ListGroupItem data-value={field} as="li">
                  {ALL_FIELDS[field]?.label}
                </ListGroupItem>
              )}
            </React.Fragment>
          ))}
        </ListGroup>
      </Col>
    </Row>
  )
})

export default SortingFields
