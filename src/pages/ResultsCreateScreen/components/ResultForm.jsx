import useAxios from 'axios-hooks'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { Col, FloatingLabel, Form, FormControl, FormSelect, Row, FormCheck } from 'react-bootstrap'
import { useSelector } from 'react-redux'

import { APIRoute, MB_IN_B } from '@/const'
import { useToast } from '@/hooks/useToast'
import { sources } from '@/pages/ResultsScreen/constants'
import { getEditSupplier } from '@/store/supplier/selectors'
import { convertBase64 } from '@/utils/common'
import { getFormatTime } from '@/utils/date'

import BroadcastForm from './BroadcastForm'
import CopernicoForm from './CopernicoForm'

function ResultForm({ formData, onChangeForm, setFile, setEventCityId, setEventId, isExcel, setIsExcel }) {
  const activeEditItem = useSelector(getEditSupplier)
  const [isCsv, setIsCsv] = useState(false)
  const [currentField, setCurrentField] = useState(null)
  const [selectedEventCity, setSelectedEventCity] = useState(null)

  const openToast = useToast()

  const [{ data: eventTypeList }] = useAxios(
    {
      url: APIRoute.EVENT_TYPE_LIST,
      method: 'GET',
    },
    { manual: false }
  )

  const [{ data: eventCityFiltered }, apiEventCityFiltered] = useAxios(
    {
      url: APIRoute.EVENT_CITY_FILTERED,
      method: 'POST',
    },
    { manual: true }
  )

  useEffect(() => {
    if (Object.keys(activeEditItem).length > 0) {
      const event = eventTypeList?.values?.find(
        (event) => event.public_id === activeEditItem.event_city.event_public_id
      )

      if (event) {
        const body = {
          city: {
            id: 0,
          },
          date_from: moment().subtract(12, 'months'),
          date_to: moment().add(1, 'days'),
          event_type: {
            public_id: event.public_id,
          },
        }

        apiEventCityFiltered({ data: body })
      }
    }
  }, [activeEditItem, apiEventCityFiltered, eventTypeList?.values])

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = eventTypeList?.values.find((event) => event.public_id === eventId)

    if (event) {
      const body = {
        city: {
          id: 0,
        },
        date_from: moment().subtract(12, 'months'),
        date_to: moment().add(7, 'days'),
        event_type: {
          public_id: event.public_id,
        },
      }

      onChangeForm({ ...formData, event_type: { public_id: event.public_id } })
      setEventId(event.public_id)

      apiEventCityFiltered({ data: body })
    }
  }

  const handleChangeCity = (evt) => {
    const values = evt.target.value.split('#')
    const cityPublicId = values[0]
    const cityId = values[1]

    if (cityPublicId !== 0) {
      // Находим выбранный город, чтобы получить timezone
      const selectedCity = eventCityFiltered?.values?.find((city) => city.public_id === cityPublicId)
      setSelectedEventCity(selectedCity)

      onChangeForm({
        ...formData,
        event_city: {
          public_id: cityPublicId,
          id: cityId,
        },
      })
    }
    setEventCityId(cityPublicId)
  }

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    const fileSizeInB = file.size
    const base64 = await convertBase64(file)
    const ext = file.name.split('.').pop()

    if (fileSizeInB <= MB_IN_B * 40) {
      if (isExcel) {
        if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && ext === 'xlsx') {
          setFile(base64)
        } else {
          openToast.error({
            title: true,
            message: `Неподдерживаемый формат: ${file.type}`,
            duration: 6000,
          })
          setFile(null)
        }
      } else {
        setFile(base64)
      }
    } else if (fileSizeInB > MB_IN_B * 40) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
      evt.target.value = ''
    }
  }

  const handleChangeField = (evt) => {
    const name = evt.target.name
    const value = name === 'vat' ? +evt.target.value : evt.target.value

    if (name === 'source_name') {
      if (value === 'csv') {
        setIsCsv(true)
        setIsExcel(false)
        setCurrentField(value)
        setFile(null)
      } else if (value === 'Excel') {
        setIsCsv(false)
        setIsExcel(true)
        setCurrentField(value)
        setFile(null)
      } else {
        setIsCsv(false)
        setIsExcel(false)
        setFile(null)
      }

      let newFormData = { ...formData, [name]: value }

      if (value !== 'external_url') {
        delete newFormData.external_url
      } else if (value === 'external_url') {
        delete newFormData.city_code
      }

      if (value !== 'copernico' && 'distance_ids' in formData) {
        delete newFormData.distance_ids
        delete newFormData.distances_urls
        delete newFormData.broadcast
      }

      onChangeForm(newFormData)
    } else {
      onChangeForm({ ...formData, [name]: value })
    }
  }

  return (
    <Row className="g-3 mb-5">
      <Col md={4}>
        <FloatingLabel controlId="eventLabelTicket" label="Событие">
          <FormSelect
            onChange={handleChangeEvent}
            value={formData?.event_type?.public_id || ''}
            name="event_type"
            aria-label="Событие"
          >
            <option value="">выберите один из вариантов</option>
            {eventTypeList?.values?.map((event) => (
              <option value={event.public_id} key={event.public_id}>
                {event.title}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="cityLabelTicket" label="Город">
          <FormSelect
            onChange={handleChangeCity}
            value={
              formData?.event_city?.public_id && formData?.event_city?.id
                ? `${formData.event_city.public_id}#${formData.event_city.id}`
                : ''
            }
            name="event_city"
            aria-label="Город"
          >
            <option value="">выберите один из вариантов</option>
            {eventCityFiltered?.values?.map((city) => (
              <option value={`${city.public_id}#${city.city.id}`} key={city.public_id}>
                {city.address} — {getFormatTime(city.start_time, city.timezone)}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="sourceLabelTicket" label="Источник данных">
          <FormSelect
            onChange={handleChangeField}
            value={formData?.source_name || currentField || ''}
            name="source_name"
            aria-label="Источник данных"
          >
            <option value="">выберите один из вариантов</option>
            {sources.map((source) => (
              <option value={source.value} key={source.value}>
                {source.name}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      {/* Блок с инпутами для различных типов источников */}
      {formData?.source_name === 'external_url' ? (
        <Col md={4}>
          <FloatingLabel controlId="externalUrlLabelOrder" label="Внешняя ссылка">
            <FormControl
              onChange={handleChangeField}
              value={formData.external_url || ''}
              name="external_url"
              type="text"
              placeholder="Внешняя ссылка"
            />
          </FloatingLabel>
        </Col>
      ) : (
        <>
          {isCsv || isExcel ? (
            <Col md={4} className="file-input-container">
              <Form.Control type="file" onChange={handleFileRead} name="file" />
            </Col>
          ) : (
            formData?.source_name !== 'copernico' && (
              <Col md={4}>
                <FloatingLabel controlId="cityCodeLabelOrder" label="Код города">
                  <FormControl
                    onChange={handleChangeField}
                    value={formData.city_code || ''}
                    name="city_code"
                    type="text"
                    placeholder="Код города"
                  />
                </FloatingLabel>
              </Col>
            )
          )}
        </>
      )}

      <Col md={4}>
        <FloatingLabel controlId="photosUrlLabelOrder" label="Ссылка на фотографии">
          <FormControl
            onChange={handleChangeField}
            value={formData.photos_url || ''}
            name="photos_url"
            type="text"
            placeholder="Ссылка на фотографии"
          />
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="leaderResultLabelOrder" label="Основное время">
          <FormSelect
            onChange={handleChangeField}
            value={formData.leader_result || 'individual_time_result'}
            name="leader_result"
            aria-label="Основное время"
          >
            <option value="individual_time_result">Время официальное</option>
            <option value="absolute_time_result">Время чистое</option>
          </FormSelect>
        </FloatingLabel>
      </Col>

      {/* Блок настроек трансляции, отображается только если источник copernico */}
      {formData?.source_name === 'copernico' && (
        <Col md={12}>
          <BroadcastForm
            formData={formData}
            onChangeForm={onChangeForm}
            cityTimezone={selectedEventCity?.timezone || 'Europe/Moscow'}
          />
        </Col>
      )}

      {formData?.source_name === 'copernico' && <CopernicoForm formData={formData} onChangeForm={onChangeForm} />}

      <Col md={12} className="mt-3">
        <div className="card p-3">
          <FormCheck
            type="switch"
            id="leadersSwitch"
            label="Отображать лидеров"
            checked={formData.leaders || false}
            onChange={(e) => onChangeForm({ ...formData, leaders: e.target.checked })}
          />
        </div>
      </Col>
    </Row>
  )
}

export default ResultForm
