import moment from 'moment'
import React from 'react'
import { Card, Col, Form, FormCheck, FormControl, FloatingLabel, InputGroup, Row } from 'react-bootstrap'

const BroadcastForm = ({ formData, onChangeForm, cityTimezone }) => {
  const handleEnabledChange = (e) => {
    const newFormData = { ...formData }

    if (e.target.checked && !newFormData?.broadcast?.until) {
      newFormData.broadcast = {
        ...newFormData.broadcast,
        until: moment().tz(cityTimezone).utc().add(1, 'day').format(),
      }
    }

    onChangeForm({
      ...newFormData,
      broadcast: {
        ...(newFormData.broadcast || {}),
        enabled: e.target.checked,
      },
    })
  }

  const handleUpdateIntervalChange = (e) => {
    const value = parseInt(e.target.value, 10)
    onChangeForm({
      ...formData,
      broadcast: {
        ...(formData.broadcast || {}),
        update_interval: isNaN(value) ? 0 : value,
      },
    })
  }

  const handleUntilChange = (e) => {
    const value = e.target.value
    const formatValue = moment.tz(value, cityTimezone).utc().format()

    onChangeForm({
      ...formData,
      broadcast: {
        ...(formData.broadcast || {}),
        until: formatValue,
      },
    })
  }

  const getFormattedDate = () => {
    if (!formData.broadcast?.until) return ''

    try {
      const until = formData.broadcast.until
      let momentDate

      if (typeof until === 'number' || !isNaN(Number(until))) {
        momentDate = moment.unix(Number(until))

        if (!momentDate.isValid()) {
          momentDate = moment(until)
        }
      } else {
        momentDate = moment(until)
      }

      if (momentDate.isValid()) {
        return momentDate.tz(cityTimezone).format('YYYY-MM-DDTHH:mm:ss')
      }

      return ''
    } catch (error) {
      console.error('Ошибка при форматировании даты:', error)
      return ''
    }
  }

  return (
    <Card className="mt-3 mb-4">
      <Card.Header className="bg-primary text-white">
        <div className="d-flex justify-content-between align-items-center">
          <span>Трансляция</span>
        </div>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={12} className="mb-3">
            <FormCheck
              type="switch"
              id="broadcastEnabledSwitch"
              label="Включить трансляцию"
              checked={formData.broadcast?.enabled || false}
              onChange={handleEnabledChange}
            />
          </Col>

          <Col className="mb-3">
            <Form.Group>
              <InputGroup>
                <FloatingLabel controlId="untilDateLabel" label="Длительность до">
                  <FormControl
                    onChange={handleUntilChange}
                    value={getFormattedDate()}
                    type="datetime-local"
                    disabled={!formData.broadcast?.enabled}
                    placeholder=""
                  />
                </FloatingLabel>
                <InputGroup.Text className="border-0">
                  {cityTimezone ? `UTC ${moment().tz(cityTimezone).format('Z')}` : ''}
                </InputGroup.Text>
              </InputGroup>
            </Form.Group>
          </Col>

          <Col className="mb-3">
            <Form.Group>
              <FloatingLabel controlId="updateIntervalLabel" label="Регулярность обновления (сек)">
                <FormControl
                  type="number"
                  placeholder=""
                  value={formData.broadcast?.update_interval || ''}
                  onChange={handleUpdateIntervalChange}
                  disabled={!formData.broadcast?.enabled}
                  min={1}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  )
}

export default BroadcastForm
