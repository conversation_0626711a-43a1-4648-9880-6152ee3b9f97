import { useEffect, useState } from 'react'
import { Accordion, Col, Floating<PERSON><PERSON><PERSON>, FormSelect, Row } from 'react-bootstrap'

import SortingFields from '../SortingFields/SortingFields'

function SortingMatching({
  formats,
  externalFormats,
  externalFields,
  defaultFormatsData,
  defaultFieldsData,
  onChangeFormatsData,
}) {
  const [formatsData, setFormatsData] = useState([])
  const [activeFormatId, setActiveFormatId] = useState(0)
  const [formatsMap, setFormatsMap] = useState([])

  useEffect(() => {
    if (!defaultFieldsData && formatsData.length === 0 && externalFormats?.length > 0) {
      const newFormats = externalFormats.map((format) => ({ external_format: format }))

      setFormatsData([...newFormats])
      setFormatsMap([...externalFormats])
    } else if (defaultFormatsData?.length > 0) {
      setFormatsData([...defaultFormatsData])

      setFormatsMap(defaultFormatsData.map((format) => format.external_format))
    }
  }, [formats, externalFormats, defaultFieldsData, formatsData.length, defaultFormatsData])

  const handleChangeInnerFormat = (evt, index) => {
    const value = evt.target.value
    const copyFormatsData = [...formatsData]

    copyFormatsData[index].format_public_id = value

    setFormatsData([...copyFormatsData])
    onChangeFormatsData([...copyFormatsData])
  }

  const handleChangeFields = (fields) => {
    if (formatsData.length === 0 || activeFormatId >= formatsData.length) return

    const copyFormatsData = [...formatsData]
    copyFormatsData[activeFormatId].fields_list = fields

    setFormatsData([...copyFormatsData])
    onChangeFormatsData([...copyFormatsData])
  }

  const isEditMode = Boolean(defaultFormatsData?.length > 0)

  const shouldRender = isEditMode
    ? externalFormats?.length > 0 && externalFields?.length > 0
    : formats?.length > 0 && externalFormats?.length > 0 && externalFields?.length > 0

  if (!shouldRender) return null

  return (
    <Row className="mb-4">
      <Col>
        <Accordion defaultActiveKey={0}>
          {formatsMap.map((format, index) => (
            <Accordion.Item eventKey={index} key={format}>
              <Accordion.Header onClick={() => setActiveFormatId(index)}>{format}</Accordion.Header>
              <Accordion.Body>
                {!defaultFormatsData && (
                  <Row className="mb-3">
                    <Col md={5}>
                      <FloatingLabel controlId={`format-select-${index}`} label="Внутренний формат">
                        <FormSelect
                          onChange={(evt) => handleChangeInnerFormat(evt, index)}
                          name="format_public_id"
                          aria-label="Внутренний формат"
                          value={formatsData[index]?.format_public_id || ''}
                        >
                          <option value="">выберите один из вариантов</option>
                          {formats?.map((format) => (
                            <option value={format.public_id} key={format.public_id}>
                              {format.title}
                            </option>
                          ))}
                        </FormSelect>
                      </FloatingLabel>
                    </Col>
                  </Row>
                )}

                <Row>
                  <Col>
                    <SortingFields
                      fields={externalFields}
                      fieldsData={formatsData[index]?.fields_list}
                      defaultFieldsData={defaultFieldsData}
                      onChangeFields={handleChangeFields}
                    />
                  </Col>
                </Row>
              </Accordion.Body>
            </Accordion.Item>
          ))}
        </Accordion>
      </Col>
    </Row>
  )
}

export default SortingMatching
