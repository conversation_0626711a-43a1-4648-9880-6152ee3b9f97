import { useState, useEffect } from 'react'
import { Container, Row, Col, Form, Button, InputGroup, Card, Badge } from 'react-bootstrap'

const CopernicoForm = ({ formData, onChangeForm }) => {
  const [distanceIds, setDistanceIds] = useState([])
  const [distanceUrls, setDistanceUrls] = useState([])

  useEffect(() => {
    if (formData.distance_ids) {
      if (Array.isArray(formData.distance_ids)) {
        setDistanceIds(formData.distance_ids)
      } else {
        setDistanceIds(formData.distance_ids.split(/[\s,]+/).filter((id) => id.length > 0))
      }
    }

    if (formData.distances_urls) {
      if (Array.isArray(formData.distances_urls)) {
        setDistanceUrls(formData.distances_urls)
      } else {
        setDistanceUrls(formData.distances_urls.split(/[\s,]+/).filter((url) => url.length > 0))
      }
    }
  }, [formData.distance_ids, formData.distances_urls])

  const updateParentForm = (newIds, newUrls) => {
    onChangeForm({
      ...formData,
      distance_ids: newIds,
      distances_urls: newUrls,
    })
  }

  const handleDistanceIdChange = (index, value) => {
    const newIds = [...distanceIds]
    newIds[index] = value
    setDistanceIds(newIds)
    updateParentForm(newIds, distanceUrls)
  }

  const handleDistanceUrlChange = (index, value) => {
    const newUrls = [...distanceUrls]
    newUrls[index] = value
    setDistanceUrls(newUrls)
    updateParentForm(distanceIds, newUrls)
  }

  const addDistanceField = () => {
    const newIds = [...distanceIds, '']
    const newUrls = [...distanceUrls, '']
    setDistanceIds(newIds)
    setDistanceUrls(newUrls)
    updateParentForm(newIds, newUrls)
  }

  const removeDistanceField = (index) => {
    const newIds = [...distanceIds]
    const newUrls = [...distanceUrls]
    newIds.splice(index, 1)
    newUrls.splice(index, 1)
    setDistanceIds(newIds)
    setDistanceUrls(newUrls)
    updateParentForm(newIds, newUrls)
  }

  return (
    <Container fluid className="mt-3 mb-4">
      <Row className="justify-content-center mb-4">
        <Col md={12}>
          <Card>
            <Card.Header className="bg-primary text-white">
              <div className="d-flex justify-content-between align-items-center">
                <span>Дистанции и URLs</span>
                <Badge bg="light" text="dark" pill>
                  {distanceIds.length}
                </Badge>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <Button variant="success" onClick={addDistanceField} className="w-100">
                  Добавить дистанцию и URL
                </Button>
              </div>

              {distanceIds.map((id, index) => (
                <Row key={index} className="mb-3 align-items-center">
                  <Col md={4}>
                    <InputGroup>
                      <InputGroup.Text>ID</InputGroup.Text>
                      <Form.Control
                        className="border"
                        type="text"
                        value={id}
                        onChange={(e) => handleDistanceIdChange(index, e.target.value)}
                        placeholder="ID дистанции"
                      />
                    </InputGroup>
                  </Col>

                  <Col>
                    <InputGroup>
                      <InputGroup.Text>URL</InputGroup.Text>
                      <Form.Control
                        className="border"
                        type="text"
                        value={distanceUrls[index] || ''}
                        onChange={(e) => handleDistanceUrlChange(index, e.target.value)}
                        placeholder="URL дистанции"
                      />
                    </InputGroup>
                  </Col>

                  <Col md="auto">
                    <Button variant="outline-danger" onClick={() => removeDistanceField(index)} className="w-100">
                      <i className="bi bi-trash3-fill" />
                    </Button>
                  </Col>
                </Row>
              ))}

              {distanceIds.length === 0 && (
                <div className="text-center text-muted py-4">
                  Нет добавленных дистанций. Нажмите кнопку &quot;Добавить дистанцию и URL&quot;.
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  )
}

export default CopernicoForm
