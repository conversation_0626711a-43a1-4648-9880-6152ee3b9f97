import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { Button, Col, Row, Spinner } from 'react-bootstrap'
import { Link, useNavigate } from 'react-router-dom'

import { APIRoute } from '@/const'
import { useGetEventFormats } from '@/features/events/api/getEventFormats'
import { useSendResultsCsv, useSendResultsExcel } from '@/features/results/api'
import { queryClient } from '@/lib/react-query'

import ResultForm from './components/ResultForm'
import SortingMatching from './components/SortingMatching/SortingMatching'
import Layout from '../../components/Layout/Layout'
import SuccessModal from '../../components/Modal/SuccessModal/SuccessModal'

function ResultsCreateScreen() {
  const [{ loading: isLoading }, api] = useAxios(
    {
      url: APIRoute.RESULTS,
    },
    {
      manual: true,
    }
  )
  const [{ loading: isLoadingList }, apiList] = useAxios(
    {
      url: APIRoute.MATCHING_RESULTS,
      method: 'POST',
    },
    {
      manual: true,
    }
  )
  const [formData, setFormData] = useState({
    leader_result: 'individual_time_result',
    leaders: false,
  })
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [buttonLoading, setButtonLoading] = useState('')
  const [externalFormats, setExternalFormats] = useState([])
  const [fields, setFields] = useState([])
  const [matchFormatsData, setMatchFormatsData] = useState([])
  const [file, setFile] = useState(null)
  const [eventCityId, setEventCityId] = useState('')
  const [eventId, setEventId] = useState('')
  const [isExcel, setIsExcel] = useState(false)

  const sendResultsCsvMutation = useSendResultsCsv()
  const sendResultsExcelMutation = useSendResultsExcel()
  const eventFormatsQuery = useGetEventFormats({}, eventCityId)
  const formats = eventFormatsQuery?.data?.data?.values || []

  // Получаем данные о форматах результатов из кэша React Query
  const resultsFormat = queryClient.getQueryData(['resultsFormat'])

  const navigate = useNavigate()

  useEffect(() => {
    if (resultsFormat) {
      setFields(resultsFormat.fields_list || [])
      setExternalFormats(resultsFormat.formats || [])
    }
  }, [resultsFormat])

  const handleSubmitForm = (method) => {
    const newFormData = { ...formData, event_city: { ...formData.event_city } }
    delete newFormData.event_city.id

    api({ method: method, data: newFormData }).then((r) => {
      setButtonLoading('')

      if (r.status === 200) {
        setShowSuccessModal(true)
        if (r?.data?.formats?.length > 0) {
          setExternalFormats(r.data.formats)
          setFields(r.data.fields_list)
        }
      }
    })
  }

  const handleCreateResults = () => {
    if (file) {
      const formData = {
        file_content: file,
        external_url: '',
        event_type: {
          public_id: eventId,
        },
        event_city: {
          public_id: eventCityId,
        },
      }
      if (isExcel) {
        sendResultsExcelMutation.mutate(formData, {
          onSuccess: (data) => {
            setFields(data.data.fields_list || [])
            setExternalFormats(data.data.formats || [])
          },
        })
      } else {
        sendResultsCsvMutation.mutate(formData, {
          onSuccess: (data) => {
            setFields(data.data.fields_list || [])
            setExternalFormats(data.data.formats || [])
          },
        })
      }
    } else {
      handleSubmitForm('POST')
      setButtonLoading('create')
    }
  }

  const handleSubmitMatchingList = () => {
    const body = {
      event_city: {
        public_id: formData.event_city.public_id,
      },
      formats: [...matchFormatsData],
    }

    apiList({ data: body }).then((r) => {
      if (r.status === 200) {
        setShowSuccessModal(true)
        queryClient.setQueryData(['resultsFormat'], null)
        setFormData({})
        setExternalFormats([])
        setFields([])
        setMatchFormatsData([])
        setTimeout(() => navigate('/results'), 1100)
      }
    })
  }

  return (
    <>
      <Layout>
        <Row className="mb-3">
          <Col>
            <Button as={Link} to={'/results'} variant="outline-secondary">
              <i className="bi bi-arrow-left me-2" />
              Вернуться к списку результатов
            </Button>
          </Col>
        </Row>
        <Row className="mb-3">
          <h3>Добавление результатов</h3>
        </Row>

        <ResultForm
          setFile={setFile}
          isExcel={isExcel}
          setIsExcel={setIsExcel}
          setEventId={setEventId}
          setEventCityId={setEventCityId}
          formData={formData}
          onChangeForm={setFormData}
        />

        <Row>
          <Col md={{ offset: 1, span: 10 }}>
            <SortingMatching
              formats={formats}
              externalFormats={externalFormats}
              externalFields={fields}
              onChangeFormatsData={setMatchFormatsData}
            />
          </Col>
        </Row>

        <Row className="justify-content-md-center mb-4">
          <Col className="d-grid" md={2}>
            {fields?.length === 0 || matchFormatsData?.length === 0 ? (
              <Button
                onClick={handleCreateResults}
                variant="success"
                type="button"
                size="lg"
                disabled={isLoading || sendResultsCsvMutation.isLoading || sendResultsExcelMutation.isLoading}
              >
                {(isLoading && buttonLoading === 'create') ||
                sendResultsCsvMutation.isLoading ||
                sendResultsExcelMutation.isLoading ? (
                  <Spinner animation="grow" size="sm" />
                ) : (
                  'Добавить'
                )}
              </Button>
            ) : (
              <Button onClick={handleSubmitMatchingList}>
                {isLoadingList ? <Spinner animation="grow" size="sm" /> : 'Отправить'}
              </Button>
            )}
          </Col>
        </Row>
      </Layout>

      <SuccessModal show={showSuccessModal} handleCloseModal={setShowSuccessModal} description="Данные сохранены." />
    </>
  )
}

export default ResultsCreateScreen
