import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { Alert, ButtonGroup, Col, Dropdown, Floating<PERSON>abel, FormSelect, Row } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import Loader from '@/components/Loader/Loader'
import PageSearch from '@/components/PageSearch/PageSearch'

import { useGetEvents } from '@/features/events/api/getEvents'

import { ticketsOnlineTableData } from './onlineResultsData'
import styles from './OnlineResultsScreen.module.scss'
import Layout from '../../components/Layout/Layout'
import SuccessModal from '../../components/Modal/SuccessModal/SuccessModal'
import TableTemplate from '../../components/TableTemplate/TableTemplate'
import { APIRoute } from '../../const'

function OnlineResultsScreen() {
  const [{ loading: isLoadingTickets }, getTickets] = useAxios(
    {
      method: 'GET',
    },
    {
      manual: true,
    }
  )
  const [, api] = useAxios({ method: 'PUT' }, { manual: true })
  const { data: eventsData } = useGetEvents()
  const events = eventsData?.data?.values || []
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [tickets, setTickets] = useState(null)
  const [isNoneTickets, setIsNoneTickets] = useState(false)
  const [successModal, setSuccessModal] = useState(false)
  const [filteredValues, setFilteredValues] = useState([])
  const [valuesRender, setValuesRender] = useState([])

  useEffect(() => {
    if (isNoneTickets) {
      setTimeout(() => {
        setIsNoneTickets(false)
      }, 4000)
    }
  }, [isNoneTickets])

  useEffect(() => {
    if (tickets) {
      setFilteredValues(tickets)
    }
  }, [tickets])

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = events.find((event) => event.public_id === eventId)

    setIsNoneTickets(false)

    if (event) {
      setSelectedEvent(event)
      handleGetTickets(eventId)
    } else {
      setSelectedEvent(null)
    }
  }

  const handleGetTickets = (eventId) => {
    getTickets({ url: `${APIRoute.ONLINE_RESULTS}/${eventId}` }).then((r) => {
      if (r.status === 200 && r.data?.values.length > 0) {
        setTickets(r.data.values)
      } else {
        setIsNoneTickets(true)
      }
    })
  }

  const returnActionsTable = (ticket) => {
    return (
      <td onClick={(evt) => evt.stopPropagation()}>
        <Dropdown drop="start">
          <Dropdown.Toggle as={ButtonGroup} bsPrefix={`${styles.hideArrow}`} variant="success" id="dropdown-basic">
            &bull; &bull; &bull;
          </Dropdown.Toggle>

          <Dropdown.Menu>
            {ticket?.online?.confirmed ? (
              <Dropdown.Item className={styles.danger} as="button" onClick={() => handleConfirmResults(ticket)}>
                Отменить подтверждение результата
              </Dropdown.Item>
            ) : (
              <Dropdown.Item className={styles.success} as="button" onClick={() => handleConfirmResults(ticket)}>
                Подтвердить результат
              </Dropdown.Item>
            )}
            {ticket?.online?.package_sent ? (
              <Dropdown.Item className={styles.danger} as="button" onClick={() => handleConfirmDelivery(ticket)}>
                Отменить подтверждение доставки
              </Dropdown.Item>
            ) : (
              <Dropdown.Item className={styles.success} as="button" onClick={() => handleConfirmDelivery(ticket)}>
                Подтвердить доставку
              </Dropdown.Item>
            )}
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  const handleConfirmResults = (ticket) => {
    const body = {
      confirmed: !ticket.online.confirmed,
      public_id: ticket.public_id,
    }

    api({ url: '/api/admin/online/ticket', data: body }).then((r) => {
      if (r.status === 200) {
        handleGetTickets(selectedEvent.public_id)
        setSuccessModal(true)
      }
    })
  }

  const handleConfirmDelivery = (ticket) => {
    const body = {
      package_sent: true,
      public_id: ticket.public_id,
    }

    api({ url: '/api/admin/online/ticket', data: body }).then((r) => {
      if (r.status === 200) {
        handleGetTickets(selectedEvent.public_id)
        setSuccessModal(true)
      }
    })
  }

  return (
    <Layout title="Онлайн результаты">
      <Row className="mb-3">
        <Col md={3}>
          <FloatingLabel controlId="eventLabelTicket" label="Событие">
            <FormSelect onChange={handleChangeEvent} name="event_public_id" aria-label="Событие">
              <option value="">выберите один из вариантов</option>
              {events.map((event) => (
                <option value={event.public_id} key={event.public_id}>
                  {event.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col>
          {isNoneTickets && (
            <Alert variant="danger" dismissible onClose={setIsNoneTickets}>
              Нет билетов для выбранного события.
            </Alert>
          )}
        </Col>
        {selectedEvent && (
          <Col md="auto">
            <DownloadButton
              url={`/api/admin/xlsx/online/tickets/${selectedEvent.public_id}`}
              fileName={`online_results_${selectedEvent.public_id}.xlsx`}
              label="Скачать"
            />
          </Col>
        )}
      </Row>

      <Row className="mb-3 align-items-center">
        <Col>
          <PageSearch values={tickets} setValues={setFilteredValues} />
        </Col>
      </Row>

      <Loader isLoading={isLoadingTickets}>
        {valuesRender?.length > 0 && (
          <>
            <Row className={' mb-4'}>
              <Col>
                <div className={`${styles.ticketsWrap}`} style={{ overflow: 'auto' }}>
                  <div style={{ minWidth: '2200px' }}>
                    <TableTemplate data={ticketsOnlineTableData} values={valuesRender} actions={returnActionsTable} />
                  </div>
                </div>
              </Col>
            </Row>
          </>
        )}

        <AdvancedPagination values={filteredValues} setValues={setValuesRender} limitValues={100} />
      </Loader>

      <SuccessModal show={successModal} handleCloseModal={setSuccessModal} />
    </Layout>
  )
}

export default OnlineResultsScreen
