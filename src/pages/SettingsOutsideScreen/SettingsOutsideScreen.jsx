import useAxios from 'axios-hooks'
import { useEffect } from 'react'
import { Button, Col, Row, Tab, Tabs } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import { notificationCardData } from './settingsOutsideScreenData'
import Layout from '../../components/Layout/Layout'
import ListCard from '../../components/ListCard/ListCard'
import Loader from '../../components/Loader/Loader'
import { APIRoute } from '../../const'

function SettingsOutsideScreen() {
  const navigate = useNavigate()

  const [{ data: settings, loading: isLoadingSettings }, apiSettings] = useAxios(
    {
      url: APIRoute.ACTIONS_SETTINGS,
      method: 'GET',
    },
    { manual: true }
  )

  useEffect(() => {
    apiSettings()
  }, [apiSettings])

  const returnEditBtn = (item) => {
    return (
      <Button onClick={() => navigate(`form/${item._id}`)} variant="link" size="sm">
        изменить
      </Button>
    )
  }

  return (
    <Layout title="Настройки" href="settings/form">
      <Row>
        <Col>
          <Tabs defaultActiveKey="notifications" className="mb-3">
            <Tab title="Уведомления" eventKey="notifications">
              <Loader isLoading={isLoadingSettings}>
                <Row className="g-3">
                  {settings?.values?.map((item) => (
                    <Col md={4} key={item._id}>
                      <ListCard
                        data={notificationCardData}
                        event={item}
                        actions={() => returnEditBtn(item)}
                        xsLabel={4}
                        xsValue={8}
                      />
                    </Col>
                  ))}
                </Row>
              </Loader>
            </Tab>
          </Tabs>
        </Col>
      </Row>
    </Layout>
  )
}

export default SettingsOutsideScreen
