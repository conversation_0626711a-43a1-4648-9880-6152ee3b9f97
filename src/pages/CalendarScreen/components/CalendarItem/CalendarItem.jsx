import useAxios from 'axios-hooks'
import { useState } from 'react'
import { <PERSON><PERSON>, Card, Col, ListGroup, Row } from 'react-bootstrap'

import styles from './CalendarItem.module.scss'
import ConfirmDeleteModal from '../../../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import { APIRoute } from '../../../../const'
import { useToast } from '../../../../hooks/useToast'
import { getEventCategoryName } from '../../../../utils/calendar'
import { unixToMoment } from '../../../../utils/date'
import {
  calendarChanges,
  calendarStatus,
  category,
  distance,
  event_subtype,
  event_type,
} from '../../calendarScreenData'
import CalendarPopup from '../CalendarPopup/CalendarPopup'

function CalendarItem({ item, onUpdateCalendarList }) {
  const [showPopup, setShowPopup] = useState(false)
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState('')

  const openToast = useToast()

  const [, apiDelete] = useAxios(
    {
      url: `${APIRoute.ACTIONS_BANNERS}`,
      method: 'DELETE',
    },
    { manual: true }
  )

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedDeleteItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ url: `${APIRoute.ACTIONS_CALENDAR}/${selectedDeleteItem.public_id}` }).then((r) => {
      if (r.status === 200) {
        openToast.success({ message: 'Событие удалено' })
        setSelectedDeleteItem('')
        onUpdateCalendarList()
      }
    })
  }

  return (
    <Card>
      <Card.Header>
        {/*<Card.Title className={styles.title}>{item.title}</Card.Title>*/}
        <Card.Subtitle>
          {item.title} ({item.changes ? getEventCategoryName(item.changes, calendarChanges) : 'Без изменений'})
        </Card.Subtitle>
      </Card.Header>
      <ListGroup>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Дата старта:</span>
            </Col>
            <Col xs={7}>{item.start_date && unixToMoment(item.start_date).format('DD.MM.YYYY')}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Дата окончания:</span>
            </Col>
            <Col xs={7}>{item.end_date && unixToMoment(item.end_date).format('DD.MM.YYYY')}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Место проведения:</span>
            </Col>
            <Col xs={7}>{item.place}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Тип события:</span>
            </Col>
            <Col xs={7}>{getEventCategoryName(item.event_type, event_type)}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Статус:</span>
            </Col>
            <Col xs={7}>{getEventCategoryName(item.status, calendarStatus)}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Вид:</span>
            </Col>
            <Col xs={7}>{getEventCategoryName(item.event_subtype, event_subtype)}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Дистанция:</span>
            </Col>
            <Col xs={7}>{getEventCategoryName(item.distance, distance)}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Категория:</span>
            </Col>
            <Col xs={7}>{getEventCategoryName(item.category, category)}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Флаг:</span>
            </Col>
            <Col xs={7}>{item.country}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Описание:</span>
            </Col>
            <Col xs={7}>{item.desc}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Условия участия:</span>
            </Col>
            <Col xs={7}>{item.limits}</Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col xs={5}>
              <span className={styles.listLabel}>Ссылка:</span>
            </Col>
            <Col xs={7}>
              {
                <a href={item.link} target="_blank" rel="noopener noreferrer">
                  <span>{item.link}</span>
                </a>
              }
            </Col>
          </Row>
        </ListGroup.Item>
        <ListGroup.Item className={styles.listItem}>
          <Row>
            <Col />
            <Col md="auto">
              <Button onClick={() => setShowPopup(true)} size="sm">
                изменить
              </Button>
            </Col>
            <Col md="auto">
              <Button onClick={() => handleConfirmDelete(item)} variant="danger" size="sm">
                удалить
              </Button>
            </Col>
          </Row>
        </ListGroup.Item>
      </ListGroup>

      {showPopup && (
        <CalendarPopup item={item} onHide={() => setShowPopup(false)} onUpdateCalendarList={onUpdateCalendarList} />
      )}

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItem}
        onDeleteItem={handleDeleteItem}
        text="Удалить событие"
      />
    </Card>
  )
}

export default CalendarItem
