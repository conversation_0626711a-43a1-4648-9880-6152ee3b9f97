import useAxios from 'axios-hooks'
import moment from 'moment'
import { useEffect, useState, useCallback } from 'react'
import { Button, Col, Modal, Row, FormControl, FormGroup, Form, FormLabel } from 'react-bootstrap'

import QuillEditor from '@/components/QuillEditor/QuillEditor'

import { APIRoute } from '@/const'
import {
  event_type,
  event_subtype,
  countries,
  category,
  calendarStatus,
  distance,
  calendarChanges,
} from '@/pages/CalendarScreen/calendarScreenData'
import { unixToMoment } from '@/utils/date'
import { updateFormData } from '@/utils/forms'

export default function CalendarPopup({ item, onHide, onUpdateCalendarList }) {
  const [defaultStoryData, setDefaultStoryData] = useState({})
  const [calendarStoryData, setCalendarStoryData] = useState({})

  const [, api] = useAxios(
    {
      url: APIRoute.ACTIONS_CALENDAR,
      method: 'POST',
    },
    { manual: true }
  )

  // для старых событий в которых в event_type, вместо массива приходит строка
  useEffect(() => {
    if (item?.event_type && typeof item.event_type === 'string')
      setCalendarStoryData((prev) => ({ ...prev, event_type: [item.event_type] }))

    if (item) {
      setDefaultStoryData({ ...item })
    } else {
      setCalendarStoryData((prev) => ({
        ...prev,
        start_date: new Date(),
        changes: 'no_change',
      }))
    }
  }, [item])

  const handleChangeField = (evt) => {
    updateFormData(evt, calendarStoryData, setCalendarStoryData, setDefaultStoryData)
  }

  const handleEditorChange = useCallback((html) => {
    setCalendarStoryData((prev) => ({ ...prev, html: html }))
  }, [])

  const onSubmit = (evt) => {
    evt.preventDefault()

    const finalEvents = {
      ...calendarStoryData,
    }

    if (item) {
      api({
        url: `${APIRoute.ACTIONS_CALENDAR}/${item.public_id}`,
        method: 'PUT',
        data: finalEvents,
      }).then((r) => {
        if (r.status === 200) {
          onUpdateCalendarList()
          onHide()
        }
      })
    } else {
      api({ data: finalEvents }).then((r) => {
        if (r.status === 200) {
          onUpdateCalendarList()
          onHide()
        }
      })
    }
  }

  const handleChangeCheckbox = (evt) => {
    const isChecked = evt.target.checked
    const name = evt.target.name
    const value = evt.target.value
    const newCalendarStory = { ...calendarStoryData }
    let newArr = []

    if (name in calendarStoryData) {
      newArr = [...calendarStoryData[name]]
    } else if (name in defaultStoryData) {
      newArr = [...defaultStoryData[name]]
    }

    if (isChecked) {
      newArr.push(value)
      setCalendarStoryData((prev) => ({ ...prev, [name]: [...newArr] }))
    } else {
      const filteredArr = newArr.filter((item) => item !== value)

      if (filteredArr.length === 0) {
        delete newCalendarStory[name]
        setCalendarStoryData({ ...newCalendarStory })
      } else {
        setCalendarStoryData((prev) => ({ ...prev, [name]: [...filteredArr] }))
      }
    }
  }

  const checkCheckbox = (name, value) => {
    if (calendarStoryData[name]) {
      return calendarStoryData[name] && calendarStoryData[name].includes(value)
    } else {
      return defaultStoryData[name] && defaultStoryData[name].includes(value)
    }
  }

  const getDateValue = (data, defaultData, field) => {
    if (field in data) {
      return moment(data[field]).format('YYYY-MM-DD')
    } else {
      return unixToMoment(defaultData[field]).format('YYYY-MM-DD')
    }
  }

  return (
    <>
      <Modal show onHide={onHide} size="xl">
        <Modal.Header closeButton className="ml-3 mr-3">
          <Modal.Title>{item ? 'Редактирование события' : 'Создание события'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={onSubmit} className="mr-3 ml-3 mb-1">
            <FormGroup className="mb-2">
              <Row>
                <Col>
                  <FormControl
                    type="text"
                    name="title"
                    placeholder="Заголовок"
                    defaultValue={defaultStoryData?.title}
                    onChange={handleChangeField}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <Row>
                <Col>
                  <Form.Select
                    name="country"
                    placeholder="Флаг"
                    value={calendarStoryData?.country || defaultStoryData?.country}
                    onChange={handleChangeField}
                    required
                  >
                    <option value="">Флаг</option>
                    {countries.map(({ value, name }) => (
                      <option value={value} key={value}>
                        {name}
                      </option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <Row>
                <Col>
                  <FormControl
                    type="text"
                    name="place"
                    placeholder="Место проведения"
                    defaultValue={defaultStoryData?.place}
                    onChange={handleChangeField}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <Row>
                <Col>
                  <FormControl
                    type="text"
                    name="link"
                    placeholder="Ссылка"
                    defaultValue={defaultStoryData?.link}
                    onChange={handleChangeField}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <Row>
                <Col>
                  <FormControl
                    type="text"
                    name="desc"
                    placeholder="Описание"
                    defaultValue={defaultStoryData?.desc}
                    onChange={handleChangeField}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup className="mb-2" controlId="dateForm">
              <Row>
                <Col>
                  <FormControl
                    type="text"
                    name="limits"
                    placeholder="Условия участия"
                    defaultValue={defaultStoryData?.limits}
                    onChange={handleChangeField}
                  />
                </Col>
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <Row>
                <Col>
                  <Form.Select
                    name="changes"
                    placeholder="Изменения"
                    value={calendarStoryData?.changes || defaultStoryData?.changes}
                    onChange={handleChangeField}
                    required
                  >
                    {calendarChanges.map(({ value, name }) => (
                      <option value={value} key={value}>
                        {name}
                      </option>
                    ))}
                  </Form.Select>
                </Col>
              </Row>
            </FormGroup>

            <p>Даты проведения:</p>
            <Row>
              <Col>
                <FormGroup className="mb-2" controlId="startDateForm">
                  <Row>
                    <Col md="auto">
                      <FormLabel>от:</FormLabel>
                    </Col>
                    <Col>
                      <FormControl
                        type="date"
                        name="start_date"
                        value={getDateValue(calendarStoryData, defaultStoryData, 'start_date')}
                        onChange={handleChangeField}
                      />
                    </Col>
                  </Row>
                </FormGroup>
              </Col>
              <Col>
                <FormGroup className="mb-2" controlId="endDateForm">
                  <Row>
                    <Col md="auto">
                      <FormLabel>до:</FormLabel>
                    </Col>
                    <Col>
                      <FormControl
                        type="date"
                        name="end_date"
                        value={getDateValue(calendarStoryData, defaultStoryData, 'end_date')}
                        onChange={handleChangeField}
                      />
                    </Col>
                  </Row>
                </FormGroup>
              </Col>
            </Row>

            <FormGroup
              className="mb-2 pt-2"
              style={{ borderBottom: '1px solid #ced4da', borderTop: '1px solid #ced4da' }}
            >
              <Row>
                <Col md="auto">Тип события:</Col>
              </Row>
              <Row className="d-flex flex-wrap mb-2">
                {event_type.map(({ value, name }) => (
                  <Col md="auto" key={value}>
                    <Form.Check
                      type="checkbox"
                      id={value}
                      label={name}
                      name="event_type"
                      value={value}
                      onChange={handleChangeCheckbox}
                      checked={checkCheckbox('event_type', value)}
                    />
                  </Col>
                ))}
              </Row>
            </FormGroup>

            <FormGroup className="mb-2" style={{ borderBottom: '1px solid #ced4da' }}>
              <Row>
                <Col md="auto">Статус:</Col>
              </Row>
              <Row className="d-flex flex-wrap mb-2">
                {calendarStatus.map(({ value, name }) => (
                  <Col md="auto" key={value}>
                    <Form.Check
                      type="checkbox"
                      id={value}
                      label={name}
                      name="status"
                      value={value}
                      onChange={handleChangeCheckbox}
                      checked={checkCheckbox('status', value)}
                    />
                  </Col>
                ))}
              </Row>
            </FormGroup>

            <FormGroup className="mb-2" style={{ borderBottom: '1px solid #ced4da' }}>
              <Row>
                <Col md="auto">Вид:</Col>
              </Row>
              <Row className="d-flex flex-wrap mb-2">
                {event_subtype.map(({ value, name }) => (
                  <Col md="auto" key={value}>
                    <Form.Check
                      type="checkbox"
                      id={value}
                      label={name}
                      name="event_subtype"
                      value={value}
                      onChange={handleChangeCheckbox}
                      checked={checkCheckbox('event_subtype', value)}
                    />
                  </Col>
                ))}
              </Row>
            </FormGroup>

            <FormGroup className="mb-2" style={{ borderBottom: '1px solid #ced4da' }}>
              <Row>
                <Col md="auto">Дистанция:</Col>
              </Row>
              <Row className="d-flex flex-wrap mb-2">
                {distance.map(({ value, name }) => (
                  <Col md="auto" key={value}>
                    <Form.Check
                      type="checkbox"
                      id={value}
                      label={name}
                      name="distance"
                      value={value}
                      onChange={handleChangeCheckbox}
                      checked={checkCheckbox('distance', value)}
                    />
                  </Col>
                ))}
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <Row>
                <Col md="auto">Категория:</Col>
              </Row>
              <Row className="d-flex flex-wrap mb-2">
                {category.map(({ value, name }) => (
                  <Col md="auto" key={value}>
                    <Form.Check
                      type="checkbox"
                      id={value}
                      label={name}
                      name="category"
                      value={value}
                      onChange={handleChangeCheckbox}
                      checked={checkCheckbox('category', value)}
                    />
                  </Col>
                ))}
              </Row>
            </FormGroup>

            <FormGroup className="mb-2">
              <QuillEditor
                value={calendarStoryData.html || defaultStoryData.html || ''}
                onChange={handleEditorChange}
                placeholder="Введите описание"
              />
            </FormGroup>

            <div className="pt-3 d-flex justify-content-around">
              <Button variant="outline-primary" type="submit">
                Сохранить
              </Button>
              <Button variant="outline-danger" onClick={onHide}>
                Отменить
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  )
}
