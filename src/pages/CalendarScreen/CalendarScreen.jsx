import useAxios from 'axios-hooks'
import React from 'react'
import { Col, Row } from 'react-bootstrap'

import CalendarItem from './components/CalendarItem/CalendarItem'
import CalendarPopup from './components/CalendarPopup/CalendarPopup'
import Layout from '../../components/Layout/Layout'
import PagePreloader from '../../components/PagePreloader/PagePreloader'
import { APIRoute } from '../../const'

function CalendarScreen() {
  const [showPopup, setShowPopup] = React.useState(false)

  const [{ data, loading: isLoading }, api] = useAxios({
    url: APIRoute.GET_CALENDAR_LIST,
    method: 'GET',
  })

  const handleClickAddButton = () => {
    setShowPopup(true)
  }

  return (
    <Layout title="Календарь" onClickAddButton={handleClickAddButton}>
      <PagePreloader isLoading={isLoading}>
        <Row className="mb-4 g-3 align-items-start">
          {data?.map((item) => (
            <Col md="4" key={item._id}>
              <CalendarItem item={item} onUpdateCalendarList={api} key={item.public_id} />
            </Col>
          ))}
        </Row>
      </PagePreloader>

      {showPopup && <CalendarPopup onHide={() => setShowPopup(false)} onUpdateCalendarList={api} />}
    </Layout>
  )
}

export default CalendarScreen
