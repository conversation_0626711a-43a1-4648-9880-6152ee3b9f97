import useAxios from 'axios-hooks'
import { useEffect } from 'react'
import { Card, Col, Dropdown, Row } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import styles from './PagesScreen.module.scss'
import Layout from '../../components/Layout/Layout'
import PagePreloader from '../../components/PagePreloader/PagePreloader'
import { APIRoute, AppRoute } from '../../const'
import { useToast } from '../../hooks/useToast'
import { sliceNewsSubtitle, times } from '../../utils/common'
import { checkAndSetImgUrl, getImageSrc } from '../../utils/images'

function PagesScreen() {
  const [{ data: pages, loading: isLoading }, apiGetPages] = useAxios(
    {
      url: APIRoute.GET_PAGES,
      method: 'GET',
    },
    { manual: true }
  )
  const [, apiDeletePage] = useAxios(
    {
      url: APIRoute.ACTIONS_PAGE,
      method: 'DELETE',
    },
    { manual: true }
  )

  useEffect(() => {
    apiGetPages()
  }, [apiGetPages])

  const navigate = useNavigate()
  const openToast = useToast()

  const handleDeletePage = (public_id) => {
    const body = {
      public_id: public_id,
    }

    apiDeletePage({ data: body }).then((r) => {
      if (r.status === 200 && r?.data?.message) {
        openToast.success({ message: 'Страница удалена' })
        apiGetPages()
      }
    })
  }

  return (
    <Layout title="Страницы" href="pages/form">
      <PagePreloader isLoading={isLoading}>
        <Row className="g-3 mb-5 justify-content-start">
          {pages?.map((item) => (
            <Col style={{ minWidth: '300px', maxWidth: '320px' }} key={item.public_id}>
              <Card className={styles.card} style={{ height: '100%' }} key={item.public_id}>
                <Card.Header>
                  <Row>
                    <Col />
                    <Col md="auto">
                      <Dropdown>
                        <Dropdown.Toggle
                          bsPrefix=" "
                          variant="outline-secondary border-0"
                          id="dropdown-basic"
                          size="sm"
                        >
                          <i className="bi bi-three-dots" />
                        </Dropdown.Toggle>

                        <Dropdown.Menu>
                          <Dropdown.Item onClick={() => navigate(`${AppRoute.PAGES_FORM}/${item.public_id}`)}>
                            <i className="bi bi-pencil me-2" />
                            Изменить
                          </Dropdown.Item>
                          <Dropdown.Item className="text-danger" onClick={() => handleDeletePage(item.public_id)}>
                            <i className="bi bi-trash me-2" />
                            Удалить
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>
                    </Col>
                  </Row>
                </Card.Header>
                <Card.Body>
                  <div className={styles.imgWrapper}>
                    {item.banners && (
                      <Card.Img
                        className={styles.img}
                        onError={checkAndSetImgUrl}
                        variant="top"
                        src={getImageSrc(item.banners.small) || ''}
                      />
                    )}
                  </div>
                  <div className={styles.content}>
                    <Card.Title className={styles.cardTitle}>{item.headline}</Card.Title>
                    <Card.Subtitle className={styles.subTitle}>{sliceNewsSubtitle(item.subtitle)}</Card.Subtitle>
                  </div>
                </Card.Body>
                <Card.Footer>
                  <Row>
                    <Col className="d-flex align-items-center">
                      <small className={styles.date}>{times.getFullDate(item.publication_date)}</small>
                    </Col>
                  </Row>
                </Card.Footer>
              </Card>
            </Col>
          ))}
        </Row>
      </PagePreloader>
    </Layout>
  )
}

export default PagesScreen
