import { useState } from 'react'
import { But<PERSON>, Col, FloatingLabe<PERSON>, Form, FormControl, Row } from 'react-bootstrap'
import { Link, useNavigate } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { MB_IN_B } from '@/const'
import { useCreateDocument, useGetSectionLists } from '@/features/files/api'
import { useToast } from '@/hooks/useToast'
import { convertBase64 } from '@/utils/common'
import { updateFormData } from '@/utils/forms'

function DocumentsFormScreen() {
  const { mutate: createDocument } = useCreateDocument()
  const { data: sectionsData } = useGetSectionLists()
  const sections = sectionsData?.data?.values

  const [section, setCurrentSection] = useState({})
  const [formData, setFormData] = useState({})
  const [isPublic, setIsPublic] = useState(false)

  const navigate = useNavigate()
  const openToast = useToast()

  const handleSubmitForm = () => {
    createDocument(
      { ...formData, section, public: isPublic },
      {
        onSuccess: () => {
          navigate('/documents')
        },
      }
    )
  }

  const handleChangeToggle = (evt) => {
    const value = evt.target.checked
    setIsPublic(value)
  }

  const handleChangeField = (evt) => {
    updateFormData(evt, formData, setFormData, {})
  }

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    const fileSizeInB = file.size
    const name = evt.target.name
    const filename = file.name.replace(' ', '_')
    const base64 = await convertBase64(file)

    const newFormData = { ...formData }

    if (base64 === '') {
      delete newFormData[name]
      setFormData({ ...newFormData })
    } else if (fileSizeInB <= MB_IN_B * 40) {
      setFormData({ ...formData, [name]: base64, filename_original: filename })
    } else if (fileSizeInB > MB_IN_B * 40) {
      openToast.error({
        title: true,
        message: `Файл слишком большой: ${file.name}`,
        duration: 6000,
      })
      evt.target.value = ''
    }
  }

  const handleSectionChange = (event) => {
    const currentSection = sections && sections.find((el) => el.public_id === event.target.value)

    setCurrentSection({
      name: currentSection.name,
      public_id: currentSection.public_id,
      user_public_id: currentSection.user_public_id,
    })
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col>
          <Button as={Link} to={'/documents'} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Вернуться к списку документов
          </Button>
        </Col>
      </Row>
      <Row className="mb-3">
        <h3>Добавление документа</h3>
      </Row>

      <Row className="g-3 mb-5">
        <Col md={4}>
          <FloatingLabel controlId="titleFileLabel" label="Название">
            <FormControl onChange={handleChangeField} name="title" type="text" placeholder="Название" />
          </FloatingLabel>
        </Col>
        <Col md={8}>
          <FloatingLabel controlId="descriptionFileLabel" label="Описание">
            <FormControl onChange={handleChangeField} name="description" type="text" placeholder="Описание" />
          </FloatingLabel>
        </Col>
        <Col className="mb-3">
          <Form.Group controlId="formFile">
            <div style={{ display: 'flex', gap: '40px' }}>
              <Form.Select onChange={handleSectionChange}>
                <option disabled selected>
                  Выберите раздел
                </option>
                {sections &&
                  sections.map((el) => (
                    <option key={el.public_id} value={el.public_id}>
                      {el.name}
                    </option>
                  ))}
              </Form.Select>

              <Form.Control type="file" onChange={handleFileRead} name="file" />
            </div>
          </Form.Group>
        </Col>
        <Col className="d-flex align-items-center" md={3}>
          <Form.Check
            onChange={handleChangeToggle}
            checked={isPublic}
            name="public"
            type="switch"
            id="public-switch-addDocs"
            label={<span style={{ whiteSpace: 'nowrap' }}>Отображать на странице</span>}
          />
        </Col>
      </Row>

      <Row>
        <Col className="d-grid" md={{ offset: 4, span: 4 }}>
          <Button onClick={handleSubmitForm} variant="success" size="lg">
            Сохранить
          </Button>
        </Col>
      </Row>
    </Layout>
  )
}

export default DocumentsFormScreen
