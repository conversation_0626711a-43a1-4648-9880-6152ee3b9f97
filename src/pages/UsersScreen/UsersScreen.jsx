import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState } from 'react'
import { ButtonGroup, Col, Dropdown, Row, Tab, Tabs } from 'react-bootstrap'

import { CheckPermission } from '@/components/CheckPermission/CheckPermission'
import Layout from '@/components/Layout/Layout'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import Paginator from '@/components/Paginator/Paginator'
import Search from '@/components/Search/Search'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { accessConfig } from '@/accessConfig'
import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { AdminsTable } from '@/pages/UsersScreen/components/AdminsTable/AdminsTable'
import { EditUserModal } from '@/pages/UsersScreen/components/EditUserModal/EditUserModal'
import UserStatisticsTab from '@/pages/UsersScreen/components/UserStatisticsTab/UserStatisticsTab'
import { unixToMoment } from '@/utils/date'

import { usersTableData } from './usersScreenData'

const PAGE_LIMIT_PAGINATION = 10

function UsersScreen() {
  const [, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )
  const [editUserPopup, setEditUserPopup] = useState(false)
  const [users, setUsers] = useState([])
  const [selectedUser, setSelectedUser] = useState({})

  const [totalPages, setTotalPages] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItemId, setSelectedDeleteItemId] = useState('')
  const [activeTab, setActiveTab] = useState('')

  const openToast = useToast()

  const fetchUsers = useCallback(
    (skip, limit) => {
      api({ url: `${APIRoute.USER_LIST}/${skip}/${limit}` }).then((response) => {
        if (response.data?.values?.length > 0) {
          setUsers(response.data.values)
          setTotalPages(Math.ceil(response.data.count / PAGE_LIMIT_PAGINATION))
        }
      })
    },
    [api]
  )

  useEffect(() => {
    fetchUsers(0, PAGE_LIMIT_PAGINATION)
  }, [fetchUsers])

  const handlePage = (pageNumber) => {
    setCurrentPage(pageNumber)
    const skip = PAGE_LIMIT_PAGINATION * pageNumber - PAGE_LIMIT_PAGINATION
    fetchUsers(skip, PAGE_LIMIT_PAGINATION)
  }

  const handleClosePopup = () => {
    setEditUserPopup(false)
    setSelectedUser({})
  }

  const updateUsers = () => {
    fetchUsers(0, PAGE_LIMIT_PAGINATION)
    setCurrentPage(1)
  }

  const handleEditUser = (user) => {
    const { gender } = user
    if (gender !== null) gender?.toLowerCase()

    setSelectedUser({
      public_id: user.public_id,
      name: user.name,
      last_name: user.last_name,
      second_name: user.second_name,
      gender: user.gender,
      phone: user.phone,
      contact_phone: user.contact_phone,
      email: user.email,
      role: user.role,
      extra_contact: user.extra_contact,
      birth_date: unixToMoment(user?.birth_date).format('YYYY-MM-DD'),
      type_relate: user.type_relate,
    })

    setEditUserPopup(true)
  }

  const handleConfirmDelete = (public_id) => {
    setConfirmModal(true)
    setSelectedDeleteItemId(public_id)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    api({ url: `${APIRoute.CUD_USER}/${selectedDeleteItemId}`, method: 'DELETE' }).then((r) => {
      if (r.status === 200 && r.data.message) {
        openToast.success({ message: 'Пользователь был удалён' })
        fetchUsers(0, PAGE_LIMIT_PAGINATION)
        setSelectedDeleteItemId('')
      }
    })
  }

  const handleSelectTab = (evt) => {
    setActiveTab(evt)
  }

  const returnActionsTable = (user) => {
    return (
      <td onClick={(evt) => evt.stopPropagation()}>
        <Dropdown className="text-center">
          <Dropdown.Toggle as={ButtonGroup} bsPrefix=" " variant="success" id="dropdown-basic">
            <i className="bi bi-three-dots" />
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item href={`user/${user.public_id}`}>
              <i className="bi bi-person" /> Профиль
            </Dropdown.Item>
            <CheckPermission allowedRoles={accessConfig.users.actions}>
              <Dropdown.Item as="button" onClick={() => handleEditUser(user)}>
                <i className="bi bi-pencil" /> Изменить
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item className="text-danger" as="button" onClick={() => handleConfirmDelete(user.public_id)}>
                <i className="bi bi-trash" /> Удалить
              </Dropdown.Item>
            </CheckPermission>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  const handleAddUser = () => {
    setSelectedUser({})
    setEditUserPopup(true)
  }

  return (
    <Layout title="Пользователи" onClickAddButton={handleAddUser} addButtonPermissions={accessConfig.users.actions}>
      <Tabs className="mb-3" defaultActiveKey="users" onSelect={handleSelectTab}>
        <Tab title="Пользователи" eventKey="users">
          <Search foundData={setUsers} onResetSearch={fetchUsers} totalPages={setTotalPages} />

          <Row className="mb-3">
            <Col>
              <TableTemplate data={usersTableData} values={users} actions={returnActionsTable} />
            </Col>
          </Row>

          <Row>
            <Col>
              <Paginator currentPage={currentPage} totalPages={totalPages} changePageHandler={handlePage} />
            </Col>
          </Row>
        </Tab>
        <Tab title="Админы" eventKey="admins">
          {activeTab === 'admins' && <AdminsTable />}
        </Tab>
        <Tab title="Статистика" eventKey="statistics">
          {activeTab === 'statistics' && <UserStatisticsTab />}
        </Tab>
      </Tabs>

      <EditUserModal
        selectedUser={selectedUser}
        isShowModal={editUserPopup}
        onCloseModal={handleClosePopup}
        onUpdate={updateUsers}
      />

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItemId}
        onDeleteItem={handleDeleteItem}
        text="Удалить пользователя"
      />
    </Layout>
  )
}

export default UsersScreen
