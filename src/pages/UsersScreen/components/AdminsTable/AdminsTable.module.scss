@import '../../../../assets/styles/mixins';

.cardBody {
  @include scrollbar;

  padding-top: 0;

  height: calc(100vh - 230px);

  overflow: auto;
}

.table {
  position: relative;
  min-width: 2300px;

  thead {
    position: sticky;
    top: 0;
  }
}

.th {
  font-size: 13px;
}

.tdName {
  text-transform: capitalize;
}

.toggle {
  width: 100%;

  text-align: center;

  background: none;
  border: none;
}

.toggleDisabled {
  pointer-events: none;
}
