import moment from 'moment'
import { useState } from 'react'
import { Button, Col, Form, Row, Alert } from 'react-bootstrap'

import LineChart from '@/components/Charts/LineChart/LineChart'
import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'

import { APIRoute } from '@/const'
import { useGetUserDateStatistics } from '@/features/users/api/getUserDateStatistics'
import { useToast } from '@/hooks/useToast'

function UserStatisticsTab() {
  const today = new Date()
  const monthAgo = new Date()
  monthAgo.setMonth(monthAgo.getMonth() - 1)

  const [startDate, setStartDate] = useState(monthAgo.toISOString().split('T')[0])
  const [endDate, setEndDate] = useState(today.toISOString().split('T')[0])
  const [statisticsData, setStatisticsData] = useState(null)

  const openToast = useToast()
  const { mutate: getStatistics, isLoading } = useGetUserDateStatistics()

  const handleGetStatistics = () => {
    if (!startDate || !endDate) {
      openToast.error({ message: 'Пожалуйста, выберите даты "от" и "до"' })
      return
    }

    if (new Date(startDate) > new Date(endDate)) {
      openToast.error({ message: 'Дата "от" не может быть больше даты "до"' })
      return
    }

    const requestData = {
      start_date: new Date(startDate).toISOString(),
      end_date: new Date(endDate).toISOString(),
    }

    getStatistics(requestData, {
      onSuccess: (response) => {
        if (response.status === 200 && response.data) {
          setStatisticsData(response.data)
          openToast.success({ message: 'Статистика пользователей успешно загружена' })
        }
      },
      onError: (error) => {
        console.error('Error fetching user statistics:', error)
        openToast.error({ message: 'Ошибка при загрузке статистики пользователей' })
      },
    })
  }

  // Подготавливаем общие метки для обоих графиков
  const chartLabels = statisticsData
    ? statisticsData.daily_stats.map((item) => {
        // Используем поле "date" вместо "day" согласно структуре данных
        return moment(item.date).locale('ru').format('D MMMM YYYY')
      })
    : []

  // Данные для одного графика с двумя линиями и двумя осями Y
  const chartData = statisticsData
    ? {
        labels: chartLabels,
        datasets: [
          {
            label: 'Общее количество',
            data: statisticsData.daily_stats.map((item) => item.total_users_up_to_date),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            yAxisID: 'y', // Левая ось
          },
          {
            label: 'Создано за день',
            data: statisticsData.daily_stats.map((item) => item.users_created_on_day),
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            yAxisID: 'y1', // Правая ось
          },
        ],
      }
    : null

  // Данные для кнопки выгрузки
  const downloadData =
    startDate && endDate
      ? {
          start_date: new Date(startDate).toISOString(),
          end_date: new Date(endDate).toISOString(),
        }
      : null

  return (
    <div>
      <Row className="mb-4">
        <Col md={3}>
          <Form.Group>
            <Form.Label>Дата от</Form.Label>
            <Form.Control type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group>
            <Form.Label>Дата до</Form.Label>
            <Form.Control type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
          </Form.Group>
        </Col>
        <Col md={6} className="d-flex align-items-end gap-2">
          <Button variant="primary" onClick={handleGetStatistics} disabled={isLoading}>
            {isLoading ? 'Загрузка...' : 'Получить'}
          </Button>
          <DownloadButton
            url={APIRoute.USER_DATE_STATISTICS_XLSX}
            fileName="user_statistics.xlsx"
            label="Выгрузить Excel"
            data={downloadData}
            disabled={!startDate || !endDate}
          />
        </Col>
      </Row>

      {/* Отображение общей статистики */}
      {statisticsData && statisticsData.overall_summary && (
        <Row className="mb-4">
          <Col md={12}>
            <Alert variant="info">
              <h6>Общая статистика за период:</h6>
              <ul className="mb-0">
                <li>
                  Пользователей на начало периода:{' '}
                  <strong>{statisticsData.overall_summary.users_at_start_period}</strong>
                </li>
                <li>
                  Создано пользователей за период:{' '}
                  <strong>{statisticsData.overall_summary.users_created_in_period}</strong>
                </li>
                <li>
                  Общее количество пользователей на конец периода:{' '}
                  <strong>{statisticsData.overall_summary.total_users_at_end_period}</strong>
                </li>
              </ul>
            </Alert>
          </Col>
        </Row>
      )}

      {/* Графики */}
      {statisticsData && (
        <div className="w-100" style={{ width: '100%', minWidth: '100%' }}>
          {statisticsData.daily_stats.length > 0 ? (
            <LineChart
              data={chartData}
              title="Статистика пользователей"
              height={450}
              showLegend={true}
              showGrid={true}
              tension={0.4}
              fill={true}
              dualYAxis={true}
            />
          ) : (
            <Alert variant="info">За выбранный период данных о пользователях не найдено</Alert>
          )}
        </div>
      )}
    </div>
  )
}

export default UserStatisticsTab
