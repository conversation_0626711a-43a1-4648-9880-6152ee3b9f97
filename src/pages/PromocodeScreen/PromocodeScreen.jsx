import { useEffect, useState } from 'react'
import { Accordion, Row, Col } from 'react-bootstrap'
import { useParams } from 'react-router-dom'

import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'

import { APIRoute } from '@/const'
import { Order, useGetPromocodeOrders } from '@/features/orders'
import { Promocode } from '@/features/promocodes/components/Promocode/Promocode'

import Layout from '../../components/Layout/Layout'

const PromocodeScreen = () => {
  const { code } = useParams()
  const [stats, setStats] = useState({
    all: 0,
    paid: 0,
  })

  const getPromocodeOrdersQuery = useGetPromocodeOrders({}, code)
  const orders = getPromocodeOrdersQuery?.data?.data?.values
  const promocode = getPromocodeOrdersQuery?.data?.data?.promocode

  useEffect(() => {
    if (orders?.length > 0) {
      const paid = orders.filter((item) => item.status === 'paid')
      setStats({ all: orders.length, paid: paid.length })
    }
  }, [orders])

  const handleUpdateOrders = () => {
    getPromocodeOrdersQuery.refetch()
  }

  return (
    <Layout documentTitle={`Промокод — ${code}`}>
      <Row>
        <Col>
          <h2>{`Заказы по промокоду ${code}`}</h2>
        </Col>
        <Col md="auto">
          <DownloadButton
            url={`${APIRoute.PROMOCODE_TICKETS_XLSX}/${code}`}
            fileName={`promocode_tickets_${code}.xlsx`}
            label="Скачать"
            disabled={!code}
          />
        </Col>
      </Row>
      <h5 className="mb-5">
        Всего заказов: {stats.all}, Оплачены: {stats.paid}
      </h5>
      {promocode && (
        <div className="mb-5">
          <Promocode promocode={promocode} large />
        </div>
      )}
      {orders?.length > 0 &&
        orders.map((order, index) => (
          <Accordion
            defaultActiveKey={index === 0 ? `${order.public_id}-${index}` : null}
            key={`${order.public_id}-${index}`}
          >
            <Accordion.Item eventKey={`${order.public_id}-${index}`}>
              <Accordion.Header>Заказ {order.public_id}</Accordion.Header>
              <Accordion.Body className="pt-3">
                <Order order={order} onUpdateOrders={handleUpdateOrders} />
              </Accordion.Body>
            </Accordion.Item>
          </Accordion>
        ))}
    </Layout>
  )
}

export default PromocodeScreen
