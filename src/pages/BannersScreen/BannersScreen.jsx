import useAxios from 'axios-hooks'
import moment from 'moment'
import { useEffect, useState, useCallback } from 'react'
import { <PERSON><PERSON>, Card, Col, OverlayTrigger, Row, Tab, Tabs, Tooltip } from 'react-bootstrap'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'

import styles from './BannersScreen.module.scss'
import Layout from '../../components/Layout/Layout'
import ConfirmDeleteModal from '../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

const renderBtnTooltip = (props, label) => {
  return (
    <Tooltip id="button-more-tooltip" {...props}>
      {label}
    </Tooltip>
  )
}

function BannersScreen() {
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState('')
  const [selectedTab, setSelectedTab] = useState('main')
  const [isBlackBackground, setIsBlackBackground] = useState(true)

  const openToast = useToast()

  const [{ data: banners }, apiBannersMain] = useAxios(
    {
      url: `${APIRoute.BANNERS_PAGE}/main`,
      method: 'GET',
    },
    { manual: true }
  )

  const [{ data: bannersShop }, apiBannersShop] = useAxios(
    {
      url: `${APIRoute.BANNERS_PAGE}/shop`,
      method: 'GET',
    },
    { manual: true }
  )

  const [{ data: bannersPartners }, apiBannersPartners] = useAxios(
    {
      url: `${APIRoute.BANNERS_PAGE}/partners`,
      method: 'GET',
    },
    { manual: true }
  )

  const [{ data: bannersEvents }, apiBannersEvents] = useAxios(
    {
      url: `${APIRoute.BANNERS_PAGE}/events`,
      method: 'GET',
    },
    { manual: true }
  )

  const [, apiDelete] = useAxios(
    {
      url: `${APIRoute.ACTIONS_BANNERS}`,
      method: 'DELETE',
    },
    { manual: true }
  )

  const getBanners = useCallback(
    (tab = selectedTab) => {
      if (tab === 'main') {
        apiBannersMain()
      } else if (tab === 'shop') {
        apiBannersShop()
      } else if (tab === 'partners') {
        apiBannersPartners()
      } else if (tab === 'events') {
        apiBannersEvents()
      }
    },
    [selectedTab, apiBannersMain, apiBannersShop, apiBannersPartners, apiBannersEvents]
  )

  useEffect(() => {
    getBanners()
  }, [getBanners])

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedDeleteItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ url: `${APIRoute.ACTIONS_BANNERS}/${selectedDeleteItem.public_id}` }).then((r) => {
      if (r.status === 200 && r.data.message) {
        openToast.success({ message: 'Баннер был удалён' })
        setSelectedDeleteItem('')
        getBanners()
      }
    })
  }

  const handleSelectTab = (tab) => {
    setSelectedTab(tab)
    getBanners(tab)
  }

  function BannerCard({ item }) {
    const isValidDate = moment().isBefore(unixToMoment(item?.validity), 'day')

    return (
      <Card className={`${styles.card} ${!isBlackBackground ? styles.cardWhite : ''}`}>
        <Card.Body className={`${styles.cardBody} p-2`}>
          <img
            className={styles.cardImg}
            src={getImageSrc(item.picture_main || item.picture_small)}
            width="200"
            alt=""
          />
        </Card.Body>
        <Card.Footer className="">
          {item?.title?.length > 0 && (
            <span style={{ display: 'inline-block', marginRight: 'auto' }}>{item.title}</span>
          )}
          <Row className="g-1">
            <Col md="auto">{item?.public ? '🟢' : '🔴'}</Col>
            {Object.prototype.hasOwnProperty.call(item, 'validity') && (
              <Col className={`${styles.date} ${!isValidDate ? styles.invalidDate : ''} align-content-center`}>
                {unixToMoment(item?.validity).format('DD.MM.YYYY')}
              </Col>
            )}
            <Col md="auto">
              <OverlayTrigger
                placement="left"
                delay={{ show: 250, hide: 400 }}
                overlay={(evt) => renderBtnTooltip(evt, 'редактировать')}
              >
                <Button
                  href={`banners/form/${item.banner_type}/${item.public_id}`}
                  className={styles.btnEdit}
                  type="button"
                  disabled={!item?.public_id}
                />
              </OverlayTrigger>
            </Col>
            <Col md="auto">
              <OverlayTrigger
                placement="left"
                delay={{ show: 250, hide: 400 }}
                overlay={(evt) => renderBtnTooltip(evt, 'удалить')}
              >
                <Button
                  onClick={() => handleConfirmDelete(item)}
                  className={`${styles.btnEdit} ${styles.btnDelete}`}
                  type="button"
                  disabled={!item?.public_id}
                />
              </OverlayTrigger>
            </Col>
          </Row>
        </Card.Footer>
      </Card>
    )
  }

  return (
    <Layout title="Баннеры" href="banners/form">
      <Row className="mb-3">
        <Col />
        <Col md="auto">
          <Button onClick={() => setIsBlackBackground((prev) => !prev)} size="sm">
            Сменить фон
          </Button>
        </Col>
      </Row>

      <Tabs defaultActiveKey="main" className="mb-3" onSelect={handleSelectTab}>
        <Tab title="Главная" eventKey="main">
          <h4>Большой слайдер</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {banners?.main?.big?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Маленький слайдер</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {banners?.main?.small?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Сторис</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {banners?.main?.story?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Первый блок</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {banners?.main?.ad1?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Второй блок</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {banners?.main?.ad2?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Партнеры проектов</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {banners?.main?.projects?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>
        </Tab>

        <Tab title="Магазин" eventKey="shop">
          <h4>Слайдер</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {bannersShop?.shop?.shop?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>
          <h4>Внешний магазин</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {bannersShop?.shop?.external_shop?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>
        </Tab>

        <Tab title="Партнёры" eventKey="partners">
          <h4>Главные партнёры</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {bannersPartners?.partners?.partners_main?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Партнёры</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {bannersPartners?.partners?.partners?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>

          <h4>Спонсоры</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {bannersPartners?.partners?.sponsors?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>
        </Tab>

        <Tab title="События" eventKey="events">
          <h4>События</h4>
          <Row className="mb-4 g-3 align-items-stretch">
            {bannersEvents?.events?.events?.map((item) => (
              <Col md="3" key={item.public_id}>
                <BannerCard item={item} />
              </Col>
            ))}
          </Row>
        </Tab>
      </Tabs>

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onUpdateSelectedDeleteItem={setSelectedDeleteItem}
        onDeleteItem={handleDeleteItem}
        text="Удалить баннер"
      />
    </Layout>
  )
}

export default BannersScreen
