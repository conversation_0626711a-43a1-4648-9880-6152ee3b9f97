.card {
  height: 100%;

  background-color: #000000;
}

.cardWhite {
  background-color: #ffffff;
}

.cardBody {
  position: relative;
  display: grid;
  place-items: center;
}

.cardImg {
  border-radius: 4px;

  z-index: 1;
}

.btnEdit {
  width: 16px;
  height: 16px;

  background-color: transparent;
  background-image: url('../../assets/img/icons/icon_edit.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  border: 0;

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    box-shadow: none;
  }

  &:disabled {
    cursor: not-allowed;
  }
}

.btnDelete {
  background-image: url('../../assets/img/icons/icon-delete.svg');
}

.date {
  margin-left: 8px;

  font-size: 12px;
}

.invalidDate {
  color: #dc3545;
}
