import { useEffect, useState } from 'react'
import { Button, Col, Floating<PERSON>abel, Form, FormControl, Row, Spinner, FormCheck } from 'react-bootstrap'
import { useNavigate, useParams } from 'react-router-dom'

import Layout from '@/components/Layout/Layout'

import { AppRoute } from '@/const'
import { useGetEventFormats } from '@/features/events/api/getEventFormats'
import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'
import { useGetResult, useUpdateResult } from '@/features/results/api'
import BroadcastForm from '@/pages/ResultsCreateScreen/components/BroadcastForm'
import SortingMatching from '@/pages/ResultsCreateScreen/components/SortingMatching/SortingMatching'
import { unixToMoment } from '@/utils/date'

import FormatUploadForm from './components/FormatUploadForm'

export const ResultsEditScreen = () => {
  const navigate = useNavigate()
  const { id } = useParams()

  const [settings, setSettings] = useState(null)
  const [externalUrl, setExternalUrl] = useState('')
  const [leaderResult, setLeaderResult] = useState('')
  const [showLeaders, setShowLeaders] = useState(false)
  const [matchFormatsData, setMatchFormatsData] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [modifiedSettings, setModifiedSettings] = useState({})
  const [originalSettings, setOriginalSettings] = useState(null)
  const [externalFormats, setExternalFormats] = useState([])
  const [externalFields, setExternalFields] = useState([])
  const [eventCityId, setEventCityId] = useState('')

  const getEventTypeListQuery = useGetEventTypeList()
  const events = getEventTypeListQuery?.data?.data?.values
  const event = events?.filter((item) => item.public_id === settings?.event_city?.event_type?.public_id)[0]

  const resultQuery = useGetResult(id)
  const updateResultMutation = useUpdateResult()

  const eventFormatsQuery = useGetEventFormats({}, eventCityId)
  const formats = eventFormatsQuery?.data?.data?.values || []

  useEffect(() => {
    if (resultQuery.data?.data) {
      const resultData = resultQuery.data.data
      setSettings(resultData)
      setOriginalSettings(JSON.parse(JSON.stringify(resultData)))

      // Сохраняем идентификатор города мероприятия для получения форматов
      if (resultData.event_city?.public_id) {
        setEventCityId(resultData.event_city.public_id)
      }

      if (resultData.formats) {
        setExternalFormats(resultData.formats.map((format) => format.external_format))
      }
      if (resultData.source_fields) {
        setExternalFields(resultData.source_fields)
      }
      if (resultData.leaders !== undefined) {
        setShowLeaders(resultData.leaders)
      }
    }
  }, [resultQuery.data])

  useEffect(() => {
    if (settings) {
      setExternalUrl(settings.external_url || '')
      setLeaderResult(settings.leader_result || '')
    }
  }, [settings])

  const handleSettingsChange = (updatedData) => {
    const updatedSettings = {
      ...updatedData,
      external_url: externalUrl,
      leader_result: leaderResult,
    }

    setSettings(updatedSettings)

    const updatedBroadcast = updatedData.broadcast || {}
    const originalBroadcast = originalSettings?.broadcast || {}

    const changedBroadcast = {}
    let hasChanges = false

    if (updatedBroadcast.enabled !== originalBroadcast.enabled) {
      changedBroadcast.enabled = updatedBroadcast.enabled
      hasChanges = true
    }

    if (updatedBroadcast.until !== originalBroadcast.until) {
      changedBroadcast.until = updatedBroadcast.until
      hasChanges = true
    }

    if (updatedBroadcast.update_interval !== originalBroadcast.update_interval) {
      changedBroadcast.update_interval = updatedBroadcast.update_interval
      hasChanges = true
    }

    if (hasChanges) {
      setModifiedSettings({
        ...modifiedSettings,
        broadcast: changedBroadcast,
      })
    } else if (modifiedSettings.broadcast) {
      // Если изменений нет, удаляем поле broadcast из измененных настроек
      const newModifiedSettings = { ...modifiedSettings }
      delete newModifiedSettings.broadcast
      setModifiedSettings(newModifiedSettings)
    }
  }

  const handleShowLeadersChange = (e) => {
    setShowLeaders(e.target.checked)
  }

  const handleSubmitEdit = () => {
    setIsLoading(true)
    const body = {
      public_id: settings.public_id,
    }

    if (matchFormatsData?.length > 0) body.formats = [...matchFormatsData]
    if (externalUrl !== originalSettings?.external_url) body.external_url = externalUrl
    if (leaderResult?.length > 0 && leaderResult !== originalSettings?.leader_result) body.leader_result = leaderResult
    if (showLeaders !== originalSettings?.leaders) body.leaders = showLeaders

    // Добавляем только изменившиеся поля broadcast
    if (modifiedSettings.broadcast && Object.keys(modifiedSettings.broadcast).length > 0) {
      body.broadcast = modifiedSettings.broadcast
    }

    updateResultMutation.mutate(body, {
      onSuccess: (r) => {
        if (r.status === 200) {
          navigate(`${AppRoute.RESULTS_TABLE.replace(':id', id)}`)
          resultQuery.refetch()
        }
      },
      onSettled: () => {
        setIsLoading(false)
      },
    })
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col md="auto" className="mb-3">
          <Button
            onClick={() => navigate(`${AppRoute.RESULTS_TABLE.replace(':id', id)}`)}
            variant="outline-secondary"
            size="sm"
          >
            <i className="bi bi-arrow-left" /> Назад
          </Button>
        </Col>
      </Row>
      <Row>
        <Col>
          <h2 className="mb-4">Редактирование настроек</h2>
        </Col>
      </Row>

      {settings && event && (
        <div className="mb-3">
          <h5>
            {event?.title}, {settings?.event_city?.address},{' '}
            {settings?.event_city?.start_time && unixToMoment(settings.event_city.start_time).format('DD.MM.YYYY')}
          </h5>
        </div>
      )}

      {Object.keys(settings ?? {})?.length > 0 ? (
        <>
          <div className="card p-4 mb-4" style={{ backgroundColor: '#eef0f8' }}>
            <Row className="mb-4">
              <Col>
                <FloatingLabel controlId="resultExternalUrl" label="Внешняя ссылка">
                  <FormControl
                    onChange={(evt) => setExternalUrl(evt.target.value)}
                    value={externalUrl}
                    name="external_url"
                    type="text"
                    placeholder="Внешняя ссылка"
                  />
                </FloatingLabel>
              </Col>
              <Col>
                <FloatingLabel controlId="leaderResultLabel" label="Основное время">
                  <Form.Select
                    onChange={(evt) => setLeaderResult(evt.target.value)}
                    value={leaderResult}
                    name="leader_result"
                    aria-label="Основное время"
                  >
                    <option value="">Выберите один из вариантов</option>
                    <option value="individual_time_result">Время официальное</option>
                    <option value="absolute_time_result">Время чистое</option>
                  </Form.Select>
                </FloatingLabel>
              </Col>
            </Row>

            {/* Блок настроек трансляции */}
            <Row className="mb-4">
              <Col>
                <BroadcastForm
                  formData={settings}
                  onChangeForm={handleSettingsChange}
                  cityTimezone={settings?.event_city?.timezone || 'Europe/Moscow'}
                />
              </Col>
            </Row>

            <Row className="mb-4">
              <Col md={12}>
                <div className="card p-3">
                  <FormCheck
                    type="switch"
                    id="showLeadersSwitch"
                    label="Отображать лидеров"
                    checked={showLeaders}
                    onChange={handleShowLeadersChange}
                  />
                </div>
              </Col>
            </Row>

            <SortingMatching
              formats={formats}
              externalFormats={externalFormats}
              externalFields={externalFields}
              defaultFormatsData={settings?.formats}
              defaultFieldsData={settings?.fields_list}
              onChangeFormatsData={setMatchFormatsData}
            />

            <Row className="mt-4">
              <Col className="d-flex justify-content-end">
                <Button
                  onClick={() => navigate(`${AppRoute.RESULTS_TABLE.replace(':id', id)}`)}
                  variant="secondary"
                  className="me-2"
                  disabled={isLoading}
                >
                  Отменить
                </Button>
                <Button onClick={handleSubmitEdit} disabled={isLoading || updateResultMutation.isLoading}>
                  {isLoading || updateResultMutation.isLoading ? (
                    <Spinner size="sm" animation="border" className="me-2" />
                  ) : null}
                  Сохранить
                </Button>
              </Col>
            </Row>
          </div>

          {/* Компонент для добавления формата CSV */}
          {settings?.event_city?.public_id && (
            <FormatUploadForm eventCityId={settings.event_city.public_id} externalFields={externalFields} />
          )}
        </>
      ) : resultQuery.isLoading ? (
        <div className="d-flex justify-content-center">
          <Spinner animation="border" />
        </div>
      ) : (
        <div className="d-flex justify-content-center">
          <p>Ошибка загрузки данных</p>
        </div>
      )}
    </Layout>
  )
}

export default ResultsEditScreen
