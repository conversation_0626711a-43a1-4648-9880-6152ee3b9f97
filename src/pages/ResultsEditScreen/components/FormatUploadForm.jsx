import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Row, Spinner } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import { AppRoute } from '@/const'
import { useUploadResultFormat } from '@/features/results/api'
import { useToast } from '@/hooks/useToast'
import SortingFields from '@/pages/ResultsCreateScreen/components/SortingFields/SortingFields'
import { convertBase64 } from '@/utils/common'

const FormatUploadForm = ({ eventCityId, externalFields }) => {
  const navigate = useNavigate()
  const openToast = useToast()
  const uploadResultFormatMutation = useUploadResultFormat()

  const [csvFile, setCsvFile] = useState(null)
  const [externalFormatName, setExternalFormatName] = useState('')
  const [selectedFields, setSelectedFields] = useState([])

  const handleFileRead = async (evt) => {
    const file = evt.target.files[0]
    if (!file) return

    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      openToast.error({
        message: 'Поддерживаются только CSV файлы',
        duration: 6000,
      })
      evt.target.value = ''
      return
    }

    try {
      const base64 = await convertBase64(file)
      setCsvFile(base64)
    } catch {
      openToast.error({
        message: 'Ошибка при чтении файла',
        duration: 6000,
      })
      evt.target.value = ''
    }
  }

  const handleSubmitCsvFormat = () => {
    if (!csvFile) {
      openToast.error({ message: 'Выберите CSV файл' })
      return
    }

    if (!externalFormatName) {
      openToast.error({ message: 'Укажите название формата' })
      return
    }

    if (!selectedFields || selectedFields.length === 0) {
      openToast.error({ message: 'Выберите поля' })
      return
    }

    const data = {
      file_content: csvFile,
      event_city: {
        public_id: eventCityId,
      },
      source_name: 'csv',
      external_format: externalFormatName,
      fields_list: selectedFields,
      format_public_id: '123test',
    }

    // console.log(data)

    uploadResultFormatMutation.mutate(data, {
      onSuccess: () => {
        navigate(`${AppRoute.RESULTS_TABLE.replace(':id', eventCityId)}`)
      },
    })
  }

  return (
    <div className="card p-4 mb-4" style={{ backgroundColor: '#eef0f8' }}>
      <h4 className="mb-3">Добавить новый формат</h4>

      <Row className="mb-3">
        <Col md={6}>
          <Form.Group controlId="csvFileUpload" className="mb-3">
            <Form.Label>Выберите CSV файл</Form.Label>
            <Form.Control type="file" accept=".csv" onChange={handleFileRead} />
            <Form.Text className="text-muted">Поддерживаются только файлы в формате CSV</Form.Text>
          </Form.Group>
        </Col>

        <Col md={6}>
          <Form.Group controlId="externalFormatName" className="mb-3">
            <Form.Label>Название формата</Form.Label>
            <Form.Control
              type="text"
              placeholder="Введите название формата"
              value={externalFormatName}
              onChange={(e) => setExternalFormatName(e.target.value)}
            />
          </Form.Group>
        </Col>
      </Row>

      <Row className="mb-3">
        <Col md={12}>
          <Form.Group controlId="fieldsList" className="mb-3">
            <Form.Label>Выберите поля</Form.Label>
            <SortingFields
              fields={externalFields}
              fieldsData={selectedFields}
              defaultFieldsData={[]}
              onChangeFields={setSelectedFields}
            />
          </Form.Group>
        </Col>
      </Row>

      <Row>
        <Col className="d-flex justify-content-end">
          <Button
            variant="primary"
            onClick={handleSubmitCsvFormat}
            disabled={uploadResultFormatMutation.isLoading || !csvFile || !externalFormatName}
          >
            {uploadResultFormatMutation.isLoading ? <Spinner size="sm" animation="border" className="me-2" /> : null}
            Загрузить формат
          </Button>
        </Col>
      </Row>
    </div>
  )
}

export default FormatUploadForm
