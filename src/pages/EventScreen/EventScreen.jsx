import { useParams } from 'react-router-dom'

import { useGetEvent } from '@/features/events/api/getEvent'

import EventInfo from '../../components/EventInfo/EventInfo'
import Layout from '../../components/Layout/Layout'
import PagePreloader from '../../components/PagePreloader/PagePreloader'

function EventScreen() {
  const { publicId } = useParams()

  const getEventQuery = useGetEvent(publicId)
  const data = getEventQuery?.data?.data
  const isLoading = getEventQuery?.isLoading

  return (
    <Layout documentTitle={`События - ${data?.title}`}>
      <PagePreloader isLoading={isLoading}>
        <EventInfo item={data} />
      </PagePreloader>
    </Layout>
  )
}

export default EventScreen
