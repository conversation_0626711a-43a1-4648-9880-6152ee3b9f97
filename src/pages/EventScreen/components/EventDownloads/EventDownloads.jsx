import useAxios from 'axios-hooks'
import { useState, useEffect } from 'react'
import { But<PERSON>, Col, Form, Row, Spinner, <PERSON>, Badge, Alert, ButtonGroup } from 'react-bootstrap'

import { CheckPermission } from '@/components/CheckPermission/CheckPermission'

import { accessConfig } from '@/accessConfig'
import { APIRoute } from '@/const'
import { getFormatTime } from '@/utils/date'

const urls = {
  insurance: APIRoute.SOLDGOODS_EXCEL,
  listEmails: APIRoute.ORDERS_EMAIL,
  listPromocodes: APIRoute.PROMOCODES_XLSX,
  listShirt: APIRoute.SHIRTS_XLSX,
  listMembers: APIRoute.SOLDGOODS_EXCEL,
}

const urlsAllCities = {
  insurance: APIRoute.SOLDGOODS_EVENT_EXCEL,
  listEmails: APIRoute.ORDERS_EMAIL,
  listPromocodes: APIRoute.PROMOCODES_XLSX,
  listShirt: APIRoute.SHIRTS_XLSX,
  listMembers: APIRoute.SOLDGOODS_EVENT_EXCEL,
}

const DOWNLOAD_TYPES = {
  insurance: {
    label: 'Страховки',
    icon: 'bi-shield-check',
    variant: 'success',
    description: 'Список участников со страховкой',
  },
  listEmails: {
    label: 'Email адреса',
    icon: 'bi-envelope',
    variant: 'info',
    description: 'Email адреса участников',
  },
  listPromocodes: {
    label: 'Промокоды',
    icon: 'bi-tags',
    variant: 'warning',
    description: 'Использованные промокоды',
  },
  listShirt: {
    label: 'Футболки',
    icon: 'bi-person',
    variant: 'secondary',
    description: 'Размеры футболок участников',
  },
  listMembers: {
    label: 'Участники',
    icon: 'bi-people',
    variant: 'primary',
    description: 'Полный список участников',
  },
}

function ButtonDownload({
  event,
  fileName,
  name,
  label,
  downloadCityId,
  eventCityPublicId,
  params,
  variant = 'outline-primary',
  icon,
  description,
  disabled = false,
}) {
  const [{ loading }, api] = useAxios(
    {
      method: 'GET',
    },
    { manual: true }
  )

  const downloadFile = () => {
    const urlApi = returnUrl()
    const urlParams = params ? new URLSearchParams(params) : ''
    let config

    if (name === 'listEmails') {
      config = {
        url: urls[name] + `/${eventCityPublicId}`,
        method: 'GET',
        responseType: 'blob',
      }

      if (downloadCityId === 'allCities') {
        config.url += '/all'
      } else {
        config.url += `/${downloadCityId}`
      }
    } else {
      config = {
        url: urlApi + urlParams,
        method: 'GET',
        responseType: 'blob',
      }
    }

    api({ ...config })
      .then((response) => {
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }, 100)
      })
      .catch((error) => {
        console.error('Download failed:', error)
      })
  }

  const returnUrl = () => {
    if (downloadCityId === 'allCities') {
      return `${urlsAllCities[name]}/${event.public_id}?`
    }

    return eventCityPublicId ? `${urls[name]}/${eventCityPublicId}?` : urls[name]
  }

  return (
    <Card
      className="h-100 shadow-sm border-0"
      style={{
        transition: 'box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out',
        cursor: 'pointer',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)'
        e.currentTarget.style.transform = 'translateY(-2px)'
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = ''
        e.currentTarget.style.transform = ''
      }}
    >
      <Card.Body className="d-flex flex-column p-3">
        <div className="d-flex align-items-center mb-2">
          <div
            className={`rounded-circle bg-${variant} bg-opacity-10 me-2 d-flex align-items-center justify-content-center`}
            style={{ width: '40px', height: '40px', minWidth: '40px', minHeight: '40px' }}
          >
            <i className={`bi ${icon} text-${variant}`} style={{ fontSize: '1.25rem', lineHeight: 1 }}></i>
          </div>
          <div className="flex-grow-1">
            <h6 className="mb-0 fw-semibold">{label}</h6>
            {description && <small className="text-muted">{description}</small>}
          </div>
        </div>

        <Button
          variant={variant}
          size="sm"
          onClick={downloadFile}
          disabled={loading || disabled}
          className="mt-auto d-flex align-items-center justify-content-center gap-2"
        >
          {loading ? (
            <>
              <Spinner animation="border" size="sm" />
              <span>Загрузка...</span>
            </>
          ) : (
            <>
              <i className="bi bi-download"></i>
              <span>Скачать</span>
            </>
          )}
        </Button>
      </Card.Body>
    </Card>
  )
}

function EventDownloads({ event, cities }) {
  const [downloadCityId, setDownloadCityId] = useState(null)
  const [isAdditionalFields, setIsAdditionalFields] = useState(false)
  const [isCompany, setIsCompany] = useState(false)
  const [selectedCityInfo, setSelectedCityInfo] = useState(null)

  useEffect(() => {
    if (cities?.length > 0) {
      const firstCity = cities[0]
      setDownloadCityId(firstCity.public_id)
      setSelectedCityInfo(firstCity)
    }
  }, [cities])

  const handleCityChange = (cityId) => {
    setDownloadCityId(cityId)
    if (cityId === 'allCities') {
      setSelectedCityInfo({ address: 'Все города', public_id: 'allCities' })
    } else {
      const city = cities?.find((city) => city.public_id === cityId)
      setSelectedCityInfo(city)
    }
  }

  const generateEmailsFileName = () => {
    const eventTitle = event.title ? event.title.replace(/[^a-zA-Zа-яёА-ЯЁ0-9]/g, '_') : event.public_id
    const currentDate = new Date().toISOString().split('T')[0]

    if (downloadCityId === 'allCities') {
      return `Emails_${eventTitle}_все_города_${currentDate}.xlsx`
    }

    const cityName =
      cities?.find((city) => city.public_id === downloadCityId)?.address?.replace(/[^a-zA-Zа-яёА-ЯЁ0-9]/g, '_') ||
      downloadCityId
    return `Emails_${eventTitle}_${cityName}_${currentDate}.xlsx`
  }

  const generateFileName = (type, customName = null) => {
    const eventTitle = event.title ? event.title.replace(/[^a-zA-Zа-яёА-ЯЁ0-9]/g, '_') : event.public_id
    const currentDate = new Date().toISOString().split('T')[0]

    let cityInfo = ''
    if (downloadCityId === 'allCities') {
      cityInfo = 'все_города'
    } else {
      const cityName =
        cities?.find((city) => city.public_id === downloadCityId)?.address?.replace(/[^a-zA-Zа-яёА-ЯЁ0-9]/g, '_') ||
        downloadCityId
      cityInfo = cityName
    }

    const baseFileName = customName || type
    return `${baseFileName}_${eventTitle}_${cityInfo}_${currentDate}.xlsx`
  }

  const returnParams = () => {
    const params = {}

    if (isAdditionalFields) params.additional_fields = true
    if (isCompany) params.company = true

    return params
  }

  if (!cities || cities.length === 0) {
    return (
      <Alert variant="warning" className="text-center">
        <i className="bi bi-exclamation-triangle mb-2" style={{ fontSize: '2rem' }}></i>
        <div className="fw-semibold">Нет доступных городов</div>
        <small>Добавьте города к событию для скачивания документов</small>
      </Alert>
    )
  }

  return (
    <div className="downloads-section">
      {/* City Selection */}
      <Card className="mb-4 border-0 bg-body-tertiary">
        <Card.Body className="py-3">
          <Row className="align-items-center">
            <Col md={6}>
              <div className="d-flex align-items-center mb-2 mb-md-0">
                <i className="bi bi-geo-alt text-primary me-2" style={{ fontSize: '1.25rem' }}></i>
                <div>
                  <h6 className="mb-0 fw-semibold">Выберите город</h6>
                  <small className="text-muted">Данные будут загружены для выбранного города</small>
                </div>
              </div>
            </Col>
            <Col md={6}>
              <Form.Select
                size="sm"
                aria-label="Выбор города"
                value={downloadCityId || ''}
                onChange={(evt) => handleCityChange(evt.target.value)}
              >
                {cities.map((city) => (
                  <option value={city.public_id} key={city.public_id}>
                    {city.address} — {getFormatTime(city.start_time, city.timezone)}
                  </option>
                ))}
                <CheckPermission allowedRoles={accessConfig.eventScreen.selectAllCities}>
                  <option value="allCities">🌍 Все города</option>
                </CheckPermission>
              </Form.Select>
            </Col>
          </Row>

          {selectedCityInfo && (
            <Row className="mt-3">
              <Col>
                <div className="d-flex align-items-center">
                  <Badge bg="primary" className="me-2">
                    <i className="bi bi-check-circle me-1"></i>
                    Выбрано
                  </Badge>
                  <span className="fw-semibold">{selectedCityInfo.address}</span>
                  {selectedCityInfo.start_time && (
                    <small className="text-muted ms-2">
                      {getFormatTime(selectedCityInfo.start_time, selectedCityInfo.timezone)}
                    </small>
                  )}
                </div>
              </Col>
            </Row>
          )}
        </Card.Body>
      </Card>

      {/* Download Options */}
      <Row className="g-3 mb-4">
        <Col xs={12}>
          <h6 className="mb-3 text-muted">
            <i className="bi bi-file-earmark-text me-2"></i>
            Основные документы
          </h6>
        </Col>

        <Col xs={12} sm={6} lg={3}>
          <ButtonDownload
            event={event}
            fileName={generateFileName('Страховки')}
            label={DOWNLOAD_TYPES.insurance.label}
            name="insurance"
            downloadCityId={downloadCityId}
            eventCityPublicId={downloadCityId}
            params={{ insurance: true }}
            variant={DOWNLOAD_TYPES.insurance.variant}
            icon={DOWNLOAD_TYPES.insurance.icon}
            description={DOWNLOAD_TYPES.insurance.description}
          />
        </Col>

        <CheckPermission allowedRoles={accessConfig.eventScreen.downloadInsurance}>
          <Col xs={12} sm={6} lg={3}>
            <ButtonDownload
              event={event}
              fileName={generateEmailsFileName()}
              label={DOWNLOAD_TYPES.listEmails.label}
              name="listEmails"
              downloadCityId={downloadCityId}
              eventCityPublicId={event?.public_id}
              variant={DOWNLOAD_TYPES.listEmails.variant}
              icon={DOWNLOAD_TYPES.listEmails.icon}
              description={DOWNLOAD_TYPES.listEmails.description}
            />
          </Col>
        </CheckPermission>

        <Col xs={12} sm={6} lg={3}>
          <ButtonDownload
            event={event}
            fileName={generateFileName('Промокоды')}
            label={DOWNLOAD_TYPES.listPromocodes.label}
            name="listPromocodes"
            downloadCityId={downloadCityId}
            eventCityPublicId={downloadCityId}
            variant={DOWNLOAD_TYPES.listPromocodes.variant}
            icon={DOWNLOAD_TYPES.listPromocodes.icon}
            description={DOWNLOAD_TYPES.listPromocodes.description}
          />
        </Col>

        <CheckPermission allowedRoles={accessConfig.eventScreen.downloadShirts}>
          <Col xs={12} sm={6} lg={3}>
            <ButtonDownload
              event={event}
              fileName={generateFileName('Футболки')}
              label={DOWNLOAD_TYPES.listShirt.label}
              name="listShirt"
              downloadCityId={downloadCityId}
              eventCityPublicId={event.public_id}
              variant={DOWNLOAD_TYPES.listShirt.variant}
              icon={DOWNLOAD_TYPES.listShirt.icon}
              description={DOWNLOAD_TYPES.listShirt.description}
            />
          </Col>
        </CheckPermission>
      </Row>

      {/* Participants with Settings */}
      <Card className="border-0 shadow-sm">
        <Card.Header className="bg-primary text-white py-2">
          <div className="d-flex align-items-center">
            <i className="bi bi-people me-2"></i>
            <h6 className="mb-0">Список участников</h6>
          </div>
        </Card.Header>
        <Card.Body className="p-3">
          <Row className="align-items-center">
            <Col xs={12} lg={4} className="mb-3 mb-lg-0">
              <ButtonDownload
                event={event}
                fileName={generateFileName(
                  'Участники',
                  isAdditionalFields || isCompany
                    ? `Участники${isAdditionalFields ? '_доп_поля' : ''}${isCompany ? '_компании' : ''}`
                    : 'Участники'
                )}
                label={DOWNLOAD_TYPES.listMembers.label}
                name="listMembers"
                downloadCityId={downloadCityId}
                eventCityPublicId={downloadCityId}
                params={returnParams()}
                variant={DOWNLOAD_TYPES.listMembers.variant}
                icon={DOWNLOAD_TYPES.listMembers.icon}
                description={DOWNLOAD_TYPES.listMembers.description}
              />
            </Col>

            <Col xs={12} lg={8}>
              <div className="d-flex flex-column gap-2">
                <h6 className="mb-2 text-muted">
                  <i className="bi bi-gear me-2"></i>
                  Дополнительные настройки
                </h6>
                <div className="d-flex flex-wrap gap-3">
                  <Form.Check
                    type="switch"
                    id="checkAdditionalFields"
                    label="Дополнительные поля"
                    onChange={() => setIsAdditionalFields(!isAdditionalFields)}
                    checked={isAdditionalFields}
                    style={{ userSelect: 'none' }}
                  />

                  <Form.Check
                    type="switch"
                    id="checkCompany"
                    label="Информация о компаниях"
                    onChange={() => setIsCompany(!isCompany)}
                    checked={isCompany}
                    style={{ userSelect: 'none' }}
                  />
                </div>
                {(isAdditionalFields || isCompany) && (
                  <small className="text-muted">
                    <i className="bi bi-info-circle me-1"></i>
                    Файл будет содержать {isAdditionalFields && 'дополнительные поля'}
                    {isAdditionalFields && isCompany && ' и '}
                    {isCompany && 'информацию о компаниях'}
                  </small>
                )}
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </div>
  )
}

export default EventDownloads
