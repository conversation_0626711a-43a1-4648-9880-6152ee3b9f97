import { useState } from 'react'
import { Button, Col, Form, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'

export const MyDocumentPopup = ({
  isChangeDocumentPopup,
  setIsChangeDocumentPopup,
  handleChangeDocument,
  currentDocument,
  sections,
}) => {
  const [public_id, setPublicId] = useState(null)
  const [title, setTitle] = useState(null)
  const [description, setDescription] = useState(null)
  const [isPublic, setIsPublic] = useState(false)

  const handleSubmit = () => {
    handleChangeDocument({
      public_id: currentDocument?.public_id,
      public: isPublic,
      description: description || currentDocument?.description,
      title: title || currentDocument?.title,
      section: { public_id: public_id || currentDocument?.section?.public_id },
    })
  }

  const handleChangeToggle = (evt) => {
    const value = evt.target.checked
    setIsPublic(value)
  }

  return (
    <Modal show={isChangeDocumentPopup} onHide={() => setIsChangeDocumentPopup(false)}>
      <Modal.Header closeButton>
        <Modal.Title>Редактирование</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Row>
          <FormGroup controlId="titleForm">
            <Row>
              <Col>
                <Form.Label style={{ marginLeft: '10px' }}>Название</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="text"
                  name="title"
                  style={{ border: '1px solid black' }}
                  placeholder="title"
                  onChange={(e) => setTitle(e.target.value)}
                  defaultValue={currentDocument?.title}
                  className="mb-3"
                  required
                />
              </Col>
            </Row>
          </FormGroup>
          <FormGroup controlId="descriptionForm">
            <Row>
              <Col>
                <Form.Label style={{ marginLeft: '10px' }}>Описание</Form.Label>
              </Col>
              <Col>
                <FormControl
                  type="text"
                  name="description"
                  style={{ border: '1px solid black' }}
                  placeholder="description"
                  onChange={(e) => setDescription(e.target.value)}
                  defaultValue={currentDocument?.description}
                  className="mb-3"
                  required
                />
              </Col>
            </Row>
          </FormGroup>
          <Col className="d-flex align-items-center" md={3}>
            <Form.Check
              onChange={handleChangeToggle}
              checked={currentDocument?.public}
              name="public"
              type="switch"
              id="public-switch-docs"
              label={<span style={{ whiteSpace: 'nowrap' }}>Отображать на странице</span>}
            />
          </Col>
          <Form.Group controlId="formFile">
            <Form.Select
              required
              style={{ marginTop: '10px', border: '1px solid black' }}
              onChange={(e) => setPublicId(e.target.value)}
              defaultValue={currentDocument?.section?.public_id}
            >
              <option disabled selected>
                Выберите раздел
              </option>

              {sections &&
                sections.map((el) => (
                  <option key={el.public_id} value={el.public_id}>
                    {el.name}
                  </option>
                ))}
            </Form.Select>
          </Form.Group>
        </Row>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="link" onClick={() => setIsChangeDocumentPopup(false)}>
          Отменить
        </Button>
        <Button onClick={handleSubmit}>Сохранить</Button>
      </Modal.Footer>
    </Modal>
  )
}
