import { Button, Col, Form, FormControl, FormGroup, Modal, Row } from 'react-bootstrap'

export const SectionPopup = ({
  isChangeSectionPopup,
  setChangeSectionPopup,
  isNewSection,
  handleNewSection,
  handleChangeItem,
  setNewNameSection,
  currentSection,
}) => {
  return (
    <Modal show={isChangeSectionPopup} onHide={() => setChangeSectionPopup(false)}>
      <Modal.Header closeButton>
        <Modal.Title>{isNewSection ? 'Добавить раздел' : 'Редактирование'}</Modal.Title>
      </Modal.Header>
      <Modal.Footer>
        <FormGroup controlId="nameForm">
          <Row>
            <Col>
              <Form.Label>Введите название раздела</Form.Label>
            </Col>
            <Col>
              <FormControl
                type="text"
                name="name"
                style={{ border: '1px solid black' }}
                placeholder="sections name"
                onChange={(e) => setNewNameSection(e.target.value)}
                defaultValue={isNewSection ? null : currentSection?.name}
                className="mb-3"
                required
              />
            </Col>
          </Row>
        </FormGroup>
        <Button variant="link" onClick={() => setChangeSectionPopup(false)}>
          Отменить
        </Button>
        <Button onClick={isNewSection ? handleNewSection : handleChangeItem}>Сохранить</Button>
      </Modal.Footer>
    </Modal>
  )
}
