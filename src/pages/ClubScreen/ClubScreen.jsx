import useAxios from 'axios-hooks'
import { useEffect, useState, useCallback } from 'react'
import { Button, Card, Col, Row, Tab, Tabs } from 'react-bootstrap'
import { useNavigate, useParams } from 'react-router-dom'

import Loader from '@/components/Loader/Loader'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { queryClient } from '@/lib/react-query'
import ClubUsersTable from '@/pages/ClubScreen/components/ClubUsersTable/ClubUsersTable'
import { times } from '@/utils/common'
import { getImageSrc } from '@/utils/images'

import styles from './ClubScreen.module.scss'
import ClubStatisticsTab from './components/ClubStatisticsTab/ClubStatisticsTab'
import CompetitionResultsTable from './components/CompetitionResultsTable/CompetitionResultsTable'
import Layout from '../../components/Layout/Layout'
import ClubsFormModal from '../../components/Modal/ClubsFormModal/ClubsFormModal'
import ConfirmDeleteModal from '../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'

function CardInfo({ label, value }) {
  return (
    <Card>
      <Card.Body>
        <p className={styles.label}>{label}</p>
        <p className={styles.text}>{value}</p>
      </Card.Body>
    </Card>
  )
}

function ClubEventTypes({ club }) {
  const len = club?.event_type?.length - 1

  return <span>{club?.event_type?.map((item, index) => `${item.title}${index === len ? '' : ', '}`)}</span>
}

function ClubScreen() {
  const [confirmModal, setConfirmModal] = useState(false)
  const [isOpenFormModal, setIsOpenFormModal] = useState(false)
  const [miniStatistics, setMiniStatistics] = useState({
    all: 0,
    valid: 0,
    invalid: 0,
  })

  const { public_id } = useParams()
  const navigate = useNavigate()
  const openToast = useToast()

  const [{ data: club, loading: isLoadingStatistics }, getClubStatistics] = useAxios(
    {
      url: `${APIRoute.GET_CLUB_STATISTICS}/${public_id}`,
      method: 'GET',
    },
    { manual: true }
  )

  const [, apiDelete] = useAxios(
    {
      url: `${APIRoute.ACTIONS_CLUB}`,
      method: 'DELETE',
    },
    { manual: true }
  )

  const getClubs = useCallback(() => {
    getClubStatistics()
  }, [getClubStatistics])

  useEffect(() => {
    if (public_id) {
      getClubs()
    }
  }, [public_id, getClubs])

  const handleEdit = () => {
    setIsOpenFormModal(true)
  }

  const handleCloseModal = (state) => {
    setIsOpenFormModal(state)
  }

  const handleConfirmDelete = () => {
    setConfirmModal(true)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ url: `${APIRoute.ACTIONS_CLUB}/${public_id}` }).then((r) => {
      if (r.status === 200 && r.data.message) {
        queryClient.invalidateQueries(['clubs'])
        openToast.success({ message: 'Клуб был удалён' })
        navigate('/clubs')
      }
    })
  }

  return (
    <Layout>
      <Loader isLoading={isLoadingStatistics}>
        {club && (
          <>
            <Row className="mb-4">
              <Col xs="auto" md="auto">
                <div className={styles.logoWrap}>
                  <img className={styles.logo} src={getImageSrc(club?.picture)} alt="" />
                </div>
              </Col>
              <Col>
                <Row>
                  <h2>{club?.title}</h2>
                </Row>
                <Row>
                  <p>{club?.description}</p>
                </Row>
                <Row>
                  <Col className="px-0" md="auto">
                    <Button onClick={handleEdit} className={styles.link} variant="link">
                      Редактировать
                    </Button>
                  </Col>
                  <Col className="px-0" md="auto">
                    <Button onClick={handleConfirmDelete} className={styles.link} variant="link">
                      Удалить
                    </Button>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row className="mb-4 g-3">
              <Col md="auto">
                <CardInfo label="Город" value={club?.city?.name_ru} />
              </Col>
              <Col md="auto">
                <CardInfo
                  label="Дата создания"
                  value={club?.created_date ? times.getFullDate(club.created_date) : ''}
                />
              </Col>
              <Col md="auto">
                <CardInfo label="Участников" value={club?.total_users} />
              </Col>
              <Col md="auto">
                <CardInfo label="Всего результатов" value={miniStatistics.all} />
              </Col>
              <Col md="auto">
                <CardInfo label="Подтверждены" value={miniStatistics.valid} />
              </Col>
              <Col md="auto">
                <CardInfo label="Не подтверждены" value={miniStatistics.invalid} />
              </Col>
              <Col md="auto">
                <CardInfo label="Типы событий" value={<ClubEventTypes club={club} />} />
              </Col>
            </Row>
          </>
        )}

        <Tabs className="mb-4">
          <Tab eventKey="keyResults" title="Результаты">
            <CompetitionResultsTable
              clubPublicId={public_id}
              onUpdateStatistics={setMiniStatistics}
              clubName={club?.title}
            />
          </Tab>
          <Tab eventKey="keyUsers" title="Пользователи">
            <ClubUsersTable clubPublicId={public_id} clubName={club?.title} />
          </Tab>
          <Tab eventKey="keyStatistics" title="Статистика">
            <ClubStatisticsTab clubPublicId={public_id} />
          </Tab>
        </Tabs>

        <ClubsFormModal
          isShowModal={isOpenFormModal}
          onCloseModal={handleCloseModal}
          selectedItem={club || {}}
          clubPublicId={public_id}
          isViewForm={false}
          onUpdateClubs={getClubStatistics}
        />

        <ConfirmDeleteModal
          isShow={confirmModal}
          onClose={setConfirmModal}
          onDeleteItem={handleDeleteItem}
          text="Удалить клуб?"
        />
      </Loader>
    </Layout>
  )
}

export default ClubScreen
