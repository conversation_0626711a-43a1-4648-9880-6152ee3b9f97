import useAxios from 'axios-hooks'
import { useCallback, useEffect, useState } from 'react'
import { Col, Row } from 'react-bootstrap'

import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import PageSearch from '@/components/PageSearch/PageSearch'
import TableTemplate from '@/components/TableTemplate/TableTemplate'

import { APIRoute } from '@/const'
import { clubUsersData } from '@/pages/ClubScreen/components/ClubUsersTable/clubUsersData'

function ClubUsersTable({ clubPublicId, clubName }) {
  const [filteredValues, setFilteredValues] = useState([])

  const [{ data: users }, apiGetUsers] = useAxios(
    {
      url: `${APIRoute.GET_CLUB_USERS}/${clubPublicId}`,
      method: 'GET',
    },
    { manual: true }
  )

  const fetchUsers = useCallback(() => {
    if (clubPublicId) {
      apiGetUsers()
    }
  }, [clubPublicId, apiGetUsers])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  if (users?.values?.length < 0) return 'Нет пользователей'

  return (
    <>
      <Row className="mb-4">
        <Col>
          <PageSearch values={users?.values || []} setValues={setFilteredValues} />
        </Col>
      </Row>

      <Row className="mb-4">
        <Col />
        <Col md="auto">
          <DownloadButton
            url={`${APIRoute.CLUB_USERS_XLSX}/${clubPublicId}`}
            fileName={`Участники клуба ${clubName}.xlsx`}
            label="Скачать"
          />
        </Col>
      </Row>

      <TableTemplate values={filteredValues} data={clubUsersData} />
    </>
  )
}

export default ClubUsersTable
