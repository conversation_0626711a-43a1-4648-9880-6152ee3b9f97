import useAxios from 'axios-hooks'
import PhotoSwipeLightbox from 'photoswipe/lightbox'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Button, Col, Modal, OverlayTrigger, Row } from 'react-bootstrap'
import 'photoswipe/style.css'

import DownloadButton from '@/components/EventInfo/components/DownloadButton/DownloadButton'
import PageSearch from '@/components/PageSearch/PageSearch'

import { APIRoute } from '@/const'
import { useToast } from '@/hooks/useToast'
import { toHHMMSS } from '@/utils/common'
import { unixToMoment } from '@/utils/date'
import { getImageSrc } from '@/utils/images'
import { renderBtnTooltip } from '@/utils/tooltips.jsx'

import styles from './CompetitionResultsTable.module.scss'
import ConfirmDeleteModal from '../../../../components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import CompetitionResultForm from '../CompetitionResultForm/CompetitionResultForm'

function Photos({ photos, index }) {
  const lightbox = useRef(null)

  useEffect(() => {
    lightbox.current = new PhotoSwipeLightbox({
      gallery: `#gallery-${index}`,
      children: 'a',
      pswpModule: () => import('photoswipe'),
    })
    lightbox.current.addFilter('itemData', (itemData) => {
      return {
        src: itemData.element.href,
        width: itemData.element.children[0].naturalWidth,
        height: itemData.element.children[0].naturalHeight,
      }
    })
    lightbox.current.init()

    return () => {
      lightbox.current.destroy()
    }
  }, [index])

  const handleOpenGallery = (event) => {
    event.preventDefault()
    lightbox.current.loadAndOpen(0)
  }

  return (
    <>
      <a className={styles.linkImg} href="/" onClick={handleOpenGallery}>
        посмотреть
      </a>

      <div className="visually-hidden" id={`gallery-${index}`}>
        {photos.map((image, index) => (
          <a
            href={getImageSrc(image)}
            data-pswp-width={800}
            data-pswp-height={300}
            key={image + '-' + index}
            target="_blank"
            rel="noreferrer"
          >
            <img className={styles.picture} src={getImageSrc(image)} alt="" />
          </a>
        ))}
      </div>
    </>
  )
}

function CompetitionResultsTable({ clubPublicId, clubName, onUpdateStatistics }) {
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeleteItem, setSelectedDeleteItem] = useState('')
  const [selectedItem, setSelectedItem] = useState({})
  const [isOpenModalForm, setIsModalForm] = useState(false)
  const [filteredValues, setFilteredValues] = useState([])

  const openToast = useToast()

  const [, api] = useAxios({}, { manual: true })

  const [{ data: competitions }, apiCompetitions] = useAxios(
    {
      url: APIRoute.GET_CLUB_COMPETITIONS,
      method: 'GET',
    },
    { manual: true }
  )

  const [{ data: competitionsType }] = useAxios(
    {
      url: APIRoute.GET_COMPETITIONS_TYPE,
      method: 'GET',
    },
    { manual: false }
  )

  const [, apiDelete] = useAxios(
    {
      url: `${APIRoute.UD_CLUB_COMPETITION}`,
      method: 'DELETE',
    },
    { manual: true }
  )

  const getCompetitions = useCallback(() => {
    apiCompetitions({ url: `${APIRoute.GET_CLUB_COMPETITIONS}/${clubPublicId}` }).then((r) => {
      if (r?.data?.values?.length > 0) {
        const values = r.data.values
        const all = values.length
        let valid = 0
        let invalid = 0
        values.forEach((item) => {
          item.valid ? (valid += 1) : (invalid += 1)
        })
        onUpdateStatistics({ all, valid, invalid })
      }
    })
  }, [apiCompetitions, clubPublicId, onUpdateStatistics])

  useEffect(() => {
    if (clubPublicId) {
      getCompetitions()
    }
  }, [clubPublicId, getCompetitions])

  useEffect(() => {
    if (competitions?.values) {
      setFilteredValues(competitions.values)
    }
  }, [competitions?.values])

  const getCompetitionTypeName = (id) => {
    const findType = competitionsType?.values?.find((item) => item.public_id === id)

    return findType ? findType.title : null
  }

  const handleClickConformResult = (id, isValid) => {
    api({
      url: `${APIRoute.UD_CLUB_COMPETITION}/${id}`,
      method: 'PUT',
      data: { valid: isValid },
    }).then(() => {
      getCompetitions()
      openToast.success({ message: 'Статус подтверждения изменён' })
    })
  }

  const handleClickEdit = (item) => {
    setSelectedItem(item)
    setIsModalForm(true)
  }

  const handleClickCloseModal = () => {
    setSelectedItem({})
    setIsModalForm(false)
  }

  const handleConfirmDelete = (item) => {
    setConfirmModal(true)
    setSelectedDeleteItem(item)
  }

  const handleDeleteItem = () => {
    setConfirmModal(false)

    apiDelete({ url: `${APIRoute.UD_CLUB_COMPETITION}/${selectedDeleteItem.public_id}` }).then((r) => {
      if (r.status === 200 && r.data.message) {
        getCompetitions()
        openToast.success({ message: 'Результат был удалён' })
        setSelectedDeleteItem('')
      }
    })
  }

  if (competitions?.values?.length === 0) return <p>Нет результатов</p>

  return (
    <>
      <Row className="mb-4">
        <Col>
          <PageSearch values={competitions?.values || []} setValues={setFilteredValues} />
        </Col>
      </Row>

      <Row className="mb-4">
        <Col />
        <Col md="auto">
          <DownloadButton
            url={`${APIRoute.CLUB_COMPETITION_XLSX}/${clubPublicId}`}
            fileName={`Результаты клуба ${clubName}.xlsx`}
            label="Скачать"
          />
        </Col>
      </Row>

      <Row>
        <Col className="px-4">
          <Row className="mb-1">
            <Col>
              <Row>
                <Col className={styles.th} md={1}>
                  Результат
                </Col>
                <Col className={styles.th} md={3}>
                  Имя Фамилия
                </Col>
                <Col className={styles.th}>Дата события</Col>
                <Col className={styles.th}>Дата добавления</Col>
                <Col className={styles.th}>Изображения</Col>
                <Col className={styles.th} md={3}>
                  Тип соревнования
                </Col>
              </Row>
            </Col>
          </Row>
          {filteredValues?.map((item, rowIndex) => (
            <Row className="mb-2" key={item.public_id}>
              <Col className={`${styles.rowWrap} ${item.valid ? styles.validRow : styles.invalidRow}`}>
                <Row className={styles.row}>
                  <Col md={1}>{toHHMMSS(item.passing_time)}</Col>
                  <Col md={3}>
                    {item.user.name} {item.user.last_name}
                  </Col>
                  <Col>{unixToMoment(item.event_date).format('DD MMMM YYYY')}</Col>
                  <Col>{unixToMoment(item.created_date).format('DD MMMM YYYY')}</Col>
                  <Col>
                    {Array.isArray(item.picture) && item.picture?.length > 0 ? (
                      <Photos photos={item.picture} index={rowIndex} />
                    ) : (
                      <span className={styles.invalid}>не загружено</span>
                    )}
                  </Col>
                  <Col md={3}>{getCompetitionTypeName(item.competition_type.public_id)}</Col>
                  <div className={styles.actions}>
                    <Col md="auto">
                      <Button
                        onClick={() => handleClickConformResult(item.public_id, !item.valid)}
                        className={`${styles.btn} ${item.valid ? styles.btnDanger : styles.btnSuccess}`}
                        size="sm"
                      >
                        {item.valid ? 'Отменить' : 'Подтвердить'}
                      </Button>
                    </Col>
                    <OverlayTrigger
                      placement="left"
                      delay={{ show: 250, hide: 400 }}
                      overlay={(evt) => renderBtnTooltip(evt, 'Редактировать')}
                    >
                      <Button onClick={() => handleClickEdit(item)} className={styles.btnEdit} type="button" />
                    </OverlayTrigger>
                    <OverlayTrigger
                      placement="left"
                      delay={{ show: 250, hide: 400 }}
                      overlay={(evt) => renderBtnTooltip(evt, 'Удалить')}
                    >
                      <Button
                        onClick={() => handleConfirmDelete(item)}
                        className={`${styles.btnEdit} ${styles.btnDelete}`}
                        type="button"
                      />
                    </OverlayTrigger>
                  </div>
                </Row>
              </Col>
            </Row>
          ))}
        </Col>

        <Modal show={isOpenModalForm} onHide={handleClickCloseModal}>
          <Modal.Header closeButton />
          <Modal.Body>
            <CompetitionResultForm
              selectedItem={selectedItem}
              competitionsType={competitionsType?.values}
              onUpdateValues={getCompetitions}
              onCloseModal={handleClickCloseModal}
            />
          </Modal.Body>
          <Modal.Footer>
            <Button type="submit" variant="success" form="form">
              Сохранить
            </Button>
            <Button onClick={handleClickCloseModal} variant="outline-secondary">
              Отмена
            </Button>
          </Modal.Footer>
        </Modal>

        <ConfirmDeleteModal
          isShow={confirmModal}
          onClose={setConfirmModal}
          onUpdateSelectedDeleteItem={setSelectedDeleteItem}
          onDeleteItem={handleDeleteItem}
          text="Удалить результат?"
        />
      </Row>
    </>
  )
}

export default CompetitionResultsTable
