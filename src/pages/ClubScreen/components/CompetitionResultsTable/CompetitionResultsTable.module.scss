.th {
  font-weight: 500;
}

.rowWrap {
  position: relative;
  padding: 8px 12px;
  padding-left: 18px;

  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;

  &::before {
    position: absolute;
    top: 0;
    left: 0;

    display: block;
    width: 6px;
    height: 100%;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;

    content: '';
  }
}

.validRow {
  &::before {
    background-color: #5debab;
  }
}

.invalidRow {
  &::before {
    background-color: #dc3545;
  }
}

.linkImg {
  color: #5debab;
}

.invalid {
  color: #dc3545;
}

.row {
  position: relative;
  padding: 14px 0;

  background-color: #ffffff;

  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

.actions {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  padding-left: 70px;

  display: grid;
  grid-template-columns: auto auto auto;
  justify-content: space-around;
  align-items: center;
  width: 280px;
  height: 100%;
  background: linear-gradient(-90deg, rgb(255, 255, 255) 80%, rgba(255, 255, 255, 0) 100%);

  opacity: 0;

  transition: opacity 0.2s;
}

.row:hover .actions {
  opacity: 1;
}

.btn {
  color: #ffffff;

  border: 0;

  &:hover,
  &:active {
    color: #ffffff;
  }
}

.btnSuccess {
  background: linear-gradient(267.53deg, #1ced2b -0.27%, #2047b9 100%), #c7252b;

  &:hover,
  &:active {
    background: linear-gradient(90deg, #1ced2b -0.27%, #2047b9 100%), #c7252b;
  }
}

.btnDanger {
  background: linear-gradient(267.53deg, #ed1c24 -0.27%, #8820b9), #c7252b;

  &:hover,
  &:active {
    background: linear-gradient(90deg, #ed1c24 -0.27%, #8820b9), #c7252b;
  }
}

.btnEdit {
  width: 16px;
  height: 16px;

  background-color: transparent;
  background-image: url('../../../../assets/img/icons/icon_edit.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  border: 0;

  &:hover,
  &:active,
  &:focus {
    background-color: transparent;
    box-shadow: none;
  }
}

.btnDelete {
  background-image: url('../../../../assets/img/icons/icon-delete.svg');
}
