import { useState } from 'react'
import { But<PERSON>, Col, Form, Row, Alert } from 'react-bootstrap'

import LineChart from '@/components/Charts/LineChart/LineChart'

import { useGetClubJoinStatistics } from '@/features/clubs/api/getClubJoinStatistics'
import { useToast } from '@/hooks/useToast'
import { times } from '@/utils/common'

function ClubStatisticsTab({ clubPublicId }) {
  const today = new Date()
  const monthAgo = new Date()
  monthAgo.setMonth(monthAgo.getMonth() - 1)

  const [startDate, setStartDate] = useState(monthAgo.toISOString().split('T')[0])
  const [endDate, setEndDate] = useState(today.toISOString().split('T')[0])
  const [statisticsData, setStatisticsData] = useState(null)
  const [isAllClubs, setIsAllClubs] = useState(false)

  const openToast = useToast()
  const { mutate: getStatistics, isLoading } = useGetClubJoinStatistics()

  const handleGetStatistics = (includeClubId = true) => {
    if (!startDate || !endDate) {
      openToast.error({ message: 'Пожалуйста, выберите даты "от" и "до"' })
      return
    }

    if (new Date(startDate) > new Date(endDate)) {
      openToast.error({ message: 'Дата "от" не может быть больше даты "до"' })
      return
    }

    const requestData = {
      start_date: new Date(startDate).toISOString(),
      end_date: new Date(endDate).toISOString(),
    }

    // Добавляем ID клуба только если нужно
    if (includeClubId) {
      requestData.club_public_id = clubPublicId
    }

    getStatistics(requestData, {
      onSuccess: (response) => {
        if (response.status === 200 && response.data) {
          setStatisticsData(response.data)
          setIsAllClubs(!includeClubId)
          const message = includeClubId
            ? 'Статистика клуба успешно загружена'
            : 'Статистика по всем клубам успешно загружена'
          openToast.success({ message })
        }
      },
      onError: (error) => {
        console.error('Error fetching statistics:', error)
        openToast.error({ message: 'Ошибка при загрузке статистики' })
      },
    })
  }

  const handleGetClubStatistics = () => handleGetStatistics(true)
  const handleGetAllClubsStatistics = () => handleGetStatistics(false)

  const chartData = statisticsData
    ? {
        labels: statisticsData.values.map((item) => times.getFullDate(item.date)),
        datasets: [
          {
            label: 'Вступлений',
            data: statisticsData.values.map((item) => item.count),
          },
        ],
      }
    : null

  return (
    <div>
      <Row className="mb-4">
        <Col md={3}>
          <Form.Group>
            <Form.Label>Дата от</Form.Label>
            <Form.Control type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group>
            <Form.Label>Дата до</Form.Label>
            <Form.Control type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
          </Form.Group>
        </Col>
        <Col md={6} className="d-flex align-items-end gap-2">
          <Button variant="primary" onClick={handleGetClubStatistics} disabled={isLoading}>
            {isLoading ? 'Загрузка...' : 'По этому клубу'}
          </Button>
          <Button variant="outline-primary" onClick={handleGetAllClubsStatistics} disabled={isLoading}>
            {isLoading ? 'Загрузка...' : 'По всем клубам'}
          </Button>
        </Col>
      </Row>

      {statisticsData && (
        <div className="w-100" style={{ width: '100%', minWidth: '100%' }}>
          {statisticsData.values.length > 0 ? (
            <LineChart
              data={chartData}
              title={isAllClubs ? 'Статистика вступлений по всем клубам' : 'Статистика вступлений в клуб'}
              height={450}
              showLegend={true}
              showGrid={true}
              tension={0.4}
              fill={true}
            />
          ) : (
            <Alert variant="info">За выбранный период данных о вступлениях не найдено</Alert>
          )}
        </div>
      )}
    </div>
  )
}

export default ClubStatisticsTab
