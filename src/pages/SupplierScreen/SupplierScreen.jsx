import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { ButtonGroup, Col, Dropdown, Row } from 'react-bootstrap'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import styles from './SupplierScreen.module.scss'
import { suppliersTableData } from './supplierScreenData'
import Layout from '../../components/Layout/Layout'
import SuccessModal from '../../components/Modal/SuccessModal/SuccessModal'
import PagePreloader from '../../components/PagePreloader/PagePreloader'
import TableTemplate from '../../components/TableTemplate/TableTemplate'
import { APIRoute, AppRoute } from '../../const'
import { setSupplierEdit } from '../../store/action'

function SupplierScreen() {
  const [{ data: suppliers, loading: isLoadingSuppliers }, apiSuppliers] = useAxios(
    {
      url: APIRoute.SUPPLIER,
      method: 'GET',
    },
    { manual: true }
  )
  const [, apiDeleteSupplier] = useAxios(
    {
      url: APIRoute.SUPPLIER,
      method: 'DELETE',
    },
    {
      manual: true,
    }
  )
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [showDeleteModal, setShowDeleteModal] = useState(false)

  useEffect(() => {
    apiSuppliers()
  }, [apiSuppliers])

  const returnActionsTable = (item) => {
    return (
      <td onClick={(evt) => evt.stopPropagation()}>
        <Dropdown>
          <Dropdown.Toggle as={ButtonGroup} bsPrefix={`${styles.hideArrow}`} variant="success" id="dropdown-basic">
            &bull; &bull; &bull;
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item onClick={() => handleEditSupplier(item)}>Редактировать</Dropdown.Item>
            <Dropdown.Item className={styles.danger} onClick={() => handleDeleteSupplier(item)}>
              Удалить
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    )
  }

  const handleDeleteSupplier = (item) => {
    const body = {
      suppliername: item.suppliername,
      event_city: {
        public_id: item.event_city.public_id,
      },
      public_id: item.public_id,
      supplierinn: item.supplierinn,
      vat: item.vat,
      supplierphone: item.supplierphone,
    }

    apiDeleteSupplier({ data: body }).then((r) => {
      if (r.status === 200) {
        setShowDeleteModal(true)
        apiSuppliers()
      }
    })
  }

  const handleEditSupplier = (item) => {
    dispatch(setSupplierEdit(item))
    navigate(AppRoute.CREATE_SUPPLIER_SCREEN)
  }

  return (
    <Layout title="Платёжные Агенты" href="create-supplier">
      <PagePreloader isLoading={isLoadingSuppliers}>
        <Row>
          <Col>
            <div className={`${styles.wrap}`} style={{ overflow: 'auto' }}>
              <div style={{ minWidth: '2200px' }}>
                <TableTemplate data={suppliersTableData} values={suppliers?.values} actions={returnActionsTable} />
              </div>
            </div>
          </Col>
        </Row>
      </PagePreloader>

      <SuccessModal show={showDeleteModal} handleCloseModal={setShowDeleteModal} description="Запись удалена." />
    </Layout>
  )
}

export default SupplierScreen
