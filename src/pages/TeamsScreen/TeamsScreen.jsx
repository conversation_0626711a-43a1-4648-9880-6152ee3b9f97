import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'

import { useGetGroup } from '@/features/groups/api'

import Layout from '../../components/Layout/Layout'
import Participants from '../AthletesScreen/components/Participants/Participants'

export const TeamsScreen = () => {
  const { public_id, title } = useParams()

  const { data: groups = [] } = useGetGroup(public_id)
  const [group, setGroup] = useState([])

  useEffect(() => {
    setGroup(groups)
  }, [groups])

  return (
    <Layout title={title}>
      <Participants allAthletes={group} />
    </Layout>
  )
}
