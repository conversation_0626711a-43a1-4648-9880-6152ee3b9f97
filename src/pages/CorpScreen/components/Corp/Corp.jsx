import useAxios from 'axios-hooks'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Accordion, Button, Col, OverlayTrigger, Row, Toast, ToastContainer } from 'react-bootstrap'
import { Link } from 'react-router-dom'

import Loader from '@/components/Loader/Loader'
import Paginator from '@/components/Paginator/Paginator'

import { APIRoute } from '@/const'
import { Order } from '@/features/orders'
import { useDeleteCompanyOrder } from '@/features/orders/api/deleteCompanyOrder'
import { useGetOrderTickets } from '@/features/tickets'
import SearchCorp from '@/pages/CorpScreen/components/SearchCorp/SearchCorp'
import { renderEditBtnTooltip } from '@/pages/ShopScreen/components/constants.jsx'

const PAGE_LIMIT_PAGINATION = 10

function Corp({ kind }) {
  const [activeAccordion, setActiveAccordion] = useState(0)
  const [filteredValues, setFilteredValues] = useState([])
  const [totalPages, setTotalPages] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [isWarningSearch, setIsWarningSearch] = useState(false)
  const [isSuccessSearch, setIsSuccessSearch] = useState(false)
  const hasInitialLoadedRef = useRef(false)

  const [, setPagination] = useState({
    currentPage: null,
    current_values: [],
    totalPages: null,
    values: [],
    query: null,
  })
  const [{ data: orders, loading: isLoadingOrders }, api] = useAxios(
    {
      url: APIRoute.CORP_ORDERS,
      method: 'GET',
    },
    {
      manual: true,
    }
  )
  const getOrderTicketsQuery = useGetOrderTickets(orders?.values?.[activeAccordion]?.public_id)
  const tickets = getOrderTicketsQuery?.data?.data?.values
  const deleteOrderMutation = useDeleteCompanyOrder()

  const getOrders = useCallback(
    (skip, limit) => {
      api({ url: `/api/admin/corporate/orders/kind/${kind}/${skip}/${limit}` }).then((r) => {
        if (r.data?.values && r.data?.values?.length > 0) {
          setTotalPages(Math.ceil(r.data.count / PAGE_LIMIT_PAGINATION))
        }
      })
    },
    [api, kind]
  )

  useEffect(() => {
    if (!hasInitialLoadedRef.current) {
      hasInitialLoadedRef.current = true
      getOrders(0, PAGE_LIMIT_PAGINATION)
    }
  }, [getOrders])

  // Reset ref when kind changes to allow reloading for different kinds
  useEffect(() => {
    hasInitialLoadedRef.current = false
  }, [kind])

  useEffect(() => {
    if (orders && orders?.values) {
      setFilteredValues(orders.values)
      setPagination((prev) => ({ ...prev, values: orders.values }))
    }
  }, [orders])

  const handleToggleAccordion = (index) => {
    setActiveAccordion(index)
  }

  const handlePage = (pageNumber) => {
    setCurrentPage(pageNumber)
    const skip = PAGE_LIMIT_PAGINATION * pageNumber - PAGE_LIMIT_PAGINATION
    getOrders(skip, PAGE_LIMIT_PAGINATION)
  }

  const handleSearchOrders = (orderList) => {
    if (orderList.length > 0) {
      setTotalPages(null)
      setCurrentPage(1)
      setFilteredValues(orderList)
      setIsSuccessSearch(true)

      setPagination({
        currentPage: null,
        current_values: [],
        totalPages: null,
        values: [],
        query: null,
      })
    } else {
      setIsWarningSearch(true)
    }
  }

  const handleResetSearch = () => {
    if (!totalPages) {
      setFilteredValues(orders.values)
      setPagination((prev) => ({ ...prev, values: orders.values }))
      setTotalPages(Math.ceil(orders.count / PAGE_LIMIT_PAGINATION))
    }
  }

  const handleUpdateTickets = () => {
    getOrderTicketsQuery.refetch()
  }

  const handleDeleteOrder = (order) => {
    const data = {
      public_id: order.public_id,
    }

    deleteOrderMutation.mutate(data)
  }

  return (
    <>
      <SearchCorp onSearchOrders={handleSearchOrders} onResetSearch={handleResetSearch} kind={kind} />

      <ToastContainer position="top-end" className="p-3">
        <Toast onClose={() => setIsSuccessSearch(false)} show={isSuccessSearch} bg="success" delay={3000} autohide>
          <Toast.Header className="p-3">
            <span className="me-auto">
              Найдено заказов: <strong> {filteredValues.length}</strong>
            </span>
          </Toast.Header>
        </Toast>

        <Toast onClose={() => setIsWarningSearch(false)} show={isWarningSearch} bg="warning" delay={3000} autohide>
          <Toast.Header className="p-3">
            <span className="me-auto">Заказы по данному запросу не найдены!</span>
          </Toast.Header>
        </Toast>
      </ToastContainer>

      <Loader isLoading={isLoadingOrders} text="Загрузка заказов">
        {orders?.values?.length > 0 ? (
          <Accordion defaultActiveKey={`${orders?.values[0].public_id}`}>
            {filteredValues?.length > 0 &&
              filteredValues.map((order, index) => (
                <Accordion.Item eventKey={`${order.public_id}`} key={order.public_id}>
                  <Accordion.Header onClick={() => handleToggleAccordion(index)}>
                    <div className="me-2">
                      {order?.amount ? order.amount.toLocaleString() : 0} &#8381;, {order.email}
                    </div>
                    <OverlayTrigger placement="left" delay={{ show: 250, hide: 400 }} overlay={renderEditBtnTooltip}>
                      <Button
                        as={Link}
                        to={`/corp/form/${order.public_id}`}
                        onClick={(e) => e.stopPropagation()}
                        variant="outline-secondary"
                        size="sm"
                        className="border-0"
                      >
                        <i className="bi bi-pencil" />
                      </Button>
                    </OverlayTrigger>
                  </Accordion.Header>
                  <Accordion.Body className="pt-3">
                    {activeAccordion === index && (
                      <Order
                        order={order}
                        tickets={tickets}
                        onDeleteOrder={() => handleDeleteOrder(order)}
                        onUpdateTickets={handleUpdateTickets}
                      />
                    )}
                  </Accordion.Body>
                </Accordion.Item>
              ))}

            <Row className="mt-4">
              <Col>
                <Paginator currentPage={currentPage} totalPages={totalPages} changePageHandler={handlePage} />
              </Col>
            </Row>
          </Accordion>
        ) : (
          <p className="d-flex justify-content-center">Нет заказов</p>
        )}
      </Loader>
    </>
  )
}

export default Corp
