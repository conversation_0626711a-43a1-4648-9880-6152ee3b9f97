.alerts {
  position: fixed;
  top: 10px;
  right: 10px;

  z-index: 10;
}

.alertWarning {
}

.btnEdit {
  width: 16px;
  height: 16px;

  background-color: transparent;
  background-image: url('../../../../assets/img/icons/icon_edit.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  border: 0;

  &:hover,
  &:active {
    background-color: transparent;
  }
}
