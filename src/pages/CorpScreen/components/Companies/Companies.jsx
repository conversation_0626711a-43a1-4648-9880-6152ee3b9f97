import { useEffect, useState } from 'react'
import { Button, Col, Modal, Row } from 'react-bootstrap'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import PageSearch from '@/components/PageSearch/PageSearch'
import TableTemplate from '@/components/TableTemplate/TableTemplate'
import AddButton from '@/components/ui/AddButton/AddButton'

import { useGetCompanies } from '@/features/corp/api'
import CompaniesForm from '@/pages/CorpScreen/components/CompaniesForm/CompaniesForm'

import { corpCompaniesTableData } from './companiesData'

const EMPTY_ARRAY = []

function Companies() {
  const [filteredValues, setFilteredValues] = useState([]) // для пагинации
  const [renderValues, setRenderValues] = useState([]) // для отрисовки на странице
  const [isOpenModalForm, setIsModalForm] = useState(false)

  const { data: companiesData } = useGetCompanies({})
  const companies = companiesData?.data?.values || EMPTY_ARRAY

  useEffect(() => {
    if (companies) {
      setFilteredValues(companies)
    }
  }, [companies])

  const handleCloseFormModal = () => {
    setIsModalForm(false)
  }

  return (
    <>
      <Row className="mb-4">
        <Col>
          <PageSearch values={companies || EMPTY_ARRAY} setValues={setFilteredValues} />
        </Col>
        <Col md="auto">
          <AddButton onClickAddButton={() => setIsModalForm(true)} />
        </Col>
      </Row>

      <TableTemplate data={corpCompaniesTableData} values={renderValues} />

      <AdvancedPagination values={filteredValues} setValues={setRenderValues} />

      <Modal show={isOpenModalForm} onHide={handleCloseFormModal}>
        <Modal.Header closeButton />
        <Modal.Body>
          <CompaniesForm selectedItem={{}} onCloseModal={handleCloseFormModal} />
        </Modal.Body>
        <Modal.Footer>
          <Button type="submit" variant="success" form="form">
            Сохранить
          </Button>
          <Button onClick={handleCloseFormModal} variant="outline-secondary">
            Отмена
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default Companies
