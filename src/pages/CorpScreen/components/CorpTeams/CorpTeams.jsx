import { useState } from 'react'
import { <PERSON>ton, Col, FloatingLabel, FormControl, FormGroup, FormSelect, Modal, Row } from 'react-bootstrap'

import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import ActionsRow from '@/components/TableTemplate/ActionsRow/ActionsRow'
import TableTemplate from '@/components/TableTemplate/TableTemplate'
import AddButton from '@/components/ui/AddButton/AddButton'

import { useCreateCorpTeam, useDeleteCorpTeam, useGetCorpTeams, useUpdateCorpTeam } from '@/features/corp/api'
import { useGetEventFormats } from '@/features/events/api/getEventFormats'
import { useGetEvents } from '@/features/events/api/getEvents'
import { getFormatTime } from '@/utils/date'

import styles from './CorpTeams.module.scss'
import { CorpTeamsTableData } from './corpTeamsData'

export const CorpTeams = ({ company_public_id }) => {
  const [isModal, setIsModal] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [formatId, setFormatId] = useState(null)
  const [cityId, setCityId] = useState(null)
  const [teamName, setTeamName] = useState(null)
  const [isEdit, setIsEdit] = useState(false)
  const [isRemove, setIsRemove] = useState(false)
  const [currentTeamId, setCurrentTeamId] = useState(null)
  const [eventId, setEventId] = useState(null)
  const [eventCityPublicId, setEventCityPublicId] = useState('')

  const isDisabledSubmitButton = !formatId || !company_public_id || !cityId || !teamName

  const { data: eventsData } = useGetEvents()
  const events = eventsData?.data?.values || []
  const { data: formatData } = useGetEventFormats({}, eventCityPublicId)
  const formats = formatData?.data?.values || []
  const { data: teamsData } = useGetCorpTeams(company_public_id)
  const createTeam = useCreateCorpTeam(company_public_id)
  const updateTeam = useUpdateCorpTeam(company_public_id)
  const deleteTeam = useDeleteCorpTeam(company_public_id)
  const teams = teamsData?.data?.values || []

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = events.find((event) => event.public_id === eventId)

    setEventId(eventId)

    if (event) {
      setSelectedEvent(event)
    }
  }

  const handleChangeCity = (evt) => {
    const cityId = evt.target.value
    const eventCityPublicId = evt.target.options[evt.target.selectedIndex].dataset.eventCityPublicId
    setCityId(cityId)

    if (cityId !== 0 && cityId !== '') {
      setEventCityPublicId(eventCityPublicId)
    }
  }

  const handleChangeFormat = (evt) => {
    const formatId = evt.target.value
    setFormatId(formatId)
  }

  const handleClickButton = () => {
    setIsModal(true)
    setCityId(null)
    setFormatId(null)
    setTeamName('')
    setEventId(null)
    setIsEdit(false)
  }

  const handleCloseFormModal = () => {
    setIsModal(false)
    setTeamName('')
  }

  const handleSubmit = () => {
    const sendObj = {
      company: {
        public_id: company_public_id,
      },
      format: {
        public_id: formatId,
      },
      city: {
        id: Number(cityId),
      },
      name: teamName,
      /*  number: 0, */
    }

    if (formatId && company_public_id && cityId && teamName && !isEdit) {
      createTeam.mutate(sendObj)
      setIsModal(false)
      setTeamName('')
    } else if (isEdit) {
      sendObj.event = { public_id: eventId }
      updateTeam.mutate({ teamId: currentTeamId, data: sendObj })
      setIsEdit(false)
      setIsModal(false)
    }
  }

  const handleTeamNameChange = (e) => {
    setTeamName(e.target.value)
  }

  const handleEditRow = (data) => {
    setFormatId(data.format.public_id)
    setCityId(data.city.id)
    setTeamName(data.name)
    setEventId(data.event.public_id)
    setCurrentTeamId(data.public_id)

    const event = events.find((event) => event.public_id === data.event.public_id)

    if (event) {
      setSelectedEvent(event)

      const currentEvent = event.event_city.find((el) => el.city.id === data.city.id)

      if (data.city.id !== 0 && data.city.id !== '') {
        setEventCityPublicId(currentEvent.public_id)
      }
    }

    setIsEdit(true)
    setIsModal(true)
  }

  const handleConfirmDelete = (data) => {
    setCurrentTeamId(data.public_id)
    setIsRemove(true)
  }

  const handleDeleteTeam = () => {
    if (currentTeamId) {
      deleteTeam.mutate(currentTeamId)
      setIsRemove(false)
    }
  }

  const returnActionsTable = (data) => {
    return <ActionsRow item={data} onClickDelete={handleConfirmDelete} onClickEdit={handleEditRow} />
  }

  return (
    <>
      <div className={styles.corpWrap}>
        <div className={styles.addButton}>
          <AddButton onClickAddButton={handleClickButton} label="Cоздать команду" />
        </div>
      </div>
      <div className={styles.table}>
        {teams?.length ? (
          <TableTemplate actionsName="" data={CorpTeamsTableData} values={teams} actions={returnActionsTable} />
        ) : null}
      </div>
      <Modal show={isModal} onHide={handleCloseFormModal}>
        <Modal.Header closeButton />
        <Modal.Body>
          <div>
            <h4 className="mb-3">{isEdit ? 'Редактировать команду' : 'Cоздать команду'}</h4>
            <FormGroup controlId="teamNameForm">
              <Row>
                <Col>
                  <FormControl
                    type="text"
                    name="teamName"
                    placeholder="Название команды"
                    onChange={handleTeamNameChange}
                    className="mb-3"
                    value={teamName}
                    required
                  />
                </Col>
              </Row>
            </FormGroup>
            <Col xs={8}>
              <FloatingLabel controlId="eventLabelTicket" label="Событие">
                <FormSelect
                  className="mb-3"
                  required
                  onChange={handleChangeEvent}
                  name="event_public_id"
                  aria-label="Событие"
                  value={eventId}
                >
                  <option value="">выберите один из вариантов</option>
                  {events.map((event) => (
                    <option value={event.public_id} key={event.public_id}>
                      {event.title}
                    </option>
                  ))}
                </FormSelect>
              </FloatingLabel>
            </Col>

            <Col xs={8}>
              <FloatingLabel controlId="cityLabelTicket" label="Город">
                <FormSelect
                  required
                  className="mb-3"
                  onChange={handleChangeCity}
                  name="city_id"
                  aria-label="Город"
                  value={cityId}
                >
                  <option value="">выберите один из вариантов</option>
                  {selectedEvent?.event_city.map((city) => (
                    <option value={city.city.id} data-event-city-public-id={city.public_id} key={city.public_id}>
                      {city.city.name_ru} — {getFormatTime(city.start_time, city.timezone)}
                    </option>
                  ))}
                </FormSelect>
              </FloatingLabel>
            </Col>

            <Col xs={8}>
              <FloatingLabel controlId="formatLabelTicket" label="Формат">
                <FormSelect
                  required
                  onChange={handleChangeFormat}
                  name="format_public_id"
                  aria-label="Формат"
                  className="mb-3"
                  value={formatId}
                >
                  <option value="">выберите один из вариантов</option>
                  {formats?.map((format) => (
                    <option value={format.public_id} key={format.public_id}>
                      {format.title}
                    </option>
                  ))}
                </FormSelect>
              </FloatingLabel>
            </Col>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button disabled={isDisabledSubmitButton} type="submit" onClick={handleSubmit} variant="success" form="form">
            {isEdit ? 'Сохранить' : 'Создать'}
          </Button>
          <Button onClick={handleCloseFormModal} variant="outline-secondary">
            Закрыть
          </Button>
        </Modal.Footer>
      </Modal>
      <ConfirmDeleteModal isShow={isRemove} onClose={() => setIsRemove(false)} onDeleteItem={handleDeleteTeam} />
    </>
  )
}
