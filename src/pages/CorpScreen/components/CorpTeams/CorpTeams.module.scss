.corpWrap {
  display: flex;
}

.addButton {
  margin-left: auto;
}

.editBtnWrap {
  position: relative;
}

.editBtn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  display: grid;
  place-items: center;
  width: 32px;
  height: 32px;

  background-color: transparent;
  border: 0;
  border-radius: 4px;

  transition: background-color 0.2s;

  &:hover {
    background-color: #ffffff;
  }
}

.table {
  margin-top: 40px;
}

.hideArrow {
  display: grid;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: #646d8f;
  cursor: pointer;
}

.danger,
.danger:hover {
  color: #dc3545;
}
