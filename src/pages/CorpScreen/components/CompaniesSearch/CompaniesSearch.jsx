import { nanoid } from '@reduxjs/toolkit'
import React from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useGetCompaniesMutation } from '@/features/corp/api'
import { removeEmptyString } from '@/utils/common'

const fields = [
  {
    id: nanoid(),
    name: 'name',
    label: 'Название',
    type: 'text',
    required: true,
  },
  {
    id: nanoid(),
    name: 'fullname',
    label: 'Полное имя',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'inn',
    label: 'ИНН',
    type: 'text',
    pattern: /[0-9]{10}$/,
    required: false,
  },
  {
    id: nanoid(),
    name: 'description',
    label: 'Описание',
    type: 'text',
    required: false,
  },
  {
    id: nanoid(),
    name: 'contact_name',
    label: 'Имя',
    type: 'text',
    required: false,
  },
]

function CompaniesSearch() {
  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm()

  const getCompaniesMutation = useGetCompaniesMutation()

  const onSubmit = (data) => {
    const filteredData = Object.keys(data)
      .map((key) => {
        const name = key.replace('_', '.')
        return { [name]: data[key] }
      })
      .reduce((prev, cur) => {
        prev[Object.keys(cur)[0]] = Object.values(cur)[0]
        return prev
      }, {})

    const finalOutput = removeEmptyString(filteredData)

    if (Object.keys(finalOutput).length > 0) {
      getCompaniesMutation.mutate(finalOutput)
    }
  }

  const handleClickReset = () => {
    reset({
      name: '',
      fullname: '',
      inn: '',
      description: '',
      contact_name: '',
    })

    getCompaniesMutation.mutate({})
  }

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <Row className="g-3 mb-5">
        {fields.map((field) => (
          <Col md={4} key={field.id}>
            <Form.Group>
              <FloatingLabel controlId={`${field.name}Input`} label={field.label}>
                <FormControl
                  {...register(field.name, { pattern: field.pattern })}
                  type={field.type}
                  isInvalid={errors[field.name]}
                  placeholder={field.label}
                />
              </FloatingLabel>
            </Form.Group>
          </Col>
        ))}

        <Col className="d-flex align-items-center" md="auto">
          <Button type="submit">Искать</Button>
        </Col>
        <Col className="d-flex align-items-center" md="auto">
          <Button onClick={handleClickReset} variant="outline-secondary" type="button">
            Сбросить
          </Button>
        </Col>
      </Row>
    </Form>
  )
}

export default CompaniesSearch
