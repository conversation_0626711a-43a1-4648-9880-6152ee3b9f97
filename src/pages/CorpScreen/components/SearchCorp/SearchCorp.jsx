import useAxios from 'axios-hooks'
import { useState } from 'react'
import { Button, Col, FloatingLabel, FormControl, Row } from 'react-bootstrap'

import { APIRoute } from '@/const'
import { toLowerCaseEmail } from '@/utils/common'

import styles from './SearchCorp.module.scss'

function SearchCorp({ onSearchOrders, onResetSearch, kind }) {
  const [companyName, setCompanyName] = useState('')
  const [email, setEmail] = useState('')

  const [{ loading: isLoadingSearch }, fetchSearch] = useAxios(
    {
      url: APIRoute.CORP_ORDERS_SEARCH,
      method: 'POST',
    },
    {
      manual: true,
    }
  )

  const handleSearchCompanyName = () => {
    const body = {
      company_name: companyName,
      kind: kind,
    }

    searchOrders(body)
  }

  const handleSearchEmail = () => {
    const body = {
      email: email,
      kind: kind,
    }

    searchOrders(body)
  }

  const searchOrders = (body) => {
    fetchSearch({ data: body }).then((r) => {
      if (r.status === 200) {
        onSearchOrders(r.data.values)
      }
    })
  }

  const handleChangeEmail = (evt) => {
    const value = toLowerCaseEmail(evt.target.value)

    setEmail(value)
  }

  const handleClickResetSearch = () => {
    setCompanyName('')
    setEmail('')

    onResetSearch()
  }

  return (
    <Row className="mb-3">
      <Col className={`${styles.searchNumberWrap}`} md={6}>
        <FloatingLabel controlId="ticketCountLabelOrder" label="Поиск по названию компании">
          <FormControl
            className={styles.field}
            onChange={(e) => setCompanyName(e.target.value)}
            name="company_name"
            type="text"
            value={companyName}
            placeholder="Название компании"
          />
        </FloatingLabel>
        <Button
          className={styles.btn}
          onClick={handleSearchCompanyName}
          variant="primary"
          type="button"
          disabled={companyName.length === 0 || isLoadingSearch}
        >
          <i className="bi bi-search" />
        </Button>
      </Col>
      <Col className={`${styles.searchNumberWrap}`} md={6}>
        <FloatingLabel controlId="ticketCountLabelOrder" label="Поиск по email">
          <FormControl
            className={styles.field}
            onChange={handleChangeEmail}
            name="email"
            type="email"
            value={email}
            placeholder="Поиск по email"
          />
        </FloatingLabel>
        <Button
          className={styles.btn}
          onClick={handleSearchEmail}
          variant="primary"
          type="button"
          disabled={email.length === 0 || isLoadingSearch}
        >
          <i className="bi bi-search" />
        </Button>
      </Col>
      <Col md={12} style={{ minHeight: '38px' }}>
        {(companyName.length > 0 || email.length > 0) && (
          <Button variant="link" onClick={handleClickResetSearch}>
            Сбросить поиск
          </Button>
        )}
      </Col>
    </Row>
  )
}

export default SearchCorp
