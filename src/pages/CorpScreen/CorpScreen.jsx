import { useMemo } from 'react'
import { Tab, Tabs } from 'react-bootstrap'

import { Kind } from '@/const'

import Companies from './components/Companies/Companies'
import Corp from './components/Corp/Corp'
import Layout from '../../components/Layout/Layout'

const TAB_CONFIG = [
  { key: 'companies', title: 'Компании', component: Companies },
  { key: 'main', title: 'Корпоративные заказы', kind: Kind.CORPORATE },
  { key: 'partners', title: 'Партнёрские заказы', kind: Kind.PARTNER },
  { key: 'brand', title: 'Бренд заказы', kind: Kind.BRAND },
  { key: 'sport', title: 'Спорт заказы', kind: Kind.SPORT },
  { key: 'sport', title: 'Продуктовые заказы', kind: Kind.PRODUCTS },
  { key: 'others', title: 'Другие заказы', kind: Kind.OTHER },
]

function CorpScreen() {
  const tabContent = useMemo(
    () =>
      TAB_CONFIG.map(({ key, title, component: Component, kind }) => (
        <Tab eventKey={key} title={title} key={key}>
          {Component ? <Component /> : <Corp kind={kind} />}
        </Tab>
      )),
    []
  )

  return (
    <Layout title="Корпы и партнёры">
      <Tabs className="mb-4" defaultActiveKey="companies">
        {tabContent}
      </Tabs>
    </Layout>
  )
}

export default CorpScreen
