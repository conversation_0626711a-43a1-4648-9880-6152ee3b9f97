import { useState } from 'react'
import { <PERSON><PERSON>rdi<PERSON>, Button, Col, Modal, OverlayTrigger, <PERSON>, Tab, Tabs } from 'react-bootstrap'
import { Link, useNavigate, useParams } from 'react-router-dom'

import AdvancedPagination from '@/components/AdvancedPagination/AdvancedPagination'
import { CheckPermission } from '@/components/CheckPermission/CheckPermission'
import Layout from '@/components/Layout/Layout'
import ListCard from '@/components/ListCard/ListCard'
import Loader from '@/components/Loader/Loader'
import ConfirmDeleteModal from '@/components/Modal/ConfirmDeleteModal/ConfirmDeleteModal'
import AddButton from '@/components/ui/AddButton/AddButton'

import { accessConfig } from '@/accessConfig'
import { useDeleteCompany, useGetCompany, useGetCorpTeams } from '@/features/corp/api'
import { Order, useGetCompanyOrders } from '@/features/orders'
import { useDeleteCompanyOrder } from '@/features/orders/api/deleteCompanyOrder'
import { useGetOrderTickets } from '@/features/tickets'
import CompaniesForm from '@/pages/CorpScreen/components/CompaniesForm/CompaniesForm'
import { CorpTeams } from '@/pages/CorpScreen/components/CorpTeams/CorpTeams'
import { renderEditBtnTooltip } from '@/pages/ShopScreen/components/constants.jsx'
import { getImageSrc } from '@/utils/images'

import styles from './CompanyScreen.module.scss'
import { contactsData, mainData } from './companyScreenData'

function CompanyScreen() {
  const [activeTab, setActiveTab] = useState('')
  const [confirmModal, setConfirmModal] = useState(false)
  const [isOpenModalForm, setIsModalForm] = useState(false)
  const [activeAccordion, setActiveAccordion] = useState(0)
  const [renderValues, setRenderValues] = useState([])

  const navigate = useNavigate()
  const { public_id } = useParams()

  const { data: companyData } = useGetCompany(public_id)
  const company = companyData?.data
  const { data: teamsData } = useGetCorpTeams(public_id)
  const teams = teamsData?.data?.values
  const deleteCompanyMutation = useDeleteCompany()

  const getCompanyOrdersQuery = useGetCompanyOrders(public_id)
  const orders = getCompanyOrdersQuery?.data?.data?.values
  const getOrderTicketsQuery = useGetOrderTickets(orders?.[activeAccordion]?.public_id)
  const tickets = getOrderTicketsQuery?.data?.data?.values
  const deleteOrderMutation = useDeleteCompanyOrder({ companyPublicId: public_id })

  const handleDeleteItem = () => {
    setConfirmModal(false)

    deleteCompanyMutation.mutate(company?.public_id, {
      onSuccess: () => {
        navigate('/corp')
      },
    })
  }

  const handleUpdateOrders = () => {
    getCompanyOrdersQuery.refetch()
  }

  const handleUpdateTickets = () => {
    getOrderTicketsQuery.refetch()
  }

  const handleSelectTab = (evt) => {
    setActiveTab(evt)
  }

  const handleEdit = () => {
    setIsModalForm(true)
  }

  const handleCloseFormModal = () => {
    setIsModalForm(false)
  }

  const handleConfirmDelete = () => {
    setConfirmModal(true)
  }

  const handleToggleAccordion = (index) => {
    setActiveAccordion(index)
  }

  const handleDeleteOrder = (order) => {
    const data = {
      public_id: order.public_id,
    }

    deleteOrderMutation.mutate(data)
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col xs="auto" md="auto">
          <div className={styles.logoWrap}>
            <img className={styles.logo} src={getImageSrc(company?.logo)} alt="" />
          </div>
        </Col>
        <Col>
          <Row>
            <h2 className="mb-0">{company?.name}</h2>
          </Row>
          <Row>
            <Col className="px-0" md="auto">
              <Button onClick={handleEdit} className={styles.link} variant="link">
                Редактировать
              </Button>
            </Col>
            <Col className="px-0" md="auto">
              <Button onClick={handleConfirmDelete} className={styles.link} variant="link">
                Удалить
              </Button>
            </Col>
          </Row>
        </Col>
        <CheckPermission allowedRoles={accessConfig.company.createOrderButton}>
          <Col md="auto">
            <AddButton as={Link} to={`/corp/form/?company-create=${company?.public_id}`} label="Создать заказ" />
          </Col>
        </CheckPermission>
      </Row>

      <Row className="mb-4 g-3">
        <Col xs={12} md={6}>
          <ListCard data={mainData} event={company} xsLabel={4} xsValue={8} />
        </Col>
        <Col xs={12} md={6}>
          <ListCard data={contactsData} event={company} />
        </Col>
      </Row>

      <Tabs className="mb-4" defaultActiveKey="corp-orders" onSelect={handleSelectTab}>
        <Tab eventKey="corp-orders" title="Заказы">
          <Loader isLoading={getCompanyOrdersQuery.isLoading} text="Загрузка заказов">
            {orders?.length > 0 ? (
              <Accordion defaultActiveKey={`${orders?.[0].public_id}`}>
                {renderValues?.length > 0 &&
                  renderValues?.map((order, index) => (
                    <Accordion.Item eventKey={`${order.public_id}`} key={order.public_id}>
                      <Accordion.Header onClick={() => handleToggleAccordion(index)}>
                        {order?.amount ? order.amount.toLocaleString() : 0} &#8381;, {order.email}
                        <OverlayTrigger
                          placement="left"
                          delay={{ show: 250, hide: 400 }}
                          overlay={renderEditBtnTooltip}
                        >
                          <Button
                            as={Link}
                            to={`/corp/form/${order.public_id}`}
                            onClick={(e) => e.stopPropagation()}
                            className={styles.btnEdit}
                            type="button"
                          />
                        </OverlayTrigger>
                      </Accordion.Header>
                      <Accordion.Body className="pt-3">
                        {activeAccordion === index && (
                          <Order
                            order={order}
                            teams={teams}
                            tickets={tickets}
                            onDeleteOrder={handleDeleteOrder}
                            onUpdateOrders={handleUpdateOrders}
                            onUpdateTickets={handleUpdateTickets}
                            isLoadingTickets={getOrderTicketsQuery.isLoading}
                          />
                        )}
                      </Accordion.Body>
                    </Accordion.Item>
                  ))}

                <Row className="mt-4">
                  <Col>
                    <AdvancedPagination values={orders} setValues={setRenderValues} />
                  </Col>
                </Row>
              </Accordion>
            ) : (
              <p className="d-flex justify-content-center">Нет заказов</p>
            )}
          </Loader>
        </Tab>
        <Tab eventKey="corp-teams" title="Команды">
          {'corp-teams' === activeTab && <CorpTeams company_public_id={public_id} />}
        </Tab>
      </Tabs>

      <Modal show={isOpenModalForm} onHide={handleCloseFormModal}>
        <Modal.Header closeButton />
        <Modal.Body>
          <CompaniesForm selectedItem={company} onCloseModal={handleCloseFormModal} />
        </Modal.Body>
        <Modal.Footer>
          <Button type="submit" variant="success" form="form">
            Сохранить
          </Button>
          <Button onClick={handleCloseFormModal} variant="outline-secondary">
            Отмена
          </Button>
        </Modal.Footer>
      </Modal>

      <ConfirmDeleteModal
        isShow={confirmModal}
        onClose={setConfirmModal}
        onDeleteItem={handleDeleteItem}
        text="Удалить компанию?"
      />
    </Layout>
  )
}

export default CompanyScreen
