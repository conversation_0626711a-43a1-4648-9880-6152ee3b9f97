import { useEffect, useMemo, useState } from 'react'
import { Col, FloatingLabel, FormControl, FormSelect, Row } from 'react-bootstrap'
import { useSelector } from 'react-redux'

import { useGetEvents } from '@/features/events/api/getEvents'
import { getEditSupplier } from '@/store/supplier/selectors'
import { getFormatTime } from '@/utils/date'

import { vat } from '../../CreateOrderScreen/createOrderData'

function SupplierForm({ formData, onChangeForm }) {
  const activeEditItem = useSelector(getEditSupplier)
  const { data: eventsData } = useGetEvents()
  const [selectedEvent, setSelectedEvent] = useState(null)
  const events = useMemo(() => {
    return eventsData?.data?.values || []
  }, [eventsData?.data?.values])

  useEffect(() => {
    if (Object.keys(activeEditItem).length > 0) {
      const event = events.find((event) => event.public_id === activeEditItem.event_city.event_public_id)

      if (event) {
        setSelectedEvent(event)
      }
    }
  }, [activeEditItem, events])

  const handleChangeEvent = (evt) => {
    const eventId = evt.target.value
    const event = events.find((event) => event.public_id === eventId)

    if (event) {
      setSelectedEvent(event)
    }
  }

  const handleChangeCity = (evt) => {
    const cityId = evt.target.value

    if (cityId !== 0) {
      onChangeForm({ ...formData, event_city: { public_id: cityId } })
    }
  }

  const handleChangeField = (evt) => {
    const name = evt.target.name
    const value = name === 'vat' ? +evt.target.value : evt.target.value

    onChangeForm({ ...formData, [name]: value })
  }

  return (
    <Row className="g-3 mb-4">
      <Col md={4}>
        <FloatingLabel controlId="eventLabelTicket" label="Событие">
          <FormSelect
            onChange={handleChangeEvent}
            value={selectedEvent?.public_id || ''}
            name="event_public_id"
            aria-label="Событие"
          >
            <option value="">выберите один из вариантов</option>
            {events.map((event) => (
              <option value={event.public_id} key={event.public_id}>
                {event.title}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="cityLabelTicket" label="Город">
          <FormSelect
            onChange={handleChangeCity}
            value={formData?.event_city?.public_id || ''}
            name="city_id"
            aria-label="Город"
          >
            <option value="">выберите один из вариантов</option>
            {selectedEvent?.event_city.map((city) => (
              <option value={city.public_id} key={city.city.id}>
                {city.address} — {getFormatTime(city.start_time, city.timezone)}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="suppliernameLabelOrder" label="Имя">
          <FormControl
            onChange={handleChangeField}
            value={formData.suppliername || ''}
            name="suppliername"
            type="text"
            placeholder="suppliername"
          />
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="supplierphoneLabelOrder" label="Телефон">
          <FormControl
            onChange={handleChangeField}
            value={formData.supplierphone || ''}
            name="supplierphone"
            type="text"
            placeholder="supplierphone"
          />
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="supplierinnLabelOrder" label="ИНН">
          <FormControl
            onChange={handleChangeField}
            value={formData.supplierinn || ''}
            name="supplierinn"
            type="text"
            placeholder="supplierinn"
          />
        </FloatingLabel>
      </Col>

      <Col md={4}>
        <FloatingLabel controlId="vatLabelTicket" label="НДС">
          <FormSelect onChange={handleChangeField} value={formData?.vat || ''} name="vat" aria-label="НДС">
            <option value="">выберите один из вариантов</option>
            {vat.map((item) => (
              <option value={item.code} key={item.code}>
                {item.desc}
              </option>
            ))}
          </FormSelect>
        </FloatingLabel>
      </Col>
    </Row>
  )
}

export default SupplierForm
