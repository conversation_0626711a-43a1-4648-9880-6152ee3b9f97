import useAxios from 'axios-hooks'
import { useEffect, useState } from 'react'
import { Button, Col, Row } from 'react-bootstrap'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import SupplierForm from './components/SupplierForm'
import Layout from '../../components/Layout/Layout'
import SuccessModal from '../../components/Modal/SuccessModal/SuccessModal'
import { APIRoute, AppRoute } from '../../const'
import { setSupplierEdit } from '../../store/action'
import { getEditSupplier } from '../../store/supplier/selectors'

function CreateSupplierScreen() {
  const [, api] = useAxios(
    {
      url: APIRoute.SUPPLIER,
      method: 'POST',
    },
    {
      manual: true,
    }
  )
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [formData, setFormData] = useState({})
  const activeEditItem = useSelector(getEditSupplier)
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  useEffect(() => {
    if (Object.keys(activeEditItem).length > 0) {
      setFormData({
        suppliername: activeEditItem.suppliername,
        event_city: {
          public_id: activeEditItem.event_city.public_id,
        },
        public_id: activeEditItem.public_id,
        supplierinn: activeEditItem.supplierinn,
        vat: activeEditItem.vat,
        supplierphone: activeEditItem.supplierphone,
      })
    }
  }, [activeEditItem])

  const handleSubmitForm = () => {
    if (Object.keys(activeEditItem).length > 0) {
      api({ method: 'PUT', data: formData }).then((r) => {
        if (r.status === 200) setShowSuccessModal(true)
      })
    } else {
      api({ data: formData }).then((r) => {
        if (r.status === 200) {
          setShowSuccessModal(true)
          setFormData({})
        }
      })
    }
  }

  const handleBackToList = () => {
    dispatch(setSupplierEdit({}))
    navigate(AppRoute.SUPPLIER)
  }

  return (
    <Layout>
      <Row className="mb-3">
        <Col>
          <Button onClick={handleBackToList} variant="outline-secondary">
            <i className="bi bi-arrow-left me-2" />
            Вернуться к списку агентов
          </Button>
        </Col>
      </Row>
      <Row>
        <h3 className="mb-3">{Object.keys(activeEditItem).length > 0 ? 'Редактирование записи' : 'Создание записи'}</h3>
      </Row>

      <SupplierForm formData={formData} onChangeForm={setFormData} editItem={activeEditItem} />

      <Row className="justify-content-md-center mb-5">
        <Col className="d-grid" md={{ span: 4, offset: 4 }}>
          <Button onClick={handleSubmitForm} variant="primary" type="button" size="lg">
            {Object.keys(activeEditItem).length > 0 ? 'Обновить запись' : 'Создать запись'}
          </Button>
        </Col>
      </Row>

      <SuccessModal show={showSuccessModal} handleCloseModal={setShowSuccessModal} description="Данные сохранены." />
    </Layout>
  )
}

export default CreateSupplierScreen
